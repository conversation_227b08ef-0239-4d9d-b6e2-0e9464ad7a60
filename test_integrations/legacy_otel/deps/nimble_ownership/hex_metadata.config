{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/dashbitco/nimble_ownership">>}]}.
{<<"name">>,<<"nimble_ownership">>}.
{<<"version">>,<<"1.0.1">>}.
{<<"description">>,<<"Track ownership of resources across processes.">>}.
{<<"elixir">>,<<"~> 1.11">>}.
{<<"app">>,<<"nimble_ownership">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/nimble_ownership">>,<<"lib/nimble_ownership/error.ex">>,
  <<"lib/nimble_ownership.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
