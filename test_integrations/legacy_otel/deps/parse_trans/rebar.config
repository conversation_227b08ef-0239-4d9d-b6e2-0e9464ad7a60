%% -*- erlang-indent-level: 4;indent-tabs-mode: nil -*-
%% --------------------------------------------------
%% This file is provided to you under the Apache License,
%% Version 2.0 (the "License"); you may not use this file
%% except in compliance with the License.  You may obtain
%% a copy of the License at
%%
%%   http://www.apache.org/licenses/LICENSE-2.0
%%
%% Unless required by applicable law or agreed to in writing,
%% software distributed under the License is distributed on an
%% "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
%% KIND, either express or implied.  See the License for the
%% specific language governing permissions and limitations
%% under the License.
%% --------------------------------------------------

{minimum_otp_vsn, "21.0"}.

{erl_first_files, ["src/parse_trans.erl",
                   "src/parse_trans_pp.erl",
                   "src/parse_trans_codegen.erl"]}.

{erl_opts, [debug_info
           ]}.
{xref_checks, [undefined_function_calls]}.

{profiles,
 [{docs,
   [
    {deps, [{edown, "0.8.1"}]},
    {edoc_opts, [{doclet, edown_doclet},
                 {top_level_readme,
                  {"./README.md",
                   "http://github.com/uwiger/parse_trans",
                   "master"}}]}
   ]},
  {test,
   [
    {extra_src_dirs, ["examples"]}
   ]
  }
 ]}.
