%%
%% this file is generated do not modify
%% see ../uc_spec/gen_idna_mapping.escript

-module(idna_mapping).
-compile(compressed).
-export([uts46_map/1]).
uts46_map(47) -> '3';
uts46_map(65) -> {'M', [97]};
uts46_map(66) -> {'M', [98]};
uts46_map(67) -> {'M', [99]};
uts46_map(68) -> {'M', [100]};
uts46_map(69) -> {'M', [101]};
uts46_map(70) -> {'M', [102]};
uts46_map(71) -> {'M', [103]};
uts46_map(72) -> {'M', [104]};
uts46_map(73) -> {'M', [105]};
uts46_map(74) -> {'M', [106]};
uts46_map(75) -> {'M', [107]};
uts46_map(76) -> {'M', [108]};
uts46_map(77) -> {'M', [109]};
uts46_map(78) -> {'M', [110]};
uts46_map(79) -> {'M', [111]};
uts46_map(80) -> {'M', [112]};
uts46_map(81) -> {'M', [113]};
uts46_map(82) -> {'M', [114]};
uts46_map(83) -> {'M', [115]};
uts46_map(84) -> {'M', [116]};
uts46_map(85) -> {'M', [117]};
uts46_map(86) -> {'M', [118]};
uts46_map(87) -> {'M', [119]};
uts46_map(88) -> {'M', [120]};
uts46_map(89) -> {'M', [121]};
uts46_map(90) -> {'M', [122]};
uts46_map(160) -> {'3', [32]};
uts46_map(168) -> {'3', [32,776]};
uts46_map(169) -> 'V';
uts46_map(170) -> {'M', [97]};
uts46_map(173) -> 'I';
uts46_map(174) -> 'V';
uts46_map(175) -> {'3', [32,772]};
uts46_map(178) -> {'M', [50]};
uts46_map(179) -> {'M', [51]};
uts46_map(180) -> {'3', [32,769]};
uts46_map(181) -> {'M', [956]};
uts46_map(182) -> 'V';
uts46_map(183) -> 'V';
uts46_map(184) -> {'3', [32,807]};
uts46_map(185) -> {'M', [49]};
uts46_map(186) -> {'M', [111]};
uts46_map(187) -> 'V';
uts46_map(188) -> {'M', [49,8260,52]};
uts46_map(189) -> {'M', [49,8260,50]};
uts46_map(190) -> {'M', [51,8260,52]};
uts46_map(191) -> 'V';
uts46_map(192) -> {'M', [224]};
uts46_map(193) -> {'M', [225]};
uts46_map(194) -> {'M', [226]};
uts46_map(195) -> {'M', [227]};
uts46_map(196) -> {'M', [228]};
uts46_map(197) -> {'M', [229]};
uts46_map(198) -> {'M', [230]};
uts46_map(199) -> {'M', [231]};
uts46_map(200) -> {'M', [232]};
uts46_map(201) -> {'M', [233]};
uts46_map(202) -> {'M', [234]};
uts46_map(203) -> {'M', [235]};
uts46_map(204) -> {'M', [236]};
uts46_map(205) -> {'M', [237]};
uts46_map(206) -> {'M', [238]};
uts46_map(207) -> {'M', [239]};
uts46_map(208) -> {'M', [240]};
uts46_map(209) -> {'M', [241]};
uts46_map(210) -> {'M', [242]};
uts46_map(211) -> {'M', [243]};
uts46_map(212) -> {'M', [244]};
uts46_map(213) -> {'M', [245]};
uts46_map(214) -> {'M', [246]};
uts46_map(215) -> 'V';
uts46_map(216) -> {'M', [248]};
uts46_map(217) -> {'M', [249]};
uts46_map(218) -> {'M', [250]};
uts46_map(219) -> {'M', [251]};
uts46_map(220) -> {'M', [252]};
uts46_map(221) -> {'M', [253]};
uts46_map(222) -> {'M', [254]};
uts46_map(223) -> {'D', [115,115]};
uts46_map(247) -> 'V';
uts46_map(256) -> {'M', [257]};
uts46_map(257) -> 'V';
uts46_map(258) -> {'M', [259]};
uts46_map(259) -> 'V';
uts46_map(260) -> {'M', [261]};
uts46_map(261) -> 'V';
uts46_map(262) -> {'M', [263]};
uts46_map(263) -> 'V';
uts46_map(264) -> {'M', [265]};
uts46_map(265) -> 'V';
uts46_map(266) -> {'M', [267]};
uts46_map(267) -> 'V';
uts46_map(268) -> {'M', [269]};
uts46_map(269) -> 'V';
uts46_map(270) -> {'M', [271]};
uts46_map(271) -> 'V';
uts46_map(272) -> {'M', [273]};
uts46_map(273) -> 'V';
uts46_map(274) -> {'M', [275]};
uts46_map(275) -> 'V';
uts46_map(276) -> {'M', [277]};
uts46_map(277) -> 'V';
uts46_map(278) -> {'M', [279]};
uts46_map(279) -> 'V';
uts46_map(280) -> {'M', [281]};
uts46_map(281) -> 'V';
uts46_map(282) -> {'M', [283]};
uts46_map(283) -> 'V';
uts46_map(284) -> {'M', [285]};
uts46_map(285) -> 'V';
uts46_map(286) -> {'M', [287]};
uts46_map(287) -> 'V';
uts46_map(288) -> {'M', [289]};
uts46_map(289) -> 'V';
uts46_map(290) -> {'M', [291]};
uts46_map(291) -> 'V';
uts46_map(292) -> {'M', [293]};
uts46_map(293) -> 'V';
uts46_map(294) -> {'M', [295]};
uts46_map(295) -> 'V';
uts46_map(296) -> {'M', [297]};
uts46_map(297) -> 'V';
uts46_map(298) -> {'M', [299]};
uts46_map(299) -> 'V';
uts46_map(300) -> {'M', [301]};
uts46_map(301) -> 'V';
uts46_map(302) -> {'M', [303]};
uts46_map(303) -> 'V';
uts46_map(304) -> {'M', [105,775]};
uts46_map(305) -> 'V';
uts46_map(308) -> {'M', [309]};
uts46_map(309) -> 'V';
uts46_map(310) -> {'M', [311]};
uts46_map(313) -> {'M', [314]};
uts46_map(314) -> 'V';
uts46_map(315) -> {'M', [316]};
uts46_map(316) -> 'V';
uts46_map(317) -> {'M', [318]};
uts46_map(318) -> 'V';
uts46_map(321) -> {'M', [322]};
uts46_map(322) -> 'V';
uts46_map(323) -> {'M', [324]};
uts46_map(324) -> 'V';
uts46_map(325) -> {'M', [326]};
uts46_map(326) -> 'V';
uts46_map(327) -> {'M', [328]};
uts46_map(328) -> 'V';
uts46_map(329) -> {'M', [700,110]};
uts46_map(330) -> {'M', [331]};
uts46_map(331) -> 'V';
uts46_map(332) -> {'M', [333]};
uts46_map(333) -> 'V';
uts46_map(334) -> {'M', [335]};
uts46_map(335) -> 'V';
uts46_map(336) -> {'M', [337]};
uts46_map(337) -> 'V';
uts46_map(338) -> {'M', [339]};
uts46_map(339) -> 'V';
uts46_map(340) -> {'M', [341]};
uts46_map(341) -> 'V';
uts46_map(342) -> {'M', [343]};
uts46_map(343) -> 'V';
uts46_map(344) -> {'M', [345]};
uts46_map(345) -> 'V';
uts46_map(346) -> {'M', [347]};
uts46_map(347) -> 'V';
uts46_map(348) -> {'M', [349]};
uts46_map(349) -> 'V';
uts46_map(350) -> {'M', [351]};
uts46_map(351) -> 'V';
uts46_map(352) -> {'M', [353]};
uts46_map(353) -> 'V';
uts46_map(354) -> {'M', [355]};
uts46_map(355) -> 'V';
uts46_map(356) -> {'M', [357]};
uts46_map(357) -> 'V';
uts46_map(358) -> {'M', [359]};
uts46_map(359) -> 'V';
uts46_map(360) -> {'M', [361]};
uts46_map(361) -> 'V';
uts46_map(362) -> {'M', [363]};
uts46_map(363) -> 'V';
uts46_map(364) -> {'M', [365]};
uts46_map(365) -> 'V';
uts46_map(366) -> {'M', [367]};
uts46_map(367) -> 'V';
uts46_map(368) -> {'M', [369]};
uts46_map(369) -> 'V';
uts46_map(370) -> {'M', [371]};
uts46_map(371) -> 'V';
uts46_map(372) -> {'M', [373]};
uts46_map(373) -> 'V';
uts46_map(374) -> {'M', [375]};
uts46_map(375) -> 'V';
uts46_map(376) -> {'M', [255]};
uts46_map(377) -> {'M', [378]};
uts46_map(378) -> 'V';
uts46_map(379) -> {'M', [380]};
uts46_map(380) -> 'V';
uts46_map(381) -> {'M', [382]};
uts46_map(382) -> 'V';
uts46_map(383) -> {'M', [115]};
uts46_map(384) -> 'V';
uts46_map(385) -> {'M', [595]};
uts46_map(386) -> {'M', [387]};
uts46_map(387) -> 'V';
uts46_map(388) -> {'M', [389]};
uts46_map(389) -> 'V';
uts46_map(390) -> {'M', [596]};
uts46_map(391) -> {'M', [392]};
uts46_map(392) -> 'V';
uts46_map(393) -> {'M', [598]};
uts46_map(394) -> {'M', [599]};
uts46_map(395) -> {'M', [396]};
uts46_map(398) -> {'M', [477]};
uts46_map(399) -> {'M', [601]};
uts46_map(400) -> {'M', [603]};
uts46_map(401) -> {'M', [402]};
uts46_map(402) -> 'V';
uts46_map(403) -> {'M', [608]};
uts46_map(404) -> {'M', [611]};
uts46_map(405) -> 'V';
uts46_map(406) -> {'M', [617]};
uts46_map(407) -> {'M', [616]};
uts46_map(408) -> {'M', [409]};
uts46_map(412) -> {'M', [623]};
uts46_map(413) -> {'M', [626]};
uts46_map(414) -> 'V';
uts46_map(415) -> {'M', [629]};
uts46_map(416) -> {'M', [417]};
uts46_map(417) -> 'V';
uts46_map(418) -> {'M', [419]};
uts46_map(419) -> 'V';
uts46_map(420) -> {'M', [421]};
uts46_map(421) -> 'V';
uts46_map(422) -> {'M', [640]};
uts46_map(423) -> {'M', [424]};
uts46_map(424) -> 'V';
uts46_map(425) -> {'M', [643]};
uts46_map(428) -> {'M', [429]};
uts46_map(429) -> 'V';
uts46_map(430) -> {'M', [648]};
uts46_map(431) -> {'M', [432]};
uts46_map(432) -> 'V';
uts46_map(433) -> {'M', [650]};
uts46_map(434) -> {'M', [651]};
uts46_map(435) -> {'M', [436]};
uts46_map(436) -> 'V';
uts46_map(437) -> {'M', [438]};
uts46_map(438) -> 'V';
uts46_map(439) -> {'M', [658]};
uts46_map(440) -> {'M', [441]};
uts46_map(444) -> {'M', [445]};
uts46_map(461) -> {'M', [462]};
uts46_map(462) -> 'V';
uts46_map(463) -> {'M', [464]};
uts46_map(464) -> 'V';
uts46_map(465) -> {'M', [466]};
uts46_map(466) -> 'V';
uts46_map(467) -> {'M', [468]};
uts46_map(468) -> 'V';
uts46_map(469) -> {'M', [470]};
uts46_map(470) -> 'V';
uts46_map(471) -> {'M', [472]};
uts46_map(472) -> 'V';
uts46_map(473) -> {'M', [474]};
uts46_map(474) -> 'V';
uts46_map(475) -> {'M', [476]};
uts46_map(478) -> {'M', [479]};
uts46_map(479) -> 'V';
uts46_map(480) -> {'M', [481]};
uts46_map(481) -> 'V';
uts46_map(482) -> {'M', [483]};
uts46_map(483) -> 'V';
uts46_map(484) -> {'M', [485]};
uts46_map(485) -> 'V';
uts46_map(486) -> {'M', [487]};
uts46_map(487) -> 'V';
uts46_map(488) -> {'M', [489]};
uts46_map(489) -> 'V';
uts46_map(490) -> {'M', [491]};
uts46_map(491) -> 'V';
uts46_map(492) -> {'M', [493]};
uts46_map(493) -> 'V';
uts46_map(494) -> {'M', [495]};
uts46_map(500) -> {'M', [501]};
uts46_map(501) -> 'V';
uts46_map(502) -> {'M', [405]};
uts46_map(503) -> {'M', [447]};
uts46_map(504) -> {'M', [505]};
uts46_map(505) -> 'V';
uts46_map(506) -> {'M', [507]};
uts46_map(507) -> 'V';
uts46_map(508) -> {'M', [509]};
uts46_map(509) -> 'V';
uts46_map(510) -> {'M', [511]};
uts46_map(511) -> 'V';
uts46_map(512) -> {'M', [513]};
uts46_map(513) -> 'V';
uts46_map(514) -> {'M', [515]};
uts46_map(515) -> 'V';
uts46_map(516) -> {'M', [517]};
uts46_map(517) -> 'V';
uts46_map(518) -> {'M', [519]};
uts46_map(519) -> 'V';
uts46_map(520) -> {'M', [521]};
uts46_map(521) -> 'V';
uts46_map(522) -> {'M', [523]};
uts46_map(523) -> 'V';
uts46_map(524) -> {'M', [525]};
uts46_map(525) -> 'V';
uts46_map(526) -> {'M', [527]};
uts46_map(527) -> 'V';
uts46_map(528) -> {'M', [529]};
uts46_map(529) -> 'V';
uts46_map(530) -> {'M', [531]};
uts46_map(531) -> 'V';
uts46_map(532) -> {'M', [533]};
uts46_map(533) -> 'V';
uts46_map(534) -> {'M', [535]};
uts46_map(535) -> 'V';
uts46_map(536) -> {'M', [537]};
uts46_map(537) -> 'V';
uts46_map(538) -> {'M', [539]};
uts46_map(539) -> 'V';
uts46_map(540) -> {'M', [541]};
uts46_map(541) -> 'V';
uts46_map(542) -> {'M', [543]};
uts46_map(543) -> 'V';
uts46_map(544) -> {'M', [414]};
uts46_map(545) -> 'V';
uts46_map(546) -> {'M', [547]};
uts46_map(547) -> 'V';
uts46_map(548) -> {'M', [549]};
uts46_map(549) -> 'V';
uts46_map(550) -> {'M', [551]};
uts46_map(551) -> 'V';
uts46_map(552) -> {'M', [553]};
uts46_map(553) -> 'V';
uts46_map(554) -> {'M', [555]};
uts46_map(555) -> 'V';
uts46_map(556) -> {'M', [557]};
uts46_map(557) -> 'V';
uts46_map(558) -> {'M', [559]};
uts46_map(559) -> 'V';
uts46_map(560) -> {'M', [561]};
uts46_map(561) -> 'V';
uts46_map(562) -> {'M', [563]};
uts46_map(563) -> 'V';
uts46_map(570) -> {'M', [11365]};
uts46_map(571) -> {'M', [572]};
uts46_map(572) -> 'V';
uts46_map(573) -> {'M', [410]};
uts46_map(574) -> {'M', [11366]};
uts46_map(577) -> {'M', [578]};
uts46_map(578) -> 'V';
uts46_map(579) -> {'M', [384]};
uts46_map(580) -> {'M', [649]};
uts46_map(581) -> {'M', [652]};
uts46_map(582) -> {'M', [583]};
uts46_map(583) -> 'V';
uts46_map(584) -> {'M', [585]};
uts46_map(585) -> 'V';
uts46_map(586) -> {'M', [587]};
uts46_map(587) -> 'V';
uts46_map(588) -> {'M', [589]};
uts46_map(589) -> 'V';
uts46_map(590) -> {'M', [591]};
uts46_map(591) -> 'V';
uts46_map(688) -> {'M', [104]};
uts46_map(689) -> {'M', [614]};
uts46_map(690) -> {'M', [106]};
uts46_map(691) -> {'M', [114]};
uts46_map(692) -> {'M', [633]};
uts46_map(693) -> {'M', [635]};
uts46_map(694) -> {'M', [641]};
uts46_map(695) -> {'M', [119]};
uts46_map(696) -> {'M', [121]};
uts46_map(728) -> {'3', [32,774]};
uts46_map(729) -> {'3', [32,775]};
uts46_map(730) -> {'3', [32,778]};
uts46_map(731) -> {'3', [32,808]};
uts46_map(732) -> {'3', [32,771]};
uts46_map(733) -> {'3', [32,779]};
uts46_map(734) -> 'V';
uts46_map(735) -> 'V';
uts46_map(736) -> {'M', [611]};
uts46_map(737) -> {'M', [108]};
uts46_map(738) -> {'M', [115]};
uts46_map(739) -> {'M', [120]};
uts46_map(740) -> {'M', [661]};
uts46_map(748) -> 'V';
uts46_map(749) -> 'V';
uts46_map(750) -> 'V';
uts46_map(832) -> {'M', [768]};
uts46_map(833) -> {'M', [769]};
uts46_map(834) -> 'V';
uts46_map(835) -> {'M', [787]};
uts46_map(836) -> {'M', [776,769]};
uts46_map(837) -> {'M', [953]};
uts46_map(847) -> 'I';
uts46_map(866) -> 'V';
uts46_map(880) -> {'M', [881]};
uts46_map(881) -> 'V';
uts46_map(882) -> {'M', [883]};
uts46_map(883) -> 'V';
uts46_map(884) -> {'M', [697]};
uts46_map(885) -> 'V';
uts46_map(886) -> {'M', [887]};
uts46_map(887) -> 'V';
uts46_map(890) -> {'3', [32,953]};
uts46_map(894) -> {'3', [59]};
uts46_map(895) -> {'M', [1011]};
uts46_map(900) -> {'3', [32,769]};
uts46_map(901) -> {'3', [32,776,769]};
uts46_map(902) -> {'M', [940]};
uts46_map(903) -> {'M', [183]};
uts46_map(904) -> {'M', [941]};
uts46_map(905) -> {'M', [942]};
uts46_map(906) -> {'M', [943]};
uts46_map(907) -> 'X';
uts46_map(908) -> {'M', [972]};
uts46_map(909) -> 'X';
uts46_map(910) -> {'M', [973]};
uts46_map(911) -> {'M', [974]};
uts46_map(912) -> 'V';
uts46_map(913) -> {'M', [945]};
uts46_map(914) -> {'M', [946]};
uts46_map(915) -> {'M', [947]};
uts46_map(916) -> {'M', [948]};
uts46_map(917) -> {'M', [949]};
uts46_map(918) -> {'M', [950]};
uts46_map(919) -> {'M', [951]};
uts46_map(920) -> {'M', [952]};
uts46_map(921) -> {'M', [953]};
uts46_map(922) -> {'M', [954]};
uts46_map(923) -> {'M', [955]};
uts46_map(924) -> {'M', [956]};
uts46_map(925) -> {'M', [957]};
uts46_map(926) -> {'M', [958]};
uts46_map(927) -> {'M', [959]};
uts46_map(928) -> {'M', [960]};
uts46_map(929) -> {'M', [961]};
uts46_map(930) -> 'X';
uts46_map(931) -> {'M', [963]};
uts46_map(932) -> {'M', [964]};
uts46_map(933) -> {'M', [965]};
uts46_map(934) -> {'M', [966]};
uts46_map(935) -> {'M', [967]};
uts46_map(936) -> {'M', [968]};
uts46_map(937) -> {'M', [969]};
uts46_map(938) -> {'M', [970]};
uts46_map(939) -> {'M', [971]};
uts46_map(962) -> {'D', [963]};
uts46_map(975) -> {'M', [983]};
uts46_map(976) -> {'M', [946]};
uts46_map(977) -> {'M', [952]};
uts46_map(978) -> {'M', [965]};
uts46_map(979) -> {'M', [973]};
uts46_map(980) -> {'M', [971]};
uts46_map(981) -> {'M', [966]};
uts46_map(982) -> {'M', [960]};
uts46_map(983) -> 'V';
uts46_map(984) -> {'M', [985]};
uts46_map(985) -> 'V';
uts46_map(986) -> {'M', [987]};
uts46_map(987) -> 'V';
uts46_map(988) -> {'M', [989]};
uts46_map(989) -> 'V';
uts46_map(990) -> {'M', [991]};
uts46_map(991) -> 'V';
uts46_map(992) -> {'M', [993]};
uts46_map(993) -> 'V';
uts46_map(994) -> {'M', [995]};
uts46_map(995) -> 'V';
uts46_map(996) -> {'M', [997]};
uts46_map(997) -> 'V';
uts46_map(998) -> {'M', [999]};
uts46_map(999) -> 'V';
uts46_map(1000) -> {'M', [1001]};
uts46_map(1001) -> 'V';
uts46_map(1002) -> {'M', [1003]};
uts46_map(1003) -> 'V';
uts46_map(1004) -> {'M', [1005]};
uts46_map(1005) -> 'V';
uts46_map(1006) -> {'M', [1007]};
uts46_map(1007) -> 'V';
uts46_map(1008) -> {'M', [954]};
uts46_map(1009) -> {'M', [961]};
uts46_map(1010) -> {'M', [963]};
uts46_map(1011) -> 'V';
uts46_map(1012) -> {'M', [952]};
uts46_map(1013) -> {'M', [949]};
uts46_map(1014) -> 'V';
uts46_map(1015) -> {'M', [1016]};
uts46_map(1016) -> 'V';
uts46_map(1017) -> {'M', [963]};
uts46_map(1018) -> {'M', [1019]};
uts46_map(1019) -> 'V';
uts46_map(1020) -> 'V';
uts46_map(1021) -> {'M', [891]};
uts46_map(1022) -> {'M', [892]};
uts46_map(1023) -> {'M', [893]};
uts46_map(1024) -> {'M', [1104]};
uts46_map(1025) -> {'M', [1105]};
uts46_map(1026) -> {'M', [1106]};
uts46_map(1027) -> {'M', [1107]};
uts46_map(1028) -> {'M', [1108]};
uts46_map(1029) -> {'M', [1109]};
uts46_map(1030) -> {'M', [1110]};
uts46_map(1031) -> {'M', [1111]};
uts46_map(1032) -> {'M', [1112]};
uts46_map(1033) -> {'M', [1113]};
uts46_map(1034) -> {'M', [1114]};
uts46_map(1035) -> {'M', [1115]};
uts46_map(1036) -> {'M', [1116]};
uts46_map(1037) -> {'M', [1117]};
uts46_map(1038) -> {'M', [1118]};
uts46_map(1039) -> {'M', [1119]};
uts46_map(1040) -> {'M', [1072]};
uts46_map(1041) -> {'M', [1073]};
uts46_map(1042) -> {'M', [1074]};
uts46_map(1043) -> {'M', [1075]};
uts46_map(1044) -> {'M', [1076]};
uts46_map(1045) -> {'M', [1077]};
uts46_map(1046) -> {'M', [1078]};
uts46_map(1047) -> {'M', [1079]};
uts46_map(1048) -> {'M', [1080]};
uts46_map(1049) -> {'M', [1081]};
uts46_map(1050) -> {'M', [1082]};
uts46_map(1051) -> {'M', [1083]};
uts46_map(1052) -> {'M', [1084]};
uts46_map(1053) -> {'M', [1085]};
uts46_map(1054) -> {'M', [1086]};
uts46_map(1055) -> {'M', [1087]};
uts46_map(1056) -> {'M', [1088]};
uts46_map(1057) -> {'M', [1089]};
uts46_map(1058) -> {'M', [1090]};
uts46_map(1059) -> {'M', [1091]};
uts46_map(1060) -> {'M', [1092]};
uts46_map(1061) -> {'M', [1093]};
uts46_map(1062) -> {'M', [1094]};
uts46_map(1063) -> {'M', [1095]};
uts46_map(1064) -> {'M', [1096]};
uts46_map(1065) -> {'M', [1097]};
uts46_map(1066) -> {'M', [1098]};
uts46_map(1067) -> {'M', [1099]};
uts46_map(1068) -> {'M', [1100]};
uts46_map(1069) -> {'M', [1101]};
uts46_map(1070) -> {'M', [1102]};
uts46_map(1071) -> {'M', [1103]};
uts46_map(1104) -> 'V';
uts46_map(1117) -> 'V';
uts46_map(1120) -> {'M', [1121]};
uts46_map(1121) -> 'V';
uts46_map(1122) -> {'M', [1123]};
uts46_map(1123) -> 'V';
uts46_map(1124) -> {'M', [1125]};
uts46_map(1125) -> 'V';
uts46_map(1126) -> {'M', [1127]};
uts46_map(1127) -> 'V';
uts46_map(1128) -> {'M', [1129]};
uts46_map(1129) -> 'V';
uts46_map(1130) -> {'M', [1131]};
uts46_map(1131) -> 'V';
uts46_map(1132) -> {'M', [1133]};
uts46_map(1133) -> 'V';
uts46_map(1134) -> {'M', [1135]};
uts46_map(1135) -> 'V';
uts46_map(1136) -> {'M', [1137]};
uts46_map(1137) -> 'V';
uts46_map(1138) -> {'M', [1139]};
uts46_map(1139) -> 'V';
uts46_map(1140) -> {'M', [1141]};
uts46_map(1141) -> 'V';
uts46_map(1142) -> {'M', [1143]};
uts46_map(1143) -> 'V';
uts46_map(1144) -> {'M', [1145]};
uts46_map(1145) -> 'V';
uts46_map(1146) -> {'M', [1147]};
uts46_map(1147) -> 'V';
uts46_map(1148) -> {'M', [1149]};
uts46_map(1149) -> 'V';
uts46_map(1150) -> {'M', [1151]};
uts46_map(1151) -> 'V';
uts46_map(1152) -> {'M', [1153]};
uts46_map(1153) -> 'V';
uts46_map(1154) -> 'V';
uts46_map(1159) -> 'V';
uts46_map(1162) -> {'M', [1163]};
uts46_map(1163) -> 'V';
uts46_map(1164) -> {'M', [1165]};
uts46_map(1165) -> 'V';
uts46_map(1166) -> {'M', [1167]};
uts46_map(1167) -> 'V';
uts46_map(1168) -> {'M', [1169]};
uts46_map(1169) -> 'V';
uts46_map(1170) -> {'M', [1171]};
uts46_map(1171) -> 'V';
uts46_map(1172) -> {'M', [1173]};
uts46_map(1173) -> 'V';
uts46_map(1174) -> {'M', [1175]};
uts46_map(1175) -> 'V';
uts46_map(1176) -> {'M', [1177]};
uts46_map(1177) -> 'V';
uts46_map(1178) -> {'M', [1179]};
uts46_map(1179) -> 'V';
uts46_map(1180) -> {'M', [1181]};
uts46_map(1181) -> 'V';
uts46_map(1182) -> {'M', [1183]};
uts46_map(1183) -> 'V';
uts46_map(1184) -> {'M', [1185]};
uts46_map(1185) -> 'V';
uts46_map(1186) -> {'M', [1187]};
uts46_map(1187) -> 'V';
uts46_map(1188) -> {'M', [1189]};
uts46_map(1189) -> 'V';
uts46_map(1190) -> {'M', [1191]};
uts46_map(1191) -> 'V';
uts46_map(1192) -> {'M', [1193]};
uts46_map(1193) -> 'V';
uts46_map(1194) -> {'M', [1195]};
uts46_map(1195) -> 'V';
uts46_map(1196) -> {'M', [1197]};
uts46_map(1197) -> 'V';
uts46_map(1198) -> {'M', [1199]};
uts46_map(1199) -> 'V';
uts46_map(1200) -> {'M', [1201]};
uts46_map(1201) -> 'V';
uts46_map(1202) -> {'M', [1203]};
uts46_map(1203) -> 'V';
uts46_map(1204) -> {'M', [1205]};
uts46_map(1205) -> 'V';
uts46_map(1206) -> {'M', [1207]};
uts46_map(1207) -> 'V';
uts46_map(1208) -> {'M', [1209]};
uts46_map(1209) -> 'V';
uts46_map(1210) -> {'M', [1211]};
uts46_map(1211) -> 'V';
uts46_map(1212) -> {'M', [1213]};
uts46_map(1213) -> 'V';
uts46_map(1214) -> {'M', [1215]};
uts46_map(1215) -> 'V';
uts46_map(1216) -> 'X';
uts46_map(1217) -> {'M', [1218]};
uts46_map(1218) -> 'V';
uts46_map(1219) -> {'M', [1220]};
uts46_map(1220) -> 'V';
uts46_map(1221) -> {'M', [1222]};
uts46_map(1222) -> 'V';
uts46_map(1223) -> {'M', [1224]};
uts46_map(1224) -> 'V';
uts46_map(1225) -> {'M', [1226]};
uts46_map(1226) -> 'V';
uts46_map(1227) -> {'M', [1228]};
uts46_map(1228) -> 'V';
uts46_map(1229) -> {'M', [1230]};
uts46_map(1230) -> 'V';
uts46_map(1231) -> 'V';
uts46_map(1232) -> {'M', [1233]};
uts46_map(1233) -> 'V';
uts46_map(1234) -> {'M', [1235]};
uts46_map(1235) -> 'V';
uts46_map(1236) -> {'M', [1237]};
uts46_map(1237) -> 'V';
uts46_map(1238) -> {'M', [1239]};
uts46_map(1239) -> 'V';
uts46_map(1240) -> {'M', [1241]};
uts46_map(1241) -> 'V';
uts46_map(1242) -> {'M', [1243]};
uts46_map(1243) -> 'V';
uts46_map(1244) -> {'M', [1245]};
uts46_map(1245) -> 'V';
uts46_map(1246) -> {'M', [1247]};
uts46_map(1247) -> 'V';
uts46_map(1248) -> {'M', [1249]};
uts46_map(1249) -> 'V';
uts46_map(1250) -> {'M', [1251]};
uts46_map(1251) -> 'V';
uts46_map(1252) -> {'M', [1253]};
uts46_map(1253) -> 'V';
uts46_map(1254) -> {'M', [1255]};
uts46_map(1255) -> 'V';
uts46_map(1256) -> {'M', [1257]};
uts46_map(1257) -> 'V';
uts46_map(1258) -> {'M', [1259]};
uts46_map(1259) -> 'V';
uts46_map(1260) -> {'M', [1261]};
uts46_map(1261) -> 'V';
uts46_map(1262) -> {'M', [1263]};
uts46_map(1263) -> 'V';
uts46_map(1264) -> {'M', [1265]};
uts46_map(1265) -> 'V';
uts46_map(1266) -> {'M', [1267]};
uts46_map(1267) -> 'V';
uts46_map(1268) -> {'M', [1269]};
uts46_map(1269) -> 'V';
uts46_map(1270) -> {'M', [1271]};
uts46_map(1271) -> 'V';
uts46_map(1272) -> {'M', [1273]};
uts46_map(1273) -> 'V';
uts46_map(1274) -> {'M', [1275]};
uts46_map(1275) -> 'V';
uts46_map(1276) -> {'M', [1277]};
uts46_map(1277) -> 'V';
uts46_map(1278) -> {'M', [1279]};
uts46_map(1279) -> 'V';
uts46_map(1280) -> {'M', [1281]};
uts46_map(1281) -> 'V';
uts46_map(1282) -> {'M', [1283]};
uts46_map(1283) -> 'V';
uts46_map(1284) -> {'M', [1285]};
uts46_map(1285) -> 'V';
uts46_map(1286) -> {'M', [1287]};
uts46_map(1287) -> 'V';
uts46_map(1288) -> {'M', [1289]};
uts46_map(1289) -> 'V';
uts46_map(1290) -> {'M', [1291]};
uts46_map(1291) -> 'V';
uts46_map(1292) -> {'M', [1293]};
uts46_map(1293) -> 'V';
uts46_map(1294) -> {'M', [1295]};
uts46_map(1295) -> 'V';
uts46_map(1296) -> {'M', [1297]};
uts46_map(1297) -> 'V';
uts46_map(1298) -> {'M', [1299]};
uts46_map(1299) -> 'V';
uts46_map(1300) -> {'M', [1301]};
uts46_map(1301) -> 'V';
uts46_map(1302) -> {'M', [1303]};
uts46_map(1303) -> 'V';
uts46_map(1304) -> {'M', [1305]};
uts46_map(1305) -> 'V';
uts46_map(1306) -> {'M', [1307]};
uts46_map(1307) -> 'V';
uts46_map(1308) -> {'M', [1309]};
uts46_map(1309) -> 'V';
uts46_map(1310) -> {'M', [1311]};
uts46_map(1311) -> 'V';
uts46_map(1312) -> {'M', [1313]};
uts46_map(1313) -> 'V';
uts46_map(1314) -> {'M', [1315]};
uts46_map(1315) -> 'V';
uts46_map(1316) -> {'M', [1317]};
uts46_map(1317) -> 'V';
uts46_map(1318) -> {'M', [1319]};
uts46_map(1319) -> 'V';
uts46_map(1320) -> {'M', [1321]};
uts46_map(1321) -> 'V';
uts46_map(1322) -> {'M', [1323]};
uts46_map(1323) -> 'V';
uts46_map(1324) -> {'M', [1325]};
uts46_map(1325) -> 'V';
uts46_map(1326) -> {'M', [1327]};
uts46_map(1327) -> 'V';
uts46_map(1328) -> 'X';
uts46_map(1329) -> {'M', [1377]};
uts46_map(1330) -> {'M', [1378]};
uts46_map(1331) -> {'M', [1379]};
uts46_map(1332) -> {'M', [1380]};
uts46_map(1333) -> {'M', [1381]};
uts46_map(1334) -> {'M', [1382]};
uts46_map(1335) -> {'M', [1383]};
uts46_map(1336) -> {'M', [1384]};
uts46_map(1337) -> {'M', [1385]};
uts46_map(1338) -> {'M', [1386]};
uts46_map(1339) -> {'M', [1387]};
uts46_map(1340) -> {'M', [1388]};
uts46_map(1341) -> {'M', [1389]};
uts46_map(1342) -> {'M', [1390]};
uts46_map(1343) -> {'M', [1391]};
uts46_map(1344) -> {'M', [1392]};
uts46_map(1345) -> {'M', [1393]};
uts46_map(1346) -> {'M', [1394]};
uts46_map(1347) -> {'M', [1395]};
uts46_map(1348) -> {'M', [1396]};
uts46_map(1349) -> {'M', [1397]};
uts46_map(1350) -> {'M', [1398]};
uts46_map(1351) -> {'M', [1399]};
uts46_map(1352) -> {'M', [1400]};
uts46_map(1353) -> {'M', [1401]};
uts46_map(1354) -> {'M', [1402]};
uts46_map(1355) -> {'M', [1403]};
uts46_map(1356) -> {'M', [1404]};
uts46_map(1357) -> {'M', [1405]};
uts46_map(1358) -> {'M', [1406]};
uts46_map(1359) -> {'M', [1407]};
uts46_map(1360) -> {'M', [1408]};
uts46_map(1361) -> {'M', [1409]};
uts46_map(1362) -> {'M', [1410]};
uts46_map(1363) -> {'M', [1411]};
uts46_map(1364) -> {'M', [1412]};
uts46_map(1365) -> {'M', [1413]};
uts46_map(1366) -> {'M', [1414]};
uts46_map(1369) -> 'V';
uts46_map(1376) -> 'V';
uts46_map(1415) -> {'M', [1381,1410]};
uts46_map(1416) -> 'V';
uts46_map(1417) -> 'V';
uts46_map(1418) -> 'V';
uts46_map(1423) -> 'V';
uts46_map(1424) -> 'X';
uts46_map(1442) -> 'V';
uts46_map(1466) -> 'V';
uts46_map(1470) -> 'V';
uts46_map(1471) -> 'V';
uts46_map(1472) -> 'V';
uts46_map(1475) -> 'V';
uts46_map(1476) -> 'V';
uts46_map(1477) -> 'V';
uts46_map(1478) -> 'V';
uts46_map(1479) -> 'V';
uts46_map(1519) -> 'V';
uts46_map(1540) -> 'X';
uts46_map(1541) -> 'X';
uts46_map(1547) -> 'V';
uts46_map(1548) -> 'V';
uts46_map(1563) -> 'V';
uts46_map(1564) -> 'X';
uts46_map(1565) -> 'X';
uts46_map(1566) -> 'V';
uts46_map(1567) -> 'V';
uts46_map(1568) -> 'V';
uts46_map(1600) -> 'V';
uts46_map(1631) -> 'V';
uts46_map(1653) -> {'M', [1575,1652]};
uts46_map(1654) -> {'M', [1608,1652]};
uts46_map(1655) -> {'M', [1735,1652]};
uts46_map(1656) -> {'M', [1610,1652]};
uts46_map(1727) -> 'V';
uts46_map(1743) -> 'V';
uts46_map(1748) -> 'V';
uts46_map(1757) -> 'X';
uts46_map(1758) -> 'V';
uts46_map(1769) -> 'V';
uts46_map(1791) -> 'V';
uts46_map(1806) -> 'X';
uts46_map(1807) -> 'X';
uts46_map(1969) -> 'V';
uts46_map(2045) -> 'V';
uts46_map(2111) -> 'X';
uts46_map(2142) -> 'V';
uts46_map(2143) -> 'X';
uts46_map(2208) -> 'V';
uts46_map(2209) -> 'V';
uts46_map(2229) -> 'X';
uts46_map(2259) -> 'V';
uts46_map(2274) -> 'X';
uts46_map(2275) -> 'V';
uts46_map(2303) -> 'V';
uts46_map(2304) -> 'V';
uts46_map(2308) -> 'V';
uts46_map(2382) -> 'V';
uts46_map(2383) -> 'V';
uts46_map(2389) -> 'V';
uts46_map(2392) -> {'M', [2325,2364]};
uts46_map(2393) -> {'M', [2326,2364]};
uts46_map(2394) -> {'M', [2327,2364]};
uts46_map(2395) -> {'M', [2332,2364]};
uts46_map(2396) -> {'M', [2337,2364]};
uts46_map(2397) -> {'M', [2338,2364]};
uts46_map(2398) -> {'M', [2347,2364]};
uts46_map(2399) -> {'M', [2351,2364]};
uts46_map(2416) -> 'V';
uts46_map(2424) -> 'V';
uts46_map(2429) -> 'V';
uts46_map(2432) -> 'V';
uts46_map(2436) -> 'X';
uts46_map(2473) -> 'X';
uts46_map(2481) -> 'X';
uts46_map(2482) -> 'V';
uts46_map(2492) -> 'V';
uts46_map(2493) -> 'V';
uts46_map(2510) -> 'V';
uts46_map(2519) -> 'V';
uts46_map(2524) -> {'M', [2465,2492]};
uts46_map(2525) -> {'M', [2466,2492]};
uts46_map(2526) -> 'X';
uts46_map(2527) -> {'M', [2479,2492]};
uts46_map(2555) -> 'V';
uts46_map(2556) -> 'V';
uts46_map(2557) -> 'V';
uts46_map(2558) -> 'V';
uts46_map(2561) -> 'V';
uts46_map(2562) -> 'V';
uts46_map(2563) -> 'V';
uts46_map(2564) -> 'X';
uts46_map(2601) -> 'X';
uts46_map(2609) -> 'X';
uts46_map(2610) -> 'V';
uts46_map(2611) -> {'M', [2610,2620]};
uts46_map(2612) -> 'X';
uts46_map(2613) -> 'V';
uts46_map(2614) -> {'M', [2616,2620]};
uts46_map(2615) -> 'X';
uts46_map(2620) -> 'V';
uts46_map(2621) -> 'X';
uts46_map(2641) -> 'V';
uts46_map(2649) -> {'M', [2582,2620]};
uts46_map(2650) -> {'M', [2583,2620]};
uts46_map(2651) -> {'M', [2588,2620]};
uts46_map(2652) -> 'V';
uts46_map(2653) -> 'X';
uts46_map(2654) -> {'M', [2603,2620]};
uts46_map(2677) -> 'V';
uts46_map(2678) -> 'V';
uts46_map(2692) -> 'X';
uts46_map(2700) -> 'V';
uts46_map(2701) -> 'V';
uts46_map(2702) -> 'X';
uts46_map(2706) -> 'X';
uts46_map(2729) -> 'X';
uts46_map(2737) -> 'X';
uts46_map(2740) -> 'X';
uts46_map(2758) -> 'X';
uts46_map(2762) -> 'X';
uts46_map(2768) -> 'V';
uts46_map(2784) -> 'V';
uts46_map(2800) -> 'V';
uts46_map(2801) -> 'V';
uts46_map(2809) -> 'V';
uts46_map(2816) -> 'X';
uts46_map(2820) -> 'X';
uts46_map(2857) -> 'X';
uts46_map(2865) -> 'X';
uts46_map(2868) -> 'X';
uts46_map(2869) -> 'V';
uts46_map(2884) -> 'V';
uts46_map(2901) -> 'V';
uts46_map(2908) -> {'M', [2849,2876]};
uts46_map(2909) -> {'M', [2850,2876]};
uts46_map(2910) -> 'X';
uts46_map(2928) -> 'V';
uts46_map(2929) -> 'V';
uts46_map(2948) -> 'X';
uts46_map(2961) -> 'X';
uts46_map(2971) -> 'X';
uts46_map(2972) -> 'V';
uts46_map(2973) -> 'X';
uts46_map(2998) -> 'V';
uts46_map(3017) -> 'X';
uts46_map(3024) -> 'V';
uts46_map(3031) -> 'V';
uts46_map(3046) -> 'V';
uts46_map(3072) -> 'V';
uts46_map(3076) -> 'V';
uts46_map(3085) -> 'X';
uts46_map(3089) -> 'X';
uts46_map(3113) -> 'X';
uts46_map(3124) -> 'V';
uts46_map(3133) -> 'V';
uts46_map(3141) -> 'X';
uts46_map(3145) -> 'X';
uts46_map(3159) -> 'X';
uts46_map(3162) -> 'V';
uts46_map(3191) -> 'V';
uts46_map(3200) -> 'V';
uts46_map(3201) -> 'V';
uts46_map(3204) -> 'V';
uts46_map(3213) -> 'X';
uts46_map(3217) -> 'X';
uts46_map(3241) -> 'X';
uts46_map(3252) -> 'X';
uts46_map(3269) -> 'X';
uts46_map(3273) -> 'X';
uts46_map(3294) -> 'V';
uts46_map(3295) -> 'X';
uts46_map(3312) -> 'X';
uts46_map(3328) -> 'V';
uts46_map(3329) -> 'V';
uts46_map(3332) -> 'V';
uts46_map(3341) -> 'X';
uts46_map(3345) -> 'X';
uts46_map(3369) -> 'V';
uts46_map(3386) -> 'V';
uts46_map(3389) -> 'V';
uts46_map(3396) -> 'V';
uts46_map(3397) -> 'X';
uts46_map(3401) -> 'X';
uts46_map(3406) -> 'V';
uts46_map(3407) -> 'V';
uts46_map(3415) -> 'V';
uts46_map(3423) -> 'V';
uts46_map(3449) -> 'V';
uts46_map(3456) -> 'X';
uts46_map(3457) -> 'V';
uts46_map(3460) -> 'X';
uts46_map(3506) -> 'X';
uts46_map(3516) -> 'X';
uts46_map(3517) -> 'V';
uts46_map(3530) -> 'V';
uts46_map(3541) -> 'X';
uts46_map(3542) -> 'V';
uts46_map(3543) -> 'X';
uts46_map(3572) -> 'V';
uts46_map(3635) -> {'M', [3661,3634]};
uts46_map(3647) -> 'V';
uts46_map(3663) -> 'V';
uts46_map(3715) -> 'X';
uts46_map(3716) -> 'V';
uts46_map(3717) -> 'X';
uts46_map(3718) -> 'V';
uts46_map(3721) -> 'V';
uts46_map(3722) -> 'V';
uts46_map(3723) -> 'X';
uts46_map(3724) -> 'V';
uts46_map(3725) -> 'V';
uts46_map(3736) -> 'V';
uts46_map(3744) -> 'V';
uts46_map(3748) -> 'X';
uts46_map(3749) -> 'V';
uts46_map(3750) -> 'X';
uts46_map(3751) -> 'V';
uts46_map(3756) -> 'V';
uts46_map(3763) -> {'M', [3789,3762]};
uts46_map(3770) -> 'V';
uts46_map(3781) -> 'X';
uts46_map(3782) -> 'V';
uts46_map(3783) -> 'X';
uts46_map(3804) -> {'M', [3755,3737]};
uts46_map(3805) -> {'M', [3755,3745]};
uts46_map(3840) -> 'V';
uts46_map(3851) -> 'V';
uts46_map(3852) -> {'M', [3851]};
uts46_map(3893) -> 'V';
uts46_map(3894) -> 'V';
uts46_map(3895) -> 'V';
uts46_map(3896) -> 'V';
uts46_map(3897) -> 'V';
uts46_map(3907) -> {'M', [3906,4023]};
uts46_map(3912) -> 'X';
uts46_map(3917) -> {'M', [3916,4023]};
uts46_map(3922) -> {'M', [3921,4023]};
uts46_map(3927) -> {'M', [3926,4023]};
uts46_map(3932) -> {'M', [3931,4023]};
uts46_map(3945) -> {'M', [3904,4021]};
uts46_map(3946) -> 'V';
uts46_map(3955) -> {'M', [3953,3954]};
uts46_map(3956) -> 'V';
uts46_map(3957) -> {'M', [3953,3956]};
uts46_map(3958) -> {'M', [4018,3968]};
uts46_map(3959) -> {'M', [4018,3953,3968]};
uts46_map(3960) -> {'M', [4019,3968]};
uts46_map(3961) -> {'M', [4019,3953,3968]};
uts46_map(3969) -> {'M', [3953,3968]};
uts46_map(3973) -> 'V';
uts46_map(3987) -> {'M', [3986,4023]};
uts46_map(3990) -> 'V';
uts46_map(3991) -> 'V';
uts46_map(3992) -> 'X';
uts46_map(3997) -> {'M', [3996,4023]};
uts46_map(4002) -> {'M', [4001,4023]};
uts46_map(4007) -> {'M', [4006,4023]};
uts46_map(4012) -> {'M', [4011,4023]};
uts46_map(4013) -> 'V';
uts46_map(4024) -> 'V';
uts46_map(4025) -> {'M', [3984,4021]};
uts46_map(4029) -> 'X';
uts46_map(4038) -> 'V';
uts46_map(4045) -> 'X';
uts46_map(4046) -> 'V';
uts46_map(4047) -> 'V';
uts46_map(4130) -> 'V';
uts46_map(4136) -> 'V';
uts46_map(4139) -> 'V';
uts46_map(4294) -> 'X';
uts46_map(4295) -> {'M', [11559]};
uts46_map(4301) -> {'M', [11565]};
uts46_map(4347) -> 'V';
uts46_map(4348) -> {'M', [4316]};
uts46_map(4615) -> 'V';
uts46_map(4679) -> 'V';
uts46_map(4680) -> 'V';
uts46_map(4681) -> 'X';
uts46_map(4695) -> 'X';
uts46_map(4696) -> 'V';
uts46_map(4697) -> 'X';
uts46_map(4743) -> 'V';
uts46_map(4744) -> 'V';
uts46_map(4745) -> 'X';
uts46_map(4783) -> 'V';
uts46_map(4784) -> 'V';
uts46_map(4785) -> 'X';
uts46_map(4799) -> 'X';
uts46_map(4800) -> 'V';
uts46_map(4801) -> 'X';
uts46_map(4815) -> 'V';
uts46_map(4823) -> 'X';
uts46_map(4847) -> 'V';
uts46_map(4879) -> 'V';
uts46_map(4880) -> 'V';
uts46_map(4881) -> 'X';
uts46_map(4895) -> 'V';
uts46_map(4935) -> 'V';
uts46_map(4959) -> 'V';
uts46_map(4960) -> 'V';
uts46_map(5109) -> 'V';
uts46_map(5112) -> {'M', [5104]};
uts46_map(5113) -> {'M', [5105]};
uts46_map(5114) -> {'M', [5106]};
uts46_map(5115) -> {'M', [5107]};
uts46_map(5116) -> {'M', [5108]};
uts46_map(5117) -> {'M', [5109]};
uts46_map(5120) -> 'V';
uts46_map(5760) -> 'X';
uts46_map(5901) -> 'X';
uts46_map(5997) -> 'X';
uts46_map(6001) -> 'X';
uts46_map(6103) -> 'V';
uts46_map(6108) -> 'V';
uts46_map(6109) -> 'V';
uts46_map(6150) -> 'X';
uts46_map(6158) -> 'X';
uts46_map(6159) -> 'X';
uts46_map(6264) -> 'V';
uts46_map(6314) -> 'V';
uts46_map(6431) -> 'X';
uts46_map(6464) -> 'V';
uts46_map(6618) -> 'V';
uts46_map(6751) -> 'X';
uts46_map(6823) -> 'V';
uts46_map(6846) -> 'V';
uts46_map(7296) -> {'M', [1074]};
uts46_map(7297) -> {'M', [1076]};
uts46_map(7298) -> {'M', [1086]};
uts46_map(7299) -> {'M', [1089]};
uts46_map(7302) -> {'M', [1098]};
uts46_map(7303) -> {'M', [1123]};
uts46_map(7304) -> {'M', [42571]};
uts46_map(7312) -> {'M', [4304]};
uts46_map(7313) -> {'M', [4305]};
uts46_map(7314) -> {'M', [4306]};
uts46_map(7315) -> {'M', [4307]};
uts46_map(7316) -> {'M', [4308]};
uts46_map(7317) -> {'M', [4309]};
uts46_map(7318) -> {'M', [4310]};
uts46_map(7319) -> {'M', [4311]};
uts46_map(7320) -> {'M', [4312]};
uts46_map(7321) -> {'M', [4313]};
uts46_map(7322) -> {'M', [4314]};
uts46_map(7323) -> {'M', [4315]};
uts46_map(7324) -> {'M', [4316]};
uts46_map(7325) -> {'M', [4317]};
uts46_map(7326) -> {'M', [4318]};
uts46_map(7327) -> {'M', [4319]};
uts46_map(7328) -> {'M', [4320]};
uts46_map(7329) -> {'M', [4321]};
uts46_map(7330) -> {'M', [4322]};
uts46_map(7331) -> {'M', [4323]};
uts46_map(7332) -> {'M', [4324]};
uts46_map(7333) -> {'M', [4325]};
uts46_map(7334) -> {'M', [4326]};
uts46_map(7335) -> {'M', [4327]};
uts46_map(7336) -> {'M', [4328]};
uts46_map(7337) -> {'M', [4329]};
uts46_map(7338) -> {'M', [4330]};
uts46_map(7339) -> {'M', [4331]};
uts46_map(7340) -> {'M', [4332]};
uts46_map(7341) -> {'M', [4333]};
uts46_map(7342) -> {'M', [4334]};
uts46_map(7343) -> {'M', [4335]};
uts46_map(7344) -> {'M', [4336]};
uts46_map(7345) -> {'M', [4337]};
uts46_map(7346) -> {'M', [4338]};
uts46_map(7347) -> {'M', [4339]};
uts46_map(7348) -> {'M', [4340]};
uts46_map(7349) -> {'M', [4341]};
uts46_map(7350) -> {'M', [4342]};
uts46_map(7351) -> {'M', [4343]};
uts46_map(7352) -> {'M', [4344]};
uts46_map(7353) -> {'M', [4345]};
uts46_map(7354) -> {'M', [4346]};
uts46_map(7357) -> {'M', [4349]};
uts46_map(7358) -> {'M', [4350]};
uts46_map(7359) -> {'M', [4351]};
uts46_map(7379) -> 'V';
uts46_map(7415) -> 'V';
uts46_map(7418) -> 'V';
uts46_map(7468) -> {'M', [97]};
uts46_map(7469) -> {'M', [230]};
uts46_map(7470) -> {'M', [98]};
uts46_map(7471) -> 'V';
uts46_map(7472) -> {'M', [100]};
uts46_map(7473) -> {'M', [101]};
uts46_map(7474) -> {'M', [477]};
uts46_map(7475) -> {'M', [103]};
uts46_map(7476) -> {'M', [104]};
uts46_map(7477) -> {'M', [105]};
uts46_map(7478) -> {'M', [106]};
uts46_map(7479) -> {'M', [107]};
uts46_map(7480) -> {'M', [108]};
uts46_map(7481) -> {'M', [109]};
uts46_map(7482) -> {'M', [110]};
uts46_map(7483) -> 'V';
uts46_map(7484) -> {'M', [111]};
uts46_map(7485) -> {'M', [547]};
uts46_map(7486) -> {'M', [112]};
uts46_map(7487) -> {'M', [114]};
uts46_map(7488) -> {'M', [116]};
uts46_map(7489) -> {'M', [117]};
uts46_map(7490) -> {'M', [119]};
uts46_map(7491) -> {'M', [97]};
uts46_map(7492) -> {'M', [592]};
uts46_map(7493) -> {'M', [593]};
uts46_map(7494) -> {'M', [7426]};
uts46_map(7495) -> {'M', [98]};
uts46_map(7496) -> {'M', [100]};
uts46_map(7497) -> {'M', [101]};
uts46_map(7498) -> {'M', [601]};
uts46_map(7499) -> {'M', [603]};
uts46_map(7500) -> {'M', [604]};
uts46_map(7501) -> {'M', [103]};
uts46_map(7502) -> 'V';
uts46_map(7503) -> {'M', [107]};
uts46_map(7504) -> {'M', [109]};
uts46_map(7505) -> {'M', [331]};
uts46_map(7506) -> {'M', [111]};
uts46_map(7507) -> {'M', [596]};
uts46_map(7508) -> {'M', [7446]};
uts46_map(7509) -> {'M', [7447]};
uts46_map(7510) -> {'M', [112]};
uts46_map(7511) -> {'M', [116]};
uts46_map(7512) -> {'M', [117]};
uts46_map(7513) -> {'M', [7453]};
uts46_map(7514) -> {'M', [623]};
uts46_map(7515) -> {'M', [118]};
uts46_map(7516) -> {'M', [7461]};
uts46_map(7517) -> {'M', [946]};
uts46_map(7518) -> {'M', [947]};
uts46_map(7519) -> {'M', [948]};
uts46_map(7520) -> {'M', [966]};
uts46_map(7521) -> {'M', [967]};
uts46_map(7522) -> {'M', [105]};
uts46_map(7523) -> {'M', [114]};
uts46_map(7524) -> {'M', [117]};
uts46_map(7525) -> {'M', [118]};
uts46_map(7526) -> {'M', [946]};
uts46_map(7527) -> {'M', [947]};
uts46_map(7528) -> {'M', [961]};
uts46_map(7529) -> {'M', [966]};
uts46_map(7530) -> {'M', [967]};
uts46_map(7531) -> 'V';
uts46_map(7544) -> {'M', [1085]};
uts46_map(7579) -> {'M', [594]};
uts46_map(7580) -> {'M', [99]};
uts46_map(7581) -> {'M', [597]};
uts46_map(7582) -> {'M', [240]};
uts46_map(7583) -> {'M', [604]};
uts46_map(7584) -> {'M', [102]};
uts46_map(7585) -> {'M', [607]};
uts46_map(7586) -> {'M', [609]};
uts46_map(7587) -> {'M', [613]};
uts46_map(7588) -> {'M', [616]};
uts46_map(7589) -> {'M', [617]};
uts46_map(7590) -> {'M', [618]};
uts46_map(7591) -> {'M', [7547]};
uts46_map(7592) -> {'M', [669]};
uts46_map(7593) -> {'M', [621]};
uts46_map(7594) -> {'M', [7557]};
uts46_map(7595) -> {'M', [671]};
uts46_map(7596) -> {'M', [625]};
uts46_map(7597) -> {'M', [624]};
uts46_map(7598) -> {'M', [626]};
uts46_map(7599) -> {'M', [627]};
uts46_map(7600) -> {'M', [628]};
uts46_map(7601) -> {'M', [629]};
uts46_map(7602) -> {'M', [632]};
uts46_map(7603) -> {'M', [642]};
uts46_map(7604) -> {'M', [643]};
uts46_map(7605) -> {'M', [427]};
uts46_map(7606) -> {'M', [649]};
uts46_map(7607) -> {'M', [650]};
uts46_map(7608) -> {'M', [7452]};
uts46_map(7609) -> {'M', [651]};
uts46_map(7610) -> {'M', [652]};
uts46_map(7611) -> {'M', [122]};
uts46_map(7612) -> {'M', [656]};
uts46_map(7613) -> {'M', [657]};
uts46_map(7614) -> {'M', [658]};
uts46_map(7615) -> {'M', [952]};
uts46_map(7674) -> 'X';
uts46_map(7675) -> 'V';
uts46_map(7676) -> 'V';
uts46_map(7677) -> 'V';
uts46_map(7680) -> {'M', [7681]};
uts46_map(7681) -> 'V';
uts46_map(7682) -> {'M', [7683]};
uts46_map(7683) -> 'V';
uts46_map(7684) -> {'M', [7685]};
uts46_map(7685) -> 'V';
uts46_map(7686) -> {'M', [7687]};
uts46_map(7687) -> 'V';
uts46_map(7688) -> {'M', [7689]};
uts46_map(7689) -> 'V';
uts46_map(7690) -> {'M', [7691]};
uts46_map(7691) -> 'V';
uts46_map(7692) -> {'M', [7693]};
uts46_map(7693) -> 'V';
uts46_map(7694) -> {'M', [7695]};
uts46_map(7695) -> 'V';
uts46_map(7696) -> {'M', [7697]};
uts46_map(7697) -> 'V';
uts46_map(7698) -> {'M', [7699]};
uts46_map(7699) -> 'V';
uts46_map(7700) -> {'M', [7701]};
uts46_map(7701) -> 'V';
uts46_map(7702) -> {'M', [7703]};
uts46_map(7703) -> 'V';
uts46_map(7704) -> {'M', [7705]};
uts46_map(7705) -> 'V';
uts46_map(7706) -> {'M', [7707]};
uts46_map(7707) -> 'V';
uts46_map(7708) -> {'M', [7709]};
uts46_map(7709) -> 'V';
uts46_map(7710) -> {'M', [7711]};
uts46_map(7711) -> 'V';
uts46_map(7712) -> {'M', [7713]};
uts46_map(7713) -> 'V';
uts46_map(7714) -> {'M', [7715]};
uts46_map(7715) -> 'V';
uts46_map(7716) -> {'M', [7717]};
uts46_map(7717) -> 'V';
uts46_map(7718) -> {'M', [7719]};
uts46_map(7719) -> 'V';
uts46_map(7720) -> {'M', [7721]};
uts46_map(7721) -> 'V';
uts46_map(7722) -> {'M', [7723]};
uts46_map(7723) -> 'V';
uts46_map(7724) -> {'M', [7725]};
uts46_map(7725) -> 'V';
uts46_map(7726) -> {'M', [7727]};
uts46_map(7727) -> 'V';
uts46_map(7728) -> {'M', [7729]};
uts46_map(7729) -> 'V';
uts46_map(7730) -> {'M', [7731]};
uts46_map(7731) -> 'V';
uts46_map(7732) -> {'M', [7733]};
uts46_map(7733) -> 'V';
uts46_map(7734) -> {'M', [7735]};
uts46_map(7735) -> 'V';
uts46_map(7736) -> {'M', [7737]};
uts46_map(7737) -> 'V';
uts46_map(7738) -> {'M', [7739]};
uts46_map(7739) -> 'V';
uts46_map(7740) -> {'M', [7741]};
uts46_map(7741) -> 'V';
uts46_map(7742) -> {'M', [7743]};
uts46_map(7743) -> 'V';
uts46_map(7744) -> {'M', [7745]};
uts46_map(7745) -> 'V';
uts46_map(7746) -> {'M', [7747]};
uts46_map(7747) -> 'V';
uts46_map(7748) -> {'M', [7749]};
uts46_map(7749) -> 'V';
uts46_map(7750) -> {'M', [7751]};
uts46_map(7751) -> 'V';
uts46_map(7752) -> {'M', [7753]};
uts46_map(7753) -> 'V';
uts46_map(7754) -> {'M', [7755]};
uts46_map(7755) -> 'V';
uts46_map(7756) -> {'M', [7757]};
uts46_map(7757) -> 'V';
uts46_map(7758) -> {'M', [7759]};
uts46_map(7759) -> 'V';
uts46_map(7760) -> {'M', [7761]};
uts46_map(7761) -> 'V';
uts46_map(7762) -> {'M', [7763]};
uts46_map(7763) -> 'V';
uts46_map(7764) -> {'M', [7765]};
uts46_map(7765) -> 'V';
uts46_map(7766) -> {'M', [7767]};
uts46_map(7767) -> 'V';
uts46_map(7768) -> {'M', [7769]};
uts46_map(7769) -> 'V';
uts46_map(7770) -> {'M', [7771]};
uts46_map(7771) -> 'V';
uts46_map(7772) -> {'M', [7773]};
uts46_map(7773) -> 'V';
uts46_map(7774) -> {'M', [7775]};
uts46_map(7775) -> 'V';
uts46_map(7776) -> {'M', [7777]};
uts46_map(7777) -> 'V';
uts46_map(7778) -> {'M', [7779]};
uts46_map(7779) -> 'V';
uts46_map(7780) -> {'M', [7781]};
uts46_map(7781) -> 'V';
uts46_map(7782) -> {'M', [7783]};
uts46_map(7783) -> 'V';
uts46_map(7784) -> {'M', [7785]};
uts46_map(7785) -> 'V';
uts46_map(7786) -> {'M', [7787]};
uts46_map(7787) -> 'V';
uts46_map(7788) -> {'M', [7789]};
uts46_map(7789) -> 'V';
uts46_map(7790) -> {'M', [7791]};
uts46_map(7791) -> 'V';
uts46_map(7792) -> {'M', [7793]};
uts46_map(7793) -> 'V';
uts46_map(7794) -> {'M', [7795]};
uts46_map(7795) -> 'V';
uts46_map(7796) -> {'M', [7797]};
uts46_map(7797) -> 'V';
uts46_map(7798) -> {'M', [7799]};
uts46_map(7799) -> 'V';
uts46_map(7800) -> {'M', [7801]};
uts46_map(7801) -> 'V';
uts46_map(7802) -> {'M', [7803]};
uts46_map(7803) -> 'V';
uts46_map(7804) -> {'M', [7805]};
uts46_map(7805) -> 'V';
uts46_map(7806) -> {'M', [7807]};
uts46_map(7807) -> 'V';
uts46_map(7808) -> {'M', [7809]};
uts46_map(7809) -> 'V';
uts46_map(7810) -> {'M', [7811]};
uts46_map(7811) -> 'V';
uts46_map(7812) -> {'M', [7813]};
uts46_map(7813) -> 'V';
uts46_map(7814) -> {'M', [7815]};
uts46_map(7815) -> 'V';
uts46_map(7816) -> {'M', [7817]};
uts46_map(7817) -> 'V';
uts46_map(7818) -> {'M', [7819]};
uts46_map(7819) -> 'V';
uts46_map(7820) -> {'M', [7821]};
uts46_map(7821) -> 'V';
uts46_map(7822) -> {'M', [7823]};
uts46_map(7823) -> 'V';
uts46_map(7824) -> {'M', [7825]};
uts46_map(7825) -> 'V';
uts46_map(7826) -> {'M', [7827]};
uts46_map(7827) -> 'V';
uts46_map(7828) -> {'M', [7829]};
uts46_map(7834) -> {'M', [97,702]};
uts46_map(7835) -> {'M', [7777]};
uts46_map(7838) -> {'M', [115,115]};
uts46_map(7839) -> 'V';
uts46_map(7840) -> {'M', [7841]};
uts46_map(7841) -> 'V';
uts46_map(7842) -> {'M', [7843]};
uts46_map(7843) -> 'V';
uts46_map(7844) -> {'M', [7845]};
uts46_map(7845) -> 'V';
uts46_map(7846) -> {'M', [7847]};
uts46_map(7847) -> 'V';
uts46_map(7848) -> {'M', [7849]};
uts46_map(7849) -> 'V';
uts46_map(7850) -> {'M', [7851]};
uts46_map(7851) -> 'V';
uts46_map(7852) -> {'M', [7853]};
uts46_map(7853) -> 'V';
uts46_map(7854) -> {'M', [7855]};
uts46_map(7855) -> 'V';
uts46_map(7856) -> {'M', [7857]};
uts46_map(7857) -> 'V';
uts46_map(7858) -> {'M', [7859]};
uts46_map(7859) -> 'V';
uts46_map(7860) -> {'M', [7861]};
uts46_map(7861) -> 'V';
uts46_map(7862) -> {'M', [7863]};
uts46_map(7863) -> 'V';
uts46_map(7864) -> {'M', [7865]};
uts46_map(7865) -> 'V';
uts46_map(7866) -> {'M', [7867]};
uts46_map(7867) -> 'V';
uts46_map(7868) -> {'M', [7869]};
uts46_map(7869) -> 'V';
uts46_map(7870) -> {'M', [7871]};
uts46_map(7871) -> 'V';
uts46_map(7872) -> {'M', [7873]};
uts46_map(7873) -> 'V';
uts46_map(7874) -> {'M', [7875]};
uts46_map(7875) -> 'V';
uts46_map(7876) -> {'M', [7877]};
uts46_map(7877) -> 'V';
uts46_map(7878) -> {'M', [7879]};
uts46_map(7879) -> 'V';
uts46_map(7880) -> {'M', [7881]};
uts46_map(7881) -> 'V';
uts46_map(7882) -> {'M', [7883]};
uts46_map(7883) -> 'V';
uts46_map(7884) -> {'M', [7885]};
uts46_map(7885) -> 'V';
uts46_map(7886) -> {'M', [7887]};
uts46_map(7887) -> 'V';
uts46_map(7888) -> {'M', [7889]};
uts46_map(7889) -> 'V';
uts46_map(7890) -> {'M', [7891]};
uts46_map(7891) -> 'V';
uts46_map(7892) -> {'M', [7893]};
uts46_map(7893) -> 'V';
uts46_map(7894) -> {'M', [7895]};
uts46_map(7895) -> 'V';
uts46_map(7896) -> {'M', [7897]};
uts46_map(7897) -> 'V';
uts46_map(7898) -> {'M', [7899]};
uts46_map(7899) -> 'V';
uts46_map(7900) -> {'M', [7901]};
uts46_map(7901) -> 'V';
uts46_map(7902) -> {'M', [7903]};
uts46_map(7903) -> 'V';
uts46_map(7904) -> {'M', [7905]};
uts46_map(7905) -> 'V';
uts46_map(7906) -> {'M', [7907]};
uts46_map(7907) -> 'V';
uts46_map(7908) -> {'M', [7909]};
uts46_map(7909) -> 'V';
uts46_map(7910) -> {'M', [7911]};
uts46_map(7911) -> 'V';
uts46_map(7912) -> {'M', [7913]};
uts46_map(7913) -> 'V';
uts46_map(7914) -> {'M', [7915]};
uts46_map(7915) -> 'V';
uts46_map(7916) -> {'M', [7917]};
uts46_map(7917) -> 'V';
uts46_map(7918) -> {'M', [7919]};
uts46_map(7919) -> 'V';
uts46_map(7920) -> {'M', [7921]};
uts46_map(7921) -> 'V';
uts46_map(7922) -> {'M', [7923]};
uts46_map(7923) -> 'V';
uts46_map(7924) -> {'M', [7925]};
uts46_map(7925) -> 'V';
uts46_map(7926) -> {'M', [7927]};
uts46_map(7927) -> 'V';
uts46_map(7928) -> {'M', [7929]};
uts46_map(7929) -> 'V';
uts46_map(7930) -> {'M', [7931]};
uts46_map(7931) -> 'V';
uts46_map(7932) -> {'M', [7933]};
uts46_map(7933) -> 'V';
uts46_map(7934) -> {'M', [7935]};
uts46_map(7935) -> 'V';
uts46_map(7944) -> {'M', [7936]};
uts46_map(7945) -> {'M', [7937]};
uts46_map(7946) -> {'M', [7938]};
uts46_map(7947) -> {'M', [7939]};
uts46_map(7948) -> {'M', [7940]};
uts46_map(7949) -> {'M', [7941]};
uts46_map(7950) -> {'M', [7942]};
uts46_map(7951) -> {'M', [7943]};
uts46_map(7960) -> {'M', [7952]};
uts46_map(7961) -> {'M', [7953]};
uts46_map(7962) -> {'M', [7954]};
uts46_map(7963) -> {'M', [7955]};
uts46_map(7964) -> {'M', [7956]};
uts46_map(7965) -> {'M', [7957]};
uts46_map(7976) -> {'M', [7968]};
uts46_map(7977) -> {'M', [7969]};
uts46_map(7978) -> {'M', [7970]};
uts46_map(7979) -> {'M', [7971]};
uts46_map(7980) -> {'M', [7972]};
uts46_map(7981) -> {'M', [7973]};
uts46_map(7982) -> {'M', [7974]};
uts46_map(7983) -> {'M', [7975]};
uts46_map(7992) -> {'M', [7984]};
uts46_map(7993) -> {'M', [7985]};
uts46_map(7994) -> {'M', [7986]};
uts46_map(7995) -> {'M', [7987]};
uts46_map(7996) -> {'M', [7988]};
uts46_map(7997) -> {'M', [7989]};
uts46_map(7998) -> {'M', [7990]};
uts46_map(7999) -> {'M', [7991]};
uts46_map(8008) -> {'M', [8000]};
uts46_map(8009) -> {'M', [8001]};
uts46_map(8010) -> {'M', [8002]};
uts46_map(8011) -> {'M', [8003]};
uts46_map(8012) -> {'M', [8004]};
uts46_map(8013) -> {'M', [8005]};
uts46_map(8024) -> 'X';
uts46_map(8025) -> {'M', [8017]};
uts46_map(8026) -> 'X';
uts46_map(8027) -> {'M', [8019]};
uts46_map(8028) -> 'X';
uts46_map(8029) -> {'M', [8021]};
uts46_map(8030) -> 'X';
uts46_map(8031) -> {'M', [8023]};
uts46_map(8040) -> {'M', [8032]};
uts46_map(8041) -> {'M', [8033]};
uts46_map(8042) -> {'M', [8034]};
uts46_map(8043) -> {'M', [8035]};
uts46_map(8044) -> {'M', [8036]};
uts46_map(8045) -> {'M', [8037]};
uts46_map(8046) -> {'M', [8038]};
uts46_map(8047) -> {'M', [8039]};
uts46_map(8048) -> 'V';
uts46_map(8049) -> {'M', [940]};
uts46_map(8050) -> 'V';
uts46_map(8051) -> {'M', [941]};
uts46_map(8052) -> 'V';
uts46_map(8053) -> {'M', [942]};
uts46_map(8054) -> 'V';
uts46_map(8055) -> {'M', [943]};
uts46_map(8056) -> 'V';
uts46_map(8057) -> {'M', [972]};
uts46_map(8058) -> 'V';
uts46_map(8059) -> {'M', [973]};
uts46_map(8060) -> 'V';
uts46_map(8061) -> {'M', [974]};
uts46_map(8064) -> {'M', [7936,953]};
uts46_map(8065) -> {'M', [7937,953]};
uts46_map(8066) -> {'M', [7938,953]};
uts46_map(8067) -> {'M', [7939,953]};
uts46_map(8068) -> {'M', [7940,953]};
uts46_map(8069) -> {'M', [7941,953]};
uts46_map(8070) -> {'M', [7942,953]};
uts46_map(8071) -> {'M', [7943,953]};
uts46_map(8072) -> {'M', [7936,953]};
uts46_map(8073) -> {'M', [7937,953]};
uts46_map(8074) -> {'M', [7938,953]};
uts46_map(8075) -> {'M', [7939,953]};
uts46_map(8076) -> {'M', [7940,953]};
uts46_map(8077) -> {'M', [7941,953]};
uts46_map(8078) -> {'M', [7942,953]};
uts46_map(8079) -> {'M', [7943,953]};
uts46_map(8080) -> {'M', [7968,953]};
uts46_map(8081) -> {'M', [7969,953]};
uts46_map(8082) -> {'M', [7970,953]};
uts46_map(8083) -> {'M', [7971,953]};
uts46_map(8084) -> {'M', [7972,953]};
uts46_map(8085) -> {'M', [7973,953]};
uts46_map(8086) -> {'M', [7974,953]};
uts46_map(8087) -> {'M', [7975,953]};
uts46_map(8088) -> {'M', [7968,953]};
uts46_map(8089) -> {'M', [7969,953]};
uts46_map(8090) -> {'M', [7970,953]};
uts46_map(8091) -> {'M', [7971,953]};
uts46_map(8092) -> {'M', [7972,953]};
uts46_map(8093) -> {'M', [7973,953]};
uts46_map(8094) -> {'M', [7974,953]};
uts46_map(8095) -> {'M', [7975,953]};
uts46_map(8096) -> {'M', [8032,953]};
uts46_map(8097) -> {'M', [8033,953]};
uts46_map(8098) -> {'M', [8034,953]};
uts46_map(8099) -> {'M', [8035,953]};
uts46_map(8100) -> {'M', [8036,953]};
uts46_map(8101) -> {'M', [8037,953]};
uts46_map(8102) -> {'M', [8038,953]};
uts46_map(8103) -> {'M', [8039,953]};
uts46_map(8104) -> {'M', [8032,953]};
uts46_map(8105) -> {'M', [8033,953]};
uts46_map(8106) -> {'M', [8034,953]};
uts46_map(8107) -> {'M', [8035,953]};
uts46_map(8108) -> {'M', [8036,953]};
uts46_map(8109) -> {'M', [8037,953]};
uts46_map(8110) -> {'M', [8038,953]};
uts46_map(8111) -> {'M', [8039,953]};
uts46_map(8114) -> {'M', [8048,953]};
uts46_map(8115) -> {'M', [945,953]};
uts46_map(8116) -> {'M', [940,953]};
uts46_map(8117) -> 'X';
uts46_map(8118) -> 'V';
uts46_map(8119) -> {'M', [8118,953]};
uts46_map(8120) -> {'M', [8112]};
uts46_map(8121) -> {'M', [8113]};
uts46_map(8122) -> {'M', [8048]};
uts46_map(8123) -> {'M', [940]};
uts46_map(8124) -> {'M', [945,953]};
uts46_map(8125) -> {'3', [32,787]};
uts46_map(8126) -> {'M', [953]};
uts46_map(8127) -> {'3', [32,787]};
uts46_map(8128) -> {'3', [32,834]};
uts46_map(8129) -> {'3', [32,776,834]};
uts46_map(8130) -> {'M', [8052,953]};
uts46_map(8131) -> {'M', [951,953]};
uts46_map(8132) -> {'M', [942,953]};
uts46_map(8133) -> 'X';
uts46_map(8134) -> 'V';
uts46_map(8135) -> {'M', [8134,953]};
uts46_map(8136) -> {'M', [8050]};
uts46_map(8137) -> {'M', [941]};
uts46_map(8138) -> {'M', [8052]};
uts46_map(8139) -> {'M', [942]};
uts46_map(8140) -> {'M', [951,953]};
uts46_map(8141) -> {'3', [32,787,768]};
uts46_map(8142) -> {'3', [32,787,769]};
uts46_map(8143) -> {'3', [32,787,834]};
uts46_map(8147) -> {'M', [912]};
uts46_map(8152) -> {'M', [8144]};
uts46_map(8153) -> {'M', [8145]};
uts46_map(8154) -> {'M', [8054]};
uts46_map(8155) -> {'M', [943]};
uts46_map(8156) -> 'X';
uts46_map(8157) -> {'3', [32,788,768]};
uts46_map(8158) -> {'3', [32,788,769]};
uts46_map(8159) -> {'3', [32,788,834]};
uts46_map(8163) -> {'M', [944]};
uts46_map(8168) -> {'M', [8160]};
uts46_map(8169) -> {'M', [8161]};
uts46_map(8170) -> {'M', [8058]};
uts46_map(8171) -> {'M', [973]};
uts46_map(8172) -> {'M', [8165]};
uts46_map(8173) -> {'3', [32,776,768]};
uts46_map(8174) -> {'3', [32,776,769]};
uts46_map(8175) -> {'3', [96]};
uts46_map(8178) -> {'M', [8060,953]};
uts46_map(8179) -> {'M', [969,953]};
uts46_map(8180) -> {'M', [974,953]};
uts46_map(8181) -> 'X';
uts46_map(8182) -> 'V';
uts46_map(8183) -> {'M', [8182,953]};
uts46_map(8184) -> {'M', [8056]};
uts46_map(8185) -> {'M', [972]};
uts46_map(8186) -> {'M', [8060]};
uts46_map(8187) -> {'M', [974]};
uts46_map(8188) -> {'M', [969,953]};
uts46_map(8189) -> {'3', [32,769]};
uts46_map(8190) -> {'3', [32,788]};
uts46_map(8191) -> 'X';
uts46_map(8203) -> 'I';
uts46_map(8208) -> 'V';
uts46_map(8209) -> {'M', [8208]};
uts46_map(8215) -> {'3', [32,819]};
uts46_map(8231) -> 'V';
uts46_map(8239) -> {'3', [32]};
uts46_map(8243) -> {'M', [8242,8242]};
uts46_map(8244) -> {'M', [8242,8242,8242]};
uts46_map(8245) -> 'V';
uts46_map(8246) -> {'M', [8245,8245]};
uts46_map(8247) -> {'M', [8245,8245,8245]};
uts46_map(8252) -> {'3', [33,33]};
uts46_map(8253) -> 'V';
uts46_map(8254) -> {'3', [32,773]};
uts46_map(8263) -> {'3', [63,63]};
uts46_map(8264) -> {'3', [63,33]};
uts46_map(8265) -> {'3', [33,63]};
uts46_map(8279) -> {'M', [8242,8242,8242,8242]};
uts46_map(8287) -> {'3', [32]};
uts46_map(8288) -> 'I';
uts46_map(8292) -> 'I';
uts46_map(8293) -> 'X';
uts46_map(8304) -> {'M', [48]};
uts46_map(8305) -> {'M', [105]};
uts46_map(8308) -> {'M', [52]};
uts46_map(8309) -> {'M', [53]};
uts46_map(8310) -> {'M', [54]};
uts46_map(8311) -> {'M', [55]};
uts46_map(8312) -> {'M', [56]};
uts46_map(8313) -> {'M', [57]};
uts46_map(8314) -> {'3', [43]};
uts46_map(8315) -> {'M', [8722]};
uts46_map(8316) -> {'3', [61]};
uts46_map(8317) -> {'3', [40]};
uts46_map(8318) -> {'3', [41]};
uts46_map(8319) -> {'M', [110]};
uts46_map(8320) -> {'M', [48]};
uts46_map(8321) -> {'M', [49]};
uts46_map(8322) -> {'M', [50]};
uts46_map(8323) -> {'M', [51]};
uts46_map(8324) -> {'M', [52]};
uts46_map(8325) -> {'M', [53]};
uts46_map(8326) -> {'M', [54]};
uts46_map(8327) -> {'M', [55]};
uts46_map(8328) -> {'M', [56]};
uts46_map(8329) -> {'M', [57]};
uts46_map(8330) -> {'3', [43]};
uts46_map(8331) -> {'M', [8722]};
uts46_map(8332) -> {'3', [61]};
uts46_map(8333) -> {'3', [40]};
uts46_map(8334) -> {'3', [41]};
uts46_map(8335) -> 'X';
uts46_map(8336) -> {'M', [97]};
uts46_map(8337) -> {'M', [101]};
uts46_map(8338) -> {'M', [111]};
uts46_map(8339) -> {'M', [120]};
uts46_map(8340) -> {'M', [601]};
uts46_map(8341) -> {'M', [104]};
uts46_map(8342) -> {'M', [107]};
uts46_map(8343) -> {'M', [108]};
uts46_map(8344) -> {'M', [109]};
uts46_map(8345) -> {'M', [110]};
uts46_map(8346) -> {'M', [112]};
uts46_map(8347) -> {'M', [115]};
uts46_map(8348) -> {'M', [116]};
uts46_map(8360) -> {'M', [114,115]};
uts46_map(8363) -> 'V';
uts46_map(8364) -> 'V';
uts46_map(8377) -> 'V';
uts46_map(8378) -> 'V';
uts46_map(8382) -> 'V';
uts46_map(8383) -> 'V';
uts46_map(8427) -> 'V';
uts46_map(8432) -> 'V';
uts46_map(8448) -> {'3', [97,47,99]};
uts46_map(8449) -> {'3', [97,47,115]};
uts46_map(8450) -> {'M', [99]};
uts46_map(8451) -> {'M', [176,99]};
uts46_map(8452) -> 'V';
uts46_map(8453) -> {'3', [99,47,111]};
uts46_map(8454) -> {'3', [99,47,117]};
uts46_map(8455) -> {'M', [603]};
uts46_map(8456) -> 'V';
uts46_map(8457) -> {'M', [176,102]};
uts46_map(8458) -> {'M', [103]};
uts46_map(8463) -> {'M', [295]};
uts46_map(8468) -> 'V';
uts46_map(8469) -> {'M', [110]};
uts46_map(8470) -> {'M', [110,111]};
uts46_map(8473) -> {'M', [112]};
uts46_map(8474) -> {'M', [113]};
uts46_map(8480) -> {'M', [115,109]};
uts46_map(8481) -> {'M', [116,101,108]};
uts46_map(8482) -> {'M', [116,109]};
uts46_map(8483) -> 'V';
uts46_map(8484) -> {'M', [122]};
uts46_map(8485) -> 'V';
uts46_map(8486) -> {'M', [969]};
uts46_map(8487) -> 'V';
uts46_map(8488) -> {'M', [122]};
uts46_map(8489) -> 'V';
uts46_map(8490) -> {'M', [107]};
uts46_map(8491) -> {'M', [229]};
uts46_map(8492) -> {'M', [98]};
uts46_map(8493) -> {'M', [99]};
uts46_map(8494) -> 'V';
uts46_map(8497) -> {'M', [102]};
uts46_map(8498) -> 'X';
uts46_map(8499) -> {'M', [109]};
uts46_map(8500) -> {'M', [111]};
uts46_map(8501) -> {'M', [1488]};
uts46_map(8502) -> {'M', [1489]};
uts46_map(8503) -> {'M', [1490]};
uts46_map(8504) -> {'M', [1491]};
uts46_map(8505) -> {'M', [105]};
uts46_map(8506) -> 'V';
uts46_map(8507) -> {'M', [102,97,120]};
uts46_map(8508) -> {'M', [960]};
uts46_map(8511) -> {'M', [960]};
uts46_map(8512) -> {'M', [8721]};
uts46_map(8519) -> {'M', [101]};
uts46_map(8520) -> {'M', [105]};
uts46_map(8521) -> {'M', [106]};
uts46_map(8524) -> 'V';
uts46_map(8525) -> 'V';
uts46_map(8526) -> 'V';
uts46_map(8527) -> 'V';
uts46_map(8528) -> {'M', [49,8260,55]};
uts46_map(8529) -> {'M', [49,8260,57]};
uts46_map(8530) -> {'M', [49,8260,49,48]};
uts46_map(8531) -> {'M', [49,8260,51]};
uts46_map(8532) -> {'M', [50,8260,51]};
uts46_map(8533) -> {'M', [49,8260,53]};
uts46_map(8534) -> {'M', [50,8260,53]};
uts46_map(8535) -> {'M', [51,8260,53]};
uts46_map(8536) -> {'M', [52,8260,53]};
uts46_map(8537) -> {'M', [49,8260,54]};
uts46_map(8538) -> {'M', [53,8260,54]};
uts46_map(8539) -> {'M', [49,8260,56]};
uts46_map(8540) -> {'M', [51,8260,56]};
uts46_map(8541) -> {'M', [53,8260,56]};
uts46_map(8542) -> {'M', [55,8260,56]};
uts46_map(8543) -> {'M', [49,8260]};
uts46_map(8544) -> {'M', [105]};
uts46_map(8545) -> {'M', [105,105]};
uts46_map(8546) -> {'M', [105,105,105]};
uts46_map(8547) -> {'M', [105,118]};
uts46_map(8548) -> {'M', [118]};
uts46_map(8549) -> {'M', [118,105]};
uts46_map(8550) -> {'M', [118,105,105]};
uts46_map(8551) -> {'M', [118,105,105,105]};
uts46_map(8552) -> {'M', [105,120]};
uts46_map(8553) -> {'M', [120]};
uts46_map(8554) -> {'M', [120,105]};
uts46_map(8555) -> {'M', [120,105,105]};
uts46_map(8556) -> {'M', [108]};
uts46_map(8557) -> {'M', [99]};
uts46_map(8558) -> {'M', [100]};
uts46_map(8559) -> {'M', [109]};
uts46_map(8560) -> {'M', [105]};
uts46_map(8561) -> {'M', [105,105]};
uts46_map(8562) -> {'M', [105,105,105]};
uts46_map(8563) -> {'M', [105,118]};
uts46_map(8564) -> {'M', [118]};
uts46_map(8565) -> {'M', [118,105]};
uts46_map(8566) -> {'M', [118,105,105]};
uts46_map(8567) -> {'M', [118,105,105,105]};
uts46_map(8568) -> {'M', [105,120]};
uts46_map(8569) -> {'M', [120]};
uts46_map(8570) -> {'M', [120,105]};
uts46_map(8571) -> {'M', [120,105,105]};
uts46_map(8572) -> {'M', [108]};
uts46_map(8573) -> {'M', [99]};
uts46_map(8574) -> {'M', [100]};
uts46_map(8575) -> {'M', [109]};
uts46_map(8579) -> 'X';
uts46_map(8580) -> 'V';
uts46_map(8585) -> {'M', [48,8260,51]};
uts46_map(8748) -> {'M', [8747,8747]};
uts46_map(8749) -> {'M', [8747,8747,8747]};
uts46_map(8750) -> 'V';
uts46_map(8751) -> {'M', [8750,8750]};
uts46_map(8752) -> {'M', [8750,8750,8750]};
uts46_map(8800) -> '3';
uts46_map(8960) -> 'V';
uts46_map(8961) -> 'V';
uts46_map(9001) -> {'M', [12296]};
uts46_map(9002) -> {'M', [12297]};
uts46_map(9083) -> 'V';
uts46_map(9084) -> 'V';
uts46_map(9192) -> 'V';
uts46_map(9215) -> 'V';
uts46_map(9312) -> {'M', [49]};
uts46_map(9313) -> {'M', [50]};
uts46_map(9314) -> {'M', [51]};
uts46_map(9315) -> {'M', [52]};
uts46_map(9316) -> {'M', [53]};
uts46_map(9317) -> {'M', [54]};
uts46_map(9318) -> {'M', [55]};
uts46_map(9319) -> {'M', [56]};
uts46_map(9320) -> {'M', [57]};
uts46_map(9321) -> {'M', [49,48]};
uts46_map(9322) -> {'M', [49,49]};
uts46_map(9323) -> {'M', [49,50]};
uts46_map(9324) -> {'M', [49,51]};
uts46_map(9325) -> {'M', [49,52]};
uts46_map(9326) -> {'M', [49,53]};
uts46_map(9327) -> {'M', [49,54]};
uts46_map(9328) -> {'M', [49,55]};
uts46_map(9329) -> {'M', [49,56]};
uts46_map(9330) -> {'M', [49,57]};
uts46_map(9331) -> {'M', [50,48]};
uts46_map(9332) -> {'3', [40,49,41]};
uts46_map(9333) -> {'3', [40,50,41]};
uts46_map(9334) -> {'3', [40,51,41]};
uts46_map(9335) -> {'3', [40,52,41]};
uts46_map(9336) -> {'3', [40,53,41]};
uts46_map(9337) -> {'3', [40,54,41]};
uts46_map(9338) -> {'3', [40,55,41]};
uts46_map(9339) -> {'3', [40,56,41]};
uts46_map(9340) -> {'3', [40,57,41]};
uts46_map(9341) -> {'3', [40,49,48,41]};
uts46_map(9342) -> {'3', [40,49,49,41]};
uts46_map(9343) -> {'3', [40,49,50,41]};
uts46_map(9344) -> {'3', [40,49,51,41]};
uts46_map(9345) -> {'3', [40,49,52,41]};
uts46_map(9346) -> {'3', [40,49,53,41]};
uts46_map(9347) -> {'3', [40,49,54,41]};
uts46_map(9348) -> {'3', [40,49,55,41]};
uts46_map(9349) -> {'3', [40,49,56,41]};
uts46_map(9350) -> {'3', [40,49,57,41]};
uts46_map(9351) -> {'3', [40,50,48,41]};
uts46_map(9372) -> {'3', [40,97,41]};
uts46_map(9373) -> {'3', [40,98,41]};
uts46_map(9374) -> {'3', [40,99,41]};
uts46_map(9375) -> {'3', [40,100,41]};
uts46_map(9376) -> {'3', [40,101,41]};
uts46_map(9377) -> {'3', [40,102,41]};
uts46_map(9378) -> {'3', [40,103,41]};
uts46_map(9379) -> {'3', [40,104,41]};
uts46_map(9380) -> {'3', [40,105,41]};
uts46_map(9381) -> {'3', [40,106,41]};
uts46_map(9382) -> {'3', [40,107,41]};
uts46_map(9383) -> {'3', [40,108,41]};
uts46_map(9384) -> {'3', [40,109,41]};
uts46_map(9385) -> {'3', [40,110,41]};
uts46_map(9386) -> {'3', [40,111,41]};
uts46_map(9387) -> {'3', [40,112,41]};
uts46_map(9388) -> {'3', [40,113,41]};
uts46_map(9389) -> {'3', [40,114,41]};
uts46_map(9390) -> {'3', [40,115,41]};
uts46_map(9391) -> {'3', [40,116,41]};
uts46_map(9392) -> {'3', [40,117,41]};
uts46_map(9393) -> {'3', [40,118,41]};
uts46_map(9394) -> {'3', [40,119,41]};
uts46_map(9395) -> {'3', [40,120,41]};
uts46_map(9396) -> {'3', [40,121,41]};
uts46_map(9397) -> {'3', [40,122,41]};
uts46_map(9398) -> {'M', [97]};
uts46_map(9399) -> {'M', [98]};
uts46_map(9400) -> {'M', [99]};
uts46_map(9401) -> {'M', [100]};
uts46_map(9402) -> {'M', [101]};
uts46_map(9403) -> {'M', [102]};
uts46_map(9404) -> {'M', [103]};
uts46_map(9405) -> {'M', [104]};
uts46_map(9406) -> {'M', [105]};
uts46_map(9407) -> {'M', [106]};
uts46_map(9408) -> {'M', [107]};
uts46_map(9409) -> {'M', [108]};
uts46_map(9410) -> {'M', [109]};
uts46_map(9411) -> {'M', [110]};
uts46_map(9412) -> {'M', [111]};
uts46_map(9413) -> {'M', [112]};
uts46_map(9414) -> {'M', [113]};
uts46_map(9415) -> {'M', [114]};
uts46_map(9416) -> {'M', [115]};
uts46_map(9417) -> {'M', [116]};
uts46_map(9418) -> {'M', [117]};
uts46_map(9419) -> {'M', [118]};
uts46_map(9420) -> {'M', [119]};
uts46_map(9421) -> {'M', [120]};
uts46_map(9422) -> {'M', [121]};
uts46_map(9423) -> {'M', [122]};
uts46_map(9424) -> {'M', [97]};
uts46_map(9425) -> {'M', [98]};
uts46_map(9426) -> {'M', [99]};
uts46_map(9427) -> {'M', [100]};
uts46_map(9428) -> {'M', [101]};
uts46_map(9429) -> {'M', [102]};
uts46_map(9430) -> {'M', [103]};
uts46_map(9431) -> {'M', [104]};
uts46_map(9432) -> {'M', [105]};
uts46_map(9433) -> {'M', [106]};
uts46_map(9434) -> {'M', [107]};
uts46_map(9435) -> {'M', [108]};
uts46_map(9436) -> {'M', [109]};
uts46_map(9437) -> {'M', [110]};
uts46_map(9438) -> {'M', [111]};
uts46_map(9439) -> {'M', [112]};
uts46_map(9440) -> {'M', [113]};
uts46_map(9441) -> {'M', [114]};
uts46_map(9442) -> {'M', [115]};
uts46_map(9443) -> {'M', [116]};
uts46_map(9444) -> {'M', [117]};
uts46_map(9445) -> {'M', [118]};
uts46_map(9446) -> {'M', [119]};
uts46_map(9447) -> {'M', [120]};
uts46_map(9448) -> {'M', [121]};
uts46_map(9449) -> {'M', [122]};
uts46_map(9450) -> {'M', [48]};
uts46_map(9471) -> 'V';
uts46_map(9752) -> 'V';
uts46_map(9753) -> 'V';
uts46_map(9885) -> 'V';
uts46_map(9906) -> 'V';
uts46_map(9934) -> 'V';
uts46_map(9954) -> 'V';
uts46_map(9955) -> 'V';
uts46_map(9984) -> 'V';
uts46_map(9989) -> 'V';
uts46_map(10024) -> 'V';
uts46_map(10060) -> 'V';
uts46_map(10061) -> 'V';
uts46_map(10062) -> 'V';
uts46_map(10070) -> 'V';
uts46_map(10071) -> 'V';
uts46_map(10160) -> 'V';
uts46_map(10175) -> 'V';
uts46_map(10187) -> 'V';
uts46_map(10188) -> 'V';
uts46_map(10189) -> 'V';
uts46_map(10764) -> {'M', [8747,8747,8747,8747]};
uts46_map(10868) -> {'3', [58,58,61]};
uts46_map(10869) -> {'3', [61,61]};
uts46_map(10870) -> {'3', [61,61,61]};
uts46_map(10972) -> {'M', [10973,824]};
uts46_map(11158) -> 'X';
uts46_map(11159) -> 'V';
uts46_map(11209) -> 'V';
uts46_map(11218) -> 'V';
uts46_map(11263) -> 'V';
uts46_map(11264) -> {'M', [11312]};
uts46_map(11265) -> {'M', [11313]};
uts46_map(11266) -> {'M', [11314]};
uts46_map(11267) -> {'M', [11315]};
uts46_map(11268) -> {'M', [11316]};
uts46_map(11269) -> {'M', [11317]};
uts46_map(11270) -> {'M', [11318]};
uts46_map(11271) -> {'M', [11319]};
uts46_map(11272) -> {'M', [11320]};
uts46_map(11273) -> {'M', [11321]};
uts46_map(11274) -> {'M', [11322]};
uts46_map(11275) -> {'M', [11323]};
uts46_map(11276) -> {'M', [11324]};
uts46_map(11277) -> {'M', [11325]};
uts46_map(11278) -> {'M', [11326]};
uts46_map(11279) -> {'M', [11327]};
uts46_map(11280) -> {'M', [11328]};
uts46_map(11281) -> {'M', [11329]};
uts46_map(11282) -> {'M', [11330]};
uts46_map(11283) -> {'M', [11331]};
uts46_map(11284) -> {'M', [11332]};
uts46_map(11285) -> {'M', [11333]};
uts46_map(11286) -> {'M', [11334]};
uts46_map(11287) -> {'M', [11335]};
uts46_map(11288) -> {'M', [11336]};
uts46_map(11289) -> {'M', [11337]};
uts46_map(11290) -> {'M', [11338]};
uts46_map(11291) -> {'M', [11339]};
uts46_map(11292) -> {'M', [11340]};
uts46_map(11293) -> {'M', [11341]};
uts46_map(11294) -> {'M', [11342]};
uts46_map(11295) -> {'M', [11343]};
uts46_map(11296) -> {'M', [11344]};
uts46_map(11297) -> {'M', [11345]};
uts46_map(11298) -> {'M', [11346]};
uts46_map(11299) -> {'M', [11347]};
uts46_map(11300) -> {'M', [11348]};
uts46_map(11301) -> {'M', [11349]};
uts46_map(11302) -> {'M', [11350]};
uts46_map(11303) -> {'M', [11351]};
uts46_map(11304) -> {'M', [11352]};
uts46_map(11305) -> {'M', [11353]};
uts46_map(11306) -> {'M', [11354]};
uts46_map(11307) -> {'M', [11355]};
uts46_map(11308) -> {'M', [11356]};
uts46_map(11309) -> {'M', [11357]};
uts46_map(11310) -> {'M', [11358]};
uts46_map(11311) -> 'X';
uts46_map(11359) -> 'X';
uts46_map(11360) -> {'M', [11361]};
uts46_map(11361) -> 'V';
uts46_map(11362) -> {'M', [619]};
uts46_map(11363) -> {'M', [7549]};
uts46_map(11364) -> {'M', [637]};
uts46_map(11367) -> {'M', [11368]};
uts46_map(11368) -> 'V';
uts46_map(11369) -> {'M', [11370]};
uts46_map(11370) -> 'V';
uts46_map(11371) -> {'M', [11372]};
uts46_map(11372) -> 'V';
uts46_map(11373) -> {'M', [593]};
uts46_map(11374) -> {'M', [625]};
uts46_map(11375) -> {'M', [592]};
uts46_map(11376) -> {'M', [594]};
uts46_map(11377) -> 'V';
uts46_map(11378) -> {'M', [11379]};
uts46_map(11379) -> 'V';
uts46_map(11380) -> 'V';
uts46_map(11381) -> {'M', [11382]};
uts46_map(11388) -> {'M', [106]};
uts46_map(11389) -> {'M', [118]};
uts46_map(11390) -> {'M', [575]};
uts46_map(11391) -> {'M', [576]};
uts46_map(11392) -> {'M', [11393]};
uts46_map(11393) -> 'V';
uts46_map(11394) -> {'M', [11395]};
uts46_map(11395) -> 'V';
uts46_map(11396) -> {'M', [11397]};
uts46_map(11397) -> 'V';
uts46_map(11398) -> {'M', [11399]};
uts46_map(11399) -> 'V';
uts46_map(11400) -> {'M', [11401]};
uts46_map(11401) -> 'V';
uts46_map(11402) -> {'M', [11403]};
uts46_map(11403) -> 'V';
uts46_map(11404) -> {'M', [11405]};
uts46_map(11405) -> 'V';
uts46_map(11406) -> {'M', [11407]};
uts46_map(11407) -> 'V';
uts46_map(11408) -> {'M', [11409]};
uts46_map(11409) -> 'V';
uts46_map(11410) -> {'M', [11411]};
uts46_map(11411) -> 'V';
uts46_map(11412) -> {'M', [11413]};
uts46_map(11413) -> 'V';
uts46_map(11414) -> {'M', [11415]};
uts46_map(11415) -> 'V';
uts46_map(11416) -> {'M', [11417]};
uts46_map(11417) -> 'V';
uts46_map(11418) -> {'M', [11419]};
uts46_map(11419) -> 'V';
uts46_map(11420) -> {'M', [11421]};
uts46_map(11421) -> 'V';
uts46_map(11422) -> {'M', [11423]};
uts46_map(11423) -> 'V';
uts46_map(11424) -> {'M', [11425]};
uts46_map(11425) -> 'V';
uts46_map(11426) -> {'M', [11427]};
uts46_map(11427) -> 'V';
uts46_map(11428) -> {'M', [11429]};
uts46_map(11429) -> 'V';
uts46_map(11430) -> {'M', [11431]};
uts46_map(11431) -> 'V';
uts46_map(11432) -> {'M', [11433]};
uts46_map(11433) -> 'V';
uts46_map(11434) -> {'M', [11435]};
uts46_map(11435) -> 'V';
uts46_map(11436) -> {'M', [11437]};
uts46_map(11437) -> 'V';
uts46_map(11438) -> {'M', [11439]};
uts46_map(11439) -> 'V';
uts46_map(11440) -> {'M', [11441]};
uts46_map(11441) -> 'V';
uts46_map(11442) -> {'M', [11443]};
uts46_map(11443) -> 'V';
uts46_map(11444) -> {'M', [11445]};
uts46_map(11445) -> 'V';
uts46_map(11446) -> {'M', [11447]};
uts46_map(11447) -> 'V';
uts46_map(11448) -> {'M', [11449]};
uts46_map(11449) -> 'V';
uts46_map(11450) -> {'M', [11451]};
uts46_map(11451) -> 'V';
uts46_map(11452) -> {'M', [11453]};
uts46_map(11453) -> 'V';
uts46_map(11454) -> {'M', [11455]};
uts46_map(11455) -> 'V';
uts46_map(11456) -> {'M', [11457]};
uts46_map(11457) -> 'V';
uts46_map(11458) -> {'M', [11459]};
uts46_map(11459) -> 'V';
uts46_map(11460) -> {'M', [11461]};
uts46_map(11461) -> 'V';
uts46_map(11462) -> {'M', [11463]};
uts46_map(11463) -> 'V';
uts46_map(11464) -> {'M', [11465]};
uts46_map(11465) -> 'V';
uts46_map(11466) -> {'M', [11467]};
uts46_map(11467) -> 'V';
uts46_map(11468) -> {'M', [11469]};
uts46_map(11469) -> 'V';
uts46_map(11470) -> {'M', [11471]};
uts46_map(11471) -> 'V';
uts46_map(11472) -> {'M', [11473]};
uts46_map(11473) -> 'V';
uts46_map(11474) -> {'M', [11475]};
uts46_map(11475) -> 'V';
uts46_map(11476) -> {'M', [11477]};
uts46_map(11477) -> 'V';
uts46_map(11478) -> {'M', [11479]};
uts46_map(11479) -> 'V';
uts46_map(11480) -> {'M', [11481]};
uts46_map(11481) -> 'V';
uts46_map(11482) -> {'M', [11483]};
uts46_map(11483) -> 'V';
uts46_map(11484) -> {'M', [11485]};
uts46_map(11485) -> 'V';
uts46_map(11486) -> {'M', [11487]};
uts46_map(11487) -> 'V';
uts46_map(11488) -> {'M', [11489]};
uts46_map(11489) -> 'V';
uts46_map(11490) -> {'M', [11491]};
uts46_map(11499) -> {'M', [11500]};
uts46_map(11500) -> 'V';
uts46_map(11501) -> {'M', [11502]};
uts46_map(11506) -> {'M', [11507]};
uts46_map(11507) -> 'V';
uts46_map(11558) -> 'X';
uts46_map(11559) -> 'V';
uts46_map(11565) -> 'V';
uts46_map(11631) -> {'M', [11617]};
uts46_map(11632) -> 'V';
uts46_map(11647) -> 'V';
uts46_map(11687) -> 'X';
uts46_map(11695) -> 'X';
uts46_map(11703) -> 'X';
uts46_map(11711) -> 'X';
uts46_map(11719) -> 'X';
uts46_map(11727) -> 'X';
uts46_map(11735) -> 'X';
uts46_map(11743) -> 'X';
uts46_map(11823) -> 'V';
uts46_map(11824) -> 'V';
uts46_map(11825) -> 'V';
uts46_map(11855) -> 'V';
uts46_map(11930) -> 'X';
uts46_map(11935) -> {'M', [27597]};
uts46_map(12019) -> {'M', [40863]};
uts46_map(12032) -> {'M', [19968]};
uts46_map(12033) -> {'M', [20008]};
uts46_map(12034) -> {'M', [20022]};
uts46_map(12035) -> {'M', [20031]};
uts46_map(12036) -> {'M', [20057]};
uts46_map(12037) -> {'M', [20101]};
uts46_map(12038) -> {'M', [20108]};
uts46_map(12039) -> {'M', [20128]};
uts46_map(12040) -> {'M', [20154]};
uts46_map(12041) -> {'M', [20799]};
uts46_map(12042) -> {'M', [20837]};
uts46_map(12043) -> {'M', [20843]};
uts46_map(12044) -> {'M', [20866]};
uts46_map(12045) -> {'M', [20886]};
uts46_map(12046) -> {'M', [20907]};
uts46_map(12047) -> {'M', [20960]};
uts46_map(12048) -> {'M', [20981]};
uts46_map(12049) -> {'M', [20992]};
uts46_map(12050) -> {'M', [21147]};
uts46_map(12051) -> {'M', [21241]};
uts46_map(12052) -> {'M', [21269]};
uts46_map(12053) -> {'M', [21274]};
uts46_map(12054) -> {'M', [21304]};
uts46_map(12055) -> {'M', [21313]};
uts46_map(12056) -> {'M', [21340]};
uts46_map(12057) -> {'M', [21353]};
uts46_map(12058) -> {'M', [21378]};
uts46_map(12059) -> {'M', [21430]};
uts46_map(12060) -> {'M', [21448]};
uts46_map(12061) -> {'M', [21475]};
uts46_map(12062) -> {'M', [22231]};
uts46_map(12063) -> {'M', [22303]};
uts46_map(12064) -> {'M', [22763]};
uts46_map(12065) -> {'M', [22786]};
uts46_map(12066) -> {'M', [22794]};
uts46_map(12067) -> {'M', [22805]};
uts46_map(12068) -> {'M', [22823]};
uts46_map(12069) -> {'M', [22899]};
uts46_map(12070) -> {'M', [23376]};
uts46_map(12071) -> {'M', [23424]};
uts46_map(12072) -> {'M', [23544]};
uts46_map(12073) -> {'M', [23567]};
uts46_map(12074) -> {'M', [23586]};
uts46_map(12075) -> {'M', [23608]};
uts46_map(12076) -> {'M', [23662]};
uts46_map(12077) -> {'M', [23665]};
uts46_map(12078) -> {'M', [24027]};
uts46_map(12079) -> {'M', [24037]};
uts46_map(12080) -> {'M', [24049]};
uts46_map(12081) -> {'M', [24062]};
uts46_map(12082) -> {'M', [24178]};
uts46_map(12083) -> {'M', [24186]};
uts46_map(12084) -> {'M', [24191]};
uts46_map(12085) -> {'M', [24308]};
uts46_map(12086) -> {'M', [24318]};
uts46_map(12087) -> {'M', [24331]};
uts46_map(12088) -> {'M', [24339]};
uts46_map(12089) -> {'M', [24400]};
uts46_map(12090) -> {'M', [24417]};
uts46_map(12091) -> {'M', [24435]};
uts46_map(12092) -> {'M', [24515]};
uts46_map(12093) -> {'M', [25096]};
uts46_map(12094) -> {'M', [25142]};
uts46_map(12095) -> {'M', [25163]};
uts46_map(12096) -> {'M', [25903]};
uts46_map(12097) -> {'M', [25908]};
uts46_map(12098) -> {'M', [25991]};
uts46_map(12099) -> {'M', [26007]};
uts46_map(12100) -> {'M', [26020]};
uts46_map(12101) -> {'M', [26041]};
uts46_map(12102) -> {'M', [26080]};
uts46_map(12103) -> {'M', [26085]};
uts46_map(12104) -> {'M', [26352]};
uts46_map(12105) -> {'M', [26376]};
uts46_map(12106) -> {'M', [26408]};
uts46_map(12107) -> {'M', [27424]};
uts46_map(12108) -> {'M', [27490]};
uts46_map(12109) -> {'M', [27513]};
uts46_map(12110) -> {'M', [27571]};
uts46_map(12111) -> {'M', [27595]};
uts46_map(12112) -> {'M', [27604]};
uts46_map(12113) -> {'M', [27611]};
uts46_map(12114) -> {'M', [27663]};
uts46_map(12115) -> {'M', [27668]};
uts46_map(12116) -> {'M', [27700]};
uts46_map(12117) -> {'M', [28779]};
uts46_map(12118) -> {'M', [29226]};
uts46_map(12119) -> {'M', [29238]};
uts46_map(12120) -> {'M', [29243]};
uts46_map(12121) -> {'M', [29247]};
uts46_map(12122) -> {'M', [29255]};
uts46_map(12123) -> {'M', [29273]};
uts46_map(12124) -> {'M', [29275]};
uts46_map(12125) -> {'M', [29356]};
uts46_map(12126) -> {'M', [29572]};
uts46_map(12127) -> {'M', [29577]};
uts46_map(12128) -> {'M', [29916]};
uts46_map(12129) -> {'M', [29926]};
uts46_map(12130) -> {'M', [29976]};
uts46_map(12131) -> {'M', [29983]};
uts46_map(12132) -> {'M', [29992]};
uts46_map(12133) -> {'M', [30000]};
uts46_map(12134) -> {'M', [30091]};
uts46_map(12135) -> {'M', [30098]};
uts46_map(12136) -> {'M', [30326]};
uts46_map(12137) -> {'M', [30333]};
uts46_map(12138) -> {'M', [30382]};
uts46_map(12139) -> {'M', [30399]};
uts46_map(12140) -> {'M', [30446]};
uts46_map(12141) -> {'M', [30683]};
uts46_map(12142) -> {'M', [30690]};
uts46_map(12143) -> {'M', [30707]};
uts46_map(12144) -> {'M', [31034]};
uts46_map(12145) -> {'M', [31160]};
uts46_map(12146) -> {'M', [31166]};
uts46_map(12147) -> {'M', [31348]};
uts46_map(12148) -> {'M', [31435]};
uts46_map(12149) -> {'M', [31481]};
uts46_map(12150) -> {'M', [31859]};
uts46_map(12151) -> {'M', [31992]};
uts46_map(12152) -> {'M', [32566]};
uts46_map(12153) -> {'M', [32593]};
uts46_map(12154) -> {'M', [32650]};
uts46_map(12155) -> {'M', [32701]};
uts46_map(12156) -> {'M', [32769]};
uts46_map(12157) -> {'M', [32780]};
uts46_map(12158) -> {'M', [32786]};
uts46_map(12159) -> {'M', [32819]};
uts46_map(12160) -> {'M', [32895]};
uts46_map(12161) -> {'M', [32905]};
uts46_map(12162) -> {'M', [33251]};
uts46_map(12163) -> {'M', [33258]};
uts46_map(12164) -> {'M', [33267]};
uts46_map(12165) -> {'M', [33276]};
uts46_map(12166) -> {'M', [33292]};
uts46_map(12167) -> {'M', [33307]};
uts46_map(12168) -> {'M', [33311]};
uts46_map(12169) -> {'M', [33390]};
uts46_map(12170) -> {'M', [33394]};
uts46_map(12171) -> {'M', [33400]};
uts46_map(12172) -> {'M', [34381]};
uts46_map(12173) -> {'M', [34411]};
uts46_map(12174) -> {'M', [34880]};
uts46_map(12175) -> {'M', [34892]};
uts46_map(12176) -> {'M', [34915]};
uts46_map(12177) -> {'M', [35198]};
uts46_map(12178) -> {'M', [35211]};
uts46_map(12179) -> {'M', [35282]};
uts46_map(12180) -> {'M', [35328]};
uts46_map(12181) -> {'M', [35895]};
uts46_map(12182) -> {'M', [35910]};
uts46_map(12183) -> {'M', [35925]};
uts46_map(12184) -> {'M', [35960]};
uts46_map(12185) -> {'M', [35997]};
uts46_map(12186) -> {'M', [36196]};
uts46_map(12187) -> {'M', [36208]};
uts46_map(12188) -> {'M', [36275]};
uts46_map(12189) -> {'M', [36523]};
uts46_map(12190) -> {'M', [36554]};
uts46_map(12191) -> {'M', [36763]};
uts46_map(12192) -> {'M', [36784]};
uts46_map(12193) -> {'M', [36789]};
uts46_map(12194) -> {'M', [37009]};
uts46_map(12195) -> {'M', [37193]};
uts46_map(12196) -> {'M', [37318]};
uts46_map(12197) -> {'M', [37324]};
uts46_map(12198) -> {'M', [37329]};
uts46_map(12199) -> {'M', [38263]};
uts46_map(12200) -> {'M', [38272]};
uts46_map(12201) -> {'M', [38428]};
uts46_map(12202) -> {'M', [38582]};
uts46_map(12203) -> {'M', [38585]};
uts46_map(12204) -> {'M', [38632]};
uts46_map(12205) -> {'M', [38737]};
uts46_map(12206) -> {'M', [38750]};
uts46_map(12207) -> {'M', [38754]};
uts46_map(12208) -> {'M', [38761]};
uts46_map(12209) -> {'M', [38859]};
uts46_map(12210) -> {'M', [38893]};
uts46_map(12211) -> {'M', [38899]};
uts46_map(12212) -> {'M', [38913]};
uts46_map(12213) -> {'M', [39080]};
uts46_map(12214) -> {'M', [39131]};
uts46_map(12215) -> {'M', [39135]};
uts46_map(12216) -> {'M', [39318]};
uts46_map(12217) -> {'M', [39321]};
uts46_map(12218) -> {'M', [39340]};
uts46_map(12219) -> {'M', [39592]};
uts46_map(12220) -> {'M', [39640]};
uts46_map(12221) -> {'M', [39647]};
uts46_map(12222) -> {'M', [39717]};
uts46_map(12223) -> {'M', [39727]};
uts46_map(12224) -> {'M', [39730]};
uts46_map(12225) -> {'M', [39740]};
uts46_map(12226) -> {'M', [39770]};
uts46_map(12227) -> {'M', [40165]};
uts46_map(12228) -> {'M', [40565]};
uts46_map(12229) -> {'M', [40575]};
uts46_map(12230) -> {'M', [40613]};
uts46_map(12231) -> {'M', [40635]};
uts46_map(12232) -> {'M', [40643]};
uts46_map(12233) -> {'M', [40653]};
uts46_map(12234) -> {'M', [40657]};
uts46_map(12235) -> {'M', [40697]};
uts46_map(12236) -> {'M', [40701]};
uts46_map(12237) -> {'M', [40718]};
uts46_map(12238) -> {'M', [40723]};
uts46_map(12239) -> {'M', [40736]};
uts46_map(12240) -> {'M', [40763]};
uts46_map(12241) -> {'M', [40778]};
uts46_map(12242) -> {'M', [40786]};
uts46_map(12243) -> {'M', [40845]};
uts46_map(12244) -> {'M', [40860]};
uts46_map(12245) -> {'M', [40864]};
uts46_map(12288) -> {'3', [32]};
uts46_map(12289) -> 'V';
uts46_map(12290) -> {'M', [46]};
uts46_map(12342) -> {'M', [12306]};
uts46_map(12343) -> 'V';
uts46_map(12344) -> {'M', [21313]};
uts46_map(12345) -> {'M', [21316]};
uts46_map(12346) -> {'M', [21317]};
uts46_map(12347) -> 'V';
uts46_map(12348) -> 'V';
uts46_map(12349) -> 'V';
uts46_map(12350) -> 'V';
uts46_map(12351) -> 'V';
uts46_map(12352) -> 'X';
uts46_map(12443) -> {'3', [32,12441]};
uts46_map(12444) -> {'3', [32,12442]};
uts46_map(12447) -> {'M', [12424,12426]};
uts46_map(12448) -> 'V';
uts46_map(12543) -> {'M', [12467,12488]};
uts46_map(12589) -> 'V';
uts46_map(12590) -> 'V';
uts46_map(12591) -> 'V';
uts46_map(12592) -> 'X';
uts46_map(12593) -> {'M', [4352]};
uts46_map(12594) -> {'M', [4353]};
uts46_map(12595) -> {'M', [4522]};
uts46_map(12596) -> {'M', [4354]};
uts46_map(12597) -> {'M', [4524]};
uts46_map(12598) -> {'M', [4525]};
uts46_map(12599) -> {'M', [4355]};
uts46_map(12600) -> {'M', [4356]};
uts46_map(12601) -> {'M', [4357]};
uts46_map(12602) -> {'M', [4528]};
uts46_map(12603) -> {'M', [4529]};
uts46_map(12604) -> {'M', [4530]};
uts46_map(12605) -> {'M', [4531]};
uts46_map(12606) -> {'M', [4532]};
uts46_map(12607) -> {'M', [4533]};
uts46_map(12608) -> {'M', [4378]};
uts46_map(12609) -> {'M', [4358]};
uts46_map(12610) -> {'M', [4359]};
uts46_map(12611) -> {'M', [4360]};
uts46_map(12612) -> {'M', [4385]};
uts46_map(12613) -> {'M', [4361]};
uts46_map(12614) -> {'M', [4362]};
uts46_map(12615) -> {'M', [4363]};
uts46_map(12616) -> {'M', [4364]};
uts46_map(12617) -> {'M', [4365]};
uts46_map(12618) -> {'M', [4366]};
uts46_map(12619) -> {'M', [4367]};
uts46_map(12620) -> {'M', [4368]};
uts46_map(12621) -> {'M', [4369]};
uts46_map(12622) -> {'M', [4370]};
uts46_map(12623) -> {'M', [4449]};
uts46_map(12624) -> {'M', [4450]};
uts46_map(12625) -> {'M', [4451]};
uts46_map(12626) -> {'M', [4452]};
uts46_map(12627) -> {'M', [4453]};
uts46_map(12628) -> {'M', [4454]};
uts46_map(12629) -> {'M', [4455]};
uts46_map(12630) -> {'M', [4456]};
uts46_map(12631) -> {'M', [4457]};
uts46_map(12632) -> {'M', [4458]};
uts46_map(12633) -> {'M', [4459]};
uts46_map(12634) -> {'M', [4460]};
uts46_map(12635) -> {'M', [4461]};
uts46_map(12636) -> {'M', [4462]};
uts46_map(12637) -> {'M', [4463]};
uts46_map(12638) -> {'M', [4464]};
uts46_map(12639) -> {'M', [4465]};
uts46_map(12640) -> {'M', [4466]};
uts46_map(12641) -> {'M', [4467]};
uts46_map(12642) -> {'M', [4468]};
uts46_map(12643) -> {'M', [4469]};
uts46_map(12644) -> 'X';
uts46_map(12645) -> {'M', [4372]};
uts46_map(12646) -> {'M', [4373]};
uts46_map(12647) -> {'M', [4551]};
uts46_map(12648) -> {'M', [4552]};
uts46_map(12649) -> {'M', [4556]};
uts46_map(12650) -> {'M', [4558]};
uts46_map(12651) -> {'M', [4563]};
uts46_map(12652) -> {'M', [4567]};
uts46_map(12653) -> {'M', [4569]};
uts46_map(12654) -> {'M', [4380]};
uts46_map(12655) -> {'M', [4573]};
uts46_map(12656) -> {'M', [4575]};
uts46_map(12657) -> {'M', [4381]};
uts46_map(12658) -> {'M', [4382]};
uts46_map(12659) -> {'M', [4384]};
uts46_map(12660) -> {'M', [4386]};
uts46_map(12661) -> {'M', [4387]};
uts46_map(12662) -> {'M', [4391]};
uts46_map(12663) -> {'M', [4393]};
uts46_map(12664) -> {'M', [4395]};
uts46_map(12665) -> {'M', [4396]};
uts46_map(12666) -> {'M', [4397]};
uts46_map(12667) -> {'M', [4398]};
uts46_map(12668) -> {'M', [4399]};
uts46_map(12669) -> {'M', [4402]};
uts46_map(12670) -> {'M', [4406]};
uts46_map(12671) -> {'M', [4416]};
uts46_map(12672) -> {'M', [4423]};
uts46_map(12673) -> {'M', [4428]};
uts46_map(12674) -> {'M', [4593]};
uts46_map(12675) -> {'M', [4594]};
uts46_map(12676) -> {'M', [4439]};
uts46_map(12677) -> {'M', [4440]};
uts46_map(12678) -> {'M', [4441]};
uts46_map(12679) -> {'M', [4484]};
uts46_map(12680) -> {'M', [4485]};
uts46_map(12681) -> {'M', [4488]};
uts46_map(12682) -> {'M', [4497]};
uts46_map(12683) -> {'M', [4498]};
uts46_map(12684) -> {'M', [4500]};
uts46_map(12685) -> {'M', [4510]};
uts46_map(12686) -> {'M', [4513]};
uts46_map(12687) -> 'X';
uts46_map(12690) -> {'M', [19968]};
uts46_map(12691) -> {'M', [20108]};
uts46_map(12692) -> {'M', [19977]};
uts46_map(12693) -> {'M', [22235]};
uts46_map(12694) -> {'M', [19978]};
uts46_map(12695) -> {'M', [20013]};
uts46_map(12696) -> {'M', [19979]};
uts46_map(12697) -> {'M', [30002]};
uts46_map(12698) -> {'M', [20057]};
uts46_map(12699) -> {'M', [19993]};
uts46_map(12700) -> {'M', [19969]};
uts46_map(12701) -> {'M', [22825]};
uts46_map(12702) -> {'M', [22320]};
uts46_map(12703) -> {'M', [20154]};
uts46_map(12800) -> {'3', [40,4352,41]};
uts46_map(12801) -> {'3', [40,4354,41]};
uts46_map(12802) -> {'3', [40,4355,41]};
uts46_map(12803) -> {'3', [40,4357,41]};
uts46_map(12804) -> {'3', [40,4358,41]};
uts46_map(12805) -> {'3', [40,4359,41]};
uts46_map(12806) -> {'3', [40,4361,41]};
uts46_map(12807) -> {'3', [40,4363,41]};
uts46_map(12808) -> {'3', [40,4364,41]};
uts46_map(12809) -> {'3', [40,4366,41]};
uts46_map(12810) -> {'3', [40,4367,41]};
uts46_map(12811) -> {'3', [40,4368,41]};
uts46_map(12812) -> {'3', [40,4369,41]};
uts46_map(12813) -> {'3', [40,4370,41]};
uts46_map(12814) -> {'3', [40,44032,41]};
uts46_map(12815) -> {'3', [40,45208,41]};
uts46_map(12816) -> {'3', [40,45796,41]};
uts46_map(12817) -> {'3', [40,46972,41]};
uts46_map(12818) -> {'3', [40,47560,41]};
uts46_map(12819) -> {'3', [40,48148,41]};
uts46_map(12820) -> {'3', [40,49324,41]};
uts46_map(12821) -> {'3', [40,50500,41]};
uts46_map(12822) -> {'3', [40,51088,41]};
uts46_map(12823) -> {'3', [40,52264,41]};
uts46_map(12824) -> {'3', [40,52852,41]};
uts46_map(12825) -> {'3', [40,53440,41]};
uts46_map(12826) -> {'3', [40,54028,41]};
uts46_map(12827) -> {'3', [40,54616,41]};
uts46_map(12828) -> {'3', [40,51452,41]};
uts46_map(12829) -> {'3', [40,50724,51204,41]};
uts46_map(12830) -> {'3', [40,50724,54980,41]};
uts46_map(12831) -> 'X';
uts46_map(12832) -> {'3', [40,19968,41]};
uts46_map(12833) -> {'3', [40,20108,41]};
uts46_map(12834) -> {'3', [40,19977,41]};
uts46_map(12835) -> {'3', [40,22235,41]};
uts46_map(12836) -> {'3', [40,20116,41]};
uts46_map(12837) -> {'3', [40,20845,41]};
uts46_map(12838) -> {'3', [40,19971,41]};
uts46_map(12839) -> {'3', [40,20843,41]};
uts46_map(12840) -> {'3', [40,20061,41]};
uts46_map(12841) -> {'3', [40,21313,41]};
uts46_map(12842) -> {'3', [40,26376,41]};
uts46_map(12843) -> {'3', [40,28779,41]};
uts46_map(12844) -> {'3', [40,27700,41]};
uts46_map(12845) -> {'3', [40,26408,41]};
uts46_map(12846) -> {'3', [40,37329,41]};
uts46_map(12847) -> {'3', [40,22303,41]};
uts46_map(12848) -> {'3', [40,26085,41]};
uts46_map(12849) -> {'3', [40,26666,41]};
uts46_map(12850) -> {'3', [40,26377,41]};
uts46_map(12851) -> {'3', [40,31038,41]};
uts46_map(12852) -> {'3', [40,21517,41]};
uts46_map(12853) -> {'3', [40,29305,41]};
uts46_map(12854) -> {'3', [40,36001,41]};
uts46_map(12855) -> {'3', [40,31069,41]};
uts46_map(12856) -> {'3', [40,21172,41]};
uts46_map(12857) -> {'3', [40,20195,41]};
uts46_map(12858) -> {'3', [40,21628,41]};
uts46_map(12859) -> {'3', [40,23398,41]};
uts46_map(12860) -> {'3', [40,30435,41]};
uts46_map(12861) -> {'3', [40,20225,41]};
uts46_map(12862) -> {'3', [40,36039,41]};
uts46_map(12863) -> {'3', [40,21332,41]};
uts46_map(12864) -> {'3', [40,31085,41]};
uts46_map(12865) -> {'3', [40,20241,41]};
uts46_map(12866) -> {'3', [40,33258,41]};
uts46_map(12867) -> {'3', [40,33267,41]};
uts46_map(12868) -> {'M', [21839]};
uts46_map(12869) -> {'M', [24188]};
uts46_map(12870) -> {'M', [25991]};
uts46_map(12871) -> {'M', [31631]};
uts46_map(12880) -> {'M', [112,116,101]};
uts46_map(12881) -> {'M', [50,49]};
uts46_map(12882) -> {'M', [50,50]};
uts46_map(12883) -> {'M', [50,51]};
uts46_map(12884) -> {'M', [50,52]};
uts46_map(12885) -> {'M', [50,53]};
uts46_map(12886) -> {'M', [50,54]};
uts46_map(12887) -> {'M', [50,55]};
uts46_map(12888) -> {'M', [50,56]};
uts46_map(12889) -> {'M', [50,57]};
uts46_map(12890) -> {'M', [51,48]};
uts46_map(12891) -> {'M', [51,49]};
uts46_map(12892) -> {'M', [51,50]};
uts46_map(12893) -> {'M', [51,51]};
uts46_map(12894) -> {'M', [51,52]};
uts46_map(12895) -> {'M', [51,53]};
uts46_map(12896) -> {'M', [4352]};
uts46_map(12897) -> {'M', [4354]};
uts46_map(12898) -> {'M', [4355]};
uts46_map(12899) -> {'M', [4357]};
uts46_map(12900) -> {'M', [4358]};
uts46_map(12901) -> {'M', [4359]};
uts46_map(12902) -> {'M', [4361]};
uts46_map(12903) -> {'M', [4363]};
uts46_map(12904) -> {'M', [4364]};
uts46_map(12905) -> {'M', [4366]};
uts46_map(12906) -> {'M', [4367]};
uts46_map(12907) -> {'M', [4368]};
uts46_map(12908) -> {'M', [4369]};
uts46_map(12909) -> {'M', [4370]};
uts46_map(12910) -> {'M', [44032]};
uts46_map(12911) -> {'M', [45208]};
uts46_map(12912) -> {'M', [45796]};
uts46_map(12913) -> {'M', [46972]};
uts46_map(12914) -> {'M', [47560]};
uts46_map(12915) -> {'M', [48148]};
uts46_map(12916) -> {'M', [49324]};
uts46_map(12917) -> {'M', [50500]};
uts46_map(12918) -> {'M', [51088]};
uts46_map(12919) -> {'M', [52264]};
uts46_map(12920) -> {'M', [52852]};
uts46_map(12921) -> {'M', [53440]};
uts46_map(12922) -> {'M', [54028]};
uts46_map(12923) -> {'M', [54616]};
uts46_map(12924) -> {'M', [52280,44256]};
uts46_map(12925) -> {'M', [51452,51032]};
uts46_map(12926) -> {'M', [50864]};
uts46_map(12927) -> 'V';
uts46_map(12928) -> {'M', [19968]};
uts46_map(12929) -> {'M', [20108]};
uts46_map(12930) -> {'M', [19977]};
uts46_map(12931) -> {'M', [22235]};
uts46_map(12932) -> {'M', [20116]};
uts46_map(12933) -> {'M', [20845]};
uts46_map(12934) -> {'M', [19971]};
uts46_map(12935) -> {'M', [20843]};
uts46_map(12936) -> {'M', [20061]};
uts46_map(12937) -> {'M', [21313]};
uts46_map(12938) -> {'M', [26376]};
uts46_map(12939) -> {'M', [28779]};
uts46_map(12940) -> {'M', [27700]};
uts46_map(12941) -> {'M', [26408]};
uts46_map(12942) -> {'M', [37329]};
uts46_map(12943) -> {'M', [22303]};
uts46_map(12944) -> {'M', [26085]};
uts46_map(12945) -> {'M', [26666]};
uts46_map(12946) -> {'M', [26377]};
uts46_map(12947) -> {'M', [31038]};
uts46_map(12948) -> {'M', [21517]};
uts46_map(12949) -> {'M', [29305]};
uts46_map(12950) -> {'M', [36001]};
uts46_map(12951) -> {'M', [31069]};
uts46_map(12952) -> {'M', [21172]};
uts46_map(12953) -> {'M', [31192]};
uts46_map(12954) -> {'M', [30007]};
uts46_map(12955) -> {'M', [22899]};
uts46_map(12956) -> {'M', [36969]};
uts46_map(12957) -> {'M', [20778]};
uts46_map(12958) -> {'M', [21360]};
uts46_map(12959) -> {'M', [27880]};
uts46_map(12960) -> {'M', [38917]};
uts46_map(12961) -> {'M', [20241]};
uts46_map(12962) -> {'M', [20889]};
uts46_map(12963) -> {'M', [27491]};
uts46_map(12964) -> {'M', [19978]};
uts46_map(12965) -> {'M', [20013]};
uts46_map(12966) -> {'M', [19979]};
uts46_map(12967) -> {'M', [24038]};
uts46_map(12968) -> {'M', [21491]};
uts46_map(12969) -> {'M', [21307]};
uts46_map(12970) -> {'M', [23447]};
uts46_map(12971) -> {'M', [23398]};
uts46_map(12972) -> {'M', [30435]};
uts46_map(12973) -> {'M', [20225]};
uts46_map(12974) -> {'M', [36039]};
uts46_map(12975) -> {'M', [21332]};
uts46_map(12976) -> {'M', [22812]};
uts46_map(12977) -> {'M', [51,54]};
uts46_map(12978) -> {'M', [51,55]};
uts46_map(12979) -> {'M', [51,56]};
uts46_map(12980) -> {'M', [51,57]};
uts46_map(12981) -> {'M', [52,48]};
uts46_map(12982) -> {'M', [52,49]};
uts46_map(12983) -> {'M', [52,50]};
uts46_map(12984) -> {'M', [52,51]};
uts46_map(12985) -> {'M', [52,52]};
uts46_map(12986) -> {'M', [52,53]};
uts46_map(12987) -> {'M', [52,54]};
uts46_map(12988) -> {'M', [52,55]};
uts46_map(12989) -> {'M', [52,56]};
uts46_map(12990) -> {'M', [52,57]};
uts46_map(12991) -> {'M', [53,48]};
uts46_map(12992) -> {'M', [49,26376]};
uts46_map(12993) -> {'M', [50,26376]};
uts46_map(12994) -> {'M', [51,26376]};
uts46_map(12995) -> {'M', [52,26376]};
uts46_map(12996) -> {'M', [53,26376]};
uts46_map(12997) -> {'M', [54,26376]};
uts46_map(12998) -> {'M', [55,26376]};
uts46_map(12999) -> {'M', [56,26376]};
uts46_map(13000) -> {'M', [57,26376]};
uts46_map(13001) -> {'M', [49,48,26376]};
uts46_map(13002) -> {'M', [49,49,26376]};
uts46_map(13003) -> {'M', [49,50,26376]};
uts46_map(13004) -> {'M', [104,103]};
uts46_map(13005) -> {'M', [101,114,103]};
uts46_map(13006) -> {'M', [101,118]};
uts46_map(13007) -> {'M', [108,116,100]};
uts46_map(13008) -> {'M', [12450]};
uts46_map(13009) -> {'M', [12452]};
uts46_map(13010) -> {'M', [12454]};
uts46_map(13011) -> {'M', [12456]};
uts46_map(13012) -> {'M', [12458]};
uts46_map(13013) -> {'M', [12459]};
uts46_map(13014) -> {'M', [12461]};
uts46_map(13015) -> {'M', [12463]};
uts46_map(13016) -> {'M', [12465]};
uts46_map(13017) -> {'M', [12467]};
uts46_map(13018) -> {'M', [12469]};
uts46_map(13019) -> {'M', [12471]};
uts46_map(13020) -> {'M', [12473]};
uts46_map(13021) -> {'M', [12475]};
uts46_map(13022) -> {'M', [12477]};
uts46_map(13023) -> {'M', [12479]};
uts46_map(13024) -> {'M', [12481]};
uts46_map(13025) -> {'M', [12484]};
uts46_map(13026) -> {'M', [12486]};
uts46_map(13027) -> {'M', [12488]};
uts46_map(13028) -> {'M', [12490]};
uts46_map(13029) -> {'M', [12491]};
uts46_map(13030) -> {'M', [12492]};
uts46_map(13031) -> {'M', [12493]};
uts46_map(13032) -> {'M', [12494]};
uts46_map(13033) -> {'M', [12495]};
uts46_map(13034) -> {'M', [12498]};
uts46_map(13035) -> {'M', [12501]};
uts46_map(13036) -> {'M', [12504]};
uts46_map(13037) -> {'M', [12507]};
uts46_map(13038) -> {'M', [12510]};
uts46_map(13039) -> {'M', [12511]};
uts46_map(13040) -> {'M', [12512]};
uts46_map(13041) -> {'M', [12513]};
uts46_map(13042) -> {'M', [12514]};
uts46_map(13043) -> {'M', [12516]};
uts46_map(13044) -> {'M', [12518]};
uts46_map(13045) -> {'M', [12520]};
uts46_map(13046) -> {'M', [12521]};
uts46_map(13047) -> {'M', [12522]};
uts46_map(13048) -> {'M', [12523]};
uts46_map(13049) -> {'M', [12524]};
uts46_map(13050) -> {'M', [12525]};
uts46_map(13051) -> {'M', [12527]};
uts46_map(13052) -> {'M', [12528]};
uts46_map(13053) -> {'M', [12529]};
uts46_map(13054) -> {'M', [12530]};
uts46_map(13055) -> {'M', [20196,21644]};
uts46_map(13056) -> {'M', [12450,12497,12540,12488]};
uts46_map(13057) -> {'M', [12450,12523,12501,12449]};
uts46_map(13058) -> {'M', [12450,12531,12506,12450]};
uts46_map(13059) -> {'M', [12450,12540,12523]};
uts46_map(13060) -> {'M', [12452,12491,12531,12464]};
uts46_map(13061) -> {'M', [12452,12531,12481]};
uts46_map(13062) -> {'M', [12454,12457,12531]};
uts46_map(13063) -> {'M', [12456,12473,12463,12540,12489]};
uts46_map(13064) -> {'M', [12456,12540,12459,12540]};
uts46_map(13065) -> {'M', [12458,12531,12473]};
uts46_map(13066) -> {'M', [12458,12540,12512]};
uts46_map(13067) -> {'M', [12459,12452,12522]};
uts46_map(13068) -> {'M', [12459,12521,12483,12488]};
uts46_map(13069) -> {'M', [12459,12525,12522,12540]};
uts46_map(13070) -> {'M', [12460,12525,12531]};
uts46_map(13071) -> {'M', [12460,12531,12510]};
uts46_map(13072) -> {'M', [12462,12460]};
uts46_map(13073) -> {'M', [12462,12491,12540]};
uts46_map(13074) -> {'M', [12461,12517,12522,12540]};
uts46_map(13075) -> {'M', [12462,12523,12480,12540]};
uts46_map(13076) -> {'M', [12461,12525]};
uts46_map(13077) -> {'M', [12461,12525,12464,12521,12512]};
uts46_map(13078) -> {'M', [12461,12525,12513,12540,12488,12523]};
uts46_map(13079) -> {'M', [12461,12525,12527,12483,12488]};
uts46_map(13080) -> {'M', [12464,12521,12512]};
uts46_map(13081) -> {'M', [12464,12521,12512,12488,12531]};
uts46_map(13082) -> {'M', [12463,12523,12476,12452,12525]};
uts46_map(13083) -> {'M', [12463,12525,12540,12493]};
uts46_map(13084) -> {'M', [12465,12540,12473]};
uts46_map(13085) -> {'M', [12467,12523,12490]};
uts46_map(13086) -> {'M', [12467,12540,12509]};
uts46_map(13087) -> {'M', [12469,12452,12463,12523]};
uts46_map(13088) -> {'M', [12469,12531,12481,12540,12512]};
uts46_map(13089) -> {'M', [12471,12522,12531,12464]};
uts46_map(13090) -> {'M', [12475,12531,12481]};
uts46_map(13091) -> {'M', [12475,12531,12488]};
uts46_map(13092) -> {'M', [12480,12540,12473]};
uts46_map(13093) -> {'M', [12487,12471]};
uts46_map(13094) -> {'M', [12489,12523]};
uts46_map(13095) -> {'M', [12488,12531]};
uts46_map(13096) -> {'M', [12490,12494]};
uts46_map(13097) -> {'M', [12494,12483,12488]};
uts46_map(13098) -> {'M', [12495,12452,12484]};
uts46_map(13099) -> {'M', [12497,12540,12475,12531,12488]};
uts46_map(13100) -> {'M', [12497,12540,12484]};
uts46_map(13101) -> {'M', [12496,12540,12524,12523]};
uts46_map(13102) -> {'M', [12500,12450,12473,12488,12523]};
uts46_map(13103) -> {'M', [12500,12463,12523]};
uts46_map(13104) -> {'M', [12500,12467]};
uts46_map(13105) -> {'M', [12499,12523]};
uts46_map(13106) -> {'M', [12501,12449,12521,12483,12489]};
uts46_map(13107) -> {'M', [12501,12451,12540,12488]};
uts46_map(13108) -> {'M', [12502,12483,12471,12455,12523]};
uts46_map(13109) -> {'M', [12501,12521,12531]};
uts46_map(13110) -> {'M', [12504,12463,12479,12540,12523]};
uts46_map(13111) -> {'M', [12506,12477]};
uts46_map(13112) -> {'M', [12506,12491,12498]};
uts46_map(13113) -> {'M', [12504,12523,12484]};
uts46_map(13114) -> {'M', [12506,12531,12473]};
uts46_map(13115) -> {'M', [12506,12540,12472]};
uts46_map(13116) -> {'M', [12505,12540,12479]};
uts46_map(13117) -> {'M', [12509,12452,12531,12488]};
uts46_map(13118) -> {'M', [12508,12523,12488]};
uts46_map(13119) -> {'M', [12507,12531]};
uts46_map(13120) -> {'M', [12509,12531,12489]};
uts46_map(13121) -> {'M', [12507,12540,12523]};
uts46_map(13122) -> {'M', [12507,12540,12531]};
uts46_map(13123) -> {'M', [12510,12452,12463,12525]};
uts46_map(13124) -> {'M', [12510,12452,12523]};
uts46_map(13125) -> {'M', [12510,12483,12495]};
uts46_map(13126) -> {'M', [12510,12523,12463]};
uts46_map(13127) -> {'M', [12510,12531,12471,12519,12531]};
uts46_map(13128) -> {'M', [12511,12463,12525,12531]};
uts46_map(13129) -> {'M', [12511,12522]};
uts46_map(13130) -> {'M', [12511,12522,12496,12540,12523]};
uts46_map(13131) -> {'M', [12513,12460]};
uts46_map(13132) -> {'M', [12513,12460,12488,12531]};
uts46_map(13133) -> {'M', [12513,12540,12488,12523]};
uts46_map(13134) -> {'M', [12516,12540,12489]};
uts46_map(13135) -> {'M', [12516,12540,12523]};
uts46_map(13136) -> {'M', [12518,12450,12531]};
uts46_map(13137) -> {'M', [12522,12483,12488,12523]};
uts46_map(13138) -> {'M', [12522,12521]};
uts46_map(13139) -> {'M', [12523,12500,12540]};
uts46_map(13140) -> {'M', [12523,12540,12502,12523]};
uts46_map(13141) -> {'M', [12524,12512]};
uts46_map(13142) -> {'M', [12524,12531,12488,12466,12531]};
uts46_map(13143) -> {'M', [12527,12483,12488]};
uts46_map(13144) -> {'M', [48,28857]};
uts46_map(13145) -> {'M', [49,28857]};
uts46_map(13146) -> {'M', [50,28857]};
uts46_map(13147) -> {'M', [51,28857]};
uts46_map(13148) -> {'M', [52,28857]};
uts46_map(13149) -> {'M', [53,28857]};
uts46_map(13150) -> {'M', [54,28857]};
uts46_map(13151) -> {'M', [55,28857]};
uts46_map(13152) -> {'M', [56,28857]};
uts46_map(13153) -> {'M', [57,28857]};
uts46_map(13154) -> {'M', [49,48,28857]};
uts46_map(13155) -> {'M', [49,49,28857]};
uts46_map(13156) -> {'M', [49,50,28857]};
uts46_map(13157) -> {'M', [49,51,28857]};
uts46_map(13158) -> {'M', [49,52,28857]};
uts46_map(13159) -> {'M', [49,53,28857]};
uts46_map(13160) -> {'M', [49,54,28857]};
uts46_map(13161) -> {'M', [49,55,28857]};
uts46_map(13162) -> {'M', [49,56,28857]};
uts46_map(13163) -> {'M', [49,57,28857]};
uts46_map(13164) -> {'M', [50,48,28857]};
uts46_map(13165) -> {'M', [50,49,28857]};
uts46_map(13166) -> {'M', [50,50,28857]};
uts46_map(13167) -> {'M', [50,51,28857]};
uts46_map(13168) -> {'M', [50,52,28857]};
uts46_map(13169) -> {'M', [104,112,97]};
uts46_map(13170) -> {'M', [100,97]};
uts46_map(13171) -> {'M', [97,117]};
uts46_map(13172) -> {'M', [98,97,114]};
uts46_map(13173) -> {'M', [111,118]};
uts46_map(13174) -> {'M', [112,99]};
uts46_map(13175) -> {'M', [100,109]};
uts46_map(13176) -> {'M', [100,109,50]};
uts46_map(13177) -> {'M', [100,109,51]};
uts46_map(13178) -> {'M', [105,117]};
uts46_map(13179) -> {'M', [24179,25104]};
uts46_map(13180) -> {'M', [26157,21644]};
uts46_map(13181) -> {'M', [22823,27491]};
uts46_map(13182) -> {'M', [26126,27835]};
uts46_map(13183) -> {'M', [26666,24335,20250,31038]};
uts46_map(13184) -> {'M', [112,97]};
uts46_map(13185) -> {'M', [110,97]};
uts46_map(13186) -> {'M', [956,97]};
uts46_map(13187) -> {'M', [109,97]};
uts46_map(13188) -> {'M', [107,97]};
uts46_map(13189) -> {'M', [107,98]};
uts46_map(13190) -> {'M', [109,98]};
uts46_map(13191) -> {'M', [103,98]};
uts46_map(13192) -> {'M', [99,97,108]};
uts46_map(13193) -> {'M', [107,99,97,108]};
uts46_map(13194) -> {'M', [112,102]};
uts46_map(13195) -> {'M', [110,102]};
uts46_map(13196) -> {'M', [956,102]};
uts46_map(13197) -> {'M', [956,103]};
uts46_map(13198) -> {'M', [109,103]};
uts46_map(13199) -> {'M', [107,103]};
uts46_map(13200) -> {'M', [104,122]};
uts46_map(13201) -> {'M', [107,104,122]};
uts46_map(13202) -> {'M', [109,104,122]};
uts46_map(13203) -> {'M', [103,104,122]};
uts46_map(13204) -> {'M', [116,104,122]};
uts46_map(13205) -> {'M', [956,108]};
uts46_map(13206) -> {'M', [109,108]};
uts46_map(13207) -> {'M', [100,108]};
uts46_map(13208) -> {'M', [107,108]};
uts46_map(13209) -> {'M', [102,109]};
uts46_map(13210) -> {'M', [110,109]};
uts46_map(13211) -> {'M', [956,109]};
uts46_map(13212) -> {'M', [109,109]};
uts46_map(13213) -> {'M', [99,109]};
uts46_map(13214) -> {'M', [107,109]};
uts46_map(13215) -> {'M', [109,109,50]};
uts46_map(13216) -> {'M', [99,109,50]};
uts46_map(13217) -> {'M', [109,50]};
uts46_map(13218) -> {'M', [107,109,50]};
uts46_map(13219) -> {'M', [109,109,51]};
uts46_map(13220) -> {'M', [99,109,51]};
uts46_map(13221) -> {'M', [109,51]};
uts46_map(13222) -> {'M', [107,109,51]};
uts46_map(13223) -> {'M', [109,8725,115]};
uts46_map(13224) -> {'M', [109,8725,115,50]};
uts46_map(13225) -> {'M', [112,97]};
uts46_map(13226) -> {'M', [107,112,97]};
uts46_map(13227) -> {'M', [109,112,97]};
uts46_map(13228) -> {'M', [103,112,97]};
uts46_map(13229) -> {'M', [114,97,100]};
uts46_map(13230) -> {'M', [114,97,100,8725,115]};
uts46_map(13231) -> {'M', [114,97,100,8725,115,50]};
uts46_map(13232) -> {'M', [112,115]};
uts46_map(13233) -> {'M', [110,115]};
uts46_map(13234) -> {'M', [956,115]};
uts46_map(13235) -> {'M', [109,115]};
uts46_map(13236) -> {'M', [112,118]};
uts46_map(13237) -> {'M', [110,118]};
uts46_map(13238) -> {'M', [956,118]};
uts46_map(13239) -> {'M', [109,118]};
uts46_map(13240) -> {'M', [107,118]};
uts46_map(13241) -> {'M', [109,118]};
uts46_map(13242) -> {'M', [112,119]};
uts46_map(13243) -> {'M', [110,119]};
uts46_map(13244) -> {'M', [956,119]};
uts46_map(13245) -> {'M', [109,119]};
uts46_map(13246) -> {'M', [107,119]};
uts46_map(13247) -> {'M', [109,119]};
uts46_map(13248) -> {'M', [107,969]};
uts46_map(13249) -> {'M', [109,969]};
uts46_map(13250) -> 'X';
uts46_map(13251) -> {'M', [98,113]};
uts46_map(13252) -> {'M', [99,99]};
uts46_map(13253) -> {'M', [99,100]};
uts46_map(13254) -> {'M', [99,8725,107,103]};
uts46_map(13255) -> 'X';
uts46_map(13256) -> {'M', [100,98]};
uts46_map(13257) -> {'M', [103,121]};
uts46_map(13258) -> {'M', [104,97]};
uts46_map(13259) -> {'M', [104,112]};
uts46_map(13260) -> {'M', [105,110]};
uts46_map(13261) -> {'M', [107,107]};
uts46_map(13262) -> {'M', [107,109]};
uts46_map(13263) -> {'M', [107,116]};
uts46_map(13264) -> {'M', [108,109]};
uts46_map(13265) -> {'M', [108,110]};
uts46_map(13266) -> {'M', [108,111,103]};
uts46_map(13267) -> {'M', [108,120]};
uts46_map(13268) -> {'M', [109,98]};
uts46_map(13269) -> {'M', [109,105,108]};
uts46_map(13270) -> {'M', [109,111,108]};
uts46_map(13271) -> {'M', [112,104]};
uts46_map(13272) -> 'X';
uts46_map(13273) -> {'M', [112,112,109]};
uts46_map(13274) -> {'M', [112,114]};
uts46_map(13275) -> {'M', [115,114]};
uts46_map(13276) -> {'M', [115,118]};
uts46_map(13277) -> {'M', [119,98]};
uts46_map(13278) -> {'M', [118,8725,109]};
uts46_map(13279) -> {'M', [97,8725,109]};
uts46_map(13280) -> {'M', [49,26085]};
uts46_map(13281) -> {'M', [50,26085]};
uts46_map(13282) -> {'M', [51,26085]};
uts46_map(13283) -> {'M', [52,26085]};
uts46_map(13284) -> {'M', [53,26085]};
uts46_map(13285) -> {'M', [54,26085]};
uts46_map(13286) -> {'M', [55,26085]};
uts46_map(13287) -> {'M', [56,26085]};
uts46_map(13288) -> {'M', [57,26085]};
uts46_map(13289) -> {'M', [49,48,26085]};
uts46_map(13290) -> {'M', [49,49,26085]};
uts46_map(13291) -> {'M', [49,50,26085]};
uts46_map(13292) -> {'M', [49,51,26085]};
uts46_map(13293) -> {'M', [49,52,26085]};
uts46_map(13294) -> {'M', [49,53,26085]};
uts46_map(13295) -> {'M', [49,54,26085]};
uts46_map(13296) -> {'M', [49,55,26085]};
uts46_map(13297) -> {'M', [49,56,26085]};
uts46_map(13298) -> {'M', [49,57,26085]};
uts46_map(13299) -> {'M', [50,48,26085]};
uts46_map(13300) -> {'M', [50,49,26085]};
uts46_map(13301) -> {'M', [50,50,26085]};
uts46_map(13302) -> {'M', [50,51,26085]};
uts46_map(13303) -> {'M', [50,52,26085]};
uts46_map(13304) -> {'M', [50,53,26085]};
uts46_map(13305) -> {'M', [50,54,26085]};
uts46_map(13306) -> {'M', [50,55,26085]};
uts46_map(13307) -> {'M', [50,56,26085]};
uts46_map(13308) -> {'M', [50,57,26085]};
uts46_map(13309) -> {'M', [51,48,26085]};
uts46_map(13310) -> {'M', [51,49,26085]};
uts46_map(13311) -> {'M', [103,97,108]};
uts46_map(40908) -> 'V';
uts46_map(42164) -> 'V';
uts46_map(42177) -> 'V';
uts46_map(42181) -> 'V';
uts46_map(42182) -> 'V';
uts46_map(42560) -> {'M', [42561]};
uts46_map(42561) -> 'V';
uts46_map(42562) -> {'M', [42563]};
uts46_map(42563) -> 'V';
uts46_map(42564) -> {'M', [42565]};
uts46_map(42565) -> 'V';
uts46_map(42566) -> {'M', [42567]};
uts46_map(42567) -> 'V';
uts46_map(42568) -> {'M', [42569]};
uts46_map(42569) -> 'V';
uts46_map(42570) -> {'M', [42571]};
uts46_map(42571) -> 'V';
uts46_map(42572) -> {'M', [42573]};
uts46_map(42573) -> 'V';
uts46_map(42574) -> {'M', [42575]};
uts46_map(42575) -> 'V';
uts46_map(42576) -> {'M', [42577]};
uts46_map(42577) -> 'V';
uts46_map(42578) -> {'M', [42579]};
uts46_map(42579) -> 'V';
uts46_map(42580) -> {'M', [42581]};
uts46_map(42581) -> 'V';
uts46_map(42582) -> {'M', [42583]};
uts46_map(42583) -> 'V';
uts46_map(42584) -> {'M', [42585]};
uts46_map(42585) -> 'V';
uts46_map(42586) -> {'M', [42587]};
uts46_map(42587) -> 'V';
uts46_map(42588) -> {'M', [42589]};
uts46_map(42589) -> 'V';
uts46_map(42590) -> {'M', [42591]};
uts46_map(42591) -> 'V';
uts46_map(42592) -> {'M', [42593]};
uts46_map(42593) -> 'V';
uts46_map(42594) -> {'M', [42595]};
uts46_map(42595) -> 'V';
uts46_map(42596) -> {'M', [42597]};
uts46_map(42597) -> 'V';
uts46_map(42598) -> {'M', [42599]};
uts46_map(42599) -> 'V';
uts46_map(42600) -> {'M', [42601]};
uts46_map(42601) -> 'V';
uts46_map(42602) -> {'M', [42603]};
uts46_map(42603) -> 'V';
uts46_map(42604) -> {'M', [42605]};
uts46_map(42622) -> 'V';
uts46_map(42623) -> 'V';
uts46_map(42624) -> {'M', [42625]};
uts46_map(42625) -> 'V';
uts46_map(42626) -> {'M', [42627]};
uts46_map(42627) -> 'V';
uts46_map(42628) -> {'M', [42629]};
uts46_map(42629) -> 'V';
uts46_map(42630) -> {'M', [42631]};
uts46_map(42631) -> 'V';
uts46_map(42632) -> {'M', [42633]};
uts46_map(42633) -> 'V';
uts46_map(42634) -> {'M', [42635]};
uts46_map(42635) -> 'V';
uts46_map(42636) -> {'M', [42637]};
uts46_map(42637) -> 'V';
uts46_map(42638) -> {'M', [42639]};
uts46_map(42639) -> 'V';
uts46_map(42640) -> {'M', [42641]};
uts46_map(42641) -> 'V';
uts46_map(42642) -> {'M', [42643]};
uts46_map(42643) -> 'V';
uts46_map(42644) -> {'M', [42645]};
uts46_map(42645) -> 'V';
uts46_map(42646) -> {'M', [42647]};
uts46_map(42647) -> 'V';
uts46_map(42648) -> {'M', [42649]};
uts46_map(42649) -> 'V';
uts46_map(42650) -> {'M', [42651]};
uts46_map(42651) -> 'V';
uts46_map(42652) -> {'M', [1098]};
uts46_map(42653) -> {'M', [1100]};
uts46_map(42654) -> 'V';
uts46_map(42655) -> 'V';
uts46_map(42786) -> {'M', [42787]};
uts46_map(42787) -> 'V';
uts46_map(42788) -> {'M', [42789]};
uts46_map(42789) -> 'V';
uts46_map(42790) -> {'M', [42791]};
uts46_map(42791) -> 'V';
uts46_map(42792) -> {'M', [42793]};
uts46_map(42793) -> 'V';
uts46_map(42794) -> {'M', [42795]};
uts46_map(42795) -> 'V';
uts46_map(42796) -> {'M', [42797]};
uts46_map(42797) -> 'V';
uts46_map(42798) -> {'M', [42799]};
uts46_map(42802) -> {'M', [42803]};
uts46_map(42803) -> 'V';
uts46_map(42804) -> {'M', [42805]};
uts46_map(42805) -> 'V';
uts46_map(42806) -> {'M', [42807]};
uts46_map(42807) -> 'V';
uts46_map(42808) -> {'M', [42809]};
uts46_map(42809) -> 'V';
uts46_map(42810) -> {'M', [42811]};
uts46_map(42811) -> 'V';
uts46_map(42812) -> {'M', [42813]};
uts46_map(42813) -> 'V';
uts46_map(42814) -> {'M', [42815]};
uts46_map(42815) -> 'V';
uts46_map(42816) -> {'M', [42817]};
uts46_map(42817) -> 'V';
uts46_map(42818) -> {'M', [42819]};
uts46_map(42819) -> 'V';
uts46_map(42820) -> {'M', [42821]};
uts46_map(42821) -> 'V';
uts46_map(42822) -> {'M', [42823]};
uts46_map(42823) -> 'V';
uts46_map(42824) -> {'M', [42825]};
uts46_map(42825) -> 'V';
uts46_map(42826) -> {'M', [42827]};
uts46_map(42827) -> 'V';
uts46_map(42828) -> {'M', [42829]};
uts46_map(42829) -> 'V';
uts46_map(42830) -> {'M', [42831]};
uts46_map(42831) -> 'V';
uts46_map(42832) -> {'M', [42833]};
uts46_map(42833) -> 'V';
uts46_map(42834) -> {'M', [42835]};
uts46_map(42835) -> 'V';
uts46_map(42836) -> {'M', [42837]};
uts46_map(42837) -> 'V';
uts46_map(42838) -> {'M', [42839]};
uts46_map(42839) -> 'V';
uts46_map(42840) -> {'M', [42841]};
uts46_map(42841) -> 'V';
uts46_map(42842) -> {'M', [42843]};
uts46_map(42843) -> 'V';
uts46_map(42844) -> {'M', [42845]};
uts46_map(42845) -> 'V';
uts46_map(42846) -> {'M', [42847]};
uts46_map(42847) -> 'V';
uts46_map(42848) -> {'M', [42849]};
uts46_map(42849) -> 'V';
uts46_map(42850) -> {'M', [42851]};
uts46_map(42851) -> 'V';
uts46_map(42852) -> {'M', [42853]};
uts46_map(42853) -> 'V';
uts46_map(42854) -> {'M', [42855]};
uts46_map(42855) -> 'V';
uts46_map(42856) -> {'M', [42857]};
uts46_map(42857) -> 'V';
uts46_map(42858) -> {'M', [42859]};
uts46_map(42859) -> 'V';
uts46_map(42860) -> {'M', [42861]};
uts46_map(42861) -> 'V';
uts46_map(42862) -> {'M', [42863]};
uts46_map(42863) -> 'V';
uts46_map(42864) -> {'M', [42863]};
uts46_map(42873) -> {'M', [42874]};
uts46_map(42874) -> 'V';
uts46_map(42875) -> {'M', [42876]};
uts46_map(42876) -> 'V';
uts46_map(42877) -> {'M', [7545]};
uts46_map(42878) -> {'M', [42879]};
uts46_map(42879) -> 'V';
uts46_map(42880) -> {'M', [42881]};
uts46_map(42881) -> 'V';
uts46_map(42882) -> {'M', [42883]};
uts46_map(42883) -> 'V';
uts46_map(42884) -> {'M', [42885]};
uts46_map(42885) -> 'V';
uts46_map(42886) -> {'M', [42887]};
uts46_map(42891) -> {'M', [42892]};
uts46_map(42892) -> 'V';
uts46_map(42893) -> {'M', [613]};
uts46_map(42894) -> 'V';
uts46_map(42895) -> 'V';
uts46_map(42896) -> {'M', [42897]};
uts46_map(42897) -> 'V';
uts46_map(42898) -> {'M', [42899]};
uts46_map(42899) -> 'V';
uts46_map(42902) -> {'M', [42903]};
uts46_map(42903) -> 'V';
uts46_map(42904) -> {'M', [42905]};
uts46_map(42905) -> 'V';
uts46_map(42906) -> {'M', [42907]};
uts46_map(42907) -> 'V';
uts46_map(42908) -> {'M', [42909]};
uts46_map(42909) -> 'V';
uts46_map(42910) -> {'M', [42911]};
uts46_map(42911) -> 'V';
uts46_map(42912) -> {'M', [42913]};
uts46_map(42913) -> 'V';
uts46_map(42914) -> {'M', [42915]};
uts46_map(42915) -> 'V';
uts46_map(42916) -> {'M', [42917]};
uts46_map(42917) -> 'V';
uts46_map(42918) -> {'M', [42919]};
uts46_map(42919) -> 'V';
uts46_map(42920) -> {'M', [42921]};
uts46_map(42921) -> 'V';
uts46_map(42922) -> {'M', [614]};
uts46_map(42923) -> {'M', [604]};
uts46_map(42924) -> {'M', [609]};
uts46_map(42925) -> {'M', [620]};
uts46_map(42926) -> {'M', [618]};
uts46_map(42927) -> 'V';
uts46_map(42928) -> {'M', [670]};
uts46_map(42929) -> {'M', [647]};
uts46_map(42930) -> {'M', [669]};
uts46_map(42931) -> {'M', [43859]};
uts46_map(42932) -> {'M', [42933]};
uts46_map(42933) -> 'V';
uts46_map(42934) -> {'M', [42935]};
uts46_map(42935) -> 'V';
uts46_map(42936) -> {'M', [42937]};
uts46_map(42937) -> 'V';
uts46_map(42938) -> {'M', [42939]};
uts46_map(42939) -> 'V';
uts46_map(42940) -> {'M', [42941]};
uts46_map(42941) -> 'V';
uts46_map(42942) -> {'M', [42943]};
uts46_map(42943) -> 'V';
uts46_map(42946) -> {'M', [42947]};
uts46_map(42947) -> 'V';
uts46_map(42948) -> {'M', [42900]};
uts46_map(42949) -> {'M', [642]};
uts46_map(42950) -> {'M', [7566]};
uts46_map(42951) -> {'M', [42952]};
uts46_map(42952) -> 'V';
uts46_map(42953) -> {'M', [42954]};
uts46_map(42954) -> 'V';
uts46_map(42997) -> {'M', [42998]};
uts46_map(42998) -> 'V';
uts46_map(42999) -> 'V';
uts46_map(43000) -> {'M', [295]};
uts46_map(43001) -> {'M', [339]};
uts46_map(43002) -> 'V';
uts46_map(43052) -> 'V';
uts46_map(43205) -> 'V';
uts46_map(43259) -> 'V';
uts46_map(43260) -> 'V';
uts46_map(43261) -> 'V';
uts46_map(43359) -> 'V';
uts46_map(43470) -> 'X';
uts46_map(43519) -> 'X';
uts46_map(43815) -> 'X';
uts46_map(43823) -> 'X';
uts46_map(43867) -> 'V';
uts46_map(43868) -> {'M', [42791]};
uts46_map(43869) -> {'M', [43831]};
uts46_map(43870) -> {'M', [619]};
uts46_map(43871) -> {'M', [43858]};
uts46_map(43880) -> 'V';
uts46_map(43881) -> {'M', [653]};
uts46_map(43888) -> {'M', [5024]};
uts46_map(43889) -> {'M', [5025]};
uts46_map(43890) -> {'M', [5026]};
uts46_map(43891) -> {'M', [5027]};
uts46_map(43892) -> {'M', [5028]};
uts46_map(43893) -> {'M', [5029]};
uts46_map(43894) -> {'M', [5030]};
uts46_map(43895) -> {'M', [5031]};
uts46_map(43896) -> {'M', [5032]};
uts46_map(43897) -> {'M', [5033]};
uts46_map(43898) -> {'M', [5034]};
uts46_map(43899) -> {'M', [5035]};
uts46_map(43900) -> {'M', [5036]};
uts46_map(43901) -> {'M', [5037]};
uts46_map(43902) -> {'M', [5038]};
uts46_map(43903) -> {'M', [5039]};
uts46_map(43904) -> {'M', [5040]};
uts46_map(43905) -> {'M', [5041]};
uts46_map(43906) -> {'M', [5042]};
uts46_map(43907) -> {'M', [5043]};
uts46_map(43908) -> {'M', [5044]};
uts46_map(43909) -> {'M', [5045]};
uts46_map(43910) -> {'M', [5046]};
uts46_map(43911) -> {'M', [5047]};
uts46_map(43912) -> {'M', [5048]};
uts46_map(43913) -> {'M', [5049]};
uts46_map(43914) -> {'M', [5050]};
uts46_map(43915) -> {'M', [5051]};
uts46_map(43916) -> {'M', [5052]};
uts46_map(43917) -> {'M', [5053]};
uts46_map(43918) -> {'M', [5054]};
uts46_map(43919) -> {'M', [5055]};
uts46_map(43920) -> {'M', [5056]};
uts46_map(43921) -> {'M', [5057]};
uts46_map(43922) -> {'M', [5058]};
uts46_map(43923) -> {'M', [5059]};
uts46_map(43924) -> {'M', [5060]};
uts46_map(43925) -> {'M', [5061]};
uts46_map(43926) -> {'M', [5062]};
uts46_map(43927) -> {'M', [5063]};
uts46_map(43928) -> {'M', [5064]};
uts46_map(43929) -> {'M', [5065]};
uts46_map(43930) -> {'M', [5066]};
uts46_map(43931) -> {'M', [5067]};
uts46_map(43932) -> {'M', [5068]};
uts46_map(43933) -> {'M', [5069]};
uts46_map(43934) -> {'M', [5070]};
uts46_map(43935) -> {'M', [5071]};
uts46_map(43936) -> {'M', [5072]};
uts46_map(43937) -> {'M', [5073]};
uts46_map(43938) -> {'M', [5074]};
uts46_map(43939) -> {'M', [5075]};
uts46_map(43940) -> {'M', [5076]};
uts46_map(43941) -> {'M', [5077]};
uts46_map(43942) -> {'M', [5078]};
uts46_map(43943) -> {'M', [5079]};
uts46_map(43944) -> {'M', [5080]};
uts46_map(43945) -> {'M', [5081]};
uts46_map(43946) -> {'M', [5082]};
uts46_map(43947) -> {'M', [5083]};
uts46_map(43948) -> {'M', [5084]};
uts46_map(43949) -> {'M', [5085]};
uts46_map(43950) -> {'M', [5086]};
uts46_map(43951) -> {'M', [5087]};
uts46_map(43952) -> {'M', [5088]};
uts46_map(43953) -> {'M', [5089]};
uts46_map(43954) -> {'M', [5090]};
uts46_map(43955) -> {'M', [5091]};
uts46_map(43956) -> {'M', [5092]};
uts46_map(43957) -> {'M', [5093]};
uts46_map(43958) -> {'M', [5094]};
uts46_map(43959) -> {'M', [5095]};
uts46_map(43960) -> {'M', [5096]};
uts46_map(43961) -> {'M', [5097]};
uts46_map(43962) -> {'M', [5098]};
uts46_map(43963) -> {'M', [5099]};
uts46_map(43964) -> {'M', [5100]};
uts46_map(43965) -> {'M', [5101]};
uts46_map(43966) -> {'M', [5102]};
uts46_map(43967) -> {'M', [5103]};
uts46_map(44011) -> 'V';
uts46_map(63744) -> {'M', [35912]};
uts46_map(63745) -> {'M', [26356]};
uts46_map(63746) -> {'M', [36554]};
uts46_map(63747) -> {'M', [36040]};
uts46_map(63748) -> {'M', [28369]};
uts46_map(63749) -> {'M', [20018]};
uts46_map(63750) -> {'M', [21477]};
uts46_map(63753) -> {'M', [22865]};
uts46_map(63754) -> {'M', [37329]};
uts46_map(63755) -> {'M', [21895]};
uts46_map(63756) -> {'M', [22856]};
uts46_map(63757) -> {'M', [25078]};
uts46_map(63758) -> {'M', [30313]};
uts46_map(63759) -> {'M', [32645]};
uts46_map(63760) -> {'M', [34367]};
uts46_map(63761) -> {'M', [34746]};
uts46_map(63762) -> {'M', [35064]};
uts46_map(63763) -> {'M', [37007]};
uts46_map(63764) -> {'M', [27138]};
uts46_map(63765) -> {'M', [27931]};
uts46_map(63766) -> {'M', [28889]};
uts46_map(63767) -> {'M', [29662]};
uts46_map(63768) -> {'M', [33853]};
uts46_map(63769) -> {'M', [37226]};
uts46_map(63770) -> {'M', [39409]};
uts46_map(63771) -> {'M', [20098]};
uts46_map(63772) -> {'M', [21365]};
uts46_map(63773) -> {'M', [27396]};
uts46_map(63774) -> {'M', [29211]};
uts46_map(63775) -> {'M', [34349]};
uts46_map(63776) -> {'M', [40478]};
uts46_map(63777) -> {'M', [23888]};
uts46_map(63778) -> {'M', [28651]};
uts46_map(63779) -> {'M', [34253]};
uts46_map(63780) -> {'M', [35172]};
uts46_map(63781) -> {'M', [25289]};
uts46_map(63782) -> {'M', [33240]};
uts46_map(63783) -> {'M', [34847]};
uts46_map(63784) -> {'M', [24266]};
uts46_map(63785) -> {'M', [26391]};
uts46_map(63786) -> {'M', [28010]};
uts46_map(63787) -> {'M', [29436]};
uts46_map(63788) -> {'M', [37070]};
uts46_map(63789) -> {'M', [20358]};
uts46_map(63790) -> {'M', [20919]};
uts46_map(63791) -> {'M', [21214]};
uts46_map(63792) -> {'M', [25796]};
uts46_map(63793) -> {'M', [27347]};
uts46_map(63794) -> {'M', [29200]};
uts46_map(63795) -> {'M', [30439]};
uts46_map(63796) -> {'M', [32769]};
uts46_map(63797) -> {'M', [34310]};
uts46_map(63798) -> {'M', [34396]};
uts46_map(63799) -> {'M', [36335]};
uts46_map(63800) -> {'M', [38706]};
uts46_map(63801) -> {'M', [39791]};
uts46_map(63802) -> {'M', [40442]};
uts46_map(63803) -> {'M', [30860]};
uts46_map(63804) -> {'M', [31103]};
uts46_map(63805) -> {'M', [32160]};
uts46_map(63806) -> {'M', [33737]};
uts46_map(63807) -> {'M', [37636]};
uts46_map(63808) -> {'M', [40575]};
uts46_map(63809) -> {'M', [35542]};
uts46_map(63810) -> {'M', [22751]};
uts46_map(63811) -> {'M', [24324]};
uts46_map(63812) -> {'M', [31840]};
uts46_map(63813) -> {'M', [32894]};
uts46_map(63814) -> {'M', [29282]};
uts46_map(63815) -> {'M', [30922]};
uts46_map(63816) -> {'M', [36034]};
uts46_map(63817) -> {'M', [38647]};
uts46_map(63818) -> {'M', [22744]};
uts46_map(63819) -> {'M', [23650]};
uts46_map(63820) -> {'M', [27155]};
uts46_map(63821) -> {'M', [28122]};
uts46_map(63822) -> {'M', [28431]};
uts46_map(63823) -> {'M', [32047]};
uts46_map(63824) -> {'M', [32311]};
uts46_map(63825) -> {'M', [38475]};
uts46_map(63826) -> {'M', [21202]};
uts46_map(63827) -> {'M', [32907]};
uts46_map(63828) -> {'M', [20956]};
uts46_map(63829) -> {'M', [20940]};
uts46_map(63830) -> {'M', [31260]};
uts46_map(63831) -> {'M', [32190]};
uts46_map(63832) -> {'M', [33777]};
uts46_map(63833) -> {'M', [38517]};
uts46_map(63834) -> {'M', [35712]};
uts46_map(63835) -> {'M', [25295]};
uts46_map(63836) -> {'M', [27138]};
uts46_map(63837) -> {'M', [35582]};
uts46_map(63838) -> {'M', [20025]};
uts46_map(63839) -> {'M', [23527]};
uts46_map(63840) -> {'M', [24594]};
uts46_map(63841) -> {'M', [29575]};
uts46_map(63842) -> {'M', [30064]};
uts46_map(63843) -> {'M', [21271]};
uts46_map(63844) -> {'M', [30971]};
uts46_map(63845) -> {'M', [20415]};
uts46_map(63846) -> {'M', [24489]};
uts46_map(63847) -> {'M', [19981]};
uts46_map(63848) -> {'M', [27852]};
uts46_map(63849) -> {'M', [25976]};
uts46_map(63850) -> {'M', [32034]};
uts46_map(63851) -> {'M', [21443]};
uts46_map(63852) -> {'M', [22622]};
uts46_map(63853) -> {'M', [30465]};
uts46_map(63854) -> {'M', [33865]};
uts46_map(63855) -> {'M', [35498]};
uts46_map(63856) -> {'M', [27578]};
uts46_map(63857) -> {'M', [36784]};
uts46_map(63858) -> {'M', [27784]};
uts46_map(63859) -> {'M', [25342]};
uts46_map(63860) -> {'M', [33509]};
uts46_map(63861) -> {'M', [25504]};
uts46_map(63862) -> {'M', [30053]};
uts46_map(63863) -> {'M', [20142]};
uts46_map(63864) -> {'M', [20841]};
uts46_map(63865) -> {'M', [20937]};
uts46_map(63866) -> {'M', [26753]};
uts46_map(63867) -> {'M', [31975]};
uts46_map(63868) -> {'M', [33391]};
uts46_map(63869) -> {'M', [35538]};
uts46_map(63870) -> {'M', [37327]};
uts46_map(63871) -> {'M', [21237]};
uts46_map(63872) -> {'M', [21570]};
uts46_map(63873) -> {'M', [22899]};
uts46_map(63874) -> {'M', [24300]};
uts46_map(63875) -> {'M', [26053]};
uts46_map(63876) -> {'M', [28670]};
uts46_map(63877) -> {'M', [31018]};
uts46_map(63878) -> {'M', [38317]};
uts46_map(63879) -> {'M', [39530]};
uts46_map(63880) -> {'M', [40599]};
uts46_map(63881) -> {'M', [40654]};
uts46_map(63882) -> {'M', [21147]};
uts46_map(63883) -> {'M', [26310]};
uts46_map(63884) -> {'M', [27511]};
uts46_map(63885) -> {'M', [36706]};
uts46_map(63886) -> {'M', [24180]};
uts46_map(63887) -> {'M', [24976]};
uts46_map(63888) -> {'M', [25088]};
uts46_map(63889) -> {'M', [25754]};
uts46_map(63890) -> {'M', [28451]};
uts46_map(63891) -> {'M', [29001]};
uts46_map(63892) -> {'M', [29833]};
uts46_map(63893) -> {'M', [31178]};
uts46_map(63894) -> {'M', [32244]};
uts46_map(63895) -> {'M', [32879]};
uts46_map(63896) -> {'M', [36646]};
uts46_map(63897) -> {'M', [34030]};
uts46_map(63898) -> {'M', [36899]};
uts46_map(63899) -> {'M', [37706]};
uts46_map(63900) -> {'M', [21015]};
uts46_map(63901) -> {'M', [21155]};
uts46_map(63902) -> {'M', [21693]};
uts46_map(63903) -> {'M', [28872]};
uts46_map(63904) -> {'M', [35010]};
uts46_map(63905) -> {'M', [35498]};
uts46_map(63906) -> {'M', [24265]};
uts46_map(63907) -> {'M', [24565]};
uts46_map(63908) -> {'M', [25467]};
uts46_map(63909) -> {'M', [27566]};
uts46_map(63910) -> {'M', [31806]};
uts46_map(63911) -> {'M', [29557]};
uts46_map(63912) -> {'M', [20196]};
uts46_map(63913) -> {'M', [22265]};
uts46_map(63914) -> {'M', [23527]};
uts46_map(63915) -> {'M', [23994]};
uts46_map(63916) -> {'M', [24604]};
uts46_map(63917) -> {'M', [29618]};
uts46_map(63918) -> {'M', [29801]};
uts46_map(63919) -> {'M', [32666]};
uts46_map(63920) -> {'M', [32838]};
uts46_map(63921) -> {'M', [37428]};
uts46_map(63922) -> {'M', [38646]};
uts46_map(63923) -> {'M', [38728]};
uts46_map(63924) -> {'M', [38936]};
uts46_map(63925) -> {'M', [20363]};
uts46_map(63926) -> {'M', [31150]};
uts46_map(63927) -> {'M', [37300]};
uts46_map(63928) -> {'M', [38584]};
uts46_map(63929) -> {'M', [24801]};
uts46_map(63930) -> {'M', [20102]};
uts46_map(63931) -> {'M', [20698]};
uts46_map(63932) -> {'M', [23534]};
uts46_map(63933) -> {'M', [23615]};
uts46_map(63934) -> {'M', [26009]};
uts46_map(63935) -> {'M', [27138]};
uts46_map(63936) -> {'M', [29134]};
uts46_map(63937) -> {'M', [30274]};
uts46_map(63938) -> {'M', [34044]};
uts46_map(63939) -> {'M', [36988]};
uts46_map(63940) -> {'M', [40845]};
uts46_map(63941) -> {'M', [26248]};
uts46_map(63942) -> {'M', [38446]};
uts46_map(63943) -> {'M', [21129]};
uts46_map(63944) -> {'M', [26491]};
uts46_map(63945) -> {'M', [26611]};
uts46_map(63946) -> {'M', [27969]};
uts46_map(63947) -> {'M', [28316]};
uts46_map(63948) -> {'M', [29705]};
uts46_map(63949) -> {'M', [30041]};
uts46_map(63950) -> {'M', [30827]};
uts46_map(63951) -> {'M', [32016]};
uts46_map(63952) -> {'M', [39006]};
uts46_map(63953) -> {'M', [20845]};
uts46_map(63954) -> {'M', [25134]};
uts46_map(63955) -> {'M', [38520]};
uts46_map(63956) -> {'M', [20523]};
uts46_map(63957) -> {'M', [23833]};
uts46_map(63958) -> {'M', [28138]};
uts46_map(63959) -> {'M', [36650]};
uts46_map(63960) -> {'M', [24459]};
uts46_map(63961) -> {'M', [24900]};
uts46_map(63962) -> {'M', [26647]};
uts46_map(63963) -> {'M', [29575]};
uts46_map(63964) -> {'M', [38534]};
uts46_map(63965) -> {'M', [21033]};
uts46_map(63966) -> {'M', [21519]};
uts46_map(63967) -> {'M', [23653]};
uts46_map(63968) -> {'M', [26131]};
uts46_map(63969) -> {'M', [26446]};
uts46_map(63970) -> {'M', [26792]};
uts46_map(63971) -> {'M', [27877]};
uts46_map(63972) -> {'M', [29702]};
uts46_map(63973) -> {'M', [30178]};
uts46_map(63974) -> {'M', [32633]};
uts46_map(63975) -> {'M', [35023]};
uts46_map(63976) -> {'M', [35041]};
uts46_map(63977) -> {'M', [37324]};
uts46_map(63978) -> {'M', [38626]};
uts46_map(63979) -> {'M', [21311]};
uts46_map(63980) -> {'M', [28346]};
uts46_map(63981) -> {'M', [21533]};
uts46_map(63982) -> {'M', [29136]};
uts46_map(63983) -> {'M', [29848]};
uts46_map(63984) -> {'M', [34298]};
uts46_map(63985) -> {'M', [38563]};
uts46_map(63986) -> {'M', [40023]};
uts46_map(63987) -> {'M', [40607]};
uts46_map(63988) -> {'M', [26519]};
uts46_map(63989) -> {'M', [28107]};
uts46_map(63990) -> {'M', [33256]};
uts46_map(63991) -> {'M', [31435]};
uts46_map(63992) -> {'M', [31520]};
uts46_map(63993) -> {'M', [31890]};
uts46_map(63994) -> {'M', [29376]};
uts46_map(63995) -> {'M', [28825]};
uts46_map(63996) -> {'M', [35672]};
uts46_map(63997) -> {'M', [20160]};
uts46_map(63998) -> {'M', [33590]};
uts46_map(63999) -> {'M', [21050]};
uts46_map(64000) -> {'M', [20999]};
uts46_map(64001) -> {'M', [24230]};
uts46_map(64002) -> {'M', [25299]};
uts46_map(64003) -> {'M', [31958]};
uts46_map(64004) -> {'M', [23429]};
uts46_map(64005) -> {'M', [27934]};
uts46_map(64006) -> {'M', [26292]};
uts46_map(64007) -> {'M', [36667]};
uts46_map(64008) -> {'M', [34892]};
uts46_map(64009) -> {'M', [38477]};
uts46_map(64010) -> {'M', [35211]};
uts46_map(64011) -> {'M', [24275]};
uts46_map(64012) -> {'M', [20800]};
uts46_map(64013) -> {'M', [21952]};
uts46_map(64016) -> {'M', [22618]};
uts46_map(64017) -> 'V';
uts46_map(64018) -> {'M', [26228]};
uts46_map(64021) -> {'M', [20958]};
uts46_map(64022) -> {'M', [29482]};
uts46_map(64023) -> {'M', [30410]};
uts46_map(64024) -> {'M', [31036]};
uts46_map(64025) -> {'M', [31070]};
uts46_map(64026) -> {'M', [31077]};
uts46_map(64027) -> {'M', [31119]};
uts46_map(64028) -> {'M', [38742]};
uts46_map(64029) -> {'M', [31934]};
uts46_map(64030) -> {'M', [32701]};
uts46_map(64031) -> 'V';
uts46_map(64032) -> {'M', [34322]};
uts46_map(64033) -> 'V';
uts46_map(64034) -> {'M', [35576]};
uts46_map(64037) -> {'M', [36920]};
uts46_map(64038) -> {'M', [37117]};
uts46_map(64042) -> {'M', [39151]};
uts46_map(64043) -> {'M', [39164]};
uts46_map(64044) -> {'M', [39208]};
uts46_map(64045) -> {'M', [40372]};
uts46_map(64046) -> {'M', [37086]};
uts46_map(64047) -> {'M', [38583]};
uts46_map(64048) -> {'M', [20398]};
uts46_map(64049) -> {'M', [20711]};
uts46_map(64050) -> {'M', [20813]};
uts46_map(64051) -> {'M', [21193]};
uts46_map(64052) -> {'M', [21220]};
uts46_map(64053) -> {'M', [21329]};
uts46_map(64054) -> {'M', [21917]};
uts46_map(64055) -> {'M', [22022]};
uts46_map(64056) -> {'M', [22120]};
uts46_map(64057) -> {'M', [22592]};
uts46_map(64058) -> {'M', [22696]};
uts46_map(64059) -> {'M', [23652]};
uts46_map(64060) -> {'M', [23662]};
uts46_map(64061) -> {'M', [24724]};
uts46_map(64062) -> {'M', [24936]};
uts46_map(64063) -> {'M', [24974]};
uts46_map(64064) -> {'M', [25074]};
uts46_map(64065) -> {'M', [25935]};
uts46_map(64066) -> {'M', [26082]};
uts46_map(64067) -> {'M', [26257]};
uts46_map(64068) -> {'M', [26757]};
uts46_map(64069) -> {'M', [28023]};
uts46_map(64070) -> {'M', [28186]};
uts46_map(64071) -> {'M', [28450]};
uts46_map(64072) -> {'M', [29038]};
uts46_map(64073) -> {'M', [29227]};
uts46_map(64074) -> {'M', [29730]};
uts46_map(64075) -> {'M', [30865]};
uts46_map(64076) -> {'M', [31038]};
uts46_map(64077) -> {'M', [31049]};
uts46_map(64078) -> {'M', [31048]};
uts46_map(64079) -> {'M', [31056]};
uts46_map(64080) -> {'M', [31062]};
uts46_map(64081) -> {'M', [31069]};
uts46_map(64082) -> {'M', [31117]};
uts46_map(64083) -> {'M', [31118]};
uts46_map(64084) -> {'M', [31296]};
uts46_map(64085) -> {'M', [31361]};
uts46_map(64086) -> {'M', [31680]};
uts46_map(64087) -> {'M', [32244]};
uts46_map(64088) -> {'M', [32265]};
uts46_map(64089) -> {'M', [32321]};
uts46_map(64090) -> {'M', [32626]};
uts46_map(64091) -> {'M', [32773]};
uts46_map(64092) -> {'M', [33261]};
uts46_map(64095) -> {'M', [33879]};
uts46_map(64096) -> {'M', [35088]};
uts46_map(64097) -> {'M', [35222]};
uts46_map(64098) -> {'M', [35585]};
uts46_map(64099) -> {'M', [35641]};
uts46_map(64100) -> {'M', [36051]};
uts46_map(64101) -> {'M', [36104]};
uts46_map(64102) -> {'M', [36790]};
uts46_map(64103) -> {'M', [36920]};
uts46_map(64104) -> {'M', [38627]};
uts46_map(64105) -> {'M', [38911]};
uts46_map(64106) -> {'M', [38971]};
uts46_map(64107) -> {'M', [24693]};
uts46_map(64108) -> {'M', [148206]};
uts46_map(64109) -> {'M', [33304]};
uts46_map(64112) -> {'M', [20006]};
uts46_map(64113) -> {'M', [20917]};
uts46_map(64114) -> {'M', [20840]};
uts46_map(64115) -> {'M', [20352]};
uts46_map(64116) -> {'M', [20805]};
uts46_map(64117) -> {'M', [20864]};
uts46_map(64118) -> {'M', [21191]};
uts46_map(64119) -> {'M', [21242]};
uts46_map(64120) -> {'M', [21917]};
uts46_map(64121) -> {'M', [21845]};
uts46_map(64122) -> {'M', [21913]};
uts46_map(64123) -> {'M', [21986]};
uts46_map(64124) -> {'M', [22618]};
uts46_map(64125) -> {'M', [22707]};
uts46_map(64126) -> {'M', [22852]};
uts46_map(64127) -> {'M', [22868]};
uts46_map(64128) -> {'M', [23138]};
uts46_map(64129) -> {'M', [23336]};
uts46_map(64130) -> {'M', [24274]};
uts46_map(64131) -> {'M', [24281]};
uts46_map(64132) -> {'M', [24425]};
uts46_map(64133) -> {'M', [24493]};
uts46_map(64134) -> {'M', [24792]};
uts46_map(64135) -> {'M', [24910]};
uts46_map(64136) -> {'M', [24840]};
uts46_map(64137) -> {'M', [24974]};
uts46_map(64138) -> {'M', [24928]};
uts46_map(64139) -> {'M', [25074]};
uts46_map(64140) -> {'M', [25140]};
uts46_map(64141) -> {'M', [25540]};
uts46_map(64142) -> {'M', [25628]};
uts46_map(64143) -> {'M', [25682]};
uts46_map(64144) -> {'M', [25942]};
uts46_map(64145) -> {'M', [26228]};
uts46_map(64146) -> {'M', [26391]};
uts46_map(64147) -> {'M', [26395]};
uts46_map(64148) -> {'M', [26454]};
uts46_map(64149) -> {'M', [27513]};
uts46_map(64150) -> {'M', [27578]};
uts46_map(64151) -> {'M', [27969]};
uts46_map(64152) -> {'M', [28379]};
uts46_map(64153) -> {'M', [28363]};
uts46_map(64154) -> {'M', [28450]};
uts46_map(64155) -> {'M', [28702]};
uts46_map(64156) -> {'M', [29038]};
uts46_map(64157) -> {'M', [30631]};
uts46_map(64158) -> {'M', [29237]};
uts46_map(64159) -> {'M', [29359]};
uts46_map(64160) -> {'M', [29482]};
uts46_map(64161) -> {'M', [29809]};
uts46_map(64162) -> {'M', [29958]};
uts46_map(64163) -> {'M', [30011]};
uts46_map(64164) -> {'M', [30237]};
uts46_map(64165) -> {'M', [30239]};
uts46_map(64166) -> {'M', [30410]};
uts46_map(64167) -> {'M', [30427]};
uts46_map(64168) -> {'M', [30452]};
uts46_map(64169) -> {'M', [30538]};
uts46_map(64170) -> {'M', [30528]};
uts46_map(64171) -> {'M', [30924]};
uts46_map(64172) -> {'M', [31409]};
uts46_map(64173) -> {'M', [31680]};
uts46_map(64174) -> {'M', [31867]};
uts46_map(64175) -> {'M', [32091]};
uts46_map(64176) -> {'M', [32244]};
uts46_map(64177) -> {'M', [32574]};
uts46_map(64178) -> {'M', [32773]};
uts46_map(64179) -> {'M', [33618]};
uts46_map(64180) -> {'M', [33775]};
uts46_map(64181) -> {'M', [34681]};
uts46_map(64182) -> {'M', [35137]};
uts46_map(64183) -> {'M', [35206]};
uts46_map(64184) -> {'M', [35222]};
uts46_map(64185) -> {'M', [35519]};
uts46_map(64186) -> {'M', [35576]};
uts46_map(64187) -> {'M', [35531]};
uts46_map(64188) -> {'M', [35585]};
uts46_map(64189) -> {'M', [35582]};
uts46_map(64190) -> {'M', [35565]};
uts46_map(64191) -> {'M', [35641]};
uts46_map(64192) -> {'M', [35722]};
uts46_map(64193) -> {'M', [36104]};
uts46_map(64194) -> {'M', [36664]};
uts46_map(64195) -> {'M', [36978]};
uts46_map(64196) -> {'M', [37273]};
uts46_map(64197) -> {'M', [37494]};
uts46_map(64198) -> {'M', [38524]};
uts46_map(64199) -> {'M', [38627]};
uts46_map(64200) -> {'M', [38742]};
uts46_map(64201) -> {'M', [38875]};
uts46_map(64202) -> {'M', [38911]};
uts46_map(64203) -> {'M', [38923]};
uts46_map(64204) -> {'M', [38971]};
uts46_map(64205) -> {'M', [39698]};
uts46_map(64206) -> {'M', [40860]};
uts46_map(64207) -> {'M', [141386]};
uts46_map(64208) -> {'M', [141380]};
uts46_map(64209) -> {'M', [144341]};
uts46_map(64210) -> {'M', [15261]};
uts46_map(64211) -> {'M', [16408]};
uts46_map(64212) -> {'M', [16441]};
uts46_map(64213) -> {'M', [152137]};
uts46_map(64214) -> {'M', [154832]};
uts46_map(64215) -> {'M', [163539]};
uts46_map(64216) -> {'M', [40771]};
uts46_map(64217) -> {'M', [40846]};
uts46_map(64256) -> {'M', [102,102]};
uts46_map(64257) -> {'M', [102,105]};
uts46_map(64258) -> {'M', [102,108]};
uts46_map(64259) -> {'M', [102,102,105]};
uts46_map(64260) -> {'M', [102,102,108]};
uts46_map(64275) -> {'M', [1396,1398]};
uts46_map(64276) -> {'M', [1396,1381]};
uts46_map(64277) -> {'M', [1396,1387]};
uts46_map(64278) -> {'M', [1406,1398]};
uts46_map(64279) -> {'M', [1396,1389]};
uts46_map(64285) -> {'M', [1497,1460]};
uts46_map(64286) -> 'V';
uts46_map(64287) -> {'M', [1522,1463]};
uts46_map(64288) -> {'M', [1506]};
uts46_map(64289) -> {'M', [1488]};
uts46_map(64290) -> {'M', [1491]};
uts46_map(64291) -> {'M', [1492]};
uts46_map(64292) -> {'M', [1499]};
uts46_map(64293) -> {'M', [1500]};
uts46_map(64294) -> {'M', [1501]};
uts46_map(64295) -> {'M', [1512]};
uts46_map(64296) -> {'M', [1514]};
uts46_map(64297) -> {'3', [43]};
uts46_map(64298) -> {'M', [1513,1473]};
uts46_map(64299) -> {'M', [1513,1474]};
uts46_map(64300) -> {'M', [1513,1468,1473]};
uts46_map(64301) -> {'M', [1513,1468,1474]};
uts46_map(64302) -> {'M', [1488,1463]};
uts46_map(64303) -> {'M', [1488,1464]};
uts46_map(64304) -> {'M', [1488,1468]};
uts46_map(64305) -> {'M', [1489,1468]};
uts46_map(64306) -> {'M', [1490,1468]};
uts46_map(64307) -> {'M', [1491,1468]};
uts46_map(64308) -> {'M', [1492,1468]};
uts46_map(64309) -> {'M', [1493,1468]};
uts46_map(64310) -> {'M', [1494,1468]};
uts46_map(64311) -> 'X';
uts46_map(64312) -> {'M', [1496,1468]};
uts46_map(64313) -> {'M', [1497,1468]};
uts46_map(64314) -> {'M', [1498,1468]};
uts46_map(64315) -> {'M', [1499,1468]};
uts46_map(64316) -> {'M', [1500,1468]};
uts46_map(64317) -> 'X';
uts46_map(64318) -> {'M', [1502,1468]};
uts46_map(64319) -> 'X';
uts46_map(64320) -> {'M', [1504,1468]};
uts46_map(64321) -> {'M', [1505,1468]};
uts46_map(64322) -> 'X';
uts46_map(64323) -> {'M', [1507,1468]};
uts46_map(64324) -> {'M', [1508,1468]};
uts46_map(64325) -> 'X';
uts46_map(64326) -> {'M', [1510,1468]};
uts46_map(64327) -> {'M', [1511,1468]};
uts46_map(64328) -> {'M', [1512,1468]};
uts46_map(64329) -> {'M', [1513,1468]};
uts46_map(64330) -> {'M', [1514,1468]};
uts46_map(64331) -> {'M', [1493,1465]};
uts46_map(64332) -> {'M', [1489,1471]};
uts46_map(64333) -> {'M', [1499,1471]};
uts46_map(64334) -> {'M', [1508,1471]};
uts46_map(64335) -> {'M', [1488,1500]};
uts46_map(64477) -> {'M', [1735,1652]};
uts46_map(64512) -> {'M', [1574,1580]};
uts46_map(64513) -> {'M', [1574,1581]};
uts46_map(64514) -> {'M', [1574,1605]};
uts46_map(64515) -> {'M', [1574,1609]};
uts46_map(64516) -> {'M', [1574,1610]};
uts46_map(64517) -> {'M', [1576,1580]};
uts46_map(64518) -> {'M', [1576,1581]};
uts46_map(64519) -> {'M', [1576,1582]};
uts46_map(64520) -> {'M', [1576,1605]};
uts46_map(64521) -> {'M', [1576,1609]};
uts46_map(64522) -> {'M', [1576,1610]};
uts46_map(64523) -> {'M', [1578,1580]};
uts46_map(64524) -> {'M', [1578,1581]};
uts46_map(64525) -> {'M', [1578,1582]};
uts46_map(64526) -> {'M', [1578,1605]};
uts46_map(64527) -> {'M', [1578,1609]};
uts46_map(64528) -> {'M', [1578,1610]};
uts46_map(64529) -> {'M', [1579,1580]};
uts46_map(64530) -> {'M', [1579,1605]};
uts46_map(64531) -> {'M', [1579,1609]};
uts46_map(64532) -> {'M', [1579,1610]};
uts46_map(64533) -> {'M', [1580,1581]};
uts46_map(64534) -> {'M', [1580,1605]};
uts46_map(64535) -> {'M', [1581,1580]};
uts46_map(64536) -> {'M', [1581,1605]};
uts46_map(64537) -> {'M', [1582,1580]};
uts46_map(64538) -> {'M', [1582,1581]};
uts46_map(64539) -> {'M', [1582,1605]};
uts46_map(64540) -> {'M', [1587,1580]};
uts46_map(64541) -> {'M', [1587,1581]};
uts46_map(64542) -> {'M', [1587,1582]};
uts46_map(64543) -> {'M', [1587,1605]};
uts46_map(64544) -> {'M', [1589,1581]};
uts46_map(64545) -> {'M', [1589,1605]};
uts46_map(64546) -> {'M', [1590,1580]};
uts46_map(64547) -> {'M', [1590,1581]};
uts46_map(64548) -> {'M', [1590,1582]};
uts46_map(64549) -> {'M', [1590,1605]};
uts46_map(64550) -> {'M', [1591,1581]};
uts46_map(64551) -> {'M', [1591,1605]};
uts46_map(64552) -> {'M', [1592,1605]};
uts46_map(64553) -> {'M', [1593,1580]};
uts46_map(64554) -> {'M', [1593,1605]};
uts46_map(64555) -> {'M', [1594,1580]};
uts46_map(64556) -> {'M', [1594,1605]};
uts46_map(64557) -> {'M', [1601,1580]};
uts46_map(64558) -> {'M', [1601,1581]};
uts46_map(64559) -> {'M', [1601,1582]};
uts46_map(64560) -> {'M', [1601,1605]};
uts46_map(64561) -> {'M', [1601,1609]};
uts46_map(64562) -> {'M', [1601,1610]};
uts46_map(64563) -> {'M', [1602,1581]};
uts46_map(64564) -> {'M', [1602,1605]};
uts46_map(64565) -> {'M', [1602,1609]};
uts46_map(64566) -> {'M', [1602,1610]};
uts46_map(64567) -> {'M', [1603,1575]};
uts46_map(64568) -> {'M', [1603,1580]};
uts46_map(64569) -> {'M', [1603,1581]};
uts46_map(64570) -> {'M', [1603,1582]};
uts46_map(64571) -> {'M', [1603,1604]};
uts46_map(64572) -> {'M', [1603,1605]};
uts46_map(64573) -> {'M', [1603,1609]};
uts46_map(64574) -> {'M', [1603,1610]};
uts46_map(64575) -> {'M', [1604,1580]};
uts46_map(64576) -> {'M', [1604,1581]};
uts46_map(64577) -> {'M', [1604,1582]};
uts46_map(64578) -> {'M', [1604,1605]};
uts46_map(64579) -> {'M', [1604,1609]};
uts46_map(64580) -> {'M', [1604,1610]};
uts46_map(64581) -> {'M', [1605,1580]};
uts46_map(64582) -> {'M', [1605,1581]};
uts46_map(64583) -> {'M', [1605,1582]};
uts46_map(64584) -> {'M', [1605,1605]};
uts46_map(64585) -> {'M', [1605,1609]};
uts46_map(64586) -> {'M', [1605,1610]};
uts46_map(64587) -> {'M', [1606,1580]};
uts46_map(64588) -> {'M', [1606,1581]};
uts46_map(64589) -> {'M', [1606,1582]};
uts46_map(64590) -> {'M', [1606,1605]};
uts46_map(64591) -> {'M', [1606,1609]};
uts46_map(64592) -> {'M', [1606,1610]};
uts46_map(64593) -> {'M', [1607,1580]};
uts46_map(64594) -> {'M', [1607,1605]};
uts46_map(64595) -> {'M', [1607,1609]};
uts46_map(64596) -> {'M', [1607,1610]};
uts46_map(64597) -> {'M', [1610,1580]};
uts46_map(64598) -> {'M', [1610,1581]};
uts46_map(64599) -> {'M', [1610,1582]};
uts46_map(64600) -> {'M', [1610,1605]};
uts46_map(64601) -> {'M', [1610,1609]};
uts46_map(64602) -> {'M', [1610,1610]};
uts46_map(64603) -> {'M', [1584,1648]};
uts46_map(64604) -> {'M', [1585,1648]};
uts46_map(64605) -> {'M', [1609,1648]};
uts46_map(64606) -> {'3', [32,1612,1617]};
uts46_map(64607) -> {'3', [32,1613,1617]};
uts46_map(64608) -> {'3', [32,1614,1617]};
uts46_map(64609) -> {'3', [32,1615,1617]};
uts46_map(64610) -> {'3', [32,1616,1617]};
uts46_map(64611) -> {'3', [32,1617,1648]};
uts46_map(64612) -> {'M', [1574,1585]};
uts46_map(64613) -> {'M', [1574,1586]};
uts46_map(64614) -> {'M', [1574,1605]};
uts46_map(64615) -> {'M', [1574,1606]};
uts46_map(64616) -> {'M', [1574,1609]};
uts46_map(64617) -> {'M', [1574,1610]};
uts46_map(64618) -> {'M', [1576,1585]};
uts46_map(64619) -> {'M', [1576,1586]};
uts46_map(64620) -> {'M', [1576,1605]};
uts46_map(64621) -> {'M', [1576,1606]};
uts46_map(64622) -> {'M', [1576,1609]};
uts46_map(64623) -> {'M', [1576,1610]};
uts46_map(64624) -> {'M', [1578,1585]};
uts46_map(64625) -> {'M', [1578,1586]};
uts46_map(64626) -> {'M', [1578,1605]};
uts46_map(64627) -> {'M', [1578,1606]};
uts46_map(64628) -> {'M', [1578,1609]};
uts46_map(64629) -> {'M', [1578,1610]};
uts46_map(64630) -> {'M', [1579,1585]};
uts46_map(64631) -> {'M', [1579,1586]};
uts46_map(64632) -> {'M', [1579,1605]};
uts46_map(64633) -> {'M', [1579,1606]};
uts46_map(64634) -> {'M', [1579,1609]};
uts46_map(64635) -> {'M', [1579,1610]};
uts46_map(64636) -> {'M', [1601,1609]};
uts46_map(64637) -> {'M', [1601,1610]};
uts46_map(64638) -> {'M', [1602,1609]};
uts46_map(64639) -> {'M', [1602,1610]};
uts46_map(64640) -> {'M', [1603,1575]};
uts46_map(64641) -> {'M', [1603,1604]};
uts46_map(64642) -> {'M', [1603,1605]};
uts46_map(64643) -> {'M', [1603,1609]};
uts46_map(64644) -> {'M', [1603,1610]};
uts46_map(64645) -> {'M', [1604,1605]};
uts46_map(64646) -> {'M', [1604,1609]};
uts46_map(64647) -> {'M', [1604,1610]};
uts46_map(64648) -> {'M', [1605,1575]};
uts46_map(64649) -> {'M', [1605,1605]};
uts46_map(64650) -> {'M', [1606,1585]};
uts46_map(64651) -> {'M', [1606,1586]};
uts46_map(64652) -> {'M', [1606,1605]};
uts46_map(64653) -> {'M', [1606,1606]};
uts46_map(64654) -> {'M', [1606,1609]};
uts46_map(64655) -> {'M', [1606,1610]};
uts46_map(64656) -> {'M', [1609,1648]};
uts46_map(64657) -> {'M', [1610,1585]};
uts46_map(64658) -> {'M', [1610,1586]};
uts46_map(64659) -> {'M', [1610,1605]};
uts46_map(64660) -> {'M', [1610,1606]};
uts46_map(64661) -> {'M', [1610,1609]};
uts46_map(64662) -> {'M', [1610,1610]};
uts46_map(64663) -> {'M', [1574,1580]};
uts46_map(64664) -> {'M', [1574,1581]};
uts46_map(64665) -> {'M', [1574,1582]};
uts46_map(64666) -> {'M', [1574,1605]};
uts46_map(64667) -> {'M', [1574,1607]};
uts46_map(64668) -> {'M', [1576,1580]};
uts46_map(64669) -> {'M', [1576,1581]};
uts46_map(64670) -> {'M', [1576,1582]};
uts46_map(64671) -> {'M', [1576,1605]};
uts46_map(64672) -> {'M', [1576,1607]};
uts46_map(64673) -> {'M', [1578,1580]};
uts46_map(64674) -> {'M', [1578,1581]};
uts46_map(64675) -> {'M', [1578,1582]};
uts46_map(64676) -> {'M', [1578,1605]};
uts46_map(64677) -> {'M', [1578,1607]};
uts46_map(64678) -> {'M', [1579,1605]};
uts46_map(64679) -> {'M', [1580,1581]};
uts46_map(64680) -> {'M', [1580,1605]};
uts46_map(64681) -> {'M', [1581,1580]};
uts46_map(64682) -> {'M', [1581,1605]};
uts46_map(64683) -> {'M', [1582,1580]};
uts46_map(64684) -> {'M', [1582,1605]};
uts46_map(64685) -> {'M', [1587,1580]};
uts46_map(64686) -> {'M', [1587,1581]};
uts46_map(64687) -> {'M', [1587,1582]};
uts46_map(64688) -> {'M', [1587,1605]};
uts46_map(64689) -> {'M', [1589,1581]};
uts46_map(64690) -> {'M', [1589,1582]};
uts46_map(64691) -> {'M', [1589,1605]};
uts46_map(64692) -> {'M', [1590,1580]};
uts46_map(64693) -> {'M', [1590,1581]};
uts46_map(64694) -> {'M', [1590,1582]};
uts46_map(64695) -> {'M', [1590,1605]};
uts46_map(64696) -> {'M', [1591,1581]};
uts46_map(64697) -> {'M', [1592,1605]};
uts46_map(64698) -> {'M', [1593,1580]};
uts46_map(64699) -> {'M', [1593,1605]};
uts46_map(64700) -> {'M', [1594,1580]};
uts46_map(64701) -> {'M', [1594,1605]};
uts46_map(64702) -> {'M', [1601,1580]};
uts46_map(64703) -> {'M', [1601,1581]};
uts46_map(64704) -> {'M', [1601,1582]};
uts46_map(64705) -> {'M', [1601,1605]};
uts46_map(64706) -> {'M', [1602,1581]};
uts46_map(64707) -> {'M', [1602,1605]};
uts46_map(64708) -> {'M', [1603,1580]};
uts46_map(64709) -> {'M', [1603,1581]};
uts46_map(64710) -> {'M', [1603,1582]};
uts46_map(64711) -> {'M', [1603,1604]};
uts46_map(64712) -> {'M', [1603,1605]};
uts46_map(64713) -> {'M', [1604,1580]};
uts46_map(64714) -> {'M', [1604,1581]};
uts46_map(64715) -> {'M', [1604,1582]};
uts46_map(64716) -> {'M', [1604,1605]};
uts46_map(64717) -> {'M', [1604,1607]};
uts46_map(64718) -> {'M', [1605,1580]};
uts46_map(64719) -> {'M', [1605,1581]};
uts46_map(64720) -> {'M', [1605,1582]};
uts46_map(64721) -> {'M', [1605,1605]};
uts46_map(64722) -> {'M', [1606,1580]};
uts46_map(64723) -> {'M', [1606,1581]};
uts46_map(64724) -> {'M', [1606,1582]};
uts46_map(64725) -> {'M', [1606,1605]};
uts46_map(64726) -> {'M', [1606,1607]};
uts46_map(64727) -> {'M', [1607,1580]};
uts46_map(64728) -> {'M', [1607,1605]};
uts46_map(64729) -> {'M', [1607,1648]};
uts46_map(64730) -> {'M', [1610,1580]};
uts46_map(64731) -> {'M', [1610,1581]};
uts46_map(64732) -> {'M', [1610,1582]};
uts46_map(64733) -> {'M', [1610,1605]};
uts46_map(64734) -> {'M', [1610,1607]};
uts46_map(64735) -> {'M', [1574,1605]};
uts46_map(64736) -> {'M', [1574,1607]};
uts46_map(64737) -> {'M', [1576,1605]};
uts46_map(64738) -> {'M', [1576,1607]};
uts46_map(64739) -> {'M', [1578,1605]};
uts46_map(64740) -> {'M', [1578,1607]};
uts46_map(64741) -> {'M', [1579,1605]};
uts46_map(64742) -> {'M', [1579,1607]};
uts46_map(64743) -> {'M', [1587,1605]};
uts46_map(64744) -> {'M', [1587,1607]};
uts46_map(64745) -> {'M', [1588,1605]};
uts46_map(64746) -> {'M', [1588,1607]};
uts46_map(64747) -> {'M', [1603,1604]};
uts46_map(64748) -> {'M', [1603,1605]};
uts46_map(64749) -> {'M', [1604,1605]};
uts46_map(64750) -> {'M', [1606,1605]};
uts46_map(64751) -> {'M', [1606,1607]};
uts46_map(64752) -> {'M', [1610,1605]};
uts46_map(64753) -> {'M', [1610,1607]};
uts46_map(64754) -> {'M', [1600,1614,1617]};
uts46_map(64755) -> {'M', [1600,1615,1617]};
uts46_map(64756) -> {'M', [1600,1616,1617]};
uts46_map(64757) -> {'M', [1591,1609]};
uts46_map(64758) -> {'M', [1591,1610]};
uts46_map(64759) -> {'M', [1593,1609]};
uts46_map(64760) -> {'M', [1593,1610]};
uts46_map(64761) -> {'M', [1594,1609]};
uts46_map(64762) -> {'M', [1594,1610]};
uts46_map(64763) -> {'M', [1587,1609]};
uts46_map(64764) -> {'M', [1587,1610]};
uts46_map(64765) -> {'M', [1588,1609]};
uts46_map(64766) -> {'M', [1588,1610]};
uts46_map(64767) -> {'M', [1581,1609]};
uts46_map(64768) -> {'M', [1581,1610]};
uts46_map(64769) -> {'M', [1580,1609]};
uts46_map(64770) -> {'M', [1580,1610]};
uts46_map(64771) -> {'M', [1582,1609]};
uts46_map(64772) -> {'M', [1582,1610]};
uts46_map(64773) -> {'M', [1589,1609]};
uts46_map(64774) -> {'M', [1589,1610]};
uts46_map(64775) -> {'M', [1590,1609]};
uts46_map(64776) -> {'M', [1590,1610]};
uts46_map(64777) -> {'M', [1588,1580]};
uts46_map(64778) -> {'M', [1588,1581]};
uts46_map(64779) -> {'M', [1588,1582]};
uts46_map(64780) -> {'M', [1588,1605]};
uts46_map(64781) -> {'M', [1588,1585]};
uts46_map(64782) -> {'M', [1587,1585]};
uts46_map(64783) -> {'M', [1589,1585]};
uts46_map(64784) -> {'M', [1590,1585]};
uts46_map(64785) -> {'M', [1591,1609]};
uts46_map(64786) -> {'M', [1591,1610]};
uts46_map(64787) -> {'M', [1593,1609]};
uts46_map(64788) -> {'M', [1593,1610]};
uts46_map(64789) -> {'M', [1594,1609]};
uts46_map(64790) -> {'M', [1594,1610]};
uts46_map(64791) -> {'M', [1587,1609]};
uts46_map(64792) -> {'M', [1587,1610]};
uts46_map(64793) -> {'M', [1588,1609]};
uts46_map(64794) -> {'M', [1588,1610]};
uts46_map(64795) -> {'M', [1581,1609]};
uts46_map(64796) -> {'M', [1581,1610]};
uts46_map(64797) -> {'M', [1580,1609]};
uts46_map(64798) -> {'M', [1580,1610]};
uts46_map(64799) -> {'M', [1582,1609]};
uts46_map(64800) -> {'M', [1582,1610]};
uts46_map(64801) -> {'M', [1589,1609]};
uts46_map(64802) -> {'M', [1589,1610]};
uts46_map(64803) -> {'M', [1590,1609]};
uts46_map(64804) -> {'M', [1590,1610]};
uts46_map(64805) -> {'M', [1588,1580]};
uts46_map(64806) -> {'M', [1588,1581]};
uts46_map(64807) -> {'M', [1588,1582]};
uts46_map(64808) -> {'M', [1588,1605]};
uts46_map(64809) -> {'M', [1588,1585]};
uts46_map(64810) -> {'M', [1587,1585]};
uts46_map(64811) -> {'M', [1589,1585]};
uts46_map(64812) -> {'M', [1590,1585]};
uts46_map(64813) -> {'M', [1588,1580]};
uts46_map(64814) -> {'M', [1588,1581]};
uts46_map(64815) -> {'M', [1588,1582]};
uts46_map(64816) -> {'M', [1588,1605]};
uts46_map(64817) -> {'M', [1587,1607]};
uts46_map(64818) -> {'M', [1588,1607]};
uts46_map(64819) -> {'M', [1591,1605]};
uts46_map(64820) -> {'M', [1587,1580]};
uts46_map(64821) -> {'M', [1587,1581]};
uts46_map(64822) -> {'M', [1587,1582]};
uts46_map(64823) -> {'M', [1588,1580]};
uts46_map(64824) -> {'M', [1588,1581]};
uts46_map(64825) -> {'M', [1588,1582]};
uts46_map(64826) -> {'M', [1591,1605]};
uts46_map(64827) -> {'M', [1592,1605]};
uts46_map(64848) -> {'M', [1578,1580,1605]};
uts46_map(64851) -> {'M', [1578,1581,1605]};
uts46_map(64852) -> {'M', [1578,1582,1605]};
uts46_map(64853) -> {'M', [1578,1605,1580]};
uts46_map(64854) -> {'M', [1578,1605,1581]};
uts46_map(64855) -> {'M', [1578,1605,1582]};
uts46_map(64858) -> {'M', [1581,1605,1610]};
uts46_map(64859) -> {'M', [1581,1605,1609]};
uts46_map(64860) -> {'M', [1587,1581,1580]};
uts46_map(64861) -> {'M', [1587,1580,1581]};
uts46_map(64862) -> {'M', [1587,1580,1609]};
uts46_map(64865) -> {'M', [1587,1605,1580]};
uts46_map(64870) -> {'M', [1589,1605,1605]};
uts46_map(64873) -> {'M', [1588,1580,1610]};
uts46_map(64878) -> {'M', [1590,1581,1609]};
uts46_map(64883) -> {'M', [1591,1605,1605]};
uts46_map(64884) -> {'M', [1591,1605,1610]};
uts46_map(64885) -> {'M', [1593,1580,1605]};
uts46_map(64888) -> {'M', [1593,1605,1609]};
uts46_map(64889) -> {'M', [1594,1605,1605]};
uts46_map(64890) -> {'M', [1594,1605,1610]};
uts46_map(64891) -> {'M', [1594,1605,1609]};
uts46_map(64894) -> {'M', [1602,1605,1581]};
uts46_map(64895) -> {'M', [1602,1605,1605]};
uts46_map(64896) -> {'M', [1604,1581,1605]};
uts46_map(64897) -> {'M', [1604,1581,1610]};
uts46_map(64898) -> {'M', [1604,1581,1609]};
uts46_map(64905) -> {'M', [1605,1581,1580]};
uts46_map(64906) -> {'M', [1605,1581,1605]};
uts46_map(64907) -> {'M', [1605,1581,1610]};
uts46_map(64908) -> {'M', [1605,1580,1581]};
uts46_map(64909) -> {'M', [1605,1580,1605]};
uts46_map(64910) -> {'M', [1605,1582,1580]};
uts46_map(64911) -> {'M', [1605,1582,1605]};
uts46_map(64914) -> {'M', [1605,1580,1582]};
uts46_map(64915) -> {'M', [1607,1605,1580]};
uts46_map(64916) -> {'M', [1607,1605,1605]};
uts46_map(64917) -> {'M', [1606,1581,1605]};
uts46_map(64918) -> {'M', [1606,1581,1609]};
uts46_map(64921) -> {'M', [1606,1580,1609]};
uts46_map(64922) -> {'M', [1606,1605,1610]};
uts46_map(64923) -> {'M', [1606,1605,1609]};
uts46_map(64926) -> {'M', [1576,1582,1610]};
uts46_map(64927) -> {'M', [1578,1580,1610]};
uts46_map(64928) -> {'M', [1578,1580,1609]};
uts46_map(64929) -> {'M', [1578,1582,1610]};
uts46_map(64930) -> {'M', [1578,1582,1609]};
uts46_map(64931) -> {'M', [1578,1605,1610]};
uts46_map(64932) -> {'M', [1578,1605,1609]};
uts46_map(64933) -> {'M', [1580,1605,1610]};
uts46_map(64934) -> {'M', [1580,1581,1609]};
uts46_map(64935) -> {'M', [1580,1605,1609]};
uts46_map(64936) -> {'M', [1587,1582,1609]};
uts46_map(64937) -> {'M', [1589,1581,1610]};
uts46_map(64938) -> {'M', [1588,1581,1610]};
uts46_map(64939) -> {'M', [1590,1581,1610]};
uts46_map(64940) -> {'M', [1604,1580,1610]};
uts46_map(64941) -> {'M', [1604,1605,1610]};
uts46_map(64942) -> {'M', [1610,1581,1610]};
uts46_map(64943) -> {'M', [1610,1580,1610]};
uts46_map(64944) -> {'M', [1610,1605,1610]};
uts46_map(64945) -> {'M', [1605,1605,1610]};
uts46_map(64946) -> {'M', [1602,1605,1610]};
uts46_map(64947) -> {'M', [1606,1581,1610]};
uts46_map(64948) -> {'M', [1602,1605,1581]};
uts46_map(64949) -> {'M', [1604,1581,1605]};
uts46_map(64950) -> {'M', [1593,1605,1610]};
uts46_map(64951) -> {'M', [1603,1605,1610]};
uts46_map(64952) -> {'M', [1606,1580,1581]};
uts46_map(64953) -> {'M', [1605,1582,1610]};
uts46_map(64954) -> {'M', [1604,1580,1605]};
uts46_map(64955) -> {'M', [1603,1605,1605]};
uts46_map(64956) -> {'M', [1604,1580,1605]};
uts46_map(64957) -> {'M', [1606,1580,1581]};
uts46_map(64958) -> {'M', [1580,1581,1610]};
uts46_map(64959) -> {'M', [1581,1580,1610]};
uts46_map(64960) -> {'M', [1605,1580,1610]};
uts46_map(64961) -> {'M', [1601,1605,1610]};
uts46_map(64962) -> {'M', [1576,1581,1610]};
uts46_map(64963) -> {'M', [1603,1605,1605]};
uts46_map(64964) -> {'M', [1593,1580,1605]};
uts46_map(64965) -> {'M', [1589,1605,1605]};
uts46_map(64966) -> {'M', [1587,1582,1610]};
uts46_map(64967) -> {'M', [1606,1580,1610]};
uts46_map(65008) -> {'M', [1589,1604,1746]};
uts46_map(65009) -> {'M', [1602,1604,1746]};
uts46_map(65010) -> {'M', [1575,1604,1604,1607]};
uts46_map(65011) -> {'M', [1575,1603,1576,1585]};
uts46_map(65012) -> {'M', [1605,1581,1605,1583]};
uts46_map(65013) -> {'M', [1589,1604,1593,1605]};
uts46_map(65014) -> {'M', [1585,1587,1608,1604]};
uts46_map(65015) -> {'M', [1593,1604,1610,1607]};
uts46_map(65016) -> {'M', [1608,1587,1604,1605]};
uts46_map(65017) -> {'M', [1589,1604,1609]};
uts46_map(65018) -> {'3', [1589,1604,1609,32,1575,1604,1604,1607,32,1593,1604,1610,1607,32,1608,1587,1604,1605]};
uts46_map(65019) -> {'3', [1580,1604,32,1580,1604,1575,1604,1607]};
uts46_map(65020) -> {'M', [1585,1740,1575,1604]};
uts46_map(65021) -> 'V';
uts46_map(65040) -> {'3', [44]};
uts46_map(65041) -> {'M', [12289]};
uts46_map(65042) -> 'X';
uts46_map(65043) -> {'3', [58]};
uts46_map(65044) -> {'3', [59]};
uts46_map(65045) -> {'3', [33]};
uts46_map(65046) -> {'3', [63]};
uts46_map(65047) -> {'M', [12310]};
uts46_map(65048) -> {'M', [12311]};
uts46_map(65049) -> 'X';
uts46_map(65072) -> 'X';
uts46_map(65073) -> {'M', [8212]};
uts46_map(65074) -> {'M', [8211]};
uts46_map(65077) -> {'3', [40]};
uts46_map(65078) -> {'3', [41]};
uts46_map(65079) -> {'3', [123]};
uts46_map(65080) -> {'3', [125]};
uts46_map(65081) -> {'M', [12308]};
uts46_map(65082) -> {'M', [12309]};
uts46_map(65083) -> {'M', [12304]};
uts46_map(65084) -> {'M', [12305]};
uts46_map(65085) -> {'M', [12298]};
uts46_map(65086) -> {'M', [12299]};
uts46_map(65087) -> {'M', [12296]};
uts46_map(65088) -> {'M', [12297]};
uts46_map(65089) -> {'M', [12300]};
uts46_map(65090) -> {'M', [12301]};
uts46_map(65091) -> {'M', [12302]};
uts46_map(65092) -> {'M', [12303]};
uts46_map(65095) -> {'3', [91]};
uts46_map(65096) -> {'3', [93]};
uts46_map(65104) -> {'3', [44]};
uts46_map(65105) -> {'M', [12289]};
uts46_map(65106) -> 'X';
uts46_map(65107) -> 'X';
uts46_map(65108) -> {'3', [59]};
uts46_map(65109) -> {'3', [58]};
uts46_map(65110) -> {'3', [63]};
uts46_map(65111) -> {'3', [33]};
uts46_map(65112) -> {'M', [8212]};
uts46_map(65113) -> {'3', [40]};
uts46_map(65114) -> {'3', [41]};
uts46_map(65115) -> {'3', [123]};
uts46_map(65116) -> {'3', [125]};
uts46_map(65117) -> {'M', [12308]};
uts46_map(65118) -> {'M', [12309]};
uts46_map(65119) -> {'3', [35]};
uts46_map(65120) -> {'3', [38]};
uts46_map(65121) -> {'3', [42]};
uts46_map(65122) -> {'3', [43]};
uts46_map(65123) -> {'M', [45]};
uts46_map(65124) -> {'3', [60]};
uts46_map(65125) -> {'3', [62]};
uts46_map(65126) -> {'3', [61]};
uts46_map(65127) -> 'X';
uts46_map(65128) -> {'3', [92]};
uts46_map(65129) -> {'3', [36]};
uts46_map(65130) -> {'3', [37]};
uts46_map(65131) -> {'3', [64]};
uts46_map(65136) -> {'3', [32,1611]};
uts46_map(65137) -> {'M', [1600,1611]};
uts46_map(65138) -> {'3', [32,1612]};
uts46_map(65139) -> 'V';
uts46_map(65140) -> {'3', [32,1613]};
uts46_map(65141) -> 'X';
uts46_map(65142) -> {'3', [32,1614]};
uts46_map(65143) -> {'M', [1600,1614]};
uts46_map(65144) -> {'3', [32,1615]};
uts46_map(65145) -> {'M', [1600,1615]};
uts46_map(65146) -> {'3', [32,1616]};
uts46_map(65147) -> {'M', [1600,1616]};
uts46_map(65148) -> {'3', [32,1617]};
uts46_map(65149) -> {'M', [1600,1617]};
uts46_map(65150) -> {'3', [32,1618]};
uts46_map(65151) -> {'M', [1600,1618]};
uts46_map(65152) -> {'M', [1569]};
uts46_map(65279) -> 'I';
uts46_map(65280) -> 'X';
uts46_map(65281) -> {'3', [33]};
uts46_map(65282) -> {'3', [34]};
uts46_map(65283) -> {'3', [35]};
uts46_map(65284) -> {'3', [36]};
uts46_map(65285) -> {'3', [37]};
uts46_map(65286) -> {'3', [38]};
uts46_map(65287) -> {'3', [39]};
uts46_map(65288) -> {'3', [40]};
uts46_map(65289) -> {'3', [41]};
uts46_map(65290) -> {'3', [42]};
uts46_map(65291) -> {'3', [43]};
uts46_map(65292) -> {'3', [44]};
uts46_map(65293) -> {'M', [45]};
uts46_map(65294) -> {'M', [46]};
uts46_map(65295) -> {'3', [47]};
uts46_map(65296) -> {'M', [48]};
uts46_map(65297) -> {'M', [49]};
uts46_map(65298) -> {'M', [50]};
uts46_map(65299) -> {'M', [51]};
uts46_map(65300) -> {'M', [52]};
uts46_map(65301) -> {'M', [53]};
uts46_map(65302) -> {'M', [54]};
uts46_map(65303) -> {'M', [55]};
uts46_map(65304) -> {'M', [56]};
uts46_map(65305) -> {'M', [57]};
uts46_map(65306) -> {'3', [58]};
uts46_map(65307) -> {'3', [59]};
uts46_map(65308) -> {'3', [60]};
uts46_map(65309) -> {'3', [61]};
uts46_map(65310) -> {'3', [62]};
uts46_map(65311) -> {'3', [63]};
uts46_map(65312) -> {'3', [64]};
uts46_map(65313) -> {'M', [97]};
uts46_map(65314) -> {'M', [98]};
uts46_map(65315) -> {'M', [99]};
uts46_map(65316) -> {'M', [100]};
uts46_map(65317) -> {'M', [101]};
uts46_map(65318) -> {'M', [102]};
uts46_map(65319) -> {'M', [103]};
uts46_map(65320) -> {'M', [104]};
uts46_map(65321) -> {'M', [105]};
uts46_map(65322) -> {'M', [106]};
uts46_map(65323) -> {'M', [107]};
uts46_map(65324) -> {'M', [108]};
uts46_map(65325) -> {'M', [109]};
uts46_map(65326) -> {'M', [110]};
uts46_map(65327) -> {'M', [111]};
uts46_map(65328) -> {'M', [112]};
uts46_map(65329) -> {'M', [113]};
uts46_map(65330) -> {'M', [114]};
uts46_map(65331) -> {'M', [115]};
uts46_map(65332) -> {'M', [116]};
uts46_map(65333) -> {'M', [117]};
uts46_map(65334) -> {'M', [118]};
uts46_map(65335) -> {'M', [119]};
uts46_map(65336) -> {'M', [120]};
uts46_map(65337) -> {'M', [121]};
uts46_map(65338) -> {'M', [122]};
uts46_map(65339) -> {'3', [91]};
uts46_map(65340) -> {'3', [92]};
uts46_map(65341) -> {'3', [93]};
uts46_map(65342) -> {'3', [94]};
uts46_map(65343) -> {'3', [95]};
uts46_map(65344) -> {'3', [96]};
uts46_map(65345) -> {'M', [97]};
uts46_map(65346) -> {'M', [98]};
uts46_map(65347) -> {'M', [99]};
uts46_map(65348) -> {'M', [100]};
uts46_map(65349) -> {'M', [101]};
uts46_map(65350) -> {'M', [102]};
uts46_map(65351) -> {'M', [103]};
uts46_map(65352) -> {'M', [104]};
uts46_map(65353) -> {'M', [105]};
uts46_map(65354) -> {'M', [106]};
uts46_map(65355) -> {'M', [107]};
uts46_map(65356) -> {'M', [108]};
uts46_map(65357) -> {'M', [109]};
uts46_map(65358) -> {'M', [110]};
uts46_map(65359) -> {'M', [111]};
uts46_map(65360) -> {'M', [112]};
uts46_map(65361) -> {'M', [113]};
uts46_map(65362) -> {'M', [114]};
uts46_map(65363) -> {'M', [115]};
uts46_map(65364) -> {'M', [116]};
uts46_map(65365) -> {'M', [117]};
uts46_map(65366) -> {'M', [118]};
uts46_map(65367) -> {'M', [119]};
uts46_map(65368) -> {'M', [120]};
uts46_map(65369) -> {'M', [121]};
uts46_map(65370) -> {'M', [122]};
uts46_map(65371) -> {'3', [123]};
uts46_map(65372) -> {'3', [124]};
uts46_map(65373) -> {'3', [125]};
uts46_map(65374) -> {'3', [126]};
uts46_map(65375) -> {'M', [10629]};
uts46_map(65376) -> {'M', [10630]};
uts46_map(65377) -> {'M', [46]};
uts46_map(65378) -> {'M', [12300]};
uts46_map(65379) -> {'M', [12301]};
uts46_map(65380) -> {'M', [12289]};
uts46_map(65381) -> {'M', [12539]};
uts46_map(65382) -> {'M', [12530]};
uts46_map(65383) -> {'M', [12449]};
uts46_map(65384) -> {'M', [12451]};
uts46_map(65385) -> {'M', [12453]};
uts46_map(65386) -> {'M', [12455]};
uts46_map(65387) -> {'M', [12457]};
uts46_map(65388) -> {'M', [12515]};
uts46_map(65389) -> {'M', [12517]};
uts46_map(65390) -> {'M', [12519]};
uts46_map(65391) -> {'M', [12483]};
uts46_map(65392) -> {'M', [12540]};
uts46_map(65393) -> {'M', [12450]};
uts46_map(65394) -> {'M', [12452]};
uts46_map(65395) -> {'M', [12454]};
uts46_map(65396) -> {'M', [12456]};
uts46_map(65397) -> {'M', [12458]};
uts46_map(65398) -> {'M', [12459]};
uts46_map(65399) -> {'M', [12461]};
uts46_map(65400) -> {'M', [12463]};
uts46_map(65401) -> {'M', [12465]};
uts46_map(65402) -> {'M', [12467]};
uts46_map(65403) -> {'M', [12469]};
uts46_map(65404) -> {'M', [12471]};
uts46_map(65405) -> {'M', [12473]};
uts46_map(65406) -> {'M', [12475]};
uts46_map(65407) -> {'M', [12477]};
uts46_map(65408) -> {'M', [12479]};
uts46_map(65409) -> {'M', [12481]};
uts46_map(65410) -> {'M', [12484]};
uts46_map(65411) -> {'M', [12486]};
uts46_map(65412) -> {'M', [12488]};
uts46_map(65413) -> {'M', [12490]};
uts46_map(65414) -> {'M', [12491]};
uts46_map(65415) -> {'M', [12492]};
uts46_map(65416) -> {'M', [12493]};
uts46_map(65417) -> {'M', [12494]};
uts46_map(65418) -> {'M', [12495]};
uts46_map(65419) -> {'M', [12498]};
uts46_map(65420) -> {'M', [12501]};
uts46_map(65421) -> {'M', [12504]};
uts46_map(65422) -> {'M', [12507]};
uts46_map(65423) -> {'M', [12510]};
uts46_map(65424) -> {'M', [12511]};
uts46_map(65425) -> {'M', [12512]};
uts46_map(65426) -> {'M', [12513]};
uts46_map(65427) -> {'M', [12514]};
uts46_map(65428) -> {'M', [12516]};
uts46_map(65429) -> {'M', [12518]};
uts46_map(65430) -> {'M', [12520]};
uts46_map(65431) -> {'M', [12521]};
uts46_map(65432) -> {'M', [12522]};
uts46_map(65433) -> {'M', [12523]};
uts46_map(65434) -> {'M', [12524]};
uts46_map(65435) -> {'M', [12525]};
uts46_map(65436) -> {'M', [12527]};
uts46_map(65437) -> {'M', [12531]};
uts46_map(65438) -> {'M', [12441]};
uts46_map(65439) -> {'M', [12442]};
uts46_map(65440) -> 'X';
uts46_map(65441) -> {'M', [4352]};
uts46_map(65442) -> {'M', [4353]};
uts46_map(65443) -> {'M', [4522]};
uts46_map(65444) -> {'M', [4354]};
uts46_map(65445) -> {'M', [4524]};
uts46_map(65446) -> {'M', [4525]};
uts46_map(65447) -> {'M', [4355]};
uts46_map(65448) -> {'M', [4356]};
uts46_map(65449) -> {'M', [4357]};
uts46_map(65450) -> {'M', [4528]};
uts46_map(65451) -> {'M', [4529]};
uts46_map(65452) -> {'M', [4530]};
uts46_map(65453) -> {'M', [4531]};
uts46_map(65454) -> {'M', [4532]};
uts46_map(65455) -> {'M', [4533]};
uts46_map(65456) -> {'M', [4378]};
uts46_map(65457) -> {'M', [4358]};
uts46_map(65458) -> {'M', [4359]};
uts46_map(65459) -> {'M', [4360]};
uts46_map(65460) -> {'M', [4385]};
uts46_map(65461) -> {'M', [4361]};
uts46_map(65462) -> {'M', [4362]};
uts46_map(65463) -> {'M', [4363]};
uts46_map(65464) -> {'M', [4364]};
uts46_map(65465) -> {'M', [4365]};
uts46_map(65466) -> {'M', [4366]};
uts46_map(65467) -> {'M', [4367]};
uts46_map(65468) -> {'M', [4368]};
uts46_map(65469) -> {'M', [4369]};
uts46_map(65470) -> {'M', [4370]};
uts46_map(65474) -> {'M', [4449]};
uts46_map(65475) -> {'M', [4450]};
uts46_map(65476) -> {'M', [4451]};
uts46_map(65477) -> {'M', [4452]};
uts46_map(65478) -> {'M', [4453]};
uts46_map(65479) -> {'M', [4454]};
uts46_map(65482) -> {'M', [4455]};
uts46_map(65483) -> {'M', [4456]};
uts46_map(65484) -> {'M', [4457]};
uts46_map(65485) -> {'M', [4458]};
uts46_map(65486) -> {'M', [4459]};
uts46_map(65487) -> {'M', [4460]};
uts46_map(65490) -> {'M', [4461]};
uts46_map(65491) -> {'M', [4462]};
uts46_map(65492) -> {'M', [4463]};
uts46_map(65493) -> {'M', [4464]};
uts46_map(65494) -> {'M', [4465]};
uts46_map(65495) -> {'M', [4466]};
uts46_map(65498) -> {'M', [4467]};
uts46_map(65499) -> {'M', [4468]};
uts46_map(65500) -> {'M', [4469]};
uts46_map(65504) -> {'M', [162]};
uts46_map(65505) -> {'M', [163]};
uts46_map(65506) -> {'M', [172]};
uts46_map(65507) -> {'3', [32,772]};
uts46_map(65508) -> {'M', [166]};
uts46_map(65509) -> {'M', [165]};
uts46_map(65510) -> {'M', [8361]};
uts46_map(65511) -> 'X';
uts46_map(65512) -> {'M', [9474]};
uts46_map(65513) -> {'M', [8592]};
uts46_map(65514) -> {'M', [8593]};
uts46_map(65515) -> {'M', [8594]};
uts46_map(65516) -> {'M', [8595]};
uts46_map(65517) -> {'M', [9632]};
uts46_map(65518) -> {'M', [9675]};
uts46_map(65532) -> 'X';
uts46_map(65533) -> 'X';
uts46_map(65548) -> 'X';
uts46_map(65575) -> 'X';
uts46_map(65595) -> 'X';
uts46_map(65598) -> 'X';
uts46_map(65935) -> 'X';
uts46_map(65948) -> 'V';
uts46_map(65952) -> 'V';
uts46_map(66045) -> 'V';
uts46_map(66272) -> 'V';
uts46_map(66335) -> 'V';
uts46_map(66369) -> 'V';
uts46_map(66378) -> 'V';
uts46_map(66462) -> 'X';
uts46_map(66463) -> 'V';
uts46_map(66560) -> {'M', [66600]};
uts46_map(66561) -> {'M', [66601]};
uts46_map(66562) -> {'M', [66602]};
uts46_map(66563) -> {'M', [66603]};
uts46_map(66564) -> {'M', [66604]};
uts46_map(66565) -> {'M', [66605]};
uts46_map(66566) -> {'M', [66606]};
uts46_map(66567) -> {'M', [66607]};
uts46_map(66568) -> {'M', [66608]};
uts46_map(66569) -> {'M', [66609]};
uts46_map(66570) -> {'M', [66610]};
uts46_map(66571) -> {'M', [66611]};
uts46_map(66572) -> {'M', [66612]};
uts46_map(66573) -> {'M', [66613]};
uts46_map(66574) -> {'M', [66614]};
uts46_map(66575) -> {'M', [66615]};
uts46_map(66576) -> {'M', [66616]};
uts46_map(66577) -> {'M', [66617]};
uts46_map(66578) -> {'M', [66618]};
uts46_map(66579) -> {'M', [66619]};
uts46_map(66580) -> {'M', [66620]};
uts46_map(66581) -> {'M', [66621]};
uts46_map(66582) -> {'M', [66622]};
uts46_map(66583) -> {'M', [66623]};
uts46_map(66584) -> {'M', [66624]};
uts46_map(66585) -> {'M', [66625]};
uts46_map(66586) -> {'M', [66626]};
uts46_map(66587) -> {'M', [66627]};
uts46_map(66588) -> {'M', [66628]};
uts46_map(66589) -> {'M', [66629]};
uts46_map(66590) -> {'M', [66630]};
uts46_map(66591) -> {'M', [66631]};
uts46_map(66592) -> {'M', [66632]};
uts46_map(66593) -> {'M', [66633]};
uts46_map(66594) -> {'M', [66634]};
uts46_map(66595) -> {'M', [66635]};
uts46_map(66596) -> {'M', [66636]};
uts46_map(66597) -> {'M', [66637]};
uts46_map(66598) -> {'M', [66638]};
uts46_map(66599) -> {'M', [66639]};
uts46_map(66736) -> {'M', [66776]};
uts46_map(66737) -> {'M', [66777]};
uts46_map(66738) -> {'M', [66778]};
uts46_map(66739) -> {'M', [66779]};
uts46_map(66740) -> {'M', [66780]};
uts46_map(66741) -> {'M', [66781]};
uts46_map(66742) -> {'M', [66782]};
uts46_map(66743) -> {'M', [66783]};
uts46_map(66744) -> {'M', [66784]};
uts46_map(66745) -> {'M', [66785]};
uts46_map(66746) -> {'M', [66786]};
uts46_map(66747) -> {'M', [66787]};
uts46_map(66748) -> {'M', [66788]};
uts46_map(66749) -> {'M', [66789]};
uts46_map(66750) -> {'M', [66790]};
uts46_map(66751) -> {'M', [66791]};
uts46_map(66752) -> {'M', [66792]};
uts46_map(66753) -> {'M', [66793]};
uts46_map(66754) -> {'M', [66794]};
uts46_map(66755) -> {'M', [66795]};
uts46_map(66756) -> {'M', [66796]};
uts46_map(66757) -> {'M', [66797]};
uts46_map(66758) -> {'M', [66798]};
uts46_map(66759) -> {'M', [66799]};
uts46_map(66760) -> {'M', [66800]};
uts46_map(66761) -> {'M', [66801]};
uts46_map(66762) -> {'M', [66802]};
uts46_map(66763) -> {'M', [66803]};
uts46_map(66764) -> {'M', [66804]};
uts46_map(66765) -> {'M', [66805]};
uts46_map(66766) -> {'M', [66806]};
uts46_map(66767) -> {'M', [66807]};
uts46_map(66768) -> {'M', [66808]};
uts46_map(66769) -> {'M', [66809]};
uts46_map(66770) -> {'M', [66810]};
uts46_map(66771) -> {'M', [66811]};
uts46_map(66927) -> 'V';
uts46_map(67592) -> 'V';
uts46_map(67593) -> 'X';
uts46_map(67638) -> 'X';
uts46_map(67644) -> 'V';
uts46_map(67647) -> 'V';
uts46_map(67670) -> 'X';
uts46_map(67827) -> 'X';
uts46_map(67871) -> 'V';
uts46_map(67903) -> 'V';
uts46_map(68100) -> 'X';
uts46_map(68116) -> 'X';
uts46_map(68120) -> 'X';
uts46_map(68159) -> 'V';
uts46_map(68168) -> 'V';
uts46_map(68296) -> 'V';
uts46_map(68736) -> {'M', [68800]};
uts46_map(68737) -> {'M', [68801]};
uts46_map(68738) -> {'M', [68802]};
uts46_map(68739) -> {'M', [68803]};
uts46_map(68740) -> {'M', [68804]};
uts46_map(68741) -> {'M', [68805]};
uts46_map(68742) -> {'M', [68806]};
uts46_map(68743) -> {'M', [68807]};
uts46_map(68744) -> {'M', [68808]};
uts46_map(68745) -> {'M', [68809]};
uts46_map(68746) -> {'M', [68810]};
uts46_map(68747) -> {'M', [68811]};
uts46_map(68748) -> {'M', [68812]};
uts46_map(68749) -> {'M', [68813]};
uts46_map(68750) -> {'M', [68814]};
uts46_map(68751) -> {'M', [68815]};
uts46_map(68752) -> {'M', [68816]};
uts46_map(68753) -> {'M', [68817]};
uts46_map(68754) -> {'M', [68818]};
uts46_map(68755) -> {'M', [68819]};
uts46_map(68756) -> {'M', [68820]};
uts46_map(68757) -> {'M', [68821]};
uts46_map(68758) -> {'M', [68822]};
uts46_map(68759) -> {'M', [68823]};
uts46_map(68760) -> {'M', [68824]};
uts46_map(68761) -> {'M', [68825]};
uts46_map(68762) -> {'M', [68826]};
uts46_map(68763) -> {'M', [68827]};
uts46_map(68764) -> {'M', [68828]};
uts46_map(68765) -> {'M', [68829]};
uts46_map(68766) -> {'M', [68830]};
uts46_map(68767) -> {'M', [68831]};
uts46_map(68768) -> {'M', [68832]};
uts46_map(68769) -> {'M', [68833]};
uts46_map(68770) -> {'M', [68834]};
uts46_map(68771) -> {'M', [68835]};
uts46_map(68772) -> {'M', [68836]};
uts46_map(68773) -> {'M', [68837]};
uts46_map(68774) -> {'M', [68838]};
uts46_map(68775) -> {'M', [68839]};
uts46_map(68776) -> {'M', [68840]};
uts46_map(68777) -> {'M', [68841]};
uts46_map(68778) -> {'M', [68842]};
uts46_map(68779) -> {'M', [68843]};
uts46_map(68780) -> {'M', [68844]};
uts46_map(68781) -> {'M', [68845]};
uts46_map(68782) -> {'M', [68846]};
uts46_map(68783) -> {'M', [68847]};
uts46_map(68784) -> {'M', [68848]};
uts46_map(68785) -> {'M', [68849]};
uts46_map(68786) -> {'M', [68850]};
uts46_map(69247) -> 'X';
uts46_map(69290) -> 'X';
uts46_map(69293) -> 'V';
uts46_map(69415) -> 'V';
uts46_map(69759) -> 'V';
uts46_map(69821) -> 'X';
uts46_map(69837) -> 'X';
uts46_map(69941) -> 'X';
uts46_map(69959) -> 'V';
uts46_map(70006) -> 'V';
uts46_map(70093) -> 'V';
uts46_map(70106) -> 'V';
uts46_map(70107) -> 'V';
uts46_map(70108) -> 'V';
uts46_map(70112) -> 'X';
uts46_map(70162) -> 'X';
uts46_map(70206) -> 'V';
uts46_map(70279) -> 'X';
uts46_map(70280) -> 'V';
uts46_map(70281) -> 'X';
uts46_map(70286) -> 'X';
uts46_map(70302) -> 'X';
uts46_map(70313) -> 'V';
uts46_map(70400) -> 'V';
uts46_map(70404) -> 'X';
uts46_map(70441) -> 'X';
uts46_map(70449) -> 'X';
uts46_map(70452) -> 'X';
uts46_map(70458) -> 'X';
uts46_map(70459) -> 'V';
uts46_map(70480) -> 'V';
uts46_map(70487) -> 'V';
uts46_map(70746) -> 'V';
uts46_map(70747) -> 'V';
uts46_map(70748) -> 'X';
uts46_map(70749) -> 'V';
uts46_map(70750) -> 'V';
uts46_map(70751) -> 'V';
uts46_map(70854) -> 'V';
uts46_map(70855) -> 'V';
uts46_map(71236) -> 'V';
uts46_map(71352) -> 'V';
uts46_map(71450) -> 'V';
uts46_map(71739) -> 'V';
uts46_map(71840) -> {'M', [71872]};
uts46_map(71841) -> {'M', [71873]};
uts46_map(71842) -> {'M', [71874]};
uts46_map(71843) -> {'M', [71875]};
uts46_map(71844) -> {'M', [71876]};
uts46_map(71845) -> {'M', [71877]};
uts46_map(71846) -> {'M', [71878]};
uts46_map(71847) -> {'M', [71879]};
uts46_map(71848) -> {'M', [71880]};
uts46_map(71849) -> {'M', [71881]};
uts46_map(71850) -> {'M', [71882]};
uts46_map(71851) -> {'M', [71883]};
uts46_map(71852) -> {'M', [71884]};
uts46_map(71853) -> {'M', [71885]};
uts46_map(71854) -> {'M', [71886]};
uts46_map(71855) -> {'M', [71887]};
uts46_map(71856) -> {'M', [71888]};
uts46_map(71857) -> {'M', [71889]};
uts46_map(71858) -> {'M', [71890]};
uts46_map(71859) -> {'M', [71891]};
uts46_map(71860) -> {'M', [71892]};
uts46_map(71861) -> {'M', [71893]};
uts46_map(71862) -> {'M', [71894]};
uts46_map(71863) -> {'M', [71895]};
uts46_map(71864) -> {'M', [71896]};
uts46_map(71865) -> {'M', [71897]};
uts46_map(71866) -> {'M', [71898]};
uts46_map(71867) -> {'M', [71899]};
uts46_map(71868) -> {'M', [71900]};
uts46_map(71869) -> {'M', [71901]};
uts46_map(71870) -> {'M', [71902]};
uts46_map(71871) -> {'M', [71903]};
uts46_map(71935) -> 'V';
uts46_map(71945) -> 'V';
uts46_map(71956) -> 'X';
uts46_map(71959) -> 'X';
uts46_map(71990) -> 'X';
uts46_map(72162) -> 'V';
uts46_map(72263) -> 'V';
uts46_map(72349) -> 'V';
uts46_map(72713) -> 'X';
uts46_map(72759) -> 'X';
uts46_map(72872) -> 'X';
uts46_map(72967) -> 'X';
uts46_map(72970) -> 'X';
uts46_map(73018) -> 'V';
uts46_map(73019) -> 'X';
uts46_map(73022) -> 'X';
uts46_map(73062) -> 'X';
uts46_map(73065) -> 'X';
uts46_map(73103) -> 'X';
uts46_map(73106) -> 'X';
uts46_map(73648) -> 'V';
uts46_map(73727) -> 'V';
uts46_map(74649) -> 'V';
uts46_map(74863) -> 'X';
uts46_map(74868) -> 'V';
uts46_map(78895) -> 'X';
uts46_map(92767) -> 'X';
uts46_map(92917) -> 'V';
uts46_map(93018) -> 'X';
uts46_map(93026) -> 'X';
uts46_map(93760) -> {'M', [93792]};
uts46_map(93761) -> {'M', [93793]};
uts46_map(93762) -> {'M', [93794]};
uts46_map(93763) -> {'M', [93795]};
uts46_map(93764) -> {'M', [93796]};
uts46_map(93765) -> {'M', [93797]};
uts46_map(93766) -> {'M', [93798]};
uts46_map(93767) -> {'M', [93799]};
uts46_map(93768) -> {'M', [93800]};
uts46_map(93769) -> {'M', [93801]};
uts46_map(93770) -> {'M', [93802]};
uts46_map(93771) -> {'M', [93803]};
uts46_map(93772) -> {'M', [93804]};
uts46_map(93773) -> {'M', [93805]};
uts46_map(93774) -> {'M', [93806]};
uts46_map(93775) -> {'M', [93807]};
uts46_map(93776) -> {'M', [93808]};
uts46_map(93777) -> {'M', [93809]};
uts46_map(93778) -> {'M', [93810]};
uts46_map(93779) -> {'M', [93811]};
uts46_map(93780) -> {'M', [93812]};
uts46_map(93781) -> {'M', [93813]};
uts46_map(93782) -> {'M', [93814]};
uts46_map(93783) -> {'M', [93815]};
uts46_map(93784) -> {'M', [93816]};
uts46_map(93785) -> {'M', [93817]};
uts46_map(93786) -> {'M', [93818]};
uts46_map(93787) -> {'M', [93819]};
uts46_map(93788) -> {'M', [93820]};
uts46_map(93789) -> {'M', [93821]};
uts46_map(93790) -> {'M', [93822]};
uts46_map(93791) -> {'M', [93823]};
uts46_map(94031) -> 'V';
uts46_map(94176) -> 'V';
uts46_map(94177) -> 'V';
uts46_map(94178) -> 'V';
uts46_map(94179) -> 'V';
uts46_map(94180) -> 'V';
uts46_map(113820) -> 'V';
uts46_map(113823) -> 'V';
uts46_map(119081) -> 'V';
uts46_map(119134) -> {'M', [119127,119141]};
uts46_map(119135) -> {'M', [119128,119141]};
uts46_map(119136) -> {'M', [119128,119141,119150]};
uts46_map(119137) -> {'M', [119128,119141,119151]};
uts46_map(119138) -> {'M', [119128,119141,119152]};
uts46_map(119139) -> {'M', [119128,119141,119153]};
uts46_map(119140) -> {'M', [119128,119141,119154]};
uts46_map(119227) -> {'M', [119225,119141]};
uts46_map(119228) -> {'M', [119226,119141]};
uts46_map(119229) -> {'M', [119225,119141,119150]};
uts46_map(119230) -> {'M', [119226,119141,119150]};
uts46_map(119231) -> {'M', [119225,119141,119151]};
uts46_map(119232) -> {'M', [119226,119141,119151]};
uts46_map(119808) -> {'M', [97]};
uts46_map(119809) -> {'M', [98]};
uts46_map(119810) -> {'M', [99]};
uts46_map(119811) -> {'M', [100]};
uts46_map(119812) -> {'M', [101]};
uts46_map(119813) -> {'M', [102]};
uts46_map(119814) -> {'M', [103]};
uts46_map(119815) -> {'M', [104]};
uts46_map(119816) -> {'M', [105]};
uts46_map(119817) -> {'M', [106]};
uts46_map(119818) -> {'M', [107]};
uts46_map(119819) -> {'M', [108]};
uts46_map(119820) -> {'M', [109]};
uts46_map(119821) -> {'M', [110]};
uts46_map(119822) -> {'M', [111]};
uts46_map(119823) -> {'M', [112]};
uts46_map(119824) -> {'M', [113]};
uts46_map(119825) -> {'M', [114]};
uts46_map(119826) -> {'M', [115]};
uts46_map(119827) -> {'M', [116]};
uts46_map(119828) -> {'M', [117]};
uts46_map(119829) -> {'M', [118]};
uts46_map(119830) -> {'M', [119]};
uts46_map(119831) -> {'M', [120]};
uts46_map(119832) -> {'M', [121]};
uts46_map(119833) -> {'M', [122]};
uts46_map(119834) -> {'M', [97]};
uts46_map(119835) -> {'M', [98]};
uts46_map(119836) -> {'M', [99]};
uts46_map(119837) -> {'M', [100]};
uts46_map(119838) -> {'M', [101]};
uts46_map(119839) -> {'M', [102]};
uts46_map(119840) -> {'M', [103]};
uts46_map(119841) -> {'M', [104]};
uts46_map(119842) -> {'M', [105]};
uts46_map(119843) -> {'M', [106]};
uts46_map(119844) -> {'M', [107]};
uts46_map(119845) -> {'M', [108]};
uts46_map(119846) -> {'M', [109]};
uts46_map(119847) -> {'M', [110]};
uts46_map(119848) -> {'M', [111]};
uts46_map(119849) -> {'M', [112]};
uts46_map(119850) -> {'M', [113]};
uts46_map(119851) -> {'M', [114]};
uts46_map(119852) -> {'M', [115]};
uts46_map(119853) -> {'M', [116]};
uts46_map(119854) -> {'M', [117]};
uts46_map(119855) -> {'M', [118]};
uts46_map(119856) -> {'M', [119]};
uts46_map(119857) -> {'M', [120]};
uts46_map(119858) -> {'M', [121]};
uts46_map(119859) -> {'M', [122]};
uts46_map(119860) -> {'M', [97]};
uts46_map(119861) -> {'M', [98]};
uts46_map(119862) -> {'M', [99]};
uts46_map(119863) -> {'M', [100]};
uts46_map(119864) -> {'M', [101]};
uts46_map(119865) -> {'M', [102]};
uts46_map(119866) -> {'M', [103]};
uts46_map(119867) -> {'M', [104]};
uts46_map(119868) -> {'M', [105]};
uts46_map(119869) -> {'M', [106]};
uts46_map(119870) -> {'M', [107]};
uts46_map(119871) -> {'M', [108]};
uts46_map(119872) -> {'M', [109]};
uts46_map(119873) -> {'M', [110]};
uts46_map(119874) -> {'M', [111]};
uts46_map(119875) -> {'M', [112]};
uts46_map(119876) -> {'M', [113]};
uts46_map(119877) -> {'M', [114]};
uts46_map(119878) -> {'M', [115]};
uts46_map(119879) -> {'M', [116]};
uts46_map(119880) -> {'M', [117]};
uts46_map(119881) -> {'M', [118]};
uts46_map(119882) -> {'M', [119]};
uts46_map(119883) -> {'M', [120]};
uts46_map(119884) -> {'M', [121]};
uts46_map(119885) -> {'M', [122]};
uts46_map(119886) -> {'M', [97]};
uts46_map(119887) -> {'M', [98]};
uts46_map(119888) -> {'M', [99]};
uts46_map(119889) -> {'M', [100]};
uts46_map(119890) -> {'M', [101]};
uts46_map(119891) -> {'M', [102]};
uts46_map(119892) -> {'M', [103]};
uts46_map(119893) -> 'X';
uts46_map(119894) -> {'M', [105]};
uts46_map(119895) -> {'M', [106]};
uts46_map(119896) -> {'M', [107]};
uts46_map(119897) -> {'M', [108]};
uts46_map(119898) -> {'M', [109]};
uts46_map(119899) -> {'M', [110]};
uts46_map(119900) -> {'M', [111]};
uts46_map(119901) -> {'M', [112]};
uts46_map(119902) -> {'M', [113]};
uts46_map(119903) -> {'M', [114]};
uts46_map(119904) -> {'M', [115]};
uts46_map(119905) -> {'M', [116]};
uts46_map(119906) -> {'M', [117]};
uts46_map(119907) -> {'M', [118]};
uts46_map(119908) -> {'M', [119]};
uts46_map(119909) -> {'M', [120]};
uts46_map(119910) -> {'M', [121]};
uts46_map(119911) -> {'M', [122]};
uts46_map(119912) -> {'M', [97]};
uts46_map(119913) -> {'M', [98]};
uts46_map(119914) -> {'M', [99]};
uts46_map(119915) -> {'M', [100]};
uts46_map(119916) -> {'M', [101]};
uts46_map(119917) -> {'M', [102]};
uts46_map(119918) -> {'M', [103]};
uts46_map(119919) -> {'M', [104]};
uts46_map(119920) -> {'M', [105]};
uts46_map(119921) -> {'M', [106]};
uts46_map(119922) -> {'M', [107]};
uts46_map(119923) -> {'M', [108]};
uts46_map(119924) -> {'M', [109]};
uts46_map(119925) -> {'M', [110]};
uts46_map(119926) -> {'M', [111]};
uts46_map(119927) -> {'M', [112]};
uts46_map(119928) -> {'M', [113]};
uts46_map(119929) -> {'M', [114]};
uts46_map(119930) -> {'M', [115]};
uts46_map(119931) -> {'M', [116]};
uts46_map(119932) -> {'M', [117]};
uts46_map(119933) -> {'M', [118]};
uts46_map(119934) -> {'M', [119]};
uts46_map(119935) -> {'M', [120]};
uts46_map(119936) -> {'M', [121]};
uts46_map(119937) -> {'M', [122]};
uts46_map(119938) -> {'M', [97]};
uts46_map(119939) -> {'M', [98]};
uts46_map(119940) -> {'M', [99]};
uts46_map(119941) -> {'M', [100]};
uts46_map(119942) -> {'M', [101]};
uts46_map(119943) -> {'M', [102]};
uts46_map(119944) -> {'M', [103]};
uts46_map(119945) -> {'M', [104]};
uts46_map(119946) -> {'M', [105]};
uts46_map(119947) -> {'M', [106]};
uts46_map(119948) -> {'M', [107]};
uts46_map(119949) -> {'M', [108]};
uts46_map(119950) -> {'M', [109]};
uts46_map(119951) -> {'M', [110]};
uts46_map(119952) -> {'M', [111]};
uts46_map(119953) -> {'M', [112]};
uts46_map(119954) -> {'M', [113]};
uts46_map(119955) -> {'M', [114]};
uts46_map(119956) -> {'M', [115]};
uts46_map(119957) -> {'M', [116]};
uts46_map(119958) -> {'M', [117]};
uts46_map(119959) -> {'M', [118]};
uts46_map(119960) -> {'M', [119]};
uts46_map(119961) -> {'M', [120]};
uts46_map(119962) -> {'M', [121]};
uts46_map(119963) -> {'M', [122]};
uts46_map(119964) -> {'M', [97]};
uts46_map(119965) -> 'X';
uts46_map(119966) -> {'M', [99]};
uts46_map(119967) -> {'M', [100]};
uts46_map(119970) -> {'M', [103]};
uts46_map(119973) -> {'M', [106]};
uts46_map(119974) -> {'M', [107]};
uts46_map(119977) -> {'M', [110]};
uts46_map(119978) -> {'M', [111]};
uts46_map(119979) -> {'M', [112]};
uts46_map(119980) -> {'M', [113]};
uts46_map(119981) -> 'X';
uts46_map(119982) -> {'M', [115]};
uts46_map(119983) -> {'M', [116]};
uts46_map(119984) -> {'M', [117]};
uts46_map(119985) -> {'M', [118]};
uts46_map(119986) -> {'M', [119]};
uts46_map(119987) -> {'M', [120]};
uts46_map(119988) -> {'M', [121]};
uts46_map(119989) -> {'M', [122]};
uts46_map(119990) -> {'M', [97]};
uts46_map(119991) -> {'M', [98]};
uts46_map(119992) -> {'M', [99]};
uts46_map(119993) -> {'M', [100]};
uts46_map(119994) -> 'X';
uts46_map(119995) -> {'M', [102]};
uts46_map(119996) -> 'X';
uts46_map(119997) -> {'M', [104]};
uts46_map(119998) -> {'M', [105]};
uts46_map(119999) -> {'M', [106]};
uts46_map(120000) -> {'M', [107]};
uts46_map(120001) -> {'M', [108]};
uts46_map(120002) -> {'M', [109]};
uts46_map(120003) -> {'M', [110]};
uts46_map(120004) -> 'X';
uts46_map(120005) -> {'M', [112]};
uts46_map(120006) -> {'M', [113]};
uts46_map(120007) -> {'M', [114]};
uts46_map(120008) -> {'M', [115]};
uts46_map(120009) -> {'M', [116]};
uts46_map(120010) -> {'M', [117]};
uts46_map(120011) -> {'M', [118]};
uts46_map(120012) -> {'M', [119]};
uts46_map(120013) -> {'M', [120]};
uts46_map(120014) -> {'M', [121]};
uts46_map(120015) -> {'M', [122]};
uts46_map(120016) -> {'M', [97]};
uts46_map(120017) -> {'M', [98]};
uts46_map(120018) -> {'M', [99]};
uts46_map(120019) -> {'M', [100]};
uts46_map(120020) -> {'M', [101]};
uts46_map(120021) -> {'M', [102]};
uts46_map(120022) -> {'M', [103]};
uts46_map(120023) -> {'M', [104]};
uts46_map(120024) -> {'M', [105]};
uts46_map(120025) -> {'M', [106]};
uts46_map(120026) -> {'M', [107]};
uts46_map(120027) -> {'M', [108]};
uts46_map(120028) -> {'M', [109]};
uts46_map(120029) -> {'M', [110]};
uts46_map(120030) -> {'M', [111]};
uts46_map(120031) -> {'M', [112]};
uts46_map(120032) -> {'M', [113]};
uts46_map(120033) -> {'M', [114]};
uts46_map(120034) -> {'M', [115]};
uts46_map(120035) -> {'M', [116]};
uts46_map(120036) -> {'M', [117]};
uts46_map(120037) -> {'M', [118]};
uts46_map(120038) -> {'M', [119]};
uts46_map(120039) -> {'M', [120]};
uts46_map(120040) -> {'M', [121]};
uts46_map(120041) -> {'M', [122]};
uts46_map(120042) -> {'M', [97]};
uts46_map(120043) -> {'M', [98]};
uts46_map(120044) -> {'M', [99]};
uts46_map(120045) -> {'M', [100]};
uts46_map(120046) -> {'M', [101]};
uts46_map(120047) -> {'M', [102]};
uts46_map(120048) -> {'M', [103]};
uts46_map(120049) -> {'M', [104]};
uts46_map(120050) -> {'M', [105]};
uts46_map(120051) -> {'M', [106]};
uts46_map(120052) -> {'M', [107]};
uts46_map(120053) -> {'M', [108]};
uts46_map(120054) -> {'M', [109]};
uts46_map(120055) -> {'M', [110]};
uts46_map(120056) -> {'M', [111]};
uts46_map(120057) -> {'M', [112]};
uts46_map(120058) -> {'M', [113]};
uts46_map(120059) -> {'M', [114]};
uts46_map(120060) -> {'M', [115]};
uts46_map(120061) -> {'M', [116]};
uts46_map(120062) -> {'M', [117]};
uts46_map(120063) -> {'M', [118]};
uts46_map(120064) -> {'M', [119]};
uts46_map(120065) -> {'M', [120]};
uts46_map(120066) -> {'M', [121]};
uts46_map(120067) -> {'M', [122]};
uts46_map(120068) -> {'M', [97]};
uts46_map(120069) -> {'M', [98]};
uts46_map(120070) -> 'X';
uts46_map(120071) -> {'M', [100]};
uts46_map(120072) -> {'M', [101]};
uts46_map(120073) -> {'M', [102]};
uts46_map(120074) -> {'M', [103]};
uts46_map(120077) -> {'M', [106]};
uts46_map(120078) -> {'M', [107]};
uts46_map(120079) -> {'M', [108]};
uts46_map(120080) -> {'M', [109]};
uts46_map(120081) -> {'M', [110]};
uts46_map(120082) -> {'M', [111]};
uts46_map(120083) -> {'M', [112]};
uts46_map(120084) -> {'M', [113]};
uts46_map(120085) -> 'X';
uts46_map(120086) -> {'M', [115]};
uts46_map(120087) -> {'M', [116]};
uts46_map(120088) -> {'M', [117]};
uts46_map(120089) -> {'M', [118]};
uts46_map(120090) -> {'M', [119]};
uts46_map(120091) -> {'M', [120]};
uts46_map(120092) -> {'M', [121]};
uts46_map(120093) -> 'X';
uts46_map(120094) -> {'M', [97]};
uts46_map(120095) -> {'M', [98]};
uts46_map(120096) -> {'M', [99]};
uts46_map(120097) -> {'M', [100]};
uts46_map(120098) -> {'M', [101]};
uts46_map(120099) -> {'M', [102]};
uts46_map(120100) -> {'M', [103]};
uts46_map(120101) -> {'M', [104]};
uts46_map(120102) -> {'M', [105]};
uts46_map(120103) -> {'M', [106]};
uts46_map(120104) -> {'M', [107]};
uts46_map(120105) -> {'M', [108]};
uts46_map(120106) -> {'M', [109]};
uts46_map(120107) -> {'M', [110]};
uts46_map(120108) -> {'M', [111]};
uts46_map(120109) -> {'M', [112]};
uts46_map(120110) -> {'M', [113]};
uts46_map(120111) -> {'M', [114]};
uts46_map(120112) -> {'M', [115]};
uts46_map(120113) -> {'M', [116]};
uts46_map(120114) -> {'M', [117]};
uts46_map(120115) -> {'M', [118]};
uts46_map(120116) -> {'M', [119]};
uts46_map(120117) -> {'M', [120]};
uts46_map(120118) -> {'M', [121]};
uts46_map(120119) -> {'M', [122]};
uts46_map(120120) -> {'M', [97]};
uts46_map(120121) -> {'M', [98]};
uts46_map(120122) -> 'X';
uts46_map(120123) -> {'M', [100]};
uts46_map(120124) -> {'M', [101]};
uts46_map(120125) -> {'M', [102]};
uts46_map(120126) -> {'M', [103]};
uts46_map(120127) -> 'X';
uts46_map(120128) -> {'M', [105]};
uts46_map(120129) -> {'M', [106]};
uts46_map(120130) -> {'M', [107]};
uts46_map(120131) -> {'M', [108]};
uts46_map(120132) -> {'M', [109]};
uts46_map(120133) -> 'X';
uts46_map(120134) -> {'M', [111]};
uts46_map(120138) -> {'M', [115]};
uts46_map(120139) -> {'M', [116]};
uts46_map(120140) -> {'M', [117]};
uts46_map(120141) -> {'M', [118]};
uts46_map(120142) -> {'M', [119]};
uts46_map(120143) -> {'M', [120]};
uts46_map(120144) -> {'M', [121]};
uts46_map(120145) -> 'X';
uts46_map(120146) -> {'M', [97]};
uts46_map(120147) -> {'M', [98]};
uts46_map(120148) -> {'M', [99]};
uts46_map(120149) -> {'M', [100]};
uts46_map(120150) -> {'M', [101]};
uts46_map(120151) -> {'M', [102]};
uts46_map(120152) -> {'M', [103]};
uts46_map(120153) -> {'M', [104]};
uts46_map(120154) -> {'M', [105]};
uts46_map(120155) -> {'M', [106]};
uts46_map(120156) -> {'M', [107]};
uts46_map(120157) -> {'M', [108]};
uts46_map(120158) -> {'M', [109]};
uts46_map(120159) -> {'M', [110]};
uts46_map(120160) -> {'M', [111]};
uts46_map(120161) -> {'M', [112]};
uts46_map(120162) -> {'M', [113]};
uts46_map(120163) -> {'M', [114]};
uts46_map(120164) -> {'M', [115]};
uts46_map(120165) -> {'M', [116]};
uts46_map(120166) -> {'M', [117]};
uts46_map(120167) -> {'M', [118]};
uts46_map(120168) -> {'M', [119]};
uts46_map(120169) -> {'M', [120]};
uts46_map(120170) -> {'M', [121]};
uts46_map(120171) -> {'M', [122]};
uts46_map(120172) -> {'M', [97]};
uts46_map(120173) -> {'M', [98]};
uts46_map(120174) -> {'M', [99]};
uts46_map(120175) -> {'M', [100]};
uts46_map(120176) -> {'M', [101]};
uts46_map(120177) -> {'M', [102]};
uts46_map(120178) -> {'M', [103]};
uts46_map(120179) -> {'M', [104]};
uts46_map(120180) -> {'M', [105]};
uts46_map(120181) -> {'M', [106]};
uts46_map(120182) -> {'M', [107]};
uts46_map(120183) -> {'M', [108]};
uts46_map(120184) -> {'M', [109]};
uts46_map(120185) -> {'M', [110]};
uts46_map(120186) -> {'M', [111]};
uts46_map(120187) -> {'M', [112]};
uts46_map(120188) -> {'M', [113]};
uts46_map(120189) -> {'M', [114]};
uts46_map(120190) -> {'M', [115]};
uts46_map(120191) -> {'M', [116]};
uts46_map(120192) -> {'M', [117]};
uts46_map(120193) -> {'M', [118]};
uts46_map(120194) -> {'M', [119]};
uts46_map(120195) -> {'M', [120]};
uts46_map(120196) -> {'M', [121]};
uts46_map(120197) -> {'M', [122]};
uts46_map(120198) -> {'M', [97]};
uts46_map(120199) -> {'M', [98]};
uts46_map(120200) -> {'M', [99]};
uts46_map(120201) -> {'M', [100]};
uts46_map(120202) -> {'M', [101]};
uts46_map(120203) -> {'M', [102]};
uts46_map(120204) -> {'M', [103]};
uts46_map(120205) -> {'M', [104]};
uts46_map(120206) -> {'M', [105]};
uts46_map(120207) -> {'M', [106]};
uts46_map(120208) -> {'M', [107]};
uts46_map(120209) -> {'M', [108]};
uts46_map(120210) -> {'M', [109]};
uts46_map(120211) -> {'M', [110]};
uts46_map(120212) -> {'M', [111]};
uts46_map(120213) -> {'M', [112]};
uts46_map(120214) -> {'M', [113]};
uts46_map(120215) -> {'M', [114]};
uts46_map(120216) -> {'M', [115]};
uts46_map(120217) -> {'M', [116]};
uts46_map(120218) -> {'M', [117]};
uts46_map(120219) -> {'M', [118]};
uts46_map(120220) -> {'M', [119]};
uts46_map(120221) -> {'M', [120]};
uts46_map(120222) -> {'M', [121]};
uts46_map(120223) -> {'M', [122]};
uts46_map(120224) -> {'M', [97]};
uts46_map(120225) -> {'M', [98]};
uts46_map(120226) -> {'M', [99]};
uts46_map(120227) -> {'M', [100]};
uts46_map(120228) -> {'M', [101]};
uts46_map(120229) -> {'M', [102]};
uts46_map(120230) -> {'M', [103]};
uts46_map(120231) -> {'M', [104]};
uts46_map(120232) -> {'M', [105]};
uts46_map(120233) -> {'M', [106]};
uts46_map(120234) -> {'M', [107]};
uts46_map(120235) -> {'M', [108]};
uts46_map(120236) -> {'M', [109]};
uts46_map(120237) -> {'M', [110]};
uts46_map(120238) -> {'M', [111]};
uts46_map(120239) -> {'M', [112]};
uts46_map(120240) -> {'M', [113]};
uts46_map(120241) -> {'M', [114]};
uts46_map(120242) -> {'M', [115]};
uts46_map(120243) -> {'M', [116]};
uts46_map(120244) -> {'M', [117]};
uts46_map(120245) -> {'M', [118]};
uts46_map(120246) -> {'M', [119]};
uts46_map(120247) -> {'M', [120]};
uts46_map(120248) -> {'M', [121]};
uts46_map(120249) -> {'M', [122]};
uts46_map(120250) -> {'M', [97]};
uts46_map(120251) -> {'M', [98]};
uts46_map(120252) -> {'M', [99]};
uts46_map(120253) -> {'M', [100]};
uts46_map(120254) -> {'M', [101]};
uts46_map(120255) -> {'M', [102]};
uts46_map(120256) -> {'M', [103]};
uts46_map(120257) -> {'M', [104]};
uts46_map(120258) -> {'M', [105]};
uts46_map(120259) -> {'M', [106]};
uts46_map(120260) -> {'M', [107]};
uts46_map(120261) -> {'M', [108]};
uts46_map(120262) -> {'M', [109]};
uts46_map(120263) -> {'M', [110]};
uts46_map(120264) -> {'M', [111]};
uts46_map(120265) -> {'M', [112]};
uts46_map(120266) -> {'M', [113]};
uts46_map(120267) -> {'M', [114]};
uts46_map(120268) -> {'M', [115]};
uts46_map(120269) -> {'M', [116]};
uts46_map(120270) -> {'M', [117]};
uts46_map(120271) -> {'M', [118]};
uts46_map(120272) -> {'M', [119]};
uts46_map(120273) -> {'M', [120]};
uts46_map(120274) -> {'M', [121]};
uts46_map(120275) -> {'M', [122]};
uts46_map(120276) -> {'M', [97]};
uts46_map(120277) -> {'M', [98]};
uts46_map(120278) -> {'M', [99]};
uts46_map(120279) -> {'M', [100]};
uts46_map(120280) -> {'M', [101]};
uts46_map(120281) -> {'M', [102]};
uts46_map(120282) -> {'M', [103]};
uts46_map(120283) -> {'M', [104]};
uts46_map(120284) -> {'M', [105]};
uts46_map(120285) -> {'M', [106]};
uts46_map(120286) -> {'M', [107]};
uts46_map(120287) -> {'M', [108]};
uts46_map(120288) -> {'M', [109]};
uts46_map(120289) -> {'M', [110]};
uts46_map(120290) -> {'M', [111]};
uts46_map(120291) -> {'M', [112]};
uts46_map(120292) -> {'M', [113]};
uts46_map(120293) -> {'M', [114]};
uts46_map(120294) -> {'M', [115]};
uts46_map(120295) -> {'M', [116]};
uts46_map(120296) -> {'M', [117]};
uts46_map(120297) -> {'M', [118]};
uts46_map(120298) -> {'M', [119]};
uts46_map(120299) -> {'M', [120]};
uts46_map(120300) -> {'M', [121]};
uts46_map(120301) -> {'M', [122]};
uts46_map(120302) -> {'M', [97]};
uts46_map(120303) -> {'M', [98]};
uts46_map(120304) -> {'M', [99]};
uts46_map(120305) -> {'M', [100]};
uts46_map(120306) -> {'M', [101]};
uts46_map(120307) -> {'M', [102]};
uts46_map(120308) -> {'M', [103]};
uts46_map(120309) -> {'M', [104]};
uts46_map(120310) -> {'M', [105]};
uts46_map(120311) -> {'M', [106]};
uts46_map(120312) -> {'M', [107]};
uts46_map(120313) -> {'M', [108]};
uts46_map(120314) -> {'M', [109]};
uts46_map(120315) -> {'M', [110]};
uts46_map(120316) -> {'M', [111]};
uts46_map(120317) -> {'M', [112]};
uts46_map(120318) -> {'M', [113]};
uts46_map(120319) -> {'M', [114]};
uts46_map(120320) -> {'M', [115]};
uts46_map(120321) -> {'M', [116]};
uts46_map(120322) -> {'M', [117]};
uts46_map(120323) -> {'M', [118]};
uts46_map(120324) -> {'M', [119]};
uts46_map(120325) -> {'M', [120]};
uts46_map(120326) -> {'M', [121]};
uts46_map(120327) -> {'M', [122]};
uts46_map(120328) -> {'M', [97]};
uts46_map(120329) -> {'M', [98]};
uts46_map(120330) -> {'M', [99]};
uts46_map(120331) -> {'M', [100]};
uts46_map(120332) -> {'M', [101]};
uts46_map(120333) -> {'M', [102]};
uts46_map(120334) -> {'M', [103]};
uts46_map(120335) -> {'M', [104]};
uts46_map(120336) -> {'M', [105]};
uts46_map(120337) -> {'M', [106]};
uts46_map(120338) -> {'M', [107]};
uts46_map(120339) -> {'M', [108]};
uts46_map(120340) -> {'M', [109]};
uts46_map(120341) -> {'M', [110]};
uts46_map(120342) -> {'M', [111]};
uts46_map(120343) -> {'M', [112]};
uts46_map(120344) -> {'M', [113]};
uts46_map(120345) -> {'M', [114]};
uts46_map(120346) -> {'M', [115]};
uts46_map(120347) -> {'M', [116]};
uts46_map(120348) -> {'M', [117]};
uts46_map(120349) -> {'M', [118]};
uts46_map(120350) -> {'M', [119]};
uts46_map(120351) -> {'M', [120]};
uts46_map(120352) -> {'M', [121]};
uts46_map(120353) -> {'M', [122]};
uts46_map(120354) -> {'M', [97]};
uts46_map(120355) -> {'M', [98]};
uts46_map(120356) -> {'M', [99]};
uts46_map(120357) -> {'M', [100]};
uts46_map(120358) -> {'M', [101]};
uts46_map(120359) -> {'M', [102]};
uts46_map(120360) -> {'M', [103]};
uts46_map(120361) -> {'M', [104]};
uts46_map(120362) -> {'M', [105]};
uts46_map(120363) -> {'M', [106]};
uts46_map(120364) -> {'M', [107]};
uts46_map(120365) -> {'M', [108]};
uts46_map(120366) -> {'M', [109]};
uts46_map(120367) -> {'M', [110]};
uts46_map(120368) -> {'M', [111]};
uts46_map(120369) -> {'M', [112]};
uts46_map(120370) -> {'M', [113]};
uts46_map(120371) -> {'M', [114]};
uts46_map(120372) -> {'M', [115]};
uts46_map(120373) -> {'M', [116]};
uts46_map(120374) -> {'M', [117]};
uts46_map(120375) -> {'M', [118]};
uts46_map(120376) -> {'M', [119]};
uts46_map(120377) -> {'M', [120]};
uts46_map(120378) -> {'M', [121]};
uts46_map(120379) -> {'M', [122]};
uts46_map(120380) -> {'M', [97]};
uts46_map(120381) -> {'M', [98]};
uts46_map(120382) -> {'M', [99]};
uts46_map(120383) -> {'M', [100]};
uts46_map(120384) -> {'M', [101]};
uts46_map(120385) -> {'M', [102]};
uts46_map(120386) -> {'M', [103]};
uts46_map(120387) -> {'M', [104]};
uts46_map(120388) -> {'M', [105]};
uts46_map(120389) -> {'M', [106]};
uts46_map(120390) -> {'M', [107]};
uts46_map(120391) -> {'M', [108]};
uts46_map(120392) -> {'M', [109]};
uts46_map(120393) -> {'M', [110]};
uts46_map(120394) -> {'M', [111]};
uts46_map(120395) -> {'M', [112]};
uts46_map(120396) -> {'M', [113]};
uts46_map(120397) -> {'M', [114]};
uts46_map(120398) -> {'M', [115]};
uts46_map(120399) -> {'M', [116]};
uts46_map(120400) -> {'M', [117]};
uts46_map(120401) -> {'M', [118]};
uts46_map(120402) -> {'M', [119]};
uts46_map(120403) -> {'M', [120]};
uts46_map(120404) -> {'M', [121]};
uts46_map(120405) -> {'M', [122]};
uts46_map(120406) -> {'M', [97]};
uts46_map(120407) -> {'M', [98]};
uts46_map(120408) -> {'M', [99]};
uts46_map(120409) -> {'M', [100]};
uts46_map(120410) -> {'M', [101]};
uts46_map(120411) -> {'M', [102]};
uts46_map(120412) -> {'M', [103]};
uts46_map(120413) -> {'M', [104]};
uts46_map(120414) -> {'M', [105]};
uts46_map(120415) -> {'M', [106]};
uts46_map(120416) -> {'M', [107]};
uts46_map(120417) -> {'M', [108]};
uts46_map(120418) -> {'M', [109]};
uts46_map(120419) -> {'M', [110]};
uts46_map(120420) -> {'M', [111]};
uts46_map(120421) -> {'M', [112]};
uts46_map(120422) -> {'M', [113]};
uts46_map(120423) -> {'M', [114]};
uts46_map(120424) -> {'M', [115]};
uts46_map(120425) -> {'M', [116]};
uts46_map(120426) -> {'M', [117]};
uts46_map(120427) -> {'M', [118]};
uts46_map(120428) -> {'M', [119]};
uts46_map(120429) -> {'M', [120]};
uts46_map(120430) -> {'M', [121]};
uts46_map(120431) -> {'M', [122]};
uts46_map(120432) -> {'M', [97]};
uts46_map(120433) -> {'M', [98]};
uts46_map(120434) -> {'M', [99]};
uts46_map(120435) -> {'M', [100]};
uts46_map(120436) -> {'M', [101]};
uts46_map(120437) -> {'M', [102]};
uts46_map(120438) -> {'M', [103]};
uts46_map(120439) -> {'M', [104]};
uts46_map(120440) -> {'M', [105]};
uts46_map(120441) -> {'M', [106]};
uts46_map(120442) -> {'M', [107]};
uts46_map(120443) -> {'M', [108]};
uts46_map(120444) -> {'M', [109]};
uts46_map(120445) -> {'M', [110]};
uts46_map(120446) -> {'M', [111]};
uts46_map(120447) -> {'M', [112]};
uts46_map(120448) -> {'M', [113]};
uts46_map(120449) -> {'M', [114]};
uts46_map(120450) -> {'M', [115]};
uts46_map(120451) -> {'M', [116]};
uts46_map(120452) -> {'M', [117]};
uts46_map(120453) -> {'M', [118]};
uts46_map(120454) -> {'M', [119]};
uts46_map(120455) -> {'M', [120]};
uts46_map(120456) -> {'M', [121]};
uts46_map(120457) -> {'M', [122]};
uts46_map(120458) -> {'M', [97]};
uts46_map(120459) -> {'M', [98]};
uts46_map(120460) -> {'M', [99]};
uts46_map(120461) -> {'M', [100]};
uts46_map(120462) -> {'M', [101]};
uts46_map(120463) -> {'M', [102]};
uts46_map(120464) -> {'M', [103]};
uts46_map(120465) -> {'M', [104]};
uts46_map(120466) -> {'M', [105]};
uts46_map(120467) -> {'M', [106]};
uts46_map(120468) -> {'M', [107]};
uts46_map(120469) -> {'M', [108]};
uts46_map(120470) -> {'M', [109]};
uts46_map(120471) -> {'M', [110]};
uts46_map(120472) -> {'M', [111]};
uts46_map(120473) -> {'M', [112]};
uts46_map(120474) -> {'M', [113]};
uts46_map(120475) -> {'M', [114]};
uts46_map(120476) -> {'M', [115]};
uts46_map(120477) -> {'M', [116]};
uts46_map(120478) -> {'M', [117]};
uts46_map(120479) -> {'M', [118]};
uts46_map(120480) -> {'M', [119]};
uts46_map(120481) -> {'M', [120]};
uts46_map(120482) -> {'M', [121]};
uts46_map(120483) -> {'M', [122]};
uts46_map(120484) -> {'M', [305]};
uts46_map(120485) -> {'M', [567]};
uts46_map(120488) -> {'M', [945]};
uts46_map(120489) -> {'M', [946]};
uts46_map(120490) -> {'M', [947]};
uts46_map(120491) -> {'M', [948]};
uts46_map(120492) -> {'M', [949]};
uts46_map(120493) -> {'M', [950]};
uts46_map(120494) -> {'M', [951]};
uts46_map(120495) -> {'M', [952]};
uts46_map(120496) -> {'M', [953]};
uts46_map(120497) -> {'M', [954]};
uts46_map(120498) -> {'M', [955]};
uts46_map(120499) -> {'M', [956]};
uts46_map(120500) -> {'M', [957]};
uts46_map(120501) -> {'M', [958]};
uts46_map(120502) -> {'M', [959]};
uts46_map(120503) -> {'M', [960]};
uts46_map(120504) -> {'M', [961]};
uts46_map(120505) -> {'M', [952]};
uts46_map(120506) -> {'M', [963]};
uts46_map(120507) -> {'M', [964]};
uts46_map(120508) -> {'M', [965]};
uts46_map(120509) -> {'M', [966]};
uts46_map(120510) -> {'M', [967]};
uts46_map(120511) -> {'M', [968]};
uts46_map(120512) -> {'M', [969]};
uts46_map(120513) -> {'M', [8711]};
uts46_map(120514) -> {'M', [945]};
uts46_map(120515) -> {'M', [946]};
uts46_map(120516) -> {'M', [947]};
uts46_map(120517) -> {'M', [948]};
uts46_map(120518) -> {'M', [949]};
uts46_map(120519) -> {'M', [950]};
uts46_map(120520) -> {'M', [951]};
uts46_map(120521) -> {'M', [952]};
uts46_map(120522) -> {'M', [953]};
uts46_map(120523) -> {'M', [954]};
uts46_map(120524) -> {'M', [955]};
uts46_map(120525) -> {'M', [956]};
uts46_map(120526) -> {'M', [957]};
uts46_map(120527) -> {'M', [958]};
uts46_map(120528) -> {'M', [959]};
uts46_map(120529) -> {'M', [960]};
uts46_map(120530) -> {'M', [961]};
uts46_map(120533) -> {'M', [964]};
uts46_map(120534) -> {'M', [965]};
uts46_map(120535) -> {'M', [966]};
uts46_map(120536) -> {'M', [967]};
uts46_map(120537) -> {'M', [968]};
uts46_map(120538) -> {'M', [969]};
uts46_map(120539) -> {'M', [8706]};
uts46_map(120540) -> {'M', [949]};
uts46_map(120541) -> {'M', [952]};
uts46_map(120542) -> {'M', [954]};
uts46_map(120543) -> {'M', [966]};
uts46_map(120544) -> {'M', [961]};
uts46_map(120545) -> {'M', [960]};
uts46_map(120546) -> {'M', [945]};
uts46_map(120547) -> {'M', [946]};
uts46_map(120548) -> {'M', [947]};
uts46_map(120549) -> {'M', [948]};
uts46_map(120550) -> {'M', [949]};
uts46_map(120551) -> {'M', [950]};
uts46_map(120552) -> {'M', [951]};
uts46_map(120553) -> {'M', [952]};
uts46_map(120554) -> {'M', [953]};
uts46_map(120555) -> {'M', [954]};
uts46_map(120556) -> {'M', [955]};
uts46_map(120557) -> {'M', [956]};
uts46_map(120558) -> {'M', [957]};
uts46_map(120559) -> {'M', [958]};
uts46_map(120560) -> {'M', [959]};
uts46_map(120561) -> {'M', [960]};
uts46_map(120562) -> {'M', [961]};
uts46_map(120563) -> {'M', [952]};
uts46_map(120564) -> {'M', [963]};
uts46_map(120565) -> {'M', [964]};
uts46_map(120566) -> {'M', [965]};
uts46_map(120567) -> {'M', [966]};
uts46_map(120568) -> {'M', [967]};
uts46_map(120569) -> {'M', [968]};
uts46_map(120570) -> {'M', [969]};
uts46_map(120571) -> {'M', [8711]};
uts46_map(120572) -> {'M', [945]};
uts46_map(120573) -> {'M', [946]};
uts46_map(120574) -> {'M', [947]};
uts46_map(120575) -> {'M', [948]};
uts46_map(120576) -> {'M', [949]};
uts46_map(120577) -> {'M', [950]};
uts46_map(120578) -> {'M', [951]};
uts46_map(120579) -> {'M', [952]};
uts46_map(120580) -> {'M', [953]};
uts46_map(120581) -> {'M', [954]};
uts46_map(120582) -> {'M', [955]};
uts46_map(120583) -> {'M', [956]};
uts46_map(120584) -> {'M', [957]};
uts46_map(120585) -> {'M', [958]};
uts46_map(120586) -> {'M', [959]};
uts46_map(120587) -> {'M', [960]};
uts46_map(120588) -> {'M', [961]};
uts46_map(120591) -> {'M', [964]};
uts46_map(120592) -> {'M', [965]};
uts46_map(120593) -> {'M', [966]};
uts46_map(120594) -> {'M', [967]};
uts46_map(120595) -> {'M', [968]};
uts46_map(120596) -> {'M', [969]};
uts46_map(120597) -> {'M', [8706]};
uts46_map(120598) -> {'M', [949]};
uts46_map(120599) -> {'M', [952]};
uts46_map(120600) -> {'M', [954]};
uts46_map(120601) -> {'M', [966]};
uts46_map(120602) -> {'M', [961]};
uts46_map(120603) -> {'M', [960]};
uts46_map(120604) -> {'M', [945]};
uts46_map(120605) -> {'M', [946]};
uts46_map(120606) -> {'M', [947]};
uts46_map(120607) -> {'M', [948]};
uts46_map(120608) -> {'M', [949]};
uts46_map(120609) -> {'M', [950]};
uts46_map(120610) -> {'M', [951]};
uts46_map(120611) -> {'M', [952]};
uts46_map(120612) -> {'M', [953]};
uts46_map(120613) -> {'M', [954]};
uts46_map(120614) -> {'M', [955]};
uts46_map(120615) -> {'M', [956]};
uts46_map(120616) -> {'M', [957]};
uts46_map(120617) -> {'M', [958]};
uts46_map(120618) -> {'M', [959]};
uts46_map(120619) -> {'M', [960]};
uts46_map(120620) -> {'M', [961]};
uts46_map(120621) -> {'M', [952]};
uts46_map(120622) -> {'M', [963]};
uts46_map(120623) -> {'M', [964]};
uts46_map(120624) -> {'M', [965]};
uts46_map(120625) -> {'M', [966]};
uts46_map(120626) -> {'M', [967]};
uts46_map(120627) -> {'M', [968]};
uts46_map(120628) -> {'M', [969]};
uts46_map(120629) -> {'M', [8711]};
uts46_map(120630) -> {'M', [945]};
uts46_map(120631) -> {'M', [946]};
uts46_map(120632) -> {'M', [947]};
uts46_map(120633) -> {'M', [948]};
uts46_map(120634) -> {'M', [949]};
uts46_map(120635) -> {'M', [950]};
uts46_map(120636) -> {'M', [951]};
uts46_map(120637) -> {'M', [952]};
uts46_map(120638) -> {'M', [953]};
uts46_map(120639) -> {'M', [954]};
uts46_map(120640) -> {'M', [955]};
uts46_map(120641) -> {'M', [956]};
uts46_map(120642) -> {'M', [957]};
uts46_map(120643) -> {'M', [958]};
uts46_map(120644) -> {'M', [959]};
uts46_map(120645) -> {'M', [960]};
uts46_map(120646) -> {'M', [961]};
uts46_map(120649) -> {'M', [964]};
uts46_map(120650) -> {'M', [965]};
uts46_map(120651) -> {'M', [966]};
uts46_map(120652) -> {'M', [967]};
uts46_map(120653) -> {'M', [968]};
uts46_map(120654) -> {'M', [969]};
uts46_map(120655) -> {'M', [8706]};
uts46_map(120656) -> {'M', [949]};
uts46_map(120657) -> {'M', [952]};
uts46_map(120658) -> {'M', [954]};
uts46_map(120659) -> {'M', [966]};
uts46_map(120660) -> {'M', [961]};
uts46_map(120661) -> {'M', [960]};
uts46_map(120662) -> {'M', [945]};
uts46_map(120663) -> {'M', [946]};
uts46_map(120664) -> {'M', [947]};
uts46_map(120665) -> {'M', [948]};
uts46_map(120666) -> {'M', [949]};
uts46_map(120667) -> {'M', [950]};
uts46_map(120668) -> {'M', [951]};
uts46_map(120669) -> {'M', [952]};
uts46_map(120670) -> {'M', [953]};
uts46_map(120671) -> {'M', [954]};
uts46_map(120672) -> {'M', [955]};
uts46_map(120673) -> {'M', [956]};
uts46_map(120674) -> {'M', [957]};
uts46_map(120675) -> {'M', [958]};
uts46_map(120676) -> {'M', [959]};
uts46_map(120677) -> {'M', [960]};
uts46_map(120678) -> {'M', [961]};
uts46_map(120679) -> {'M', [952]};
uts46_map(120680) -> {'M', [963]};
uts46_map(120681) -> {'M', [964]};
uts46_map(120682) -> {'M', [965]};
uts46_map(120683) -> {'M', [966]};
uts46_map(120684) -> {'M', [967]};
uts46_map(120685) -> {'M', [968]};
uts46_map(120686) -> {'M', [969]};
uts46_map(120687) -> {'M', [8711]};
uts46_map(120688) -> {'M', [945]};
uts46_map(120689) -> {'M', [946]};
uts46_map(120690) -> {'M', [947]};
uts46_map(120691) -> {'M', [948]};
uts46_map(120692) -> {'M', [949]};
uts46_map(120693) -> {'M', [950]};
uts46_map(120694) -> {'M', [951]};
uts46_map(120695) -> {'M', [952]};
uts46_map(120696) -> {'M', [953]};
uts46_map(120697) -> {'M', [954]};
uts46_map(120698) -> {'M', [955]};
uts46_map(120699) -> {'M', [956]};
uts46_map(120700) -> {'M', [957]};
uts46_map(120701) -> {'M', [958]};
uts46_map(120702) -> {'M', [959]};
uts46_map(120703) -> {'M', [960]};
uts46_map(120704) -> {'M', [961]};
uts46_map(120707) -> {'M', [964]};
uts46_map(120708) -> {'M', [965]};
uts46_map(120709) -> {'M', [966]};
uts46_map(120710) -> {'M', [967]};
uts46_map(120711) -> {'M', [968]};
uts46_map(120712) -> {'M', [969]};
uts46_map(120713) -> {'M', [8706]};
uts46_map(120714) -> {'M', [949]};
uts46_map(120715) -> {'M', [952]};
uts46_map(120716) -> {'M', [954]};
uts46_map(120717) -> {'M', [966]};
uts46_map(120718) -> {'M', [961]};
uts46_map(120719) -> {'M', [960]};
uts46_map(120720) -> {'M', [945]};
uts46_map(120721) -> {'M', [946]};
uts46_map(120722) -> {'M', [947]};
uts46_map(120723) -> {'M', [948]};
uts46_map(120724) -> {'M', [949]};
uts46_map(120725) -> {'M', [950]};
uts46_map(120726) -> {'M', [951]};
uts46_map(120727) -> {'M', [952]};
uts46_map(120728) -> {'M', [953]};
uts46_map(120729) -> {'M', [954]};
uts46_map(120730) -> {'M', [955]};
uts46_map(120731) -> {'M', [956]};
uts46_map(120732) -> {'M', [957]};
uts46_map(120733) -> {'M', [958]};
uts46_map(120734) -> {'M', [959]};
uts46_map(120735) -> {'M', [960]};
uts46_map(120736) -> {'M', [961]};
uts46_map(120737) -> {'M', [952]};
uts46_map(120738) -> {'M', [963]};
uts46_map(120739) -> {'M', [964]};
uts46_map(120740) -> {'M', [965]};
uts46_map(120741) -> {'M', [966]};
uts46_map(120742) -> {'M', [967]};
uts46_map(120743) -> {'M', [968]};
uts46_map(120744) -> {'M', [969]};
uts46_map(120745) -> {'M', [8711]};
uts46_map(120746) -> {'M', [945]};
uts46_map(120747) -> {'M', [946]};
uts46_map(120748) -> {'M', [947]};
uts46_map(120749) -> {'M', [948]};
uts46_map(120750) -> {'M', [949]};
uts46_map(120751) -> {'M', [950]};
uts46_map(120752) -> {'M', [951]};
uts46_map(120753) -> {'M', [952]};
uts46_map(120754) -> {'M', [953]};
uts46_map(120755) -> {'M', [954]};
uts46_map(120756) -> {'M', [955]};
uts46_map(120757) -> {'M', [956]};
uts46_map(120758) -> {'M', [957]};
uts46_map(120759) -> {'M', [958]};
uts46_map(120760) -> {'M', [959]};
uts46_map(120761) -> {'M', [960]};
uts46_map(120762) -> {'M', [961]};
uts46_map(120765) -> {'M', [964]};
uts46_map(120766) -> {'M', [965]};
uts46_map(120767) -> {'M', [966]};
uts46_map(120768) -> {'M', [967]};
uts46_map(120769) -> {'M', [968]};
uts46_map(120770) -> {'M', [969]};
uts46_map(120771) -> {'M', [8706]};
uts46_map(120772) -> {'M', [949]};
uts46_map(120773) -> {'M', [952]};
uts46_map(120774) -> {'M', [954]};
uts46_map(120775) -> {'M', [966]};
uts46_map(120776) -> {'M', [961]};
uts46_map(120777) -> {'M', [960]};
uts46_map(120782) -> {'M', [48]};
uts46_map(120783) -> {'M', [49]};
uts46_map(120784) -> {'M', [50]};
uts46_map(120785) -> {'M', [51]};
uts46_map(120786) -> {'M', [52]};
uts46_map(120787) -> {'M', [53]};
uts46_map(120788) -> {'M', [54]};
uts46_map(120789) -> {'M', [55]};
uts46_map(120790) -> {'M', [56]};
uts46_map(120791) -> {'M', [57]};
uts46_map(120792) -> {'M', [48]};
uts46_map(120793) -> {'M', [49]};
uts46_map(120794) -> {'M', [50]};
uts46_map(120795) -> {'M', [51]};
uts46_map(120796) -> {'M', [52]};
uts46_map(120797) -> {'M', [53]};
uts46_map(120798) -> {'M', [54]};
uts46_map(120799) -> {'M', [55]};
uts46_map(120800) -> {'M', [56]};
uts46_map(120801) -> {'M', [57]};
uts46_map(120802) -> {'M', [48]};
uts46_map(120803) -> {'M', [49]};
uts46_map(120804) -> {'M', [50]};
uts46_map(120805) -> {'M', [51]};
uts46_map(120806) -> {'M', [52]};
uts46_map(120807) -> {'M', [53]};
uts46_map(120808) -> {'M', [54]};
uts46_map(120809) -> {'M', [55]};
uts46_map(120810) -> {'M', [56]};
uts46_map(120811) -> {'M', [57]};
uts46_map(120812) -> {'M', [48]};
uts46_map(120813) -> {'M', [49]};
uts46_map(120814) -> {'M', [50]};
uts46_map(120815) -> {'M', [51]};
uts46_map(120816) -> {'M', [52]};
uts46_map(120817) -> {'M', [53]};
uts46_map(120818) -> {'M', [54]};
uts46_map(120819) -> {'M', [55]};
uts46_map(120820) -> {'M', [56]};
uts46_map(120821) -> {'M', [57]};
uts46_map(120822) -> {'M', [48]};
uts46_map(120823) -> {'M', [49]};
uts46_map(120824) -> {'M', [50]};
uts46_map(120825) -> {'M', [51]};
uts46_map(120826) -> {'M', [52]};
uts46_map(120827) -> {'M', [53]};
uts46_map(120828) -> {'M', [54]};
uts46_map(120829) -> {'M', [55]};
uts46_map(120830) -> {'M', [56]};
uts46_map(120831) -> {'M', [57]};
uts46_map(121461) -> 'V';
uts46_map(121476) -> 'V';
uts46_map(121504) -> 'X';
uts46_map(122887) -> 'X';
uts46_map(122914) -> 'X';
uts46_map(122917) -> 'X';
uts46_map(123214) -> 'V';
uts46_map(123215) -> 'V';
uts46_map(123647) -> 'V';
uts46_map(125184) -> {'M', [125218]};
uts46_map(125185) -> {'M', [125219]};
uts46_map(125186) -> {'M', [125220]};
uts46_map(125187) -> {'M', [125221]};
uts46_map(125188) -> {'M', [125222]};
uts46_map(125189) -> {'M', [125223]};
uts46_map(125190) -> {'M', [125224]};
uts46_map(125191) -> {'M', [125225]};
uts46_map(125192) -> {'M', [125226]};
uts46_map(125193) -> {'M', [125227]};
uts46_map(125194) -> {'M', [125228]};
uts46_map(125195) -> {'M', [125229]};
uts46_map(125196) -> {'M', [125230]};
uts46_map(125197) -> {'M', [125231]};
uts46_map(125198) -> {'M', [125232]};
uts46_map(125199) -> {'M', [125233]};
uts46_map(125200) -> {'M', [125234]};
uts46_map(125201) -> {'M', [125235]};
uts46_map(125202) -> {'M', [125236]};
uts46_map(125203) -> {'M', [125237]};
uts46_map(125204) -> {'M', [125238]};
uts46_map(125205) -> {'M', [125239]};
uts46_map(125206) -> {'M', [125240]};
uts46_map(125207) -> {'M', [125241]};
uts46_map(125208) -> {'M', [125242]};
uts46_map(125209) -> {'M', [125243]};
uts46_map(125210) -> {'M', [125244]};
uts46_map(125211) -> {'M', [125245]};
uts46_map(125212) -> {'M', [125246]};
uts46_map(125213) -> {'M', [125247]};
uts46_map(125214) -> {'M', [125248]};
uts46_map(125215) -> {'M', [125249]};
uts46_map(125216) -> {'M', [125250]};
uts46_map(125217) -> {'M', [125251]};
uts46_map(125259) -> 'V';
uts46_map(126464) -> {'M', [1575]};
uts46_map(126465) -> {'M', [1576]};
uts46_map(126466) -> {'M', [1580]};
uts46_map(126467) -> {'M', [1583]};
uts46_map(126468) -> 'X';
uts46_map(126469) -> {'M', [1608]};
uts46_map(126470) -> {'M', [1586]};
uts46_map(126471) -> {'M', [1581]};
uts46_map(126472) -> {'M', [1591]};
uts46_map(126473) -> {'M', [1610]};
uts46_map(126474) -> {'M', [1603]};
uts46_map(126475) -> {'M', [1604]};
uts46_map(126476) -> {'M', [1605]};
uts46_map(126477) -> {'M', [1606]};
uts46_map(126478) -> {'M', [1587]};
uts46_map(126479) -> {'M', [1593]};
uts46_map(126480) -> {'M', [1601]};
uts46_map(126481) -> {'M', [1589]};
uts46_map(126482) -> {'M', [1602]};
uts46_map(126483) -> {'M', [1585]};
uts46_map(126484) -> {'M', [1588]};
uts46_map(126485) -> {'M', [1578]};
uts46_map(126486) -> {'M', [1579]};
uts46_map(126487) -> {'M', [1582]};
uts46_map(126488) -> {'M', [1584]};
uts46_map(126489) -> {'M', [1590]};
uts46_map(126490) -> {'M', [1592]};
uts46_map(126491) -> {'M', [1594]};
uts46_map(126492) -> {'M', [1646]};
uts46_map(126493) -> {'M', [1722]};
uts46_map(126494) -> {'M', [1697]};
uts46_map(126495) -> {'M', [1647]};
uts46_map(126496) -> 'X';
uts46_map(126497) -> {'M', [1576]};
uts46_map(126498) -> {'M', [1580]};
uts46_map(126499) -> 'X';
uts46_map(126500) -> {'M', [1607]};
uts46_map(126503) -> {'M', [1581]};
uts46_map(126504) -> 'X';
uts46_map(126505) -> {'M', [1610]};
uts46_map(126506) -> {'M', [1603]};
uts46_map(126507) -> {'M', [1604]};
uts46_map(126508) -> {'M', [1605]};
uts46_map(126509) -> {'M', [1606]};
uts46_map(126510) -> {'M', [1587]};
uts46_map(126511) -> {'M', [1593]};
uts46_map(126512) -> {'M', [1601]};
uts46_map(126513) -> {'M', [1589]};
uts46_map(126514) -> {'M', [1602]};
uts46_map(126515) -> 'X';
uts46_map(126516) -> {'M', [1588]};
uts46_map(126517) -> {'M', [1578]};
uts46_map(126518) -> {'M', [1579]};
uts46_map(126519) -> {'M', [1582]};
uts46_map(126520) -> 'X';
uts46_map(126521) -> {'M', [1590]};
uts46_map(126522) -> 'X';
uts46_map(126523) -> {'M', [1594]};
uts46_map(126530) -> {'M', [1580]};
uts46_map(126535) -> {'M', [1581]};
uts46_map(126536) -> 'X';
uts46_map(126537) -> {'M', [1610]};
uts46_map(126538) -> 'X';
uts46_map(126539) -> {'M', [1604]};
uts46_map(126540) -> 'X';
uts46_map(126541) -> {'M', [1606]};
uts46_map(126542) -> {'M', [1587]};
uts46_map(126543) -> {'M', [1593]};
uts46_map(126544) -> 'X';
uts46_map(126545) -> {'M', [1589]};
uts46_map(126546) -> {'M', [1602]};
uts46_map(126547) -> 'X';
uts46_map(126548) -> {'M', [1588]};
uts46_map(126551) -> {'M', [1582]};
uts46_map(126552) -> 'X';
uts46_map(126553) -> {'M', [1590]};
uts46_map(126554) -> 'X';
uts46_map(126555) -> {'M', [1594]};
uts46_map(126556) -> 'X';
uts46_map(126557) -> {'M', [1722]};
uts46_map(126558) -> 'X';
uts46_map(126559) -> {'M', [1647]};
uts46_map(126560) -> 'X';
uts46_map(126561) -> {'M', [1576]};
uts46_map(126562) -> {'M', [1580]};
uts46_map(126563) -> 'X';
uts46_map(126564) -> {'M', [1607]};
uts46_map(126567) -> {'M', [1581]};
uts46_map(126568) -> {'M', [1591]};
uts46_map(126569) -> {'M', [1610]};
uts46_map(126570) -> {'M', [1603]};
uts46_map(126571) -> 'X';
uts46_map(126572) -> {'M', [1605]};
uts46_map(126573) -> {'M', [1606]};
uts46_map(126574) -> {'M', [1587]};
uts46_map(126575) -> {'M', [1593]};
uts46_map(126576) -> {'M', [1601]};
uts46_map(126577) -> {'M', [1589]};
uts46_map(126578) -> {'M', [1602]};
uts46_map(126579) -> 'X';
uts46_map(126580) -> {'M', [1588]};
uts46_map(126581) -> {'M', [1578]};
uts46_map(126582) -> {'M', [1579]};
uts46_map(126583) -> {'M', [1582]};
uts46_map(126584) -> 'X';
uts46_map(126585) -> {'M', [1590]};
uts46_map(126586) -> {'M', [1592]};
uts46_map(126587) -> {'M', [1594]};
uts46_map(126588) -> {'M', [1646]};
uts46_map(126589) -> 'X';
uts46_map(126590) -> {'M', [1697]};
uts46_map(126591) -> 'X';
uts46_map(126592) -> {'M', [1575]};
uts46_map(126593) -> {'M', [1576]};
uts46_map(126594) -> {'M', [1580]};
uts46_map(126595) -> {'M', [1583]};
uts46_map(126596) -> {'M', [1607]};
uts46_map(126597) -> {'M', [1608]};
uts46_map(126598) -> {'M', [1586]};
uts46_map(126599) -> {'M', [1581]};
uts46_map(126600) -> {'M', [1591]};
uts46_map(126601) -> {'M', [1610]};
uts46_map(126602) -> 'X';
uts46_map(126603) -> {'M', [1604]};
uts46_map(126604) -> {'M', [1605]};
uts46_map(126605) -> {'M', [1606]};
uts46_map(126606) -> {'M', [1587]};
uts46_map(126607) -> {'M', [1593]};
uts46_map(126608) -> {'M', [1601]};
uts46_map(126609) -> {'M', [1589]};
uts46_map(126610) -> {'M', [1602]};
uts46_map(126611) -> {'M', [1585]};
uts46_map(126612) -> {'M', [1588]};
uts46_map(126613) -> {'M', [1578]};
uts46_map(126614) -> {'M', [1579]};
uts46_map(126615) -> {'M', [1582]};
uts46_map(126616) -> {'M', [1584]};
uts46_map(126617) -> {'M', [1590]};
uts46_map(126618) -> {'M', [1592]};
uts46_map(126619) -> {'M', [1594]};
uts46_map(126625) -> {'M', [1576]};
uts46_map(126626) -> {'M', [1580]};
uts46_map(126627) -> {'M', [1583]};
uts46_map(126628) -> 'X';
uts46_map(126629) -> {'M', [1608]};
uts46_map(126630) -> {'M', [1586]};
uts46_map(126631) -> {'M', [1581]};
uts46_map(126632) -> {'M', [1591]};
uts46_map(126633) -> {'M', [1610]};
uts46_map(126634) -> 'X';
uts46_map(126635) -> {'M', [1604]};
uts46_map(126636) -> {'M', [1605]};
uts46_map(126637) -> {'M', [1606]};
uts46_map(126638) -> {'M', [1587]};
uts46_map(126639) -> {'M', [1593]};
uts46_map(126640) -> {'M', [1601]};
uts46_map(126641) -> {'M', [1589]};
uts46_map(126642) -> {'M', [1602]};
uts46_map(126643) -> {'M', [1585]};
uts46_map(126644) -> {'M', [1588]};
uts46_map(126645) -> {'M', [1578]};
uts46_map(126646) -> {'M', [1579]};
uts46_map(126647) -> {'M', [1582]};
uts46_map(126648) -> {'M', [1584]};
uts46_map(126649) -> {'M', [1590]};
uts46_map(126650) -> {'M', [1592]};
uts46_map(126651) -> {'M', [1594]};
uts46_map(127167) -> 'V';
uts46_map(127168) -> 'X';
uts46_map(127184) -> 'X';
uts46_map(127232) -> 'X';
uts46_map(127233) -> {'3', [48,44]};
uts46_map(127234) -> {'3', [49,44]};
uts46_map(127235) -> {'3', [50,44]};
uts46_map(127236) -> {'3', [51,44]};
uts46_map(127237) -> {'3', [52,44]};
uts46_map(127238) -> {'3', [53,44]};
uts46_map(127239) -> {'3', [54,44]};
uts46_map(127240) -> {'3', [55,44]};
uts46_map(127241) -> {'3', [56,44]};
uts46_map(127242) -> {'3', [57,44]};
uts46_map(127248) -> {'3', [40,97,41]};
uts46_map(127249) -> {'3', [40,98,41]};
uts46_map(127250) -> {'3', [40,99,41]};
uts46_map(127251) -> {'3', [40,100,41]};
uts46_map(127252) -> {'3', [40,101,41]};
uts46_map(127253) -> {'3', [40,102,41]};
uts46_map(127254) -> {'3', [40,103,41]};
uts46_map(127255) -> {'3', [40,104,41]};
uts46_map(127256) -> {'3', [40,105,41]};
uts46_map(127257) -> {'3', [40,106,41]};
uts46_map(127258) -> {'3', [40,107,41]};
uts46_map(127259) -> {'3', [40,108,41]};
uts46_map(127260) -> {'3', [40,109,41]};
uts46_map(127261) -> {'3', [40,110,41]};
uts46_map(127262) -> {'3', [40,111,41]};
uts46_map(127263) -> {'3', [40,112,41]};
uts46_map(127264) -> {'3', [40,113,41]};
uts46_map(127265) -> {'3', [40,114,41]};
uts46_map(127266) -> {'3', [40,115,41]};
uts46_map(127267) -> {'3', [40,116,41]};
uts46_map(127268) -> {'3', [40,117,41]};
uts46_map(127269) -> {'3', [40,118,41]};
uts46_map(127270) -> {'3', [40,119,41]};
uts46_map(127271) -> {'3', [40,120,41]};
uts46_map(127272) -> {'3', [40,121,41]};
uts46_map(127273) -> {'3', [40,122,41]};
uts46_map(127274) -> {'M', [12308,115,12309]};
uts46_map(127275) -> {'M', [99]};
uts46_map(127276) -> {'M', [114]};
uts46_map(127277) -> {'M', [99,100]};
uts46_map(127278) -> {'M', [119,122]};
uts46_map(127279) -> 'V';
uts46_map(127280) -> {'M', [97]};
uts46_map(127281) -> {'M', [98]};
uts46_map(127282) -> {'M', [99]};
uts46_map(127283) -> {'M', [100]};
uts46_map(127284) -> {'M', [101]};
uts46_map(127285) -> {'M', [102]};
uts46_map(127286) -> {'M', [103]};
uts46_map(127287) -> {'M', [104]};
uts46_map(127288) -> {'M', [105]};
uts46_map(127289) -> {'M', [106]};
uts46_map(127290) -> {'M', [107]};
uts46_map(127291) -> {'M', [108]};
uts46_map(127292) -> {'M', [109]};
uts46_map(127293) -> {'M', [110]};
uts46_map(127294) -> {'M', [111]};
uts46_map(127295) -> {'M', [112]};
uts46_map(127296) -> {'M', [113]};
uts46_map(127297) -> {'M', [114]};
uts46_map(127298) -> {'M', [115]};
uts46_map(127299) -> {'M', [116]};
uts46_map(127300) -> {'M', [117]};
uts46_map(127301) -> {'M', [118]};
uts46_map(127302) -> {'M', [119]};
uts46_map(127303) -> {'M', [120]};
uts46_map(127304) -> {'M', [121]};
uts46_map(127305) -> {'M', [122]};
uts46_map(127306) -> {'M', [104,118]};
uts46_map(127307) -> {'M', [109,118]};
uts46_map(127308) -> {'M', [115,100]};
uts46_map(127309) -> {'M', [115,115]};
uts46_map(127310) -> {'M', [112,112,118]};
uts46_map(127311) -> {'M', [119,99]};
uts46_map(127319) -> 'V';
uts46_map(127327) -> 'V';
uts46_map(127338) -> {'M', [109,99]};
uts46_map(127339) -> {'M', [109,100]};
uts46_map(127340) -> {'M', [109,114]};
uts46_map(127353) -> 'V';
uts46_map(127354) -> 'V';
uts46_map(127359) -> 'V';
uts46_map(127376) -> {'M', [100,106]};
uts46_map(127405) -> 'V';
uts46_map(127488) -> {'M', [12411,12363]};
uts46_map(127489) -> {'M', [12467,12467]};
uts46_map(127490) -> {'M', [12469]};
uts46_map(127504) -> {'M', [25163]};
uts46_map(127505) -> {'M', [23383]};
uts46_map(127506) -> {'M', [21452]};
uts46_map(127507) -> {'M', [12487]};
uts46_map(127508) -> {'M', [20108]};
uts46_map(127509) -> {'M', [22810]};
uts46_map(127510) -> {'M', [35299]};
uts46_map(127511) -> {'M', [22825]};
uts46_map(127512) -> {'M', [20132]};
uts46_map(127513) -> {'M', [26144]};
uts46_map(127514) -> {'M', [28961]};
uts46_map(127515) -> {'M', [26009]};
uts46_map(127516) -> {'M', [21069]};
uts46_map(127517) -> {'M', [24460]};
uts46_map(127518) -> {'M', [20877]};
uts46_map(127519) -> {'M', [26032]};
uts46_map(127520) -> {'M', [21021]};
uts46_map(127521) -> {'M', [32066]};
uts46_map(127522) -> {'M', [29983]};
uts46_map(127523) -> {'M', [36009]};
uts46_map(127524) -> {'M', [22768]};
uts46_map(127525) -> {'M', [21561]};
uts46_map(127526) -> {'M', [28436]};
uts46_map(127527) -> {'M', [25237]};
uts46_map(127528) -> {'M', [25429]};
uts46_map(127529) -> {'M', [19968]};
uts46_map(127530) -> {'M', [19977]};
uts46_map(127531) -> {'M', [36938]};
uts46_map(127532) -> {'M', [24038]};
uts46_map(127533) -> {'M', [20013]};
uts46_map(127534) -> {'M', [21491]};
uts46_map(127535) -> {'M', [25351]};
uts46_map(127536) -> {'M', [36208]};
uts46_map(127537) -> {'M', [25171]};
uts46_map(127538) -> {'M', [31105]};
uts46_map(127539) -> {'M', [31354]};
uts46_map(127540) -> {'M', [21512]};
uts46_map(127541) -> {'M', [28288]};
uts46_map(127542) -> {'M', [26377]};
uts46_map(127543) -> {'M', [26376]};
uts46_map(127544) -> {'M', [30003]};
uts46_map(127545) -> {'M', [21106]};
uts46_map(127546) -> {'M', [21942]};
uts46_map(127547) -> {'M', [37197]};
uts46_map(127552) -> {'M', [12308,26412,12309]};
uts46_map(127553) -> {'M', [12308,19977,12309]};
uts46_map(127554) -> {'M', [12308,20108,12309]};
uts46_map(127555) -> {'M', [12308,23433,12309]};
uts46_map(127556) -> {'M', [12308,28857,12309]};
uts46_map(127557) -> {'M', [12308,25171,12309]};
uts46_map(127558) -> {'M', [12308,30423,12309]};
uts46_map(127559) -> {'M', [12308,21213,12309]};
uts46_map(127560) -> {'M', [12308,25943,12309]};
uts46_map(127568) -> {'M', [24471]};
uts46_map(127569) -> {'M', [21487]};
uts46_map(127798) -> 'V';
uts46_map(127869) -> 'V';
uts46_map(127941) -> 'V';
uts46_map(128063) -> 'V';
uts46_map(128064) -> 'V';
uts46_map(128065) -> 'V';
uts46_map(128248) -> 'V';
uts46_map(128255) -> 'V';
uts46_map(128378) -> 'V';
uts46_map(128420) -> 'V';
uts46_map(128512) -> 'V';
uts46_map(128529) -> 'V';
uts46_map(128533) -> 'V';
uts46_map(128534) -> 'V';
uts46_map(128535) -> 'V';
uts46_map(128536) -> 'V';
uts46_map(128537) -> 'V';
uts46_map(128538) -> 'V';
uts46_map(128539) -> 'V';
uts46_map(128543) -> 'V';
uts46_map(128556) -> 'V';
uts46_map(128557) -> 'V';
uts46_map(128564) -> 'V';
uts46_map(128720) -> 'V';
uts46_map(128725) -> 'V';
uts46_map(128761) -> 'V';
uts46_map(128762) -> 'V';
uts46_map(129292) -> 'V';
uts46_map(129311) -> 'V';
uts46_map(129328) -> 'V';
uts46_map(129343) -> 'V';
uts46_map(129356) -> 'V';
uts46_map(129393) -> 'V';
uts46_map(129394) -> 'V';
uts46_map(129401) -> 'X';
uts46_map(129402) -> 'V';
uts46_map(129403) -> 'V';
uts46_map(129472) -> 'V';
uts46_map(129483) -> 'V';
uts46_map(129484) -> 'X';
uts46_map(129652) -> 'V';
uts46_map(129939) -> 'X';
uts46_map(130032) -> {'M', [48]};
uts46_map(130033) -> {'M', [49]};
uts46_map(130034) -> {'M', [50]};
uts46_map(130035) -> {'M', [51]};
uts46_map(130036) -> {'M', [52]};
uts46_map(130037) -> {'M', [53]};
uts46_map(130038) -> {'M', [54]};
uts46_map(130039) -> {'M', [55]};
uts46_map(130040) -> {'M', [56]};
uts46_map(130041) -> {'M', [57]};
uts46_map(194560) -> {'M', [20029]};
uts46_map(194561) -> {'M', [20024]};
uts46_map(194562) -> {'M', [20033]};
uts46_map(194563) -> {'M', [131362]};
uts46_map(194564) -> {'M', [20320]};
uts46_map(194565) -> {'M', [20398]};
uts46_map(194566) -> {'M', [20411]};
uts46_map(194567) -> {'M', [20482]};
uts46_map(194568) -> {'M', [20602]};
uts46_map(194569) -> {'M', [20633]};
uts46_map(194570) -> {'M', [20711]};
uts46_map(194571) -> {'M', [20687]};
uts46_map(194572) -> {'M', [13470]};
uts46_map(194573) -> {'M', [132666]};
uts46_map(194574) -> {'M', [20813]};
uts46_map(194575) -> {'M', [20820]};
uts46_map(194576) -> {'M', [20836]};
uts46_map(194577) -> {'M', [20855]};
uts46_map(194578) -> {'M', [132380]};
uts46_map(194579) -> {'M', [13497]};
uts46_map(194580) -> {'M', [20839]};
uts46_map(194581) -> {'M', [20877]};
uts46_map(194582) -> {'M', [132427]};
uts46_map(194583) -> {'M', [20887]};
uts46_map(194584) -> {'M', [20900]};
uts46_map(194585) -> {'M', [20172]};
uts46_map(194586) -> {'M', [20908]};
uts46_map(194587) -> {'M', [20917]};
uts46_map(194588) -> {'M', [168415]};
uts46_map(194589) -> {'M', [20981]};
uts46_map(194590) -> {'M', [20995]};
uts46_map(194591) -> {'M', [13535]};
uts46_map(194592) -> {'M', [21051]};
uts46_map(194593) -> {'M', [21062]};
uts46_map(194594) -> {'M', [21106]};
uts46_map(194595) -> {'M', [21111]};
uts46_map(194596) -> {'M', [13589]};
uts46_map(194597) -> {'M', [21191]};
uts46_map(194598) -> {'M', [21193]};
uts46_map(194599) -> {'M', [21220]};
uts46_map(194600) -> {'M', [21242]};
uts46_map(194601) -> {'M', [21253]};
uts46_map(194602) -> {'M', [21254]};
uts46_map(194603) -> {'M', [21271]};
uts46_map(194604) -> {'M', [21321]};
uts46_map(194605) -> {'M', [21329]};
uts46_map(194606) -> {'M', [21338]};
uts46_map(194607) -> {'M', [21363]};
uts46_map(194608) -> {'M', [21373]};
uts46_map(194612) -> {'M', [133676]};
uts46_map(194613) -> {'M', [28784]};
uts46_map(194614) -> {'M', [21450]};
uts46_map(194615) -> {'M', [21471]};
uts46_map(194616) -> {'M', [133987]};
uts46_map(194617) -> {'M', [21483]};
uts46_map(194618) -> {'M', [21489]};
uts46_map(194619) -> {'M', [21510]};
uts46_map(194620) -> {'M', [21662]};
uts46_map(194621) -> {'M', [21560]};
uts46_map(194622) -> {'M', [21576]};
uts46_map(194623) -> {'M', [21608]};
uts46_map(194624) -> {'M', [21666]};
uts46_map(194625) -> {'M', [21750]};
uts46_map(194626) -> {'M', [21776]};
uts46_map(194627) -> {'M', [21843]};
uts46_map(194628) -> {'M', [21859]};
uts46_map(194631) -> {'M', [21913]};
uts46_map(194632) -> {'M', [21931]};
uts46_map(194633) -> {'M', [21939]};
uts46_map(194634) -> {'M', [21954]};
uts46_map(194635) -> {'M', [22294]};
uts46_map(194636) -> {'M', [22022]};
uts46_map(194637) -> {'M', [22295]};
uts46_map(194638) -> {'M', [22097]};
uts46_map(194639) -> {'M', [22132]};
uts46_map(194640) -> {'M', [20999]};
uts46_map(194641) -> {'M', [22766]};
uts46_map(194642) -> {'M', [22478]};
uts46_map(194643) -> {'M', [22516]};
uts46_map(194644) -> {'M', [22541]};
uts46_map(194645) -> {'M', [22411]};
uts46_map(194646) -> {'M', [22578]};
uts46_map(194647) -> {'M', [22577]};
uts46_map(194648) -> {'M', [22700]};
uts46_map(194649) -> {'M', [136420]};
uts46_map(194650) -> {'M', [22770]};
uts46_map(194651) -> {'M', [22775]};
uts46_map(194652) -> {'M', [22790]};
uts46_map(194653) -> {'M', [22810]};
uts46_map(194654) -> {'M', [22818]};
uts46_map(194655) -> {'M', [22882]};
uts46_map(194656) -> {'M', [136872]};
uts46_map(194657) -> {'M', [136938]};
uts46_map(194658) -> {'M', [23020]};
uts46_map(194659) -> {'M', [23067]};
uts46_map(194660) -> {'M', [23079]};
uts46_map(194661) -> {'M', [23000]};
uts46_map(194662) -> {'M', [23142]};
uts46_map(194663) -> {'M', [14062]};
uts46_map(194664) -> 'X';
uts46_map(194665) -> {'M', [23304]};
uts46_map(194668) -> {'M', [137672]};
uts46_map(194669) -> {'M', [23491]};
uts46_map(194670) -> {'M', [23512]};
uts46_map(194671) -> {'M', [23527]};
uts46_map(194672) -> {'M', [23539]};
uts46_map(194673) -> {'M', [138008]};
uts46_map(194674) -> {'M', [23551]};
uts46_map(194675) -> {'M', [23558]};
uts46_map(194676) -> 'X';
uts46_map(194677) -> {'M', [23586]};
uts46_map(194678) -> {'M', [14209]};
uts46_map(194679) -> {'M', [23648]};
uts46_map(194680) -> {'M', [23662]};
uts46_map(194681) -> {'M', [23744]};
uts46_map(194682) -> {'M', [23693]};
uts46_map(194683) -> {'M', [138724]};
uts46_map(194684) -> {'M', [23875]};
uts46_map(194685) -> {'M', [138726]};
uts46_map(194686) -> {'M', [23918]};
uts46_map(194687) -> {'M', [23915]};
uts46_map(194688) -> {'M', [23932]};
uts46_map(194689) -> {'M', [24033]};
uts46_map(194690) -> {'M', [24034]};
uts46_map(194691) -> {'M', [14383]};
uts46_map(194692) -> {'M', [24061]};
uts46_map(194693) -> {'M', [24104]};
uts46_map(194694) -> {'M', [24125]};
uts46_map(194695) -> {'M', [24169]};
uts46_map(194696) -> {'M', [14434]};
uts46_map(194697) -> {'M', [139651]};
uts46_map(194698) -> {'M', [14460]};
uts46_map(194699) -> {'M', [24240]};
uts46_map(194700) -> {'M', [24243]};
uts46_map(194701) -> {'M', [24246]};
uts46_map(194702) -> {'M', [24266]};
uts46_map(194703) -> {'M', [172946]};
uts46_map(194704) -> {'M', [24318]};
uts46_map(194707) -> {'M', [33281]};
uts46_map(194710) -> {'M', [14535]};
uts46_map(194711) -> {'M', [144056]};
uts46_map(194712) -> {'M', [156122]};
uts46_map(194713) -> {'M', [24418]};
uts46_map(194714) -> {'M', [24427]};
uts46_map(194715) -> {'M', [14563]};
uts46_map(194716) -> {'M', [24474]};
uts46_map(194717) -> {'M', [24525]};
uts46_map(194718) -> {'M', [24535]};
uts46_map(194719) -> {'M', [24569]};
uts46_map(194720) -> {'M', [24705]};
uts46_map(194721) -> {'M', [14650]};
uts46_map(194722) -> {'M', [14620]};
uts46_map(194723) -> {'M', [24724]};
uts46_map(194724) -> {'M', [141012]};
uts46_map(194725) -> {'M', [24775]};
uts46_map(194726) -> {'M', [24904]};
uts46_map(194727) -> {'M', [24908]};
uts46_map(194728) -> {'M', [24910]};
uts46_map(194729) -> {'M', [24908]};
uts46_map(194730) -> {'M', [24954]};
uts46_map(194731) -> {'M', [24974]};
uts46_map(194732) -> {'M', [25010]};
uts46_map(194733) -> {'M', [24996]};
uts46_map(194734) -> {'M', [25007]};
uts46_map(194735) -> {'M', [25054]};
uts46_map(194736) -> {'M', [25074]};
uts46_map(194737) -> {'M', [25078]};
uts46_map(194738) -> {'M', [25104]};
uts46_map(194739) -> {'M', [25115]};
uts46_map(194740) -> {'M', [25181]};
uts46_map(194741) -> {'M', [25265]};
uts46_map(194742) -> {'M', [25300]};
uts46_map(194743) -> {'M', [25424]};
uts46_map(194744) -> {'M', [142092]};
uts46_map(194745) -> {'M', [25405]};
uts46_map(194746) -> {'M', [25340]};
uts46_map(194747) -> {'M', [25448]};
uts46_map(194748) -> {'M', [25475]};
uts46_map(194749) -> {'M', [25572]};
uts46_map(194750) -> {'M', [142321]};
uts46_map(194751) -> {'M', [25634]};
uts46_map(194752) -> {'M', [25541]};
uts46_map(194753) -> {'M', [25513]};
uts46_map(194754) -> {'M', [14894]};
uts46_map(194755) -> {'M', [25705]};
uts46_map(194756) -> {'M', [25726]};
uts46_map(194757) -> {'M', [25757]};
uts46_map(194758) -> {'M', [25719]};
uts46_map(194759) -> {'M', [14956]};
uts46_map(194760) -> {'M', [25935]};
uts46_map(194761) -> {'M', [25964]};
uts46_map(194762) -> {'M', [143370]};
uts46_map(194763) -> {'M', [26083]};
uts46_map(194764) -> {'M', [26360]};
uts46_map(194765) -> {'M', [26185]};
uts46_map(194766) -> {'M', [15129]};
uts46_map(194767) -> {'M', [26257]};
uts46_map(194768) -> {'M', [15112]};
uts46_map(194769) -> {'M', [15076]};
uts46_map(194770) -> {'M', [20882]};
uts46_map(194771) -> {'M', [20885]};
uts46_map(194772) -> {'M', [26368]};
uts46_map(194773) -> {'M', [26268]};
uts46_map(194774) -> {'M', [32941]};
uts46_map(194775) -> {'M', [17369]};
uts46_map(194776) -> {'M', [26391]};
uts46_map(194777) -> {'M', [26395]};
uts46_map(194778) -> {'M', [26401]};
uts46_map(194779) -> {'M', [26462]};
uts46_map(194780) -> {'M', [26451]};
uts46_map(194781) -> {'M', [144323]};
uts46_map(194782) -> {'M', [15177]};
uts46_map(194783) -> {'M', [26618]};
uts46_map(194784) -> {'M', [26501]};
uts46_map(194785) -> {'M', [26706]};
uts46_map(194786) -> {'M', [26757]};
uts46_map(194787) -> {'M', [144493]};
uts46_map(194788) -> {'M', [26766]};
uts46_map(194789) -> {'M', [26655]};
uts46_map(194790) -> {'M', [26900]};
uts46_map(194791) -> {'M', [15261]};
uts46_map(194792) -> {'M', [26946]};
uts46_map(194793) -> {'M', [27043]};
uts46_map(194794) -> {'M', [27114]};
uts46_map(194795) -> {'M', [27304]};
uts46_map(194796) -> {'M', [145059]};
uts46_map(194797) -> {'M', [27355]};
uts46_map(194798) -> {'M', [15384]};
uts46_map(194799) -> {'M', [27425]};
uts46_map(194800) -> {'M', [145575]};
uts46_map(194801) -> {'M', [27476]};
uts46_map(194802) -> {'M', [15438]};
uts46_map(194803) -> {'M', [27506]};
uts46_map(194804) -> {'M', [27551]};
uts46_map(194805) -> {'M', [27578]};
uts46_map(194806) -> {'M', [27579]};
uts46_map(194807) -> {'M', [146061]};
uts46_map(194808) -> {'M', [138507]};
uts46_map(194809) -> {'M', [146170]};
uts46_map(194810) -> {'M', [27726]};
uts46_map(194811) -> {'M', [146620]};
uts46_map(194812) -> {'M', [27839]};
uts46_map(194813) -> {'M', [27853]};
uts46_map(194814) -> {'M', [27751]};
uts46_map(194815) -> {'M', [27926]};
uts46_map(194816) -> {'M', [27966]};
uts46_map(194817) -> {'M', [28023]};
uts46_map(194818) -> {'M', [27969]};
uts46_map(194819) -> {'M', [28009]};
uts46_map(194820) -> {'M', [28024]};
uts46_map(194821) -> {'M', [28037]};
uts46_map(194822) -> {'M', [146718]};
uts46_map(194823) -> {'M', [27956]};
uts46_map(194824) -> {'M', [28207]};
uts46_map(194825) -> {'M', [28270]};
uts46_map(194826) -> {'M', [15667]};
uts46_map(194827) -> {'M', [28363]};
uts46_map(194828) -> {'M', [28359]};
uts46_map(194829) -> {'M', [147153]};
uts46_map(194830) -> {'M', [28153]};
uts46_map(194831) -> {'M', [28526]};
uts46_map(194832) -> {'M', [147294]};
uts46_map(194833) -> {'M', [147342]};
uts46_map(194834) -> {'M', [28614]};
uts46_map(194835) -> {'M', [28729]};
uts46_map(194836) -> {'M', [28702]};
uts46_map(194837) -> {'M', [28699]};
uts46_map(194838) -> {'M', [15766]};
uts46_map(194839) -> {'M', [28746]};
uts46_map(194840) -> {'M', [28797]};
uts46_map(194841) -> {'M', [28791]};
uts46_map(194842) -> {'M', [28845]};
uts46_map(194843) -> {'M', [132389]};
uts46_map(194844) -> {'M', [28997]};
uts46_map(194845) -> {'M', [148067]};
uts46_map(194846) -> {'M', [29084]};
uts46_map(194847) -> 'X';
uts46_map(194848) -> {'M', [29224]};
uts46_map(194849) -> {'M', [29237]};
uts46_map(194850) -> {'M', [29264]};
uts46_map(194851) -> {'M', [149000]};
uts46_map(194852) -> {'M', [29312]};
uts46_map(194853) -> {'M', [29333]};
uts46_map(194854) -> {'M', [149301]};
uts46_map(194855) -> {'M', [149524]};
uts46_map(194856) -> {'M', [29562]};
uts46_map(194857) -> {'M', [29579]};
uts46_map(194858) -> {'M', [16044]};
uts46_map(194859) -> {'M', [29605]};
uts46_map(194862) -> {'M', [29767]};
uts46_map(194863) -> {'M', [29788]};
uts46_map(194864) -> {'M', [29809]};
uts46_map(194865) -> {'M', [29829]};
uts46_map(194866) -> {'M', [29898]};
uts46_map(194867) -> {'M', [16155]};
uts46_map(194868) -> {'M', [29988]};
uts46_map(194869) -> {'M', [150582]};
uts46_map(194870) -> {'M', [30014]};
uts46_map(194871) -> {'M', [150674]};
uts46_map(194872) -> {'M', [30064]};
uts46_map(194873) -> {'M', [139679]};
uts46_map(194874) -> {'M', [30224]};
uts46_map(194875) -> {'M', [151457]};
uts46_map(194876) -> {'M', [151480]};
uts46_map(194877) -> {'M', [151620]};
uts46_map(194878) -> {'M', [16380]};
uts46_map(194879) -> {'M', [16392]};
uts46_map(194880) -> {'M', [30452]};
uts46_map(194881) -> {'M', [151795]};
uts46_map(194882) -> {'M', [151794]};
uts46_map(194883) -> {'M', [151833]};
uts46_map(194884) -> {'M', [151859]};
uts46_map(194885) -> {'M', [30494]};
uts46_map(194888) -> {'M', [30538]};
uts46_map(194889) -> {'M', [16441]};
uts46_map(194890) -> {'M', [30603]};
uts46_map(194891) -> {'M', [16454]};
uts46_map(194892) -> {'M', [16534]};
uts46_map(194893) -> {'M', [152605]};
uts46_map(194894) -> {'M', [30798]};
uts46_map(194895) -> {'M', [30860]};
uts46_map(194896) -> {'M', [30924]};
uts46_map(194897) -> {'M', [16611]};
uts46_map(194898) -> {'M', [153126]};
uts46_map(194899) -> {'M', [31062]};
uts46_map(194900) -> {'M', [153242]};
uts46_map(194901) -> {'M', [153285]};
uts46_map(194902) -> {'M', [31119]};
uts46_map(194903) -> {'M', [31211]};
uts46_map(194904) -> {'M', [16687]};
uts46_map(194905) -> {'M', [31296]};
uts46_map(194906) -> {'M', [31306]};
uts46_map(194907) -> {'M', [31311]};
uts46_map(194908) -> {'M', [153980]};
uts46_map(194911) -> 'X';
uts46_map(194912) -> {'M', [16898]};
uts46_map(194913) -> {'M', [154539]};
uts46_map(194914) -> {'M', [31686]};
uts46_map(194915) -> {'M', [31689]};
uts46_map(194916) -> {'M', [16935]};
uts46_map(194917) -> {'M', [154752]};
uts46_map(194918) -> {'M', [31954]};
uts46_map(194919) -> {'M', [17056]};
uts46_map(194920) -> {'M', [31976]};
uts46_map(194921) -> {'M', [31971]};
uts46_map(194922) -> {'M', [32000]};
uts46_map(194923) -> {'M', [155526]};
uts46_map(194924) -> {'M', [32099]};
uts46_map(194925) -> {'M', [17153]};
uts46_map(194926) -> {'M', [32199]};
uts46_map(194927) -> {'M', [32258]};
uts46_map(194928) -> {'M', [32325]};
uts46_map(194929) -> {'M', [17204]};
uts46_map(194930) -> {'M', [156200]};
uts46_map(194931) -> {'M', [156231]};
uts46_map(194932) -> {'M', [17241]};
uts46_map(194933) -> {'M', [156377]};
uts46_map(194934) -> {'M', [32634]};
uts46_map(194935) -> {'M', [156478]};
uts46_map(194936) -> {'M', [32661]};
uts46_map(194937) -> {'M', [32762]};
uts46_map(194938) -> {'M', [32773]};
uts46_map(194939) -> {'M', [156890]};
uts46_map(194940) -> {'M', [156963]};
uts46_map(194941) -> {'M', [32864]};
uts46_map(194942) -> {'M', [157096]};
uts46_map(194943) -> {'M', [32880]};
uts46_map(194944) -> {'M', [144223]};
uts46_map(194945) -> {'M', [17365]};
uts46_map(194946) -> {'M', [32946]};
uts46_map(194947) -> {'M', [33027]};
uts46_map(194948) -> {'M', [17419]};
uts46_map(194949) -> {'M', [33086]};
uts46_map(194950) -> {'M', [23221]};
uts46_map(194951) -> {'M', [157607]};
uts46_map(194952) -> {'M', [157621]};
uts46_map(194953) -> {'M', [144275]};
uts46_map(194954) -> {'M', [144284]};
uts46_map(194955) -> {'M', [33281]};
uts46_map(194956) -> {'M', [33284]};
uts46_map(194957) -> {'M', [36766]};
uts46_map(194958) -> {'M', [17515]};
uts46_map(194959) -> {'M', [33425]};
uts46_map(194960) -> {'M', [33419]};
uts46_map(194961) -> {'M', [33437]};
uts46_map(194962) -> {'M', [21171]};
uts46_map(194963) -> {'M', [33457]};
uts46_map(194964) -> {'M', [33459]};
uts46_map(194965) -> {'M', [33469]};
uts46_map(194966) -> {'M', [33510]};
uts46_map(194967) -> {'M', [158524]};
uts46_map(194968) -> {'M', [33509]};
uts46_map(194969) -> {'M', [33565]};
uts46_map(194970) -> {'M', [33635]};
uts46_map(194971) -> {'M', [33709]};
uts46_map(194972) -> {'M', [33571]};
uts46_map(194973) -> {'M', [33725]};
uts46_map(194974) -> {'M', [33767]};
uts46_map(194975) -> {'M', [33879]};
uts46_map(194976) -> {'M', [33619]};
uts46_map(194977) -> {'M', [33738]};
uts46_map(194978) -> {'M', [33740]};
uts46_map(194979) -> {'M', [33756]};
uts46_map(194980) -> {'M', [158774]};
uts46_map(194981) -> {'M', [159083]};
uts46_map(194982) -> {'M', [158933]};
uts46_map(194983) -> {'M', [17707]};
uts46_map(194984) -> {'M', [34033]};
uts46_map(194985) -> {'M', [34035]};
uts46_map(194986) -> {'M', [34070]};
uts46_map(194987) -> {'M', [160714]};
uts46_map(194988) -> {'M', [34148]};
uts46_map(194989) -> {'M', [159532]};
uts46_map(194990) -> {'M', [17757]};
uts46_map(194991) -> {'M', [17761]};
uts46_map(194992) -> {'M', [159665]};
uts46_map(194993) -> {'M', [159954]};
uts46_map(194994) -> {'M', [17771]};
uts46_map(194995) -> {'M', [34384]};
uts46_map(194996) -> {'M', [34396]};
uts46_map(194997) -> {'M', [34407]};
uts46_map(194998) -> {'M', [34409]};
uts46_map(194999) -> {'M', [34473]};
uts46_map(195000) -> {'M', [34440]};
uts46_map(195001) -> {'M', [34574]};
uts46_map(195002) -> {'M', [34530]};
uts46_map(195003) -> {'M', [34681]};
uts46_map(195004) -> {'M', [34600]};
uts46_map(195005) -> {'M', [34667]};
uts46_map(195006) -> {'M', [34694]};
uts46_map(195007) -> 'X';
uts46_map(195008) -> {'M', [34785]};
uts46_map(195009) -> {'M', [34817]};
uts46_map(195010) -> {'M', [17913]};
uts46_map(195011) -> {'M', [34912]};
uts46_map(195012) -> {'M', [34915]};
uts46_map(195013) -> {'M', [161383]};
uts46_map(195014) -> {'M', [35031]};
uts46_map(195015) -> {'M', [35038]};
uts46_map(195016) -> {'M', [17973]};
uts46_map(195017) -> {'M', [35066]};
uts46_map(195018) -> {'M', [13499]};
uts46_map(195019) -> {'M', [161966]};
uts46_map(195020) -> {'M', [162150]};
uts46_map(195021) -> {'M', [18110]};
uts46_map(195022) -> {'M', [18119]};
uts46_map(195023) -> {'M', [35488]};
uts46_map(195024) -> {'M', [35565]};
uts46_map(195025) -> {'M', [35722]};
uts46_map(195026) -> {'M', [35925]};
uts46_map(195027) -> {'M', [162984]};
uts46_map(195028) -> {'M', [36011]};
uts46_map(195029) -> {'M', [36033]};
uts46_map(195030) -> {'M', [36123]};
uts46_map(195031) -> {'M', [36215]};
uts46_map(195032) -> {'M', [163631]};
uts46_map(195033) -> {'M', [133124]};
uts46_map(195034) -> {'M', [36299]};
uts46_map(195035) -> {'M', [36284]};
uts46_map(195036) -> {'M', [36336]};
uts46_map(195037) -> {'M', [133342]};
uts46_map(195038) -> {'M', [36564]};
uts46_map(195039) -> {'M', [36664]};
uts46_map(195040) -> {'M', [165330]};
uts46_map(195041) -> {'M', [165357]};
uts46_map(195042) -> {'M', [37012]};
uts46_map(195043) -> {'M', [37105]};
uts46_map(195044) -> {'M', [37137]};
uts46_map(195045) -> {'M', [165678]};
uts46_map(195046) -> {'M', [37147]};
uts46_map(195047) -> {'M', [37432]};
uts46_map(195048) -> {'M', [37591]};
uts46_map(195049) -> {'M', [37592]};
uts46_map(195050) -> {'M', [37500]};
uts46_map(195051) -> {'M', [37881]};
uts46_map(195052) -> {'M', [37909]};
uts46_map(195053) -> {'M', [166906]};
uts46_map(195054) -> {'M', [38283]};
uts46_map(195055) -> {'M', [18837]};
uts46_map(195056) -> {'M', [38327]};
uts46_map(195057) -> {'M', [167287]};
uts46_map(195058) -> {'M', [18918]};
uts46_map(195059) -> {'M', [38595]};
uts46_map(195060) -> {'M', [23986]};
uts46_map(195061) -> {'M', [38691]};
uts46_map(195062) -> {'M', [168261]};
uts46_map(195063) -> {'M', [168474]};
uts46_map(195064) -> {'M', [19054]};
uts46_map(195065) -> {'M', [19062]};
uts46_map(195066) -> {'M', [38880]};
uts46_map(195067) -> {'M', [168970]};
uts46_map(195068) -> {'M', [19122]};
uts46_map(195069) -> {'M', [169110]};
uts46_map(195072) -> {'M', [38953]};
uts46_map(195073) -> {'M', [169398]};
uts46_map(195074) -> {'M', [39138]};
uts46_map(195075) -> {'M', [19251]};
uts46_map(195076) -> {'M', [39209]};
uts46_map(195077) -> {'M', [39335]};
uts46_map(195078) -> {'M', [39362]};
uts46_map(195079) -> {'M', [39422]};
uts46_map(195080) -> {'M', [19406]};
uts46_map(195081) -> {'M', [170800]};
uts46_map(195082) -> {'M', [39698]};
uts46_map(195083) -> {'M', [40000]};
uts46_map(195084) -> {'M', [40189]};
uts46_map(195085) -> {'M', [19662]};
uts46_map(195086) -> {'M', [19693]};
uts46_map(195087) -> {'M', [40295]};
uts46_map(195088) -> {'M', [172238]};
uts46_map(195089) -> {'M', [19704]};
uts46_map(195090) -> {'M', [172293]};
uts46_map(195091) -> {'M', [172558]};
uts46_map(195092) -> {'M', [172689]};
uts46_map(195093) -> {'M', [40635]};
uts46_map(195094) -> {'M', [19798]};
uts46_map(195095) -> {'M', [40697]};
uts46_map(195096) -> {'M', [40702]};
uts46_map(195097) -> {'M', [40709]};
uts46_map(195098) -> {'M', [40719]};
uts46_map(195099) -> {'M', [40726]};
uts46_map(195100) -> {'M', [40763]};
uts46_map(195101) -> {'M', [173568]};
uts46_map(917504) -> 'X';
uts46_map(917505) -> 'X';
uts46_map(CP) when 0 =< CP, CP =< 44 -> '3';
uts46_map(CP) when 45 =< CP, CP =< 46 -> 'V';
uts46_map(CP) when 48 =< CP, CP =< 57 -> 'V';
uts46_map(CP) when 58 =< CP, CP =< 64 -> '3';
uts46_map(CP) when 91 =< CP, CP =< 96 -> '3';
uts46_map(CP) when 97 =< CP, CP =< 122 -> 'V';
uts46_map(CP) when 123 =< CP, CP =< 127 -> '3';
uts46_map(CP) when 128 =< CP, CP =< 159 -> 'X';
uts46_map(CP) when 161 =< CP, CP =< 167 -> 'V';
uts46_map(CP) when 171 =< CP, CP =< 172 -> 'V';
uts46_map(CP) when 176 =< CP, CP =< 177 -> 'V';
uts46_map(CP) when 224 =< CP, CP =< 246 -> 'V';
uts46_map(CP) when 248 =< CP, CP =< 255 -> 'V';
uts46_map(CP) when 306 =< CP, CP =< 307 -> {'M', [105,106]};
uts46_map(CP) when 311 =< CP, CP =< 312 -> 'V';
uts46_map(CP) when 319 =< CP, CP =< 320 -> {'M', [108,183]};
uts46_map(CP) when 396 =< CP, CP =< 397 -> 'V';
uts46_map(CP) when 409 =< CP, CP =< 411 -> 'V';
uts46_map(CP) when 426 =< CP, CP =< 427 -> 'V';
uts46_map(CP) when 441 =< CP, CP =< 443 -> 'V';
uts46_map(CP) when 445 =< CP, CP =< 451 -> 'V';
uts46_map(CP) when 452 =< CP, CP =< 454 -> {'M', [100,382]};
uts46_map(CP) when 455 =< CP, CP =< 457 -> {'M', [108,106]};
uts46_map(CP) when 458 =< CP, CP =< 460 -> {'M', [110,106]};
uts46_map(CP) when 476 =< CP, CP =< 477 -> 'V';
uts46_map(CP) when 495 =< CP, CP =< 496 -> 'V';
uts46_map(CP) when 497 =< CP, CP =< 499 -> {'M', [100,122]};
uts46_map(CP) when 564 =< CP, CP =< 566 -> 'V';
uts46_map(CP) when 567 =< CP, CP =< 569 -> 'V';
uts46_map(CP) when 575 =< CP, CP =< 576 -> 'V';
uts46_map(CP) when 592 =< CP, CP =< 680 -> 'V';
uts46_map(CP) when 681 =< CP, CP =< 685 -> 'V';
uts46_map(CP) when 686 =< CP, CP =< 687 -> 'V';
uts46_map(CP) when 697 =< CP, CP =< 705 -> 'V';
uts46_map(CP) when 706 =< CP, CP =< 709 -> 'V';
uts46_map(CP) when 710 =< CP, CP =< 721 -> 'V';
uts46_map(CP) when 722 =< CP, CP =< 727 -> 'V';
uts46_map(CP) when 741 =< CP, CP =< 745 -> 'V';
uts46_map(CP) when 746 =< CP, CP =< 747 -> 'V';
uts46_map(CP) when 751 =< CP, CP =< 767 -> 'V';
uts46_map(CP) when 768 =< CP, CP =< 831 -> 'V';
uts46_map(CP) when 838 =< CP, CP =< 846 -> 'V';
uts46_map(CP) when 848 =< CP, CP =< 855 -> 'V';
uts46_map(CP) when 856 =< CP, CP =< 860 -> 'V';
uts46_map(CP) when 861 =< CP, CP =< 863 -> 'V';
uts46_map(CP) when 864 =< CP, CP =< 865 -> 'V';
uts46_map(CP) when 867 =< CP, CP =< 879 -> 'V';
uts46_map(CP) when 888 =< CP, CP =< 889 -> 'X';
uts46_map(CP) when 891 =< CP, CP =< 893 -> 'V';
uts46_map(CP) when 896 =< CP, CP =< 899 -> 'X';
uts46_map(CP) when 940 =< CP, CP =< 961 -> 'V';
uts46_map(CP) when 963 =< CP, CP =< 974 -> 'V';
uts46_map(CP) when 1072 =< CP, CP =< 1103 -> 'V';
uts46_map(CP) when 1105 =< CP, CP =< 1116 -> 'V';
uts46_map(CP) when 1118 =< CP, CP =< 1119 -> 'V';
uts46_map(CP) when 1155 =< CP, CP =< 1158 -> 'V';
uts46_map(CP) when 1160 =< CP, CP =< 1161 -> 'V';
uts46_map(CP) when 1367 =< CP, CP =< 1368 -> 'X';
uts46_map(CP) when 1370 =< CP, CP =< 1375 -> 'V';
uts46_map(CP) when 1377 =< CP, CP =< 1414 -> 'V';
uts46_map(CP) when 1419 =< CP, CP =< 1420 -> 'X';
uts46_map(CP) when 1421 =< CP, CP =< 1422 -> 'V';
uts46_map(CP) when 1425 =< CP, CP =< 1441 -> 'V';
uts46_map(CP) when 1443 =< CP, CP =< 1455 -> 'V';
uts46_map(CP) when 1456 =< CP, CP =< 1465 -> 'V';
uts46_map(CP) when 1467 =< CP, CP =< 1469 -> 'V';
uts46_map(CP) when 1473 =< CP, CP =< 1474 -> 'V';
uts46_map(CP) when 1480 =< CP, CP =< 1487 -> 'X';
uts46_map(CP) when 1488 =< CP, CP =< 1514 -> 'V';
uts46_map(CP) when 1515 =< CP, CP =< 1518 -> 'X';
uts46_map(CP) when 1520 =< CP, CP =< 1524 -> 'V';
uts46_map(CP) when 1525 =< CP, CP =< 1535 -> 'X';
uts46_map(CP) when 1536 =< CP, CP =< 1539 -> 'X';
uts46_map(CP) when 1542 =< CP, CP =< 1546 -> 'V';
uts46_map(CP) when 1549 =< CP, CP =< 1551 -> 'V';
uts46_map(CP) when 1552 =< CP, CP =< 1557 -> 'V';
uts46_map(CP) when 1558 =< CP, CP =< 1562 -> 'V';
uts46_map(CP) when 1569 =< CP, CP =< 1594 -> 'V';
uts46_map(CP) when 1595 =< CP, CP =< 1599 -> 'V';
uts46_map(CP) when 1601 =< CP, CP =< 1618 -> 'V';
uts46_map(CP) when 1619 =< CP, CP =< 1621 -> 'V';
uts46_map(CP) when 1622 =< CP, CP =< 1624 -> 'V';
uts46_map(CP) when 1625 =< CP, CP =< 1630 -> 'V';
uts46_map(CP) when 1632 =< CP, CP =< 1641 -> 'V';
uts46_map(CP) when 1642 =< CP, CP =< 1645 -> 'V';
uts46_map(CP) when 1646 =< CP, CP =< 1647 -> 'V';
uts46_map(CP) when 1648 =< CP, CP =< 1652 -> 'V';
uts46_map(CP) when 1657 =< CP, CP =< 1719 -> 'V';
uts46_map(CP) when 1720 =< CP, CP =< 1721 -> 'V';
uts46_map(CP) when 1722 =< CP, CP =< 1726 -> 'V';
uts46_map(CP) when 1728 =< CP, CP =< 1742 -> 'V';
uts46_map(CP) when 1744 =< CP, CP =< 1747 -> 'V';
uts46_map(CP) when 1749 =< CP, CP =< 1756 -> 'V';
uts46_map(CP) when 1759 =< CP, CP =< 1768 -> 'V';
uts46_map(CP) when 1770 =< CP, CP =< 1773 -> 'V';
uts46_map(CP) when 1774 =< CP, CP =< 1775 -> 'V';
uts46_map(CP) when 1776 =< CP, CP =< 1785 -> 'V';
uts46_map(CP) when 1786 =< CP, CP =< 1790 -> 'V';
uts46_map(CP) when 1792 =< CP, CP =< 1805 -> 'V';
uts46_map(CP) when 1808 =< CP, CP =< 1836 -> 'V';
uts46_map(CP) when 1837 =< CP, CP =< 1839 -> 'V';
uts46_map(CP) when 1840 =< CP, CP =< 1866 -> 'V';
uts46_map(CP) when 1867 =< CP, CP =< 1868 -> 'X';
uts46_map(CP) when 1869 =< CP, CP =< 1871 -> 'V';
uts46_map(CP) when 1872 =< CP, CP =< 1901 -> 'V';
uts46_map(CP) when 1902 =< CP, CP =< 1919 -> 'V';
uts46_map(CP) when 1920 =< CP, CP =< 1968 -> 'V';
uts46_map(CP) when 1970 =< CP, CP =< 1983 -> 'X';
uts46_map(CP) when 1984 =< CP, CP =< 2037 -> 'V';
uts46_map(CP) when 2038 =< CP, CP =< 2042 -> 'V';
uts46_map(CP) when 2043 =< CP, CP =< 2044 -> 'X';
uts46_map(CP) when 2046 =< CP, CP =< 2047 -> 'V';
uts46_map(CP) when 2048 =< CP, CP =< 2093 -> 'V';
uts46_map(CP) when 2094 =< CP, CP =< 2095 -> 'X';
uts46_map(CP) when 2096 =< CP, CP =< 2110 -> 'V';
uts46_map(CP) when 2112 =< CP, CP =< 2139 -> 'V';
uts46_map(CP) when 2140 =< CP, CP =< 2141 -> 'X';
uts46_map(CP) when 2144 =< CP, CP =< 2154 -> 'V';
uts46_map(CP) when 2155 =< CP, CP =< 2207 -> 'X';
uts46_map(CP) when 2210 =< CP, CP =< 2220 -> 'V';
uts46_map(CP) when 2221 =< CP, CP =< 2226 -> 'V';
uts46_map(CP) when 2227 =< CP, CP =< 2228 -> 'V';
uts46_map(CP) when 2230 =< CP, CP =< 2237 -> 'V';
uts46_map(CP) when 2238 =< CP, CP =< 2247 -> 'V';
uts46_map(CP) when 2248 =< CP, CP =< 2258 -> 'X';
uts46_map(CP) when 2260 =< CP, CP =< 2273 -> 'V';
uts46_map(CP) when 2276 =< CP, CP =< 2302 -> 'V';
uts46_map(CP) when 2305 =< CP, CP =< 2307 -> 'V';
uts46_map(CP) when 2309 =< CP, CP =< 2361 -> 'V';
uts46_map(CP) when 2362 =< CP, CP =< 2363 -> 'V';
uts46_map(CP) when 2364 =< CP, CP =< 2381 -> 'V';
uts46_map(CP) when 2384 =< CP, CP =< 2388 -> 'V';
uts46_map(CP) when 2390 =< CP, CP =< 2391 -> 'V';
uts46_map(CP) when 2400 =< CP, CP =< 2403 -> 'V';
uts46_map(CP) when 2404 =< CP, CP =< 2405 -> 'V';
uts46_map(CP) when 2406 =< CP, CP =< 2415 -> 'V';
uts46_map(CP) when 2417 =< CP, CP =< 2418 -> 'V';
uts46_map(CP) when 2419 =< CP, CP =< 2423 -> 'V';
uts46_map(CP) when 2425 =< CP, CP =< 2426 -> 'V';
uts46_map(CP) when 2427 =< CP, CP =< 2428 -> 'V';
uts46_map(CP) when 2430 =< CP, CP =< 2431 -> 'V';
uts46_map(CP) when 2433 =< CP, CP =< 2435 -> 'V';
uts46_map(CP) when 2437 =< CP, CP =< 2444 -> 'V';
uts46_map(CP) when 2445 =< CP, CP =< 2446 -> 'X';
uts46_map(CP) when 2447 =< CP, CP =< 2448 -> 'V';
uts46_map(CP) when 2449 =< CP, CP =< 2450 -> 'X';
uts46_map(CP) when 2451 =< CP, CP =< 2472 -> 'V';
uts46_map(CP) when 2474 =< CP, CP =< 2480 -> 'V';
uts46_map(CP) when 2483 =< CP, CP =< 2485 -> 'X';
uts46_map(CP) when 2486 =< CP, CP =< 2489 -> 'V';
uts46_map(CP) when 2490 =< CP, CP =< 2491 -> 'X';
uts46_map(CP) when 2494 =< CP, CP =< 2500 -> 'V';
uts46_map(CP) when 2501 =< CP, CP =< 2502 -> 'X';
uts46_map(CP) when 2503 =< CP, CP =< 2504 -> 'V';
uts46_map(CP) when 2505 =< CP, CP =< 2506 -> 'X';
uts46_map(CP) when 2507 =< CP, CP =< 2509 -> 'V';
uts46_map(CP) when 2511 =< CP, CP =< 2518 -> 'X';
uts46_map(CP) when 2520 =< CP, CP =< 2523 -> 'X';
uts46_map(CP) when 2528 =< CP, CP =< 2531 -> 'V';
uts46_map(CP) when 2532 =< CP, CP =< 2533 -> 'X';
uts46_map(CP) when 2534 =< CP, CP =< 2545 -> 'V';
uts46_map(CP) when 2546 =< CP, CP =< 2554 -> 'V';
uts46_map(CP) when 2559 =< CP, CP =< 2560 -> 'X';
uts46_map(CP) when 2565 =< CP, CP =< 2570 -> 'V';
uts46_map(CP) when 2571 =< CP, CP =< 2574 -> 'X';
uts46_map(CP) when 2575 =< CP, CP =< 2576 -> 'V';
uts46_map(CP) when 2577 =< CP, CP =< 2578 -> 'X';
uts46_map(CP) when 2579 =< CP, CP =< 2600 -> 'V';
uts46_map(CP) when 2602 =< CP, CP =< 2608 -> 'V';
uts46_map(CP) when 2616 =< CP, CP =< 2617 -> 'V';
uts46_map(CP) when 2618 =< CP, CP =< 2619 -> 'X';
uts46_map(CP) when 2622 =< CP, CP =< 2626 -> 'V';
uts46_map(CP) when 2627 =< CP, CP =< 2630 -> 'X';
uts46_map(CP) when 2631 =< CP, CP =< 2632 -> 'V';
uts46_map(CP) when 2633 =< CP, CP =< 2634 -> 'X';
uts46_map(CP) when 2635 =< CP, CP =< 2637 -> 'V';
uts46_map(CP) when 2638 =< CP, CP =< 2640 -> 'X';
uts46_map(CP) when 2642 =< CP, CP =< 2648 -> 'X';
uts46_map(CP) when 2655 =< CP, CP =< 2661 -> 'X';
uts46_map(CP) when 2662 =< CP, CP =< 2676 -> 'V';
uts46_map(CP) when 2679 =< CP, CP =< 2688 -> 'X';
uts46_map(CP) when 2689 =< CP, CP =< 2691 -> 'V';
uts46_map(CP) when 2693 =< CP, CP =< 2699 -> 'V';
uts46_map(CP) when 2703 =< CP, CP =< 2705 -> 'V';
uts46_map(CP) when 2707 =< CP, CP =< 2728 -> 'V';
uts46_map(CP) when 2730 =< CP, CP =< 2736 -> 'V';
uts46_map(CP) when 2738 =< CP, CP =< 2739 -> 'V';
uts46_map(CP) when 2741 =< CP, CP =< 2745 -> 'V';
uts46_map(CP) when 2746 =< CP, CP =< 2747 -> 'X';
uts46_map(CP) when 2748 =< CP, CP =< 2757 -> 'V';
uts46_map(CP) when 2759 =< CP, CP =< 2761 -> 'V';
uts46_map(CP) when 2763 =< CP, CP =< 2765 -> 'V';
uts46_map(CP) when 2766 =< CP, CP =< 2767 -> 'X';
uts46_map(CP) when 2769 =< CP, CP =< 2783 -> 'X';
uts46_map(CP) when 2785 =< CP, CP =< 2787 -> 'V';
uts46_map(CP) when 2788 =< CP, CP =< 2789 -> 'X';
uts46_map(CP) when 2790 =< CP, CP =< 2799 -> 'V';
uts46_map(CP) when 2802 =< CP, CP =< 2808 -> 'X';
uts46_map(CP) when 2810 =< CP, CP =< 2815 -> 'V';
uts46_map(CP) when 2817 =< CP, CP =< 2819 -> 'V';
uts46_map(CP) when 2821 =< CP, CP =< 2828 -> 'V';
uts46_map(CP) when 2829 =< CP, CP =< 2830 -> 'X';
uts46_map(CP) when 2831 =< CP, CP =< 2832 -> 'V';
uts46_map(CP) when 2833 =< CP, CP =< 2834 -> 'X';
uts46_map(CP) when 2835 =< CP, CP =< 2856 -> 'V';
uts46_map(CP) when 2858 =< CP, CP =< 2864 -> 'V';
uts46_map(CP) when 2866 =< CP, CP =< 2867 -> 'V';
uts46_map(CP) when 2870 =< CP, CP =< 2873 -> 'V';
uts46_map(CP) when 2874 =< CP, CP =< 2875 -> 'X';
uts46_map(CP) when 2876 =< CP, CP =< 2883 -> 'V';
uts46_map(CP) when 2885 =< CP, CP =< 2886 -> 'X';
uts46_map(CP) when 2887 =< CP, CP =< 2888 -> 'V';
uts46_map(CP) when 2889 =< CP, CP =< 2890 -> 'X';
uts46_map(CP) when 2891 =< CP, CP =< 2893 -> 'V';
uts46_map(CP) when 2894 =< CP, CP =< 2900 -> 'X';
uts46_map(CP) when 2902 =< CP, CP =< 2903 -> 'V';
uts46_map(CP) when 2904 =< CP, CP =< 2907 -> 'X';
uts46_map(CP) when 2911 =< CP, CP =< 2913 -> 'V';
uts46_map(CP) when 2914 =< CP, CP =< 2915 -> 'V';
uts46_map(CP) when 2916 =< CP, CP =< 2917 -> 'X';
uts46_map(CP) when 2918 =< CP, CP =< 2927 -> 'V';
uts46_map(CP) when 2930 =< CP, CP =< 2935 -> 'V';
uts46_map(CP) when 2936 =< CP, CP =< 2945 -> 'X';
uts46_map(CP) when 2946 =< CP, CP =< 2947 -> 'V';
uts46_map(CP) when 2949 =< CP, CP =< 2954 -> 'V';
uts46_map(CP) when 2955 =< CP, CP =< 2957 -> 'X';
uts46_map(CP) when 2958 =< CP, CP =< 2960 -> 'V';
uts46_map(CP) when 2962 =< CP, CP =< 2965 -> 'V';
uts46_map(CP) when 2966 =< CP, CP =< 2968 -> 'X';
uts46_map(CP) when 2969 =< CP, CP =< 2970 -> 'V';
uts46_map(CP) when 2974 =< CP, CP =< 2975 -> 'V';
uts46_map(CP) when 2976 =< CP, CP =< 2978 -> 'X';
uts46_map(CP) when 2979 =< CP, CP =< 2980 -> 'V';
uts46_map(CP) when 2981 =< CP, CP =< 2983 -> 'X';
uts46_map(CP) when 2984 =< CP, CP =< 2986 -> 'V';
uts46_map(CP) when 2987 =< CP, CP =< 2989 -> 'X';
uts46_map(CP) when 2990 =< CP, CP =< 2997 -> 'V';
uts46_map(CP) when 2999 =< CP, CP =< 3001 -> 'V';
uts46_map(CP) when 3002 =< CP, CP =< 3005 -> 'X';
uts46_map(CP) when 3006 =< CP, CP =< 3010 -> 'V';
uts46_map(CP) when 3011 =< CP, CP =< 3013 -> 'X';
uts46_map(CP) when 3014 =< CP, CP =< 3016 -> 'V';
uts46_map(CP) when 3018 =< CP, CP =< 3021 -> 'V';
uts46_map(CP) when 3022 =< CP, CP =< 3023 -> 'X';
uts46_map(CP) when 3025 =< CP, CP =< 3030 -> 'X';
uts46_map(CP) when 3032 =< CP, CP =< 3045 -> 'X';
uts46_map(CP) when 3047 =< CP, CP =< 3055 -> 'V';
uts46_map(CP) when 3056 =< CP, CP =< 3058 -> 'V';
uts46_map(CP) when 3059 =< CP, CP =< 3066 -> 'V';
uts46_map(CP) when 3067 =< CP, CP =< 3071 -> 'X';
uts46_map(CP) when 3073 =< CP, CP =< 3075 -> 'V';
uts46_map(CP) when 3077 =< CP, CP =< 3084 -> 'V';
uts46_map(CP) when 3086 =< CP, CP =< 3088 -> 'V';
uts46_map(CP) when 3090 =< CP, CP =< 3112 -> 'V';
uts46_map(CP) when 3114 =< CP, CP =< 3123 -> 'V';
uts46_map(CP) when 3125 =< CP, CP =< 3129 -> 'V';
uts46_map(CP) when 3130 =< CP, CP =< 3132 -> 'X';
uts46_map(CP) when 3134 =< CP, CP =< 3140 -> 'V';
uts46_map(CP) when 3142 =< CP, CP =< 3144 -> 'V';
uts46_map(CP) when 3146 =< CP, CP =< 3149 -> 'V';
uts46_map(CP) when 3150 =< CP, CP =< 3156 -> 'X';
uts46_map(CP) when 3157 =< CP, CP =< 3158 -> 'V';
uts46_map(CP) when 3160 =< CP, CP =< 3161 -> 'V';
uts46_map(CP) when 3163 =< CP, CP =< 3167 -> 'X';
uts46_map(CP) when 3168 =< CP, CP =< 3169 -> 'V';
uts46_map(CP) when 3170 =< CP, CP =< 3171 -> 'V';
uts46_map(CP) when 3172 =< CP, CP =< 3173 -> 'X';
uts46_map(CP) when 3174 =< CP, CP =< 3183 -> 'V';
uts46_map(CP) when 3184 =< CP, CP =< 3190 -> 'X';
uts46_map(CP) when 3192 =< CP, CP =< 3199 -> 'V';
uts46_map(CP) when 3202 =< CP, CP =< 3203 -> 'V';
uts46_map(CP) when 3205 =< CP, CP =< 3212 -> 'V';
uts46_map(CP) when 3214 =< CP, CP =< 3216 -> 'V';
uts46_map(CP) when 3218 =< CP, CP =< 3240 -> 'V';
uts46_map(CP) when 3242 =< CP, CP =< 3251 -> 'V';
uts46_map(CP) when 3253 =< CP, CP =< 3257 -> 'V';
uts46_map(CP) when 3258 =< CP, CP =< 3259 -> 'X';
uts46_map(CP) when 3260 =< CP, CP =< 3261 -> 'V';
uts46_map(CP) when 3262 =< CP, CP =< 3268 -> 'V';
uts46_map(CP) when 3270 =< CP, CP =< 3272 -> 'V';
uts46_map(CP) when 3274 =< CP, CP =< 3277 -> 'V';
uts46_map(CP) when 3278 =< CP, CP =< 3284 -> 'X';
uts46_map(CP) when 3285 =< CP, CP =< 3286 -> 'V';
uts46_map(CP) when 3287 =< CP, CP =< 3293 -> 'X';
uts46_map(CP) when 3296 =< CP, CP =< 3297 -> 'V';
uts46_map(CP) when 3298 =< CP, CP =< 3299 -> 'V';
uts46_map(CP) when 3300 =< CP, CP =< 3301 -> 'X';
uts46_map(CP) when 3302 =< CP, CP =< 3311 -> 'V';
uts46_map(CP) when 3313 =< CP, CP =< 3314 -> 'V';
uts46_map(CP) when 3315 =< CP, CP =< 3327 -> 'X';
uts46_map(CP) when 3330 =< CP, CP =< 3331 -> 'V';
uts46_map(CP) when 3333 =< CP, CP =< 3340 -> 'V';
uts46_map(CP) when 3342 =< CP, CP =< 3344 -> 'V';
uts46_map(CP) when 3346 =< CP, CP =< 3368 -> 'V';
uts46_map(CP) when 3370 =< CP, CP =< 3385 -> 'V';
uts46_map(CP) when 3387 =< CP, CP =< 3388 -> 'V';
uts46_map(CP) when 3390 =< CP, CP =< 3395 -> 'V';
uts46_map(CP) when 3398 =< CP, CP =< 3400 -> 'V';
uts46_map(CP) when 3402 =< CP, CP =< 3405 -> 'V';
uts46_map(CP) when 3408 =< CP, CP =< 3411 -> 'X';
uts46_map(CP) when 3412 =< CP, CP =< 3414 -> 'V';
uts46_map(CP) when 3416 =< CP, CP =< 3422 -> 'V';
uts46_map(CP) when 3424 =< CP, CP =< 3425 -> 'V';
uts46_map(CP) when 3426 =< CP, CP =< 3427 -> 'V';
uts46_map(CP) when 3428 =< CP, CP =< 3429 -> 'X';
uts46_map(CP) when 3430 =< CP, CP =< 3439 -> 'V';
uts46_map(CP) when 3440 =< CP, CP =< 3445 -> 'V';
uts46_map(CP) when 3446 =< CP, CP =< 3448 -> 'V';
uts46_map(CP) when 3450 =< CP, CP =< 3455 -> 'V';
uts46_map(CP) when 3458 =< CP, CP =< 3459 -> 'V';
uts46_map(CP) when 3461 =< CP, CP =< 3478 -> 'V';
uts46_map(CP) when 3479 =< CP, CP =< 3481 -> 'X';
uts46_map(CP) when 3482 =< CP, CP =< 3505 -> 'V';
uts46_map(CP) when 3507 =< CP, CP =< 3515 -> 'V';
uts46_map(CP) when 3518 =< CP, CP =< 3519 -> 'X';
uts46_map(CP) when 3520 =< CP, CP =< 3526 -> 'V';
uts46_map(CP) when 3527 =< CP, CP =< 3529 -> 'X';
uts46_map(CP) when 3531 =< CP, CP =< 3534 -> 'X';
uts46_map(CP) when 3535 =< CP, CP =< 3540 -> 'V';
uts46_map(CP) when 3544 =< CP, CP =< 3551 -> 'V';
uts46_map(CP) when 3552 =< CP, CP =< 3557 -> 'X';
uts46_map(CP) when 3558 =< CP, CP =< 3567 -> 'V';
uts46_map(CP) when 3568 =< CP, CP =< 3569 -> 'X';
uts46_map(CP) when 3570 =< CP, CP =< 3571 -> 'V';
uts46_map(CP) when 3573 =< CP, CP =< 3584 -> 'X';
uts46_map(CP) when 3585 =< CP, CP =< 3634 -> 'V';
uts46_map(CP) when 3636 =< CP, CP =< 3642 -> 'V';
uts46_map(CP) when 3643 =< CP, CP =< 3646 -> 'X';
uts46_map(CP) when 3648 =< CP, CP =< 3662 -> 'V';
uts46_map(CP) when 3664 =< CP, CP =< 3673 -> 'V';
uts46_map(CP) when 3674 =< CP, CP =< 3675 -> 'V';
uts46_map(CP) when 3676 =< CP, CP =< 3712 -> 'X';
uts46_map(CP) when 3713 =< CP, CP =< 3714 -> 'V';
uts46_map(CP) when 3719 =< CP, CP =< 3720 -> 'V';
uts46_map(CP) when 3726 =< CP, CP =< 3731 -> 'V';
uts46_map(CP) when 3732 =< CP, CP =< 3735 -> 'V';
uts46_map(CP) when 3737 =< CP, CP =< 3743 -> 'V';
uts46_map(CP) when 3745 =< CP, CP =< 3747 -> 'V';
uts46_map(CP) when 3752 =< CP, CP =< 3753 -> 'V';
uts46_map(CP) when 3754 =< CP, CP =< 3755 -> 'V';
uts46_map(CP) when 3757 =< CP, CP =< 3762 -> 'V';
uts46_map(CP) when 3764 =< CP, CP =< 3769 -> 'V';
uts46_map(CP) when 3771 =< CP, CP =< 3773 -> 'V';
uts46_map(CP) when 3774 =< CP, CP =< 3775 -> 'X';
uts46_map(CP) when 3776 =< CP, CP =< 3780 -> 'V';
uts46_map(CP) when 3784 =< CP, CP =< 3789 -> 'V';
uts46_map(CP) when 3790 =< CP, CP =< 3791 -> 'X';
uts46_map(CP) when 3792 =< CP, CP =< 3801 -> 'V';
uts46_map(CP) when 3802 =< CP, CP =< 3803 -> 'X';
uts46_map(CP) when 3806 =< CP, CP =< 3807 -> 'V';
uts46_map(CP) when 3808 =< CP, CP =< 3839 -> 'X';
uts46_map(CP) when 3841 =< CP, CP =< 3850 -> 'V';
uts46_map(CP) when 3853 =< CP, CP =< 3863 -> 'V';
uts46_map(CP) when 3864 =< CP, CP =< 3865 -> 'V';
uts46_map(CP) when 3866 =< CP, CP =< 3871 -> 'V';
uts46_map(CP) when 3872 =< CP, CP =< 3881 -> 'V';
uts46_map(CP) when 3882 =< CP, CP =< 3892 -> 'V';
uts46_map(CP) when 3898 =< CP, CP =< 3901 -> 'V';
uts46_map(CP) when 3902 =< CP, CP =< 3906 -> 'V';
uts46_map(CP) when 3908 =< CP, CP =< 3911 -> 'V';
uts46_map(CP) when 3913 =< CP, CP =< 3916 -> 'V';
uts46_map(CP) when 3918 =< CP, CP =< 3921 -> 'V';
uts46_map(CP) when 3923 =< CP, CP =< 3926 -> 'V';
uts46_map(CP) when 3928 =< CP, CP =< 3931 -> 'V';
uts46_map(CP) when 3933 =< CP, CP =< 3944 -> 'V';
uts46_map(CP) when 3947 =< CP, CP =< 3948 -> 'V';
uts46_map(CP) when 3949 =< CP, CP =< 3952 -> 'X';
uts46_map(CP) when 3953 =< CP, CP =< 3954 -> 'V';
uts46_map(CP) when 3962 =< CP, CP =< 3968 -> 'V';
uts46_map(CP) when 3970 =< CP, CP =< 3972 -> 'V';
uts46_map(CP) when 3974 =< CP, CP =< 3979 -> 'V';
uts46_map(CP) when 3980 =< CP, CP =< 3983 -> 'V';
uts46_map(CP) when 3984 =< CP, CP =< 3986 -> 'V';
uts46_map(CP) when 3988 =< CP, CP =< 3989 -> 'V';
uts46_map(CP) when 3993 =< CP, CP =< 3996 -> 'V';
uts46_map(CP) when 3998 =< CP, CP =< 4001 -> 'V';
uts46_map(CP) when 4003 =< CP, CP =< 4006 -> 'V';
uts46_map(CP) when 4008 =< CP, CP =< 4011 -> 'V';
uts46_map(CP) when 4014 =< CP, CP =< 4016 -> 'V';
uts46_map(CP) when 4017 =< CP, CP =< 4023 -> 'V';
uts46_map(CP) when 4026 =< CP, CP =< 4028 -> 'V';
uts46_map(CP) when 4030 =< CP, CP =< 4037 -> 'V';
uts46_map(CP) when 4039 =< CP, CP =< 4044 -> 'V';
uts46_map(CP) when 4048 =< CP, CP =< 4049 -> 'V';
uts46_map(CP) when 4050 =< CP, CP =< 4052 -> 'V';
uts46_map(CP) when 4053 =< CP, CP =< 4056 -> 'V';
uts46_map(CP) when 4057 =< CP, CP =< 4058 -> 'V';
uts46_map(CP) when 4059 =< CP, CP =< 4095 -> 'X';
uts46_map(CP) when 4096 =< CP, CP =< 4129 -> 'V';
uts46_map(CP) when 4131 =< CP, CP =< 4135 -> 'V';
uts46_map(CP) when 4137 =< CP, CP =< 4138 -> 'V';
uts46_map(CP) when 4140 =< CP, CP =< 4146 -> 'V';
uts46_map(CP) when 4147 =< CP, CP =< 4149 -> 'V';
uts46_map(CP) when 4150 =< CP, CP =< 4153 -> 'V';
uts46_map(CP) when 4154 =< CP, CP =< 4159 -> 'V';
uts46_map(CP) when 4160 =< CP, CP =< 4169 -> 'V';
uts46_map(CP) when 4170 =< CP, CP =< 4175 -> 'V';
uts46_map(CP) when 4176 =< CP, CP =< 4185 -> 'V';
uts46_map(CP) when 4186 =< CP, CP =< 4249 -> 'V';
uts46_map(CP) when 4250 =< CP, CP =< 4253 -> 'V';
uts46_map(CP) when 4254 =< CP, CP =< 4255 -> 'V';
uts46_map(CP) when 4256 =< CP, CP =< 4293 -> 'X';
uts46_map(CP) when 4296 =< CP, CP =< 4300 -> 'X';
uts46_map(CP) when 4302 =< CP, CP =< 4303 -> 'X';
uts46_map(CP) when 4304 =< CP, CP =< 4342 -> 'V';
uts46_map(CP) when 4343 =< CP, CP =< 4344 -> 'V';
uts46_map(CP) when 4345 =< CP, CP =< 4346 -> 'V';
uts46_map(CP) when 4349 =< CP, CP =< 4351 -> 'V';
uts46_map(CP) when 4352 =< CP, CP =< 4441 -> 'V';
uts46_map(CP) when 4442 =< CP, CP =< 4446 -> 'V';
uts46_map(CP) when 4447 =< CP, CP =< 4448 -> 'X';
uts46_map(CP) when 4449 =< CP, CP =< 4514 -> 'V';
uts46_map(CP) when 4515 =< CP, CP =< 4519 -> 'V';
uts46_map(CP) when 4520 =< CP, CP =< 4601 -> 'V';
uts46_map(CP) when 4602 =< CP, CP =< 4607 -> 'V';
uts46_map(CP) when 4608 =< CP, CP =< 4614 -> 'V';
uts46_map(CP) when 4616 =< CP, CP =< 4678 -> 'V';
uts46_map(CP) when 4682 =< CP, CP =< 4685 -> 'V';
uts46_map(CP) when 4686 =< CP, CP =< 4687 -> 'X';
uts46_map(CP) when 4688 =< CP, CP =< 4694 -> 'V';
uts46_map(CP) when 4698 =< CP, CP =< 4701 -> 'V';
uts46_map(CP) when 4702 =< CP, CP =< 4703 -> 'X';
uts46_map(CP) when 4704 =< CP, CP =< 4742 -> 'V';
uts46_map(CP) when 4746 =< CP, CP =< 4749 -> 'V';
uts46_map(CP) when 4750 =< CP, CP =< 4751 -> 'X';
uts46_map(CP) when 4752 =< CP, CP =< 4782 -> 'V';
uts46_map(CP) when 4786 =< CP, CP =< 4789 -> 'V';
uts46_map(CP) when 4790 =< CP, CP =< 4791 -> 'X';
uts46_map(CP) when 4792 =< CP, CP =< 4798 -> 'V';
uts46_map(CP) when 4802 =< CP, CP =< 4805 -> 'V';
uts46_map(CP) when 4806 =< CP, CP =< 4807 -> 'X';
uts46_map(CP) when 4808 =< CP, CP =< 4814 -> 'V';
uts46_map(CP) when 4816 =< CP, CP =< 4822 -> 'V';
uts46_map(CP) when 4824 =< CP, CP =< 4846 -> 'V';
uts46_map(CP) when 4848 =< CP, CP =< 4878 -> 'V';
uts46_map(CP) when 4882 =< CP, CP =< 4885 -> 'V';
uts46_map(CP) when 4886 =< CP, CP =< 4887 -> 'X';
uts46_map(CP) when 4888 =< CP, CP =< 4894 -> 'V';
uts46_map(CP) when 4896 =< CP, CP =< 4934 -> 'V';
uts46_map(CP) when 4936 =< CP, CP =< 4954 -> 'V';
uts46_map(CP) when 4955 =< CP, CP =< 4956 -> 'X';
uts46_map(CP) when 4957 =< CP, CP =< 4958 -> 'V';
uts46_map(CP) when 4961 =< CP, CP =< 4988 -> 'V';
uts46_map(CP) when 4989 =< CP, CP =< 4991 -> 'X';
uts46_map(CP) when 4992 =< CP, CP =< 5007 -> 'V';
uts46_map(CP) when 5008 =< CP, CP =< 5017 -> 'V';
uts46_map(CP) when 5018 =< CP, CP =< 5023 -> 'X';
uts46_map(CP) when 5024 =< CP, CP =< 5108 -> 'V';
uts46_map(CP) when 5110 =< CP, CP =< 5111 -> 'X';
uts46_map(CP) when 5118 =< CP, CP =< 5119 -> 'X';
uts46_map(CP) when 5121 =< CP, CP =< 5740 -> 'V';
uts46_map(CP) when 5741 =< CP, CP =< 5742 -> 'V';
uts46_map(CP) when 5743 =< CP, CP =< 5750 -> 'V';
uts46_map(CP) when 5751 =< CP, CP =< 5759 -> 'V';
uts46_map(CP) when 5761 =< CP, CP =< 5786 -> 'V';
uts46_map(CP) when 5787 =< CP, CP =< 5788 -> 'V';
uts46_map(CP) when 5789 =< CP, CP =< 5791 -> 'X';
uts46_map(CP) when 5792 =< CP, CP =< 5866 -> 'V';
uts46_map(CP) when 5867 =< CP, CP =< 5872 -> 'V';
uts46_map(CP) when 5873 =< CP, CP =< 5880 -> 'V';
uts46_map(CP) when 5881 =< CP, CP =< 5887 -> 'X';
uts46_map(CP) when 5888 =< CP, CP =< 5900 -> 'V';
uts46_map(CP) when 5902 =< CP, CP =< 5908 -> 'V';
uts46_map(CP) when 5909 =< CP, CP =< 5919 -> 'X';
uts46_map(CP) when 5920 =< CP, CP =< 5940 -> 'V';
uts46_map(CP) when 5941 =< CP, CP =< 5942 -> 'V';
uts46_map(CP) when 5943 =< CP, CP =< 5951 -> 'X';
uts46_map(CP) when 5952 =< CP, CP =< 5971 -> 'V';
uts46_map(CP) when 5972 =< CP, CP =< 5983 -> 'X';
uts46_map(CP) when 5984 =< CP, CP =< 5996 -> 'V';
uts46_map(CP) when 5998 =< CP, CP =< 6000 -> 'V';
uts46_map(CP) when 6002 =< CP, CP =< 6003 -> 'V';
uts46_map(CP) when 6004 =< CP, CP =< 6015 -> 'X';
uts46_map(CP) when 6016 =< CP, CP =< 6067 -> 'V';
uts46_map(CP) when 6068 =< CP, CP =< 6069 -> 'X';
uts46_map(CP) when 6070 =< CP, CP =< 6099 -> 'V';
uts46_map(CP) when 6100 =< CP, CP =< 6102 -> 'V';
uts46_map(CP) when 6104 =< CP, CP =< 6107 -> 'V';
uts46_map(CP) when 6110 =< CP, CP =< 6111 -> 'X';
uts46_map(CP) when 6112 =< CP, CP =< 6121 -> 'V';
uts46_map(CP) when 6122 =< CP, CP =< 6127 -> 'X';
uts46_map(CP) when 6128 =< CP, CP =< 6137 -> 'V';
uts46_map(CP) when 6138 =< CP, CP =< 6143 -> 'X';
uts46_map(CP) when 6144 =< CP, CP =< 6149 -> 'V';
uts46_map(CP) when 6151 =< CP, CP =< 6154 -> 'V';
uts46_map(CP) when 6155 =< CP, CP =< 6157 -> 'I';
uts46_map(CP) when 6160 =< CP, CP =< 6169 -> 'V';
uts46_map(CP) when 6170 =< CP, CP =< 6175 -> 'X';
uts46_map(CP) when 6176 =< CP, CP =< 6263 -> 'V';
uts46_map(CP) when 6265 =< CP, CP =< 6271 -> 'X';
uts46_map(CP) when 6272 =< CP, CP =< 6313 -> 'V';
uts46_map(CP) when 6315 =< CP, CP =< 6319 -> 'X';
uts46_map(CP) when 6320 =< CP, CP =< 6389 -> 'V';
uts46_map(CP) when 6390 =< CP, CP =< 6399 -> 'X';
uts46_map(CP) when 6400 =< CP, CP =< 6428 -> 'V';
uts46_map(CP) when 6429 =< CP, CP =< 6430 -> 'V';
uts46_map(CP) when 6432 =< CP, CP =< 6443 -> 'V';
uts46_map(CP) when 6444 =< CP, CP =< 6447 -> 'X';
uts46_map(CP) when 6448 =< CP, CP =< 6459 -> 'V';
uts46_map(CP) when 6460 =< CP, CP =< 6463 -> 'X';
uts46_map(CP) when 6465 =< CP, CP =< 6467 -> 'X';
uts46_map(CP) when 6468 =< CP, CP =< 6469 -> 'V';
uts46_map(CP) when 6470 =< CP, CP =< 6509 -> 'V';
uts46_map(CP) when 6510 =< CP, CP =< 6511 -> 'X';
uts46_map(CP) when 6512 =< CP, CP =< 6516 -> 'V';
uts46_map(CP) when 6517 =< CP, CP =< 6527 -> 'X';
uts46_map(CP) when 6528 =< CP, CP =< 6569 -> 'V';
uts46_map(CP) when 6570 =< CP, CP =< 6571 -> 'V';
uts46_map(CP) when 6572 =< CP, CP =< 6575 -> 'X';
uts46_map(CP) when 6576 =< CP, CP =< 6601 -> 'V';
uts46_map(CP) when 6602 =< CP, CP =< 6607 -> 'X';
uts46_map(CP) when 6608 =< CP, CP =< 6617 -> 'V';
uts46_map(CP) when 6619 =< CP, CP =< 6621 -> 'X';
uts46_map(CP) when 6622 =< CP, CP =< 6623 -> 'V';
uts46_map(CP) when 6624 =< CP, CP =< 6655 -> 'V';
uts46_map(CP) when 6656 =< CP, CP =< 6683 -> 'V';
uts46_map(CP) when 6684 =< CP, CP =< 6685 -> 'X';
uts46_map(CP) when 6686 =< CP, CP =< 6687 -> 'V';
uts46_map(CP) when 6688 =< CP, CP =< 6750 -> 'V';
uts46_map(CP) when 6752 =< CP, CP =< 6780 -> 'V';
uts46_map(CP) when 6781 =< CP, CP =< 6782 -> 'X';
uts46_map(CP) when 6783 =< CP, CP =< 6793 -> 'V';
uts46_map(CP) when 6794 =< CP, CP =< 6799 -> 'X';
uts46_map(CP) when 6800 =< CP, CP =< 6809 -> 'V';
uts46_map(CP) when 6810 =< CP, CP =< 6815 -> 'X';
uts46_map(CP) when 6816 =< CP, CP =< 6822 -> 'V';
uts46_map(CP) when 6824 =< CP, CP =< 6829 -> 'V';
uts46_map(CP) when 6830 =< CP, CP =< 6831 -> 'X';
uts46_map(CP) when 6832 =< CP, CP =< 6845 -> 'V';
uts46_map(CP) when 6847 =< CP, CP =< 6848 -> 'V';
uts46_map(CP) when 6849 =< CP, CP =< 6911 -> 'X';
uts46_map(CP) when 6912 =< CP, CP =< 6987 -> 'V';
uts46_map(CP) when 6988 =< CP, CP =< 6991 -> 'X';
uts46_map(CP) when 6992 =< CP, CP =< 7001 -> 'V';
uts46_map(CP) when 7002 =< CP, CP =< 7018 -> 'V';
uts46_map(CP) when 7019 =< CP, CP =< 7027 -> 'V';
uts46_map(CP) when 7028 =< CP, CP =< 7036 -> 'V';
uts46_map(CP) when 7037 =< CP, CP =< 7039 -> 'X';
uts46_map(CP) when 7040 =< CP, CP =< 7082 -> 'V';
uts46_map(CP) when 7083 =< CP, CP =< 7085 -> 'V';
uts46_map(CP) when 7086 =< CP, CP =< 7097 -> 'V';
uts46_map(CP) when 7098 =< CP, CP =< 7103 -> 'V';
uts46_map(CP) when 7104 =< CP, CP =< 7155 -> 'V';
uts46_map(CP) when 7156 =< CP, CP =< 7163 -> 'X';
uts46_map(CP) when 7164 =< CP, CP =< 7167 -> 'V';
uts46_map(CP) when 7168 =< CP, CP =< 7223 -> 'V';
uts46_map(CP) when 7224 =< CP, CP =< 7226 -> 'X';
uts46_map(CP) when 7227 =< CP, CP =< 7231 -> 'V';
uts46_map(CP) when 7232 =< CP, CP =< 7241 -> 'V';
uts46_map(CP) when 7242 =< CP, CP =< 7244 -> 'X';
uts46_map(CP) when 7245 =< CP, CP =< 7293 -> 'V';
uts46_map(CP) when 7294 =< CP, CP =< 7295 -> 'V';
uts46_map(CP) when 7300 =< CP, CP =< 7301 -> {'M', [1090]};
uts46_map(CP) when 7305 =< CP, CP =< 7311 -> 'X';
uts46_map(CP) when 7355 =< CP, CP =< 7356 -> 'X';
uts46_map(CP) when 7360 =< CP, CP =< 7367 -> 'V';
uts46_map(CP) when 7368 =< CP, CP =< 7375 -> 'X';
uts46_map(CP) when 7376 =< CP, CP =< 7378 -> 'V';
uts46_map(CP) when 7380 =< CP, CP =< 7410 -> 'V';
uts46_map(CP) when 7411 =< CP, CP =< 7414 -> 'V';
uts46_map(CP) when 7416 =< CP, CP =< 7417 -> 'V';
uts46_map(CP) when 7419 =< CP, CP =< 7423 -> 'X';
uts46_map(CP) when 7424 =< CP, CP =< 7467 -> 'V';
uts46_map(CP) when 7532 =< CP, CP =< 7543 -> 'V';
uts46_map(CP) when 7545 =< CP, CP =< 7578 -> 'V';
uts46_map(CP) when 7616 =< CP, CP =< 7619 -> 'V';
uts46_map(CP) when 7620 =< CP, CP =< 7626 -> 'V';
uts46_map(CP) when 7627 =< CP, CP =< 7654 -> 'V';
uts46_map(CP) when 7655 =< CP, CP =< 7669 -> 'V';
uts46_map(CP) when 7670 =< CP, CP =< 7673 -> 'V';
uts46_map(CP) when 7678 =< CP, CP =< 7679 -> 'V';
uts46_map(CP) when 7829 =< CP, CP =< 7833 -> 'V';
uts46_map(CP) when 7836 =< CP, CP =< 7837 -> 'V';
uts46_map(CP) when 7936 =< CP, CP =< 7943 -> 'V';
uts46_map(CP) when 7952 =< CP, CP =< 7957 -> 'V';
uts46_map(CP) when 7958 =< CP, CP =< 7959 -> 'X';
uts46_map(CP) when 7966 =< CP, CP =< 7967 -> 'X';
uts46_map(CP) when 7968 =< CP, CP =< 7975 -> 'V';
uts46_map(CP) when 7984 =< CP, CP =< 7991 -> 'V';
uts46_map(CP) when 8000 =< CP, CP =< 8005 -> 'V';
uts46_map(CP) when 8006 =< CP, CP =< 8007 -> 'X';
uts46_map(CP) when 8014 =< CP, CP =< 8015 -> 'X';
uts46_map(CP) when 8016 =< CP, CP =< 8023 -> 'V';
uts46_map(CP) when 8032 =< CP, CP =< 8039 -> 'V';
uts46_map(CP) when 8062 =< CP, CP =< 8063 -> 'X';
uts46_map(CP) when 8112 =< CP, CP =< 8113 -> 'V';
uts46_map(CP) when 8144 =< CP, CP =< 8146 -> 'V';
uts46_map(CP) when 8148 =< CP, CP =< 8149 -> 'X';
uts46_map(CP) when 8150 =< CP, CP =< 8151 -> 'V';
uts46_map(CP) when 8160 =< CP, CP =< 8162 -> 'V';
uts46_map(CP) when 8164 =< CP, CP =< 8167 -> 'V';
uts46_map(CP) when 8176 =< CP, CP =< 8177 -> 'X';
uts46_map(CP) when 8192 =< CP, CP =< 8202 -> {'3', [32]};
uts46_map(CP) when 8204 =< CP, CP =< 8205 -> {'D', []};
uts46_map(CP) when 8206 =< CP, CP =< 8207 -> 'X';
uts46_map(CP) when 8210 =< CP, CP =< 8214 -> 'V';
uts46_map(CP) when 8216 =< CP, CP =< 8227 -> 'V';
uts46_map(CP) when 8228 =< CP, CP =< 8230 -> 'X';
uts46_map(CP) when 8232 =< CP, CP =< 8238 -> 'X';
uts46_map(CP) when 8240 =< CP, CP =< 8242 -> 'V';
uts46_map(CP) when 8248 =< CP, CP =< 8251 -> 'V';
uts46_map(CP) when 8255 =< CP, CP =< 8262 -> 'V';
uts46_map(CP) when 8266 =< CP, CP =< 8269 -> 'V';
uts46_map(CP) when 8270 =< CP, CP =< 8274 -> 'V';
uts46_map(CP) when 8275 =< CP, CP =< 8276 -> 'V';
uts46_map(CP) when 8277 =< CP, CP =< 8278 -> 'V';
uts46_map(CP) when 8280 =< CP, CP =< 8286 -> 'V';
uts46_map(CP) when 8289 =< CP, CP =< 8291 -> 'X';
uts46_map(CP) when 8294 =< CP, CP =< 8297 -> 'X';
uts46_map(CP) when 8298 =< CP, CP =< 8303 -> 'X';
uts46_map(CP) when 8306 =< CP, CP =< 8307 -> 'X';
uts46_map(CP) when 8349 =< CP, CP =< 8351 -> 'X';
uts46_map(CP) when 8352 =< CP, CP =< 8359 -> 'V';
uts46_map(CP) when 8361 =< CP, CP =< 8362 -> 'V';
uts46_map(CP) when 8365 =< CP, CP =< 8367 -> 'V';
uts46_map(CP) when 8368 =< CP, CP =< 8369 -> 'V';
uts46_map(CP) when 8370 =< CP, CP =< 8373 -> 'V';
uts46_map(CP) when 8374 =< CP, CP =< 8376 -> 'V';
uts46_map(CP) when 8379 =< CP, CP =< 8381 -> 'V';
uts46_map(CP) when 8384 =< CP, CP =< 8399 -> 'X';
uts46_map(CP) when 8400 =< CP, CP =< 8417 -> 'V';
uts46_map(CP) when 8418 =< CP, CP =< 8419 -> 'V';
uts46_map(CP) when 8420 =< CP, CP =< 8426 -> 'V';
uts46_map(CP) when 8428 =< CP, CP =< 8431 -> 'V';
uts46_map(CP) when 8433 =< CP, CP =< 8447 -> 'X';
uts46_map(CP) when 8459 =< CP, CP =< 8462 -> {'M', [104]};
uts46_map(CP) when 8464 =< CP, CP =< 8465 -> {'M', [105]};
uts46_map(CP) when 8466 =< CP, CP =< 8467 -> {'M', [108]};
uts46_map(CP) when 8471 =< CP, CP =< 8472 -> 'V';
uts46_map(CP) when 8475 =< CP, CP =< 8477 -> {'M', [114]};
uts46_map(CP) when 8478 =< CP, CP =< 8479 -> 'V';
uts46_map(CP) when 8495 =< CP, CP =< 8496 -> {'M', [101]};
uts46_map(CP) when 8509 =< CP, CP =< 8510 -> {'M', [947]};
uts46_map(CP) when 8513 =< CP, CP =< 8516 -> 'V';
uts46_map(CP) when 8517 =< CP, CP =< 8518 -> {'M', [100]};
uts46_map(CP) when 8522 =< CP, CP =< 8523 -> 'V';
uts46_map(CP) when 8576 =< CP, CP =< 8578 -> 'V';
uts46_map(CP) when 8581 =< CP, CP =< 8584 -> 'V';
uts46_map(CP) when 8586 =< CP, CP =< 8587 -> 'V';
uts46_map(CP) when 8588 =< CP, CP =< 8591 -> 'X';
uts46_map(CP) when 8592 =< CP, CP =< 8682 -> 'V';
uts46_map(CP) when 8683 =< CP, CP =< 8691 -> 'V';
uts46_map(CP) when 8692 =< CP, CP =< 8703 -> 'V';
uts46_map(CP) when 8704 =< CP, CP =< 8747 -> 'V';
uts46_map(CP) when 8753 =< CP, CP =< 8799 -> 'V';
uts46_map(CP) when 8801 =< CP, CP =< 8813 -> 'V';
uts46_map(CP) when 8814 =< CP, CP =< 8815 -> '3';
uts46_map(CP) when 8816 =< CP, CP =< 8945 -> 'V';
uts46_map(CP) when 8946 =< CP, CP =< 8959 -> 'V';
uts46_map(CP) when 8962 =< CP, CP =< 9000 -> 'V';
uts46_map(CP) when 9003 =< CP, CP =< 9082 -> 'V';
uts46_map(CP) when 9085 =< CP, CP =< 9114 -> 'V';
uts46_map(CP) when 9115 =< CP, CP =< 9166 -> 'V';
uts46_map(CP) when 9167 =< CP, CP =< 9168 -> 'V';
uts46_map(CP) when 9169 =< CP, CP =< 9179 -> 'V';
uts46_map(CP) when 9180 =< CP, CP =< 9191 -> 'V';
uts46_map(CP) when 9193 =< CP, CP =< 9203 -> 'V';
uts46_map(CP) when 9204 =< CP, CP =< 9210 -> 'V';
uts46_map(CP) when 9211 =< CP, CP =< 9214 -> 'V';
uts46_map(CP) when 9216 =< CP, CP =< 9252 -> 'V';
uts46_map(CP) when 9253 =< CP, CP =< 9254 -> 'V';
uts46_map(CP) when 9255 =< CP, CP =< 9279 -> 'X';
uts46_map(CP) when 9280 =< CP, CP =< 9290 -> 'V';
uts46_map(CP) when 9291 =< CP, CP =< 9311 -> 'X';
uts46_map(CP) when 9352 =< CP, CP =< 9371 -> 'X';
uts46_map(CP) when 9451 =< CP, CP =< 9470 -> 'V';
uts46_map(CP) when 9472 =< CP, CP =< 9621 -> 'V';
uts46_map(CP) when 9622 =< CP, CP =< 9631 -> 'V';
uts46_map(CP) when 9632 =< CP, CP =< 9711 -> 'V';
uts46_map(CP) when 9712 =< CP, CP =< 9719 -> 'V';
uts46_map(CP) when 9720 =< CP, CP =< 9727 -> 'V';
uts46_map(CP) when 9728 =< CP, CP =< 9747 -> 'V';
uts46_map(CP) when 9748 =< CP, CP =< 9749 -> 'V';
uts46_map(CP) when 9750 =< CP, CP =< 9751 -> 'V';
uts46_map(CP) when 9754 =< CP, CP =< 9839 -> 'V';
uts46_map(CP) when 9840 =< CP, CP =< 9841 -> 'V';
uts46_map(CP) when 9842 =< CP, CP =< 9853 -> 'V';
uts46_map(CP) when 9854 =< CP, CP =< 9855 -> 'V';
uts46_map(CP) when 9856 =< CP, CP =< 9865 -> 'V';
uts46_map(CP) when 9866 =< CP, CP =< 9873 -> 'V';
uts46_map(CP) when 9874 =< CP, CP =< 9884 -> 'V';
uts46_map(CP) when 9886 =< CP, CP =< 9887 -> 'V';
uts46_map(CP) when 9888 =< CP, CP =< 9889 -> 'V';
uts46_map(CP) when 9890 =< CP, CP =< 9905 -> 'V';
uts46_map(CP) when 9907 =< CP, CP =< 9916 -> 'V';
uts46_map(CP) when 9917 =< CP, CP =< 9919 -> 'V';
uts46_map(CP) when 9920 =< CP, CP =< 9923 -> 'V';
uts46_map(CP) when 9924 =< CP, CP =< 9933 -> 'V';
uts46_map(CP) when 9935 =< CP, CP =< 9953 -> 'V';
uts46_map(CP) when 9956 =< CP, CP =< 9959 -> 'V';
uts46_map(CP) when 9960 =< CP, CP =< 9983 -> 'V';
uts46_map(CP) when 9985 =< CP, CP =< 9988 -> 'V';
uts46_map(CP) when 9990 =< CP, CP =< 9993 -> 'V';
uts46_map(CP) when 9994 =< CP, CP =< 9995 -> 'V';
uts46_map(CP) when 9996 =< CP, CP =< 10023 -> 'V';
uts46_map(CP) when 10025 =< CP, CP =< 10059 -> 'V';
uts46_map(CP) when 10063 =< CP, CP =< 10066 -> 'V';
uts46_map(CP) when 10067 =< CP, CP =< 10069 -> 'V';
uts46_map(CP) when 10072 =< CP, CP =< 10078 -> 'V';
uts46_map(CP) when 10079 =< CP, CP =< 10080 -> 'V';
uts46_map(CP) when 10081 =< CP, CP =< 10087 -> 'V';
uts46_map(CP) when 10088 =< CP, CP =< 10101 -> 'V';
uts46_map(CP) when 10102 =< CP, CP =< 10132 -> 'V';
uts46_map(CP) when 10133 =< CP, CP =< 10135 -> 'V';
uts46_map(CP) when 10136 =< CP, CP =< 10159 -> 'V';
uts46_map(CP) when 10161 =< CP, CP =< 10174 -> 'V';
uts46_map(CP) when 10176 =< CP, CP =< 10182 -> 'V';
uts46_map(CP) when 10183 =< CP, CP =< 10186 -> 'V';
uts46_map(CP) when 10190 =< CP, CP =< 10191 -> 'V';
uts46_map(CP) when 10192 =< CP, CP =< 10219 -> 'V';
uts46_map(CP) when 10220 =< CP, CP =< 10223 -> 'V';
uts46_map(CP) when 10224 =< CP, CP =< 10239 -> 'V';
uts46_map(CP) when 10240 =< CP, CP =< 10495 -> 'V';
uts46_map(CP) when 10496 =< CP, CP =< 10763 -> 'V';
uts46_map(CP) when 10765 =< CP, CP =< 10867 -> 'V';
uts46_map(CP) when 10871 =< CP, CP =< 10971 -> 'V';
uts46_map(CP) when 10973 =< CP, CP =< 11007 -> 'V';
uts46_map(CP) when 11008 =< CP, CP =< 11021 -> 'V';
uts46_map(CP) when 11022 =< CP, CP =< 11027 -> 'V';
uts46_map(CP) when 11028 =< CP, CP =< 11034 -> 'V';
uts46_map(CP) when 11035 =< CP, CP =< 11039 -> 'V';
uts46_map(CP) when 11040 =< CP, CP =< 11043 -> 'V';
uts46_map(CP) when 11044 =< CP, CP =< 11084 -> 'V';
uts46_map(CP) when 11085 =< CP, CP =< 11087 -> 'V';
uts46_map(CP) when 11088 =< CP, CP =< 11092 -> 'V';
uts46_map(CP) when 11093 =< CP, CP =< 11097 -> 'V';
uts46_map(CP) when 11098 =< CP, CP =< 11123 -> 'V';
uts46_map(CP) when 11124 =< CP, CP =< 11125 -> 'X';
uts46_map(CP) when 11126 =< CP, CP =< 11157 -> 'V';
uts46_map(CP) when 11160 =< CP, CP =< 11193 -> 'V';
uts46_map(CP) when 11194 =< CP, CP =< 11196 -> 'V';
uts46_map(CP) when 11197 =< CP, CP =< 11208 -> 'V';
uts46_map(CP) when 11210 =< CP, CP =< 11217 -> 'V';
uts46_map(CP) when 11219 =< CP, CP =< 11243 -> 'V';
uts46_map(CP) when 11244 =< CP, CP =< 11247 -> 'V';
uts46_map(CP) when 11248 =< CP, CP =< 11262 -> 'V';
uts46_map(CP) when 11312 =< CP, CP =< 11358 -> 'V';
uts46_map(CP) when 11365 =< CP, CP =< 11366 -> 'V';
uts46_map(CP) when 11382 =< CP, CP =< 11383 -> 'V';
uts46_map(CP) when 11384 =< CP, CP =< 11387 -> 'V';
uts46_map(CP) when 11491 =< CP, CP =< 11492 -> 'V';
uts46_map(CP) when 11493 =< CP, CP =< 11498 -> 'V';
uts46_map(CP) when 11502 =< CP, CP =< 11505 -> 'V';
uts46_map(CP) when 11508 =< CP, CP =< 11512 -> 'X';
uts46_map(CP) when 11513 =< CP, CP =< 11519 -> 'V';
uts46_map(CP) when 11520 =< CP, CP =< 11557 -> 'V';
uts46_map(CP) when 11560 =< CP, CP =< 11564 -> 'X';
uts46_map(CP) when 11566 =< CP, CP =< 11567 -> 'X';
uts46_map(CP) when 11568 =< CP, CP =< 11621 -> 'V';
uts46_map(CP) when 11622 =< CP, CP =< 11623 -> 'V';
uts46_map(CP) when 11624 =< CP, CP =< 11630 -> 'X';
uts46_map(CP) when 11633 =< CP, CP =< 11646 -> 'X';
uts46_map(CP) when 11648 =< CP, CP =< 11670 -> 'V';
uts46_map(CP) when 11671 =< CP, CP =< 11679 -> 'X';
uts46_map(CP) when 11680 =< CP, CP =< 11686 -> 'V';
uts46_map(CP) when 11688 =< CP, CP =< 11694 -> 'V';
uts46_map(CP) when 11696 =< CP, CP =< 11702 -> 'V';
uts46_map(CP) when 11704 =< CP, CP =< 11710 -> 'V';
uts46_map(CP) when 11712 =< CP, CP =< 11718 -> 'V';
uts46_map(CP) when 11720 =< CP, CP =< 11726 -> 'V';
uts46_map(CP) when 11728 =< CP, CP =< 11734 -> 'V';
uts46_map(CP) when 11736 =< CP, CP =< 11742 -> 'V';
uts46_map(CP) when 11744 =< CP, CP =< 11775 -> 'V';
uts46_map(CP) when 11776 =< CP, CP =< 11799 -> 'V';
uts46_map(CP) when 11800 =< CP, CP =< 11803 -> 'V';
uts46_map(CP) when 11804 =< CP, CP =< 11805 -> 'V';
uts46_map(CP) when 11806 =< CP, CP =< 11822 -> 'V';
uts46_map(CP) when 11826 =< CP, CP =< 11835 -> 'V';
uts46_map(CP) when 11836 =< CP, CP =< 11842 -> 'V';
uts46_map(CP) when 11843 =< CP, CP =< 11844 -> 'V';
uts46_map(CP) when 11845 =< CP, CP =< 11849 -> 'V';
uts46_map(CP) when 11850 =< CP, CP =< 11854 -> 'V';
uts46_map(CP) when 11856 =< CP, CP =< 11858 -> 'V';
uts46_map(CP) when 11859 =< CP, CP =< 11903 -> 'X';
uts46_map(CP) when 11904 =< CP, CP =< 11929 -> 'V';
uts46_map(CP) when 11931 =< CP, CP =< 11934 -> 'V';
uts46_map(CP) when 11936 =< CP, CP =< 12018 -> 'V';
uts46_map(CP) when 12020 =< CP, CP =< 12031 -> 'X';
uts46_map(CP) when 12246 =< CP, CP =< 12271 -> 'X';
uts46_map(CP) when 12272 =< CP, CP =< 12283 -> 'X';
uts46_map(CP) when 12284 =< CP, CP =< 12287 -> 'X';
uts46_map(CP) when 12291 =< CP, CP =< 12292 -> 'V';
uts46_map(CP) when 12293 =< CP, CP =< 12295 -> 'V';
uts46_map(CP) when 12296 =< CP, CP =< 12329 -> 'V';
uts46_map(CP) when 12330 =< CP, CP =< 12333 -> 'V';
uts46_map(CP) when 12334 =< CP, CP =< 12341 -> 'V';
uts46_map(CP) when 12353 =< CP, CP =< 12436 -> 'V';
uts46_map(CP) when 12437 =< CP, CP =< 12438 -> 'V';
uts46_map(CP) when 12439 =< CP, CP =< 12440 -> 'X';
uts46_map(CP) when 12441 =< CP, CP =< 12442 -> 'V';
uts46_map(CP) when 12445 =< CP, CP =< 12446 -> 'V';
uts46_map(CP) when 12449 =< CP, CP =< 12542 -> 'V';
uts46_map(CP) when 12544 =< CP, CP =< 12548 -> 'X';
uts46_map(CP) when 12549 =< CP, CP =< 12588 -> 'V';
uts46_map(CP) when 12688 =< CP, CP =< 12689 -> 'V';
uts46_map(CP) when 12704 =< CP, CP =< 12727 -> 'V';
uts46_map(CP) when 12728 =< CP, CP =< 12730 -> 'V';
uts46_map(CP) when 12731 =< CP, CP =< 12735 -> 'V';
uts46_map(CP) when 12736 =< CP, CP =< 12751 -> 'V';
uts46_map(CP) when 12752 =< CP, CP =< 12771 -> 'V';
uts46_map(CP) when 12772 =< CP, CP =< 12783 -> 'X';
uts46_map(CP) when 12784 =< CP, CP =< 12799 -> 'V';
uts46_map(CP) when 12872 =< CP, CP =< 12879 -> 'V';
uts46_map(CP) when 13312 =< CP, CP =< 19893 -> 'V';
uts46_map(CP) when 19894 =< CP, CP =< 19903 -> 'V';
uts46_map(CP) when 19904 =< CP, CP =< 19967 -> 'V';
uts46_map(CP) when 19968 =< CP, CP =< 40869 -> 'V';
uts46_map(CP) when 40870 =< CP, CP =< 40891 -> 'V';
uts46_map(CP) when 40892 =< CP, CP =< 40899 -> 'V';
uts46_map(CP) when 40900 =< CP, CP =< 40907 -> 'V';
uts46_map(CP) when 40909 =< CP, CP =< 40917 -> 'V';
uts46_map(CP) when 40918 =< CP, CP =< 40938 -> 'V';
uts46_map(CP) when 40939 =< CP, CP =< 40943 -> 'V';
uts46_map(CP) when 40944 =< CP, CP =< 40956 -> 'V';
uts46_map(CP) when 40957 =< CP, CP =< 40959 -> 'X';
uts46_map(CP) when 40960 =< CP, CP =< 42124 -> 'V';
uts46_map(CP) when 42125 =< CP, CP =< 42127 -> 'X';
uts46_map(CP) when 42128 =< CP, CP =< 42145 -> 'V';
uts46_map(CP) when 42146 =< CP, CP =< 42147 -> 'V';
uts46_map(CP) when 42148 =< CP, CP =< 42163 -> 'V';
uts46_map(CP) when 42165 =< CP, CP =< 42176 -> 'V';
uts46_map(CP) when 42178 =< CP, CP =< 42180 -> 'V';
uts46_map(CP) when 42183 =< CP, CP =< 42191 -> 'X';
uts46_map(CP) when 42192 =< CP, CP =< 42237 -> 'V';
uts46_map(CP) when 42238 =< CP, CP =< 42239 -> 'V';
uts46_map(CP) when 42240 =< CP, CP =< 42508 -> 'V';
uts46_map(CP) when 42509 =< CP, CP =< 42511 -> 'V';
uts46_map(CP) when 42512 =< CP, CP =< 42539 -> 'V';
uts46_map(CP) when 42540 =< CP, CP =< 42559 -> 'X';
uts46_map(CP) when 42605 =< CP, CP =< 42607 -> 'V';
uts46_map(CP) when 42608 =< CP, CP =< 42611 -> 'V';
uts46_map(CP) when 42612 =< CP, CP =< 42619 -> 'V';
uts46_map(CP) when 42620 =< CP, CP =< 42621 -> 'V';
uts46_map(CP) when 42656 =< CP, CP =< 42725 -> 'V';
uts46_map(CP) when 42726 =< CP, CP =< 42735 -> 'V';
uts46_map(CP) when 42736 =< CP, CP =< 42737 -> 'V';
uts46_map(CP) when 42738 =< CP, CP =< 42743 -> 'V';
uts46_map(CP) when 42744 =< CP, CP =< 42751 -> 'X';
uts46_map(CP) when 42752 =< CP, CP =< 42774 -> 'V';
uts46_map(CP) when 42775 =< CP, CP =< 42778 -> 'V';
uts46_map(CP) when 42779 =< CP, CP =< 42783 -> 'V';
uts46_map(CP) when 42784 =< CP, CP =< 42785 -> 'V';
uts46_map(CP) when 42799 =< CP, CP =< 42801 -> 'V';
uts46_map(CP) when 42865 =< CP, CP =< 42872 -> 'V';
uts46_map(CP) when 42887 =< CP, CP =< 42888 -> 'V';
uts46_map(CP) when 42889 =< CP, CP =< 42890 -> 'V';
uts46_map(CP) when 42900 =< CP, CP =< 42901 -> 'V';
uts46_map(CP) when 42944 =< CP, CP =< 42945 -> 'X';
uts46_map(CP) when 42955 =< CP, CP =< 42996 -> 'X';
uts46_map(CP) when 43003 =< CP, CP =< 43007 -> 'V';
uts46_map(CP) when 43008 =< CP, CP =< 43047 -> 'V';
uts46_map(CP) when 43048 =< CP, CP =< 43051 -> 'V';
uts46_map(CP) when 43053 =< CP, CP =< 43055 -> 'X';
uts46_map(CP) when 43056 =< CP, CP =< 43065 -> 'V';
uts46_map(CP) when 43066 =< CP, CP =< 43071 -> 'X';
uts46_map(CP) when 43072 =< CP, CP =< 43123 -> 'V';
uts46_map(CP) when 43124 =< CP, CP =< 43127 -> 'V';
uts46_map(CP) when 43128 =< CP, CP =< 43135 -> 'X';
uts46_map(CP) when 43136 =< CP, CP =< 43204 -> 'V';
uts46_map(CP) when 43206 =< CP, CP =< 43213 -> 'X';
uts46_map(CP) when 43214 =< CP, CP =< 43215 -> 'V';
uts46_map(CP) when 43216 =< CP, CP =< 43225 -> 'V';
uts46_map(CP) when 43226 =< CP, CP =< 43231 -> 'X';
uts46_map(CP) when 43232 =< CP, CP =< 43255 -> 'V';
uts46_map(CP) when 43256 =< CP, CP =< 43258 -> 'V';
uts46_map(CP) when 43262 =< CP, CP =< 43263 -> 'V';
uts46_map(CP) when 43264 =< CP, CP =< 43309 -> 'V';
uts46_map(CP) when 43310 =< CP, CP =< 43311 -> 'V';
uts46_map(CP) when 43312 =< CP, CP =< 43347 -> 'V';
uts46_map(CP) when 43348 =< CP, CP =< 43358 -> 'X';
uts46_map(CP) when 43360 =< CP, CP =< 43388 -> 'V';
uts46_map(CP) when 43389 =< CP, CP =< 43391 -> 'X';
uts46_map(CP) when 43392 =< CP, CP =< 43456 -> 'V';
uts46_map(CP) when 43457 =< CP, CP =< 43469 -> 'V';
uts46_map(CP) when 43471 =< CP, CP =< 43481 -> 'V';
uts46_map(CP) when 43482 =< CP, CP =< 43485 -> 'X';
uts46_map(CP) when 43486 =< CP, CP =< 43487 -> 'V';
uts46_map(CP) when 43488 =< CP, CP =< 43518 -> 'V';
uts46_map(CP) when 43520 =< CP, CP =< 43574 -> 'V';
uts46_map(CP) when 43575 =< CP, CP =< 43583 -> 'X';
uts46_map(CP) when 43584 =< CP, CP =< 43597 -> 'V';
uts46_map(CP) when 43598 =< CP, CP =< 43599 -> 'X';
uts46_map(CP) when 43600 =< CP, CP =< 43609 -> 'V';
uts46_map(CP) when 43610 =< CP, CP =< 43611 -> 'X';
uts46_map(CP) when 43612 =< CP, CP =< 43615 -> 'V';
uts46_map(CP) when 43616 =< CP, CP =< 43638 -> 'V';
uts46_map(CP) when 43639 =< CP, CP =< 43641 -> 'V';
uts46_map(CP) when 43642 =< CP, CP =< 43643 -> 'V';
uts46_map(CP) when 43644 =< CP, CP =< 43647 -> 'V';
uts46_map(CP) when 43648 =< CP, CP =< 43714 -> 'V';
uts46_map(CP) when 43715 =< CP, CP =< 43738 -> 'X';
uts46_map(CP) when 43739 =< CP, CP =< 43741 -> 'V';
uts46_map(CP) when 43742 =< CP, CP =< 43743 -> 'V';
uts46_map(CP) when 43744 =< CP, CP =< 43759 -> 'V';
uts46_map(CP) when 43760 =< CP, CP =< 43761 -> 'V';
uts46_map(CP) when 43762 =< CP, CP =< 43766 -> 'V';
uts46_map(CP) when 43767 =< CP, CP =< 43776 -> 'X';
uts46_map(CP) when 43777 =< CP, CP =< 43782 -> 'V';
uts46_map(CP) when 43783 =< CP, CP =< 43784 -> 'X';
uts46_map(CP) when 43785 =< CP, CP =< 43790 -> 'V';
uts46_map(CP) when 43791 =< CP, CP =< 43792 -> 'X';
uts46_map(CP) when 43793 =< CP, CP =< 43798 -> 'V';
uts46_map(CP) when 43799 =< CP, CP =< 43807 -> 'X';
uts46_map(CP) when 43808 =< CP, CP =< 43814 -> 'V';
uts46_map(CP) when 43816 =< CP, CP =< 43822 -> 'V';
uts46_map(CP) when 43824 =< CP, CP =< 43866 -> 'V';
uts46_map(CP) when 43872 =< CP, CP =< 43875 -> 'V';
uts46_map(CP) when 43876 =< CP, CP =< 43877 -> 'V';
uts46_map(CP) when 43878 =< CP, CP =< 43879 -> 'V';
uts46_map(CP) when 43882 =< CP, CP =< 43883 -> 'V';
uts46_map(CP) when 43884 =< CP, CP =< 43887 -> 'X';
uts46_map(CP) when 43968 =< CP, CP =< 44010 -> 'V';
uts46_map(CP) when 44012 =< CP, CP =< 44013 -> 'V';
uts46_map(CP) when 44014 =< CP, CP =< 44015 -> 'X';
uts46_map(CP) when 44016 =< CP, CP =< 44025 -> 'V';
uts46_map(CP) when 44026 =< CP, CP =< 44031 -> 'X';
uts46_map(CP) when 44032 =< CP, CP =< 55203 -> 'V';
uts46_map(CP) when 55204 =< CP, CP =< 55215 -> 'X';
uts46_map(CP) when 55216 =< CP, CP =< 55238 -> 'V';
uts46_map(CP) when 55239 =< CP, CP =< 55242 -> 'X';
uts46_map(CP) when 55243 =< CP, CP =< 55291 -> 'V';
uts46_map(CP) when 55292 =< CP, CP =< 55295 -> 'X';
uts46_map(CP) when 55296 =< CP, CP =< 57343 -> 'X';
uts46_map(CP) when 57344 =< CP, CP =< 63743 -> 'X';
uts46_map(CP) when 63751 =< CP, CP =< 63752 -> {'M', [40860]};
uts46_map(CP) when 64014 =< CP, CP =< 64015 -> 'V';
uts46_map(CP) when 64019 =< CP, CP =< 64020 -> 'V';
uts46_map(CP) when 64035 =< CP, CP =< 64036 -> 'V';
uts46_map(CP) when 64039 =< CP, CP =< 64041 -> 'V';
uts46_map(CP) when 64093 =< CP, CP =< 64094 -> {'M', [33401]};
uts46_map(CP) when 64110 =< CP, CP =< 64111 -> 'X';
uts46_map(CP) when 64218 =< CP, CP =< 64255 -> 'X';
uts46_map(CP) when 64261 =< CP, CP =< 64262 -> {'M', [115,116]};
uts46_map(CP) when 64263 =< CP, CP =< 64274 -> 'X';
uts46_map(CP) when 64280 =< CP, CP =< 64284 -> 'X';
uts46_map(CP) when 64336 =< CP, CP =< 64337 -> {'M', [1649]};
uts46_map(CP) when 64338 =< CP, CP =< 64341 -> {'M', [1659]};
uts46_map(CP) when 64342 =< CP, CP =< 64345 -> {'M', [1662]};
uts46_map(CP) when 64346 =< CP, CP =< 64349 -> {'M', [1664]};
uts46_map(CP) when 64350 =< CP, CP =< 64353 -> {'M', [1658]};
uts46_map(CP) when 64354 =< CP, CP =< 64357 -> {'M', [1663]};
uts46_map(CP) when 64358 =< CP, CP =< 64361 -> {'M', [1657]};
uts46_map(CP) when 64362 =< CP, CP =< 64365 -> {'M', [1700]};
uts46_map(CP) when 64366 =< CP, CP =< 64369 -> {'M', [1702]};
uts46_map(CP) when 64370 =< CP, CP =< 64373 -> {'M', [1668]};
uts46_map(CP) when 64374 =< CP, CP =< 64377 -> {'M', [1667]};
uts46_map(CP) when 64378 =< CP, CP =< 64381 -> {'M', [1670]};
uts46_map(CP) when 64382 =< CP, CP =< 64385 -> {'M', [1671]};
uts46_map(CP) when 64386 =< CP, CP =< 64387 -> {'M', [1677]};
uts46_map(CP) when 64388 =< CP, CP =< 64389 -> {'M', [1676]};
uts46_map(CP) when 64390 =< CP, CP =< 64391 -> {'M', [1678]};
uts46_map(CP) when 64392 =< CP, CP =< 64393 -> {'M', [1672]};
uts46_map(CP) when 64394 =< CP, CP =< 64395 -> {'M', [1688]};
uts46_map(CP) when 64396 =< CP, CP =< 64397 -> {'M', [1681]};
uts46_map(CP) when 64398 =< CP, CP =< 64401 -> {'M', [1705]};
uts46_map(CP) when 64402 =< CP, CP =< 64405 -> {'M', [1711]};
uts46_map(CP) when 64406 =< CP, CP =< 64409 -> {'M', [1715]};
uts46_map(CP) when 64410 =< CP, CP =< 64413 -> {'M', [1713]};
uts46_map(CP) when 64414 =< CP, CP =< 64415 -> {'M', [1722]};
uts46_map(CP) when 64416 =< CP, CP =< 64419 -> {'M', [1723]};
uts46_map(CP) when 64420 =< CP, CP =< 64421 -> {'M', [1728]};
uts46_map(CP) when 64422 =< CP, CP =< 64425 -> {'M', [1729]};
uts46_map(CP) when 64426 =< CP, CP =< 64429 -> {'M', [1726]};
uts46_map(CP) when 64430 =< CP, CP =< 64431 -> {'M', [1746]};
uts46_map(CP) when 64432 =< CP, CP =< 64433 -> {'M', [1747]};
uts46_map(CP) when 64434 =< CP, CP =< 64449 -> 'V';
uts46_map(CP) when 64450 =< CP, CP =< 64466 -> 'X';
uts46_map(CP) when 64467 =< CP, CP =< 64470 -> {'M', [1709]};
uts46_map(CP) when 64471 =< CP, CP =< 64472 -> {'M', [1735]};
uts46_map(CP) when 64473 =< CP, CP =< 64474 -> {'M', [1734]};
uts46_map(CP) when 64475 =< CP, CP =< 64476 -> {'M', [1736]};
uts46_map(CP) when 64478 =< CP, CP =< 64479 -> {'M', [1739]};
uts46_map(CP) when 64480 =< CP, CP =< 64481 -> {'M', [1733]};
uts46_map(CP) when 64482 =< CP, CP =< 64483 -> {'M', [1737]};
uts46_map(CP) when 64484 =< CP, CP =< 64487 -> {'M', [1744]};
uts46_map(CP) when 64488 =< CP, CP =< 64489 -> {'M', [1609]};
uts46_map(CP) when 64490 =< CP, CP =< 64491 -> {'M', [1574,1575]};
uts46_map(CP) when 64492 =< CP, CP =< 64493 -> {'M', [1574,1749]};
uts46_map(CP) when 64494 =< CP, CP =< 64495 -> {'M', [1574,1608]};
uts46_map(CP) when 64496 =< CP, CP =< 64497 -> {'M', [1574,1735]};
uts46_map(CP) when 64498 =< CP, CP =< 64499 -> {'M', [1574,1734]};
uts46_map(CP) when 64500 =< CP, CP =< 64501 -> {'M', [1574,1736]};
uts46_map(CP) when 64502 =< CP, CP =< 64504 -> {'M', [1574,1744]};
uts46_map(CP) when 64505 =< CP, CP =< 64507 -> {'M', [1574,1609]};
uts46_map(CP) when 64508 =< CP, CP =< 64511 -> {'M', [1740]};
uts46_map(CP) when 64828 =< CP, CP =< 64829 -> {'M', [1575,1611]};
uts46_map(CP) when 64830 =< CP, CP =< 64831 -> 'V';
uts46_map(CP) when 64832 =< CP, CP =< 64847 -> 'X';
uts46_map(CP) when 64849 =< CP, CP =< 64850 -> {'M', [1578,1581,1580]};
uts46_map(CP) when 64856 =< CP, CP =< 64857 -> {'M', [1580,1605,1581]};
uts46_map(CP) when 64863 =< CP, CP =< 64864 -> {'M', [1587,1605,1581]};
uts46_map(CP) when 64866 =< CP, CP =< 64867 -> {'M', [1587,1605,1605]};
uts46_map(CP) when 64868 =< CP, CP =< 64869 -> {'M', [1589,1581,1581]};
uts46_map(CP) when 64871 =< CP, CP =< 64872 -> {'M', [1588,1581,1605]};
uts46_map(CP) when 64874 =< CP, CP =< 64875 -> {'M', [1588,1605,1582]};
uts46_map(CP) when 64876 =< CP, CP =< 64877 -> {'M', [1588,1605,1605]};
uts46_map(CP) when 64879 =< CP, CP =< 64880 -> {'M', [1590,1582,1605]};
uts46_map(CP) when 64881 =< CP, CP =< 64882 -> {'M', [1591,1605,1581]};
uts46_map(CP) when 64886 =< CP, CP =< 64887 -> {'M', [1593,1605,1605]};
uts46_map(CP) when 64892 =< CP, CP =< 64893 -> {'M', [1601,1582,1605]};
uts46_map(CP) when 64899 =< CP, CP =< 64900 -> {'M', [1604,1580,1580]};
uts46_map(CP) when 64901 =< CP, CP =< 64902 -> {'M', [1604,1582,1605]};
uts46_map(CP) when 64903 =< CP, CP =< 64904 -> {'M', [1604,1605,1581]};
uts46_map(CP) when 64912 =< CP, CP =< 64913 -> 'X';
uts46_map(CP) when 64919 =< CP, CP =< 64920 -> {'M', [1606,1580,1605]};
uts46_map(CP) when 64924 =< CP, CP =< 64925 -> {'M', [1610,1605,1605]};
uts46_map(CP) when 64968 =< CP, CP =< 64975 -> 'X';
uts46_map(CP) when 64976 =< CP, CP =< 65007 -> 'X';
uts46_map(CP) when 65022 =< CP, CP =< 65023 -> 'X';
uts46_map(CP) when 65024 =< CP, CP =< 65039 -> 'I';
uts46_map(CP) when 65050 =< CP, CP =< 65055 -> 'X';
uts46_map(CP) when 65056 =< CP, CP =< 65059 -> 'V';
uts46_map(CP) when 65060 =< CP, CP =< 65062 -> 'V';
uts46_map(CP) when 65063 =< CP, CP =< 65069 -> 'V';
uts46_map(CP) when 65070 =< CP, CP =< 65071 -> 'V';
uts46_map(CP) when 65075 =< CP, CP =< 65076 -> {'3', [95]};
uts46_map(CP) when 65093 =< CP, CP =< 65094 -> 'V';
uts46_map(CP) when 65097 =< CP, CP =< 65100 -> {'3', [32,773]};
uts46_map(CP) when 65101 =< CP, CP =< 65103 -> {'3', [95]};
uts46_map(CP) when 65132 =< CP, CP =< 65135 -> 'X';
uts46_map(CP) when 65153 =< CP, CP =< 65154 -> {'M', [1570]};
uts46_map(CP) when 65155 =< CP, CP =< 65156 -> {'M', [1571]};
uts46_map(CP) when 65157 =< CP, CP =< 65158 -> {'M', [1572]};
uts46_map(CP) when 65159 =< CP, CP =< 65160 -> {'M', [1573]};
uts46_map(CP) when 65161 =< CP, CP =< 65164 -> {'M', [1574]};
uts46_map(CP) when 65165 =< CP, CP =< 65166 -> {'M', [1575]};
uts46_map(CP) when 65167 =< CP, CP =< 65170 -> {'M', [1576]};
uts46_map(CP) when 65171 =< CP, CP =< 65172 -> {'M', [1577]};
uts46_map(CP) when 65173 =< CP, CP =< 65176 -> {'M', [1578]};
uts46_map(CP) when 65177 =< CP, CP =< 65180 -> {'M', [1579]};
uts46_map(CP) when 65181 =< CP, CP =< 65184 -> {'M', [1580]};
uts46_map(CP) when 65185 =< CP, CP =< 65188 -> {'M', [1581]};
uts46_map(CP) when 65189 =< CP, CP =< 65192 -> {'M', [1582]};
uts46_map(CP) when 65193 =< CP, CP =< 65194 -> {'M', [1583]};
uts46_map(CP) when 65195 =< CP, CP =< 65196 -> {'M', [1584]};
uts46_map(CP) when 65197 =< CP, CP =< 65198 -> {'M', [1585]};
uts46_map(CP) when 65199 =< CP, CP =< 65200 -> {'M', [1586]};
uts46_map(CP) when 65201 =< CP, CP =< 65204 -> {'M', [1587]};
uts46_map(CP) when 65205 =< CP, CP =< 65208 -> {'M', [1588]};
uts46_map(CP) when 65209 =< CP, CP =< 65212 -> {'M', [1589]};
uts46_map(CP) when 65213 =< CP, CP =< 65216 -> {'M', [1590]};
uts46_map(CP) when 65217 =< CP, CP =< 65220 -> {'M', [1591]};
uts46_map(CP) when 65221 =< CP, CP =< 65224 -> {'M', [1592]};
uts46_map(CP) when 65225 =< CP, CP =< 65228 -> {'M', [1593]};
uts46_map(CP) when 65229 =< CP, CP =< 65232 -> {'M', [1594]};
uts46_map(CP) when 65233 =< CP, CP =< 65236 -> {'M', [1601]};
uts46_map(CP) when 65237 =< CP, CP =< 65240 -> {'M', [1602]};
uts46_map(CP) when 65241 =< CP, CP =< 65244 -> {'M', [1603]};
uts46_map(CP) when 65245 =< CP, CP =< 65248 -> {'M', [1604]};
uts46_map(CP) when 65249 =< CP, CP =< 65252 -> {'M', [1605]};
uts46_map(CP) when 65253 =< CP, CP =< 65256 -> {'M', [1606]};
uts46_map(CP) when 65257 =< CP, CP =< 65260 -> {'M', [1607]};
uts46_map(CP) when 65261 =< CP, CP =< 65262 -> {'M', [1608]};
uts46_map(CP) when 65263 =< CP, CP =< 65264 -> {'M', [1609]};
uts46_map(CP) when 65265 =< CP, CP =< 65268 -> {'M', [1610]};
uts46_map(CP) when 65269 =< CP, CP =< 65270 -> {'M', [1604,1570]};
uts46_map(CP) when 65271 =< CP, CP =< 65272 -> {'M', [1604,1571]};
uts46_map(CP) when 65273 =< CP, CP =< 65274 -> {'M', [1604,1573]};
uts46_map(CP) when 65275 =< CP, CP =< 65276 -> {'M', [1604,1575]};
uts46_map(CP) when 65277 =< CP, CP =< 65278 -> 'X';
uts46_map(CP) when 65471 =< CP, CP =< 65473 -> 'X';
uts46_map(CP) when 65480 =< CP, CP =< 65481 -> 'X';
uts46_map(CP) when 65488 =< CP, CP =< 65489 -> 'X';
uts46_map(CP) when 65496 =< CP, CP =< 65497 -> 'X';
uts46_map(CP) when 65501 =< CP, CP =< 65503 -> 'X';
uts46_map(CP) when 65519 =< CP, CP =< 65528 -> 'X';
uts46_map(CP) when 65529 =< CP, CP =< 65531 -> 'X';
uts46_map(CP) when 65534 =< CP, CP =< 65535 -> 'X';
uts46_map(CP) when 65536 =< CP, CP =< 65547 -> 'V';
uts46_map(CP) when 65549 =< CP, CP =< 65574 -> 'V';
uts46_map(CP) when 65576 =< CP, CP =< 65594 -> 'V';
uts46_map(CP) when 65596 =< CP, CP =< 65597 -> 'V';
uts46_map(CP) when 65599 =< CP, CP =< 65613 -> 'V';
uts46_map(CP) when 65614 =< CP, CP =< 65615 -> 'X';
uts46_map(CP) when 65616 =< CP, CP =< 65629 -> 'V';
uts46_map(CP) when 65630 =< CP, CP =< 65663 -> 'X';
uts46_map(CP) when 65664 =< CP, CP =< 65786 -> 'V';
uts46_map(CP) when 65787 =< CP, CP =< 65791 -> 'X';
uts46_map(CP) when 65792 =< CP, CP =< 65794 -> 'V';
uts46_map(CP) when 65795 =< CP, CP =< 65798 -> 'X';
uts46_map(CP) when 65799 =< CP, CP =< 65843 -> 'V';
uts46_map(CP) when 65844 =< CP, CP =< 65846 -> 'X';
uts46_map(CP) when 65847 =< CP, CP =< 65855 -> 'V';
uts46_map(CP) when 65856 =< CP, CP =< 65930 -> 'V';
uts46_map(CP) when 65931 =< CP, CP =< 65932 -> 'V';
uts46_map(CP) when 65933 =< CP, CP =< 65934 -> 'V';
uts46_map(CP) when 65936 =< CP, CP =< 65947 -> 'V';
uts46_map(CP) when 65949 =< CP, CP =< 65951 -> 'X';
uts46_map(CP) when 65953 =< CP, CP =< 65999 -> 'X';
uts46_map(CP) when 66000 =< CP, CP =< 66044 -> 'V';
uts46_map(CP) when 66046 =< CP, CP =< 66175 -> 'X';
uts46_map(CP) when 66176 =< CP, CP =< 66204 -> 'V';
uts46_map(CP) when 66205 =< CP, CP =< 66207 -> 'X';
uts46_map(CP) when 66208 =< CP, CP =< 66256 -> 'V';
uts46_map(CP) when 66257 =< CP, CP =< 66271 -> 'X';
uts46_map(CP) when 66273 =< CP, CP =< 66299 -> 'V';
uts46_map(CP) when 66300 =< CP, CP =< 66303 -> 'X';
uts46_map(CP) when 66304 =< CP, CP =< 66334 -> 'V';
uts46_map(CP) when 66336 =< CP, CP =< 66339 -> 'V';
uts46_map(CP) when 66340 =< CP, CP =< 66348 -> 'X';
uts46_map(CP) when 66349 =< CP, CP =< 66351 -> 'V';
uts46_map(CP) when 66352 =< CP, CP =< 66368 -> 'V';
uts46_map(CP) when 66370 =< CP, CP =< 66377 -> 'V';
uts46_map(CP) when 66379 =< CP, CP =< 66383 -> 'X';
uts46_map(CP) when 66384 =< CP, CP =< 66426 -> 'V';
uts46_map(CP) when 66427 =< CP, CP =< 66431 -> 'X';
uts46_map(CP) when 66432 =< CP, CP =< 66461 -> 'V';
uts46_map(CP) when 66464 =< CP, CP =< 66499 -> 'V';
uts46_map(CP) when 66500 =< CP, CP =< 66503 -> 'X';
uts46_map(CP) when 66504 =< CP, CP =< 66511 -> 'V';
uts46_map(CP) when 66512 =< CP, CP =< 66517 -> 'V';
uts46_map(CP) when 66518 =< CP, CP =< 66559 -> 'X';
uts46_map(CP) when 66600 =< CP, CP =< 66637 -> 'V';
uts46_map(CP) when 66638 =< CP, CP =< 66717 -> 'V';
uts46_map(CP) when 66718 =< CP, CP =< 66719 -> 'X';
uts46_map(CP) when 66720 =< CP, CP =< 66729 -> 'V';
uts46_map(CP) when 66730 =< CP, CP =< 66735 -> 'X';
uts46_map(CP) when 66772 =< CP, CP =< 66775 -> 'X';
uts46_map(CP) when 66776 =< CP, CP =< 66811 -> 'V';
uts46_map(CP) when 66812 =< CP, CP =< 66815 -> 'X';
uts46_map(CP) when 66816 =< CP, CP =< 66855 -> 'V';
uts46_map(CP) when 66856 =< CP, CP =< 66863 -> 'X';
uts46_map(CP) when 66864 =< CP, CP =< 66915 -> 'V';
uts46_map(CP) when 66916 =< CP, CP =< 66926 -> 'X';
uts46_map(CP) when 66928 =< CP, CP =< 67071 -> 'X';
uts46_map(CP) when 67072 =< CP, CP =< 67382 -> 'V';
uts46_map(CP) when 67383 =< CP, CP =< 67391 -> 'X';
uts46_map(CP) when 67392 =< CP, CP =< 67413 -> 'V';
uts46_map(CP) when 67414 =< CP, CP =< 67423 -> 'X';
uts46_map(CP) when 67424 =< CP, CP =< 67431 -> 'V';
uts46_map(CP) when 67432 =< CP, CP =< 67583 -> 'X';
uts46_map(CP) when 67584 =< CP, CP =< 67589 -> 'V';
uts46_map(CP) when 67590 =< CP, CP =< 67591 -> 'X';
uts46_map(CP) when 67594 =< CP, CP =< 67637 -> 'V';
uts46_map(CP) when 67639 =< CP, CP =< 67640 -> 'V';
uts46_map(CP) when 67641 =< CP, CP =< 67643 -> 'X';
uts46_map(CP) when 67645 =< CP, CP =< 67646 -> 'X';
uts46_map(CP) when 67648 =< CP, CP =< 67669 -> 'V';
uts46_map(CP) when 67671 =< CP, CP =< 67679 -> 'V';
uts46_map(CP) when 67680 =< CP, CP =< 67702 -> 'V';
uts46_map(CP) when 67703 =< CP, CP =< 67711 -> 'V';
uts46_map(CP) when 67712 =< CP, CP =< 67742 -> 'V';
uts46_map(CP) when 67743 =< CP, CP =< 67750 -> 'X';
uts46_map(CP) when 67751 =< CP, CP =< 67759 -> 'V';
uts46_map(CP) when 67760 =< CP, CP =< 67807 -> 'X';
uts46_map(CP) when 67808 =< CP, CP =< 67826 -> 'V';
uts46_map(CP) when 67828 =< CP, CP =< 67829 -> 'V';
uts46_map(CP) when 67830 =< CP, CP =< 67834 -> 'X';
uts46_map(CP) when 67835 =< CP, CP =< 67839 -> 'V';
uts46_map(CP) when 67840 =< CP, CP =< 67861 -> 'V';
uts46_map(CP) when 67862 =< CP, CP =< 67865 -> 'V';
uts46_map(CP) when 67866 =< CP, CP =< 67867 -> 'V';
uts46_map(CP) when 67868 =< CP, CP =< 67870 -> 'X';
uts46_map(CP) when 67872 =< CP, CP =< 67897 -> 'V';
uts46_map(CP) when 67898 =< CP, CP =< 67902 -> 'X';
uts46_map(CP) when 67904 =< CP, CP =< 67967 -> 'X';
uts46_map(CP) when 67968 =< CP, CP =< 68023 -> 'V';
uts46_map(CP) when 68024 =< CP, CP =< 68027 -> 'X';
uts46_map(CP) when 68028 =< CP, CP =< 68029 -> 'V';
uts46_map(CP) when 68030 =< CP, CP =< 68031 -> 'V';
uts46_map(CP) when 68032 =< CP, CP =< 68047 -> 'V';
uts46_map(CP) when 68048 =< CP, CP =< 68049 -> 'X';
uts46_map(CP) when 68050 =< CP, CP =< 68095 -> 'V';
uts46_map(CP) when 68096 =< CP, CP =< 68099 -> 'V';
uts46_map(CP) when 68101 =< CP, CP =< 68102 -> 'V';
uts46_map(CP) when 68103 =< CP, CP =< 68107 -> 'X';
uts46_map(CP) when 68108 =< CP, CP =< 68115 -> 'V';
uts46_map(CP) when 68117 =< CP, CP =< 68119 -> 'V';
uts46_map(CP) when 68121 =< CP, CP =< 68147 -> 'V';
uts46_map(CP) when 68148 =< CP, CP =< 68149 -> 'V';
uts46_map(CP) when 68150 =< CP, CP =< 68151 -> 'X';
uts46_map(CP) when 68152 =< CP, CP =< 68154 -> 'V';
uts46_map(CP) when 68155 =< CP, CP =< 68158 -> 'X';
uts46_map(CP) when 68160 =< CP, CP =< 68167 -> 'V';
uts46_map(CP) when 68169 =< CP, CP =< 68175 -> 'X';
uts46_map(CP) when 68176 =< CP, CP =< 68184 -> 'V';
uts46_map(CP) when 68185 =< CP, CP =< 68191 -> 'X';
uts46_map(CP) when 68192 =< CP, CP =< 68220 -> 'V';
uts46_map(CP) when 68221 =< CP, CP =< 68223 -> 'V';
uts46_map(CP) when 68224 =< CP, CP =< 68252 -> 'V';
uts46_map(CP) when 68253 =< CP, CP =< 68255 -> 'V';
uts46_map(CP) when 68256 =< CP, CP =< 68287 -> 'X';
uts46_map(CP) when 68288 =< CP, CP =< 68295 -> 'V';
uts46_map(CP) when 68297 =< CP, CP =< 68326 -> 'V';
uts46_map(CP) when 68327 =< CP, CP =< 68330 -> 'X';
uts46_map(CP) when 68331 =< CP, CP =< 68342 -> 'V';
uts46_map(CP) when 68343 =< CP, CP =< 68351 -> 'X';
uts46_map(CP) when 68352 =< CP, CP =< 68405 -> 'V';
uts46_map(CP) when 68406 =< CP, CP =< 68408 -> 'X';
uts46_map(CP) when 68409 =< CP, CP =< 68415 -> 'V';
uts46_map(CP) when 68416 =< CP, CP =< 68437 -> 'V';
uts46_map(CP) when 68438 =< CP, CP =< 68439 -> 'X';
uts46_map(CP) when 68440 =< CP, CP =< 68447 -> 'V';
uts46_map(CP) when 68448 =< CP, CP =< 68466 -> 'V';
uts46_map(CP) when 68467 =< CP, CP =< 68471 -> 'X';
uts46_map(CP) when 68472 =< CP, CP =< 68479 -> 'V';
uts46_map(CP) when 68480 =< CP, CP =< 68497 -> 'V';
uts46_map(CP) when 68498 =< CP, CP =< 68504 -> 'X';
uts46_map(CP) when 68505 =< CP, CP =< 68508 -> 'V';
uts46_map(CP) when 68509 =< CP, CP =< 68520 -> 'X';
uts46_map(CP) when 68521 =< CP, CP =< 68527 -> 'V';
uts46_map(CP) when 68528 =< CP, CP =< 68607 -> 'X';
uts46_map(CP) when 68608 =< CP, CP =< 68680 -> 'V';
uts46_map(CP) when 68681 =< CP, CP =< 68735 -> 'X';
uts46_map(CP) when 68787 =< CP, CP =< 68799 -> 'X';
uts46_map(CP) when 68800 =< CP, CP =< 68850 -> 'V';
uts46_map(CP) when 68851 =< CP, CP =< 68857 -> 'X';
uts46_map(CP) when 68858 =< CP, CP =< 68863 -> 'V';
uts46_map(CP) when 68864 =< CP, CP =< 68903 -> 'V';
uts46_map(CP) when 68904 =< CP, CP =< 68911 -> 'X';
uts46_map(CP) when 68912 =< CP, CP =< 68921 -> 'V';
uts46_map(CP) when 68922 =< CP, CP =< 69215 -> 'X';
uts46_map(CP) when 69216 =< CP, CP =< 69246 -> 'V';
uts46_map(CP) when 69248 =< CP, CP =< 69289 -> 'V';
uts46_map(CP) when 69291 =< CP, CP =< 69292 -> 'V';
uts46_map(CP) when 69294 =< CP, CP =< 69295 -> 'X';
uts46_map(CP) when 69296 =< CP, CP =< 69297 -> 'V';
uts46_map(CP) when 69298 =< CP, CP =< 69375 -> 'X';
uts46_map(CP) when 69376 =< CP, CP =< 69404 -> 'V';
uts46_map(CP) when 69405 =< CP, CP =< 69414 -> 'V';
uts46_map(CP) when 69416 =< CP, CP =< 69423 -> 'X';
uts46_map(CP) when 69424 =< CP, CP =< 69456 -> 'V';
uts46_map(CP) when 69457 =< CP, CP =< 69465 -> 'V';
uts46_map(CP) when 69466 =< CP, CP =< 69551 -> 'X';
uts46_map(CP) when 69552 =< CP, CP =< 69572 -> 'V';
uts46_map(CP) when 69573 =< CP, CP =< 69579 -> 'V';
uts46_map(CP) when 69580 =< CP, CP =< 69599 -> 'X';
uts46_map(CP) when 69600 =< CP, CP =< 69622 -> 'V';
uts46_map(CP) when 69623 =< CP, CP =< 69631 -> 'X';
uts46_map(CP) when 69632 =< CP, CP =< 69702 -> 'V';
uts46_map(CP) when 69703 =< CP, CP =< 69709 -> 'V';
uts46_map(CP) when 69710 =< CP, CP =< 69713 -> 'X';
uts46_map(CP) when 69714 =< CP, CP =< 69733 -> 'V';
uts46_map(CP) when 69734 =< CP, CP =< 69743 -> 'V';
uts46_map(CP) when 69744 =< CP, CP =< 69758 -> 'X';
uts46_map(CP) when 69760 =< CP, CP =< 69818 -> 'V';
uts46_map(CP) when 69819 =< CP, CP =< 69820 -> 'V';
uts46_map(CP) when 69822 =< CP, CP =< 69825 -> 'V';
uts46_map(CP) when 69826 =< CP, CP =< 69836 -> 'X';
uts46_map(CP) when 69838 =< CP, CP =< 69839 -> 'X';
uts46_map(CP) when 69840 =< CP, CP =< 69864 -> 'V';
uts46_map(CP) when 69865 =< CP, CP =< 69871 -> 'X';
uts46_map(CP) when 69872 =< CP, CP =< 69881 -> 'V';
uts46_map(CP) when 69882 =< CP, CP =< 69887 -> 'X';
uts46_map(CP) when 69888 =< CP, CP =< 69940 -> 'V';
uts46_map(CP) when 69942 =< CP, CP =< 69951 -> 'V';
uts46_map(CP) when 69952 =< CP, CP =< 69955 -> 'V';
uts46_map(CP) when 69956 =< CP, CP =< 69958 -> 'V';
uts46_map(CP) when 69960 =< CP, CP =< 69967 -> 'X';
uts46_map(CP) when 69968 =< CP, CP =< 70003 -> 'V';
uts46_map(CP) when 70004 =< CP, CP =< 70005 -> 'V';
uts46_map(CP) when 70007 =< CP, CP =< 70015 -> 'X';
uts46_map(CP) when 70016 =< CP, CP =< 70084 -> 'V';
uts46_map(CP) when 70085 =< CP, CP =< 70088 -> 'V';
uts46_map(CP) when 70089 =< CP, CP =< 70092 -> 'V';
uts46_map(CP) when 70094 =< CP, CP =< 70095 -> 'V';
uts46_map(CP) when 70096 =< CP, CP =< 70105 -> 'V';
uts46_map(CP) when 70109 =< CP, CP =< 70111 -> 'V';
uts46_map(CP) when 70113 =< CP, CP =< 70132 -> 'V';
uts46_map(CP) when 70133 =< CP, CP =< 70143 -> 'X';
uts46_map(CP) when 70144 =< CP, CP =< 70161 -> 'V';
uts46_map(CP) when 70163 =< CP, CP =< 70199 -> 'V';
uts46_map(CP) when 70200 =< CP, CP =< 70205 -> 'V';
uts46_map(CP) when 70207 =< CP, CP =< 70271 -> 'X';
uts46_map(CP) when 70272 =< CP, CP =< 70278 -> 'V';
uts46_map(CP) when 70282 =< CP, CP =< 70285 -> 'V';
uts46_map(CP) when 70287 =< CP, CP =< 70301 -> 'V';
uts46_map(CP) when 70303 =< CP, CP =< 70312 -> 'V';
uts46_map(CP) when 70314 =< CP, CP =< 70319 -> 'X';
uts46_map(CP) when 70320 =< CP, CP =< 70378 -> 'V';
uts46_map(CP) when 70379 =< CP, CP =< 70383 -> 'X';
uts46_map(CP) when 70384 =< CP, CP =< 70393 -> 'V';
uts46_map(CP) when 70394 =< CP, CP =< 70399 -> 'X';
uts46_map(CP) when 70401 =< CP, CP =< 70403 -> 'V';
uts46_map(CP) when 70405 =< CP, CP =< 70412 -> 'V';
uts46_map(CP) when 70413 =< CP, CP =< 70414 -> 'X';
uts46_map(CP) when 70415 =< CP, CP =< 70416 -> 'V';
uts46_map(CP) when 70417 =< CP, CP =< 70418 -> 'X';
uts46_map(CP) when 70419 =< CP, CP =< 70440 -> 'V';
uts46_map(CP) when 70442 =< CP, CP =< 70448 -> 'V';
uts46_map(CP) when 70450 =< CP, CP =< 70451 -> 'V';
uts46_map(CP) when 70453 =< CP, CP =< 70457 -> 'V';
uts46_map(CP) when 70460 =< CP, CP =< 70468 -> 'V';
uts46_map(CP) when 70469 =< CP, CP =< 70470 -> 'X';
uts46_map(CP) when 70471 =< CP, CP =< 70472 -> 'V';
uts46_map(CP) when 70473 =< CP, CP =< 70474 -> 'X';
uts46_map(CP) when 70475 =< CP, CP =< 70477 -> 'V';
uts46_map(CP) when 70478 =< CP, CP =< 70479 -> 'X';
uts46_map(CP) when 70481 =< CP, CP =< 70486 -> 'X';
uts46_map(CP) when 70488 =< CP, CP =< 70492 -> 'X';
uts46_map(CP) when 70493 =< CP, CP =< 70499 -> 'V';
uts46_map(CP) when 70500 =< CP, CP =< 70501 -> 'X';
uts46_map(CP) when 70502 =< CP, CP =< 70508 -> 'V';
uts46_map(CP) when 70509 =< CP, CP =< 70511 -> 'X';
uts46_map(CP) when 70512 =< CP, CP =< 70516 -> 'V';
uts46_map(CP) when 70517 =< CP, CP =< 70655 -> 'X';
uts46_map(CP) when 70656 =< CP, CP =< 70730 -> 'V';
uts46_map(CP) when 70731 =< CP, CP =< 70735 -> 'V';
uts46_map(CP) when 70736 =< CP, CP =< 70745 -> 'V';
uts46_map(CP) when 70752 =< CP, CP =< 70753 -> 'V';
uts46_map(CP) when 70754 =< CP, CP =< 70783 -> 'X';
uts46_map(CP) when 70784 =< CP, CP =< 70853 -> 'V';
uts46_map(CP) when 70856 =< CP, CP =< 70863 -> 'X';
uts46_map(CP) when 70864 =< CP, CP =< 70873 -> 'V';
uts46_map(CP) when 70874 =< CP, CP =< 71039 -> 'X';
uts46_map(CP) when 71040 =< CP, CP =< 71093 -> 'V';
uts46_map(CP) when 71094 =< CP, CP =< 71095 -> 'X';
uts46_map(CP) when 71096 =< CP, CP =< 71104 -> 'V';
uts46_map(CP) when 71105 =< CP, CP =< 71113 -> 'V';
uts46_map(CP) when 71114 =< CP, CP =< 71127 -> 'V';
uts46_map(CP) when 71128 =< CP, CP =< 71133 -> 'V';
uts46_map(CP) when 71134 =< CP, CP =< 71167 -> 'X';
uts46_map(CP) when 71168 =< CP, CP =< 71232 -> 'V';
uts46_map(CP) when 71233 =< CP, CP =< 71235 -> 'V';
uts46_map(CP) when 71237 =< CP, CP =< 71247 -> 'X';
uts46_map(CP) when 71248 =< CP, CP =< 71257 -> 'V';
uts46_map(CP) when 71258 =< CP, CP =< 71263 -> 'X';
uts46_map(CP) when 71264 =< CP, CP =< 71276 -> 'V';
uts46_map(CP) when 71277 =< CP, CP =< 71295 -> 'X';
uts46_map(CP) when 71296 =< CP, CP =< 71351 -> 'V';
uts46_map(CP) when 71353 =< CP, CP =< 71359 -> 'X';
uts46_map(CP) when 71360 =< CP, CP =< 71369 -> 'V';
uts46_map(CP) when 71370 =< CP, CP =< 71423 -> 'X';
uts46_map(CP) when 71424 =< CP, CP =< 71449 -> 'V';
uts46_map(CP) when 71451 =< CP, CP =< 71452 -> 'X';
uts46_map(CP) when 71453 =< CP, CP =< 71467 -> 'V';
uts46_map(CP) when 71468 =< CP, CP =< 71471 -> 'X';
uts46_map(CP) when 71472 =< CP, CP =< 71481 -> 'V';
uts46_map(CP) when 71482 =< CP, CP =< 71487 -> 'V';
uts46_map(CP) when 71488 =< CP, CP =< 71679 -> 'X';
uts46_map(CP) when 71680 =< CP, CP =< 71738 -> 'V';
uts46_map(CP) when 71740 =< CP, CP =< 71839 -> 'X';
uts46_map(CP) when 71872 =< CP, CP =< 71913 -> 'V';
uts46_map(CP) when 71914 =< CP, CP =< 71922 -> 'V';
uts46_map(CP) when 71923 =< CP, CP =< 71934 -> 'X';
uts46_map(CP) when 71936 =< CP, CP =< 71942 -> 'V';
uts46_map(CP) when 71943 =< CP, CP =< 71944 -> 'X';
uts46_map(CP) when 71946 =< CP, CP =< 71947 -> 'X';
uts46_map(CP) when 71948 =< CP, CP =< 71955 -> 'V';
uts46_map(CP) when 71957 =< CP, CP =< 71958 -> 'V';
uts46_map(CP) when 71960 =< CP, CP =< 71989 -> 'V';
uts46_map(CP) when 71991 =< CP, CP =< 71992 -> 'V';
uts46_map(CP) when 71993 =< CP, CP =< 71994 -> 'X';
uts46_map(CP) when 71995 =< CP, CP =< 72003 -> 'V';
uts46_map(CP) when 72004 =< CP, CP =< 72006 -> 'V';
uts46_map(CP) when 72007 =< CP, CP =< 72015 -> 'X';
uts46_map(CP) when 72016 =< CP, CP =< 72025 -> 'V';
uts46_map(CP) when 72026 =< CP, CP =< 72095 -> 'X';
uts46_map(CP) when 72096 =< CP, CP =< 72103 -> 'V';
uts46_map(CP) when 72104 =< CP, CP =< 72105 -> 'X';
uts46_map(CP) when 72106 =< CP, CP =< 72151 -> 'V';
uts46_map(CP) when 72152 =< CP, CP =< 72153 -> 'X';
uts46_map(CP) when 72154 =< CP, CP =< 72161 -> 'V';
uts46_map(CP) when 72163 =< CP, CP =< 72164 -> 'V';
uts46_map(CP) when 72165 =< CP, CP =< 72191 -> 'X';
uts46_map(CP) when 72192 =< CP, CP =< 72254 -> 'V';
uts46_map(CP) when 72255 =< CP, CP =< 72262 -> 'V';
uts46_map(CP) when 72264 =< CP, CP =< 72271 -> 'X';
uts46_map(CP) when 72272 =< CP, CP =< 72323 -> 'V';
uts46_map(CP) when 72324 =< CP, CP =< 72325 -> 'V';
uts46_map(CP) when 72326 =< CP, CP =< 72345 -> 'V';
uts46_map(CP) when 72346 =< CP, CP =< 72348 -> 'V';
uts46_map(CP) when 72350 =< CP, CP =< 72354 -> 'V';
uts46_map(CP) when 72355 =< CP, CP =< 72383 -> 'X';
uts46_map(CP) when 72384 =< CP, CP =< 72440 -> 'V';
uts46_map(CP) when 72441 =< CP, CP =< 72703 -> 'X';
uts46_map(CP) when 72704 =< CP, CP =< 72712 -> 'V';
uts46_map(CP) when 72714 =< CP, CP =< 72758 -> 'V';
uts46_map(CP) when 72760 =< CP, CP =< 72768 -> 'V';
uts46_map(CP) when 72769 =< CP, CP =< 72773 -> 'V';
uts46_map(CP) when 72774 =< CP, CP =< 72783 -> 'X';
uts46_map(CP) when 72784 =< CP, CP =< 72793 -> 'V';
uts46_map(CP) when 72794 =< CP, CP =< 72812 -> 'V';
uts46_map(CP) when 72813 =< CP, CP =< 72815 -> 'X';
uts46_map(CP) when 72816 =< CP, CP =< 72817 -> 'V';
uts46_map(CP) when 72818 =< CP, CP =< 72847 -> 'V';
uts46_map(CP) when 72848 =< CP, CP =< 72849 -> 'X';
uts46_map(CP) when 72850 =< CP, CP =< 72871 -> 'V';
uts46_map(CP) when 72873 =< CP, CP =< 72886 -> 'V';
uts46_map(CP) when 72887 =< CP, CP =< 72959 -> 'X';
uts46_map(CP) when 72960 =< CP, CP =< 72966 -> 'V';
uts46_map(CP) when 72968 =< CP, CP =< 72969 -> 'V';
uts46_map(CP) when 72971 =< CP, CP =< 73014 -> 'V';
uts46_map(CP) when 73015 =< CP, CP =< 73017 -> 'X';
uts46_map(CP) when 73020 =< CP, CP =< 73021 -> 'V';
uts46_map(CP) when 73023 =< CP, CP =< 73031 -> 'V';
uts46_map(CP) when 73032 =< CP, CP =< 73039 -> 'X';
uts46_map(CP) when 73040 =< CP, CP =< 73049 -> 'V';
uts46_map(CP) when 73050 =< CP, CP =< 73055 -> 'X';
uts46_map(CP) when 73056 =< CP, CP =< 73061 -> 'V';
uts46_map(CP) when 73063 =< CP, CP =< 73064 -> 'V';
uts46_map(CP) when 73066 =< CP, CP =< 73102 -> 'V';
uts46_map(CP) when 73104 =< CP, CP =< 73105 -> 'V';
uts46_map(CP) when 73107 =< CP, CP =< 73112 -> 'V';
uts46_map(CP) when 73113 =< CP, CP =< 73119 -> 'X';
uts46_map(CP) when 73120 =< CP, CP =< 73129 -> 'V';
uts46_map(CP) when 73130 =< CP, CP =< 73439 -> 'X';
uts46_map(CP) when 73440 =< CP, CP =< 73462 -> 'V';
uts46_map(CP) when 73463 =< CP, CP =< 73464 -> 'V';
uts46_map(CP) when 73465 =< CP, CP =< 73647 -> 'X';
uts46_map(CP) when 73649 =< CP, CP =< 73663 -> 'X';
uts46_map(CP) when 73664 =< CP, CP =< 73713 -> 'V';
uts46_map(CP) when 73714 =< CP, CP =< 73726 -> 'X';
uts46_map(CP) when 73728 =< CP, CP =< 74606 -> 'V';
uts46_map(CP) when 74607 =< CP, CP =< 74648 -> 'V';
uts46_map(CP) when 74650 =< CP, CP =< 74751 -> 'X';
uts46_map(CP) when 74752 =< CP, CP =< 74850 -> 'V';
uts46_map(CP) when 74851 =< CP, CP =< 74862 -> 'V';
uts46_map(CP) when 74864 =< CP, CP =< 74867 -> 'V';
uts46_map(CP) when 74869 =< CP, CP =< 74879 -> 'X';
uts46_map(CP) when 74880 =< CP, CP =< 75075 -> 'V';
uts46_map(CP) when 75076 =< CP, CP =< 77823 -> 'X';
uts46_map(CP) when 77824 =< CP, CP =< 78894 -> 'V';
uts46_map(CP) when 78896 =< CP, CP =< 78904 -> 'X';
uts46_map(CP) when 78905 =< CP, CP =< 82943 -> 'X';
uts46_map(CP) when 82944 =< CP, CP =< 83526 -> 'V';
uts46_map(CP) when 83527 =< CP, CP =< 92159 -> 'X';
uts46_map(CP) when 92160 =< CP, CP =< 92728 -> 'V';
uts46_map(CP) when 92729 =< CP, CP =< 92735 -> 'X';
uts46_map(CP) when 92736 =< CP, CP =< 92766 -> 'V';
uts46_map(CP) when 92768 =< CP, CP =< 92777 -> 'V';
uts46_map(CP) when 92778 =< CP, CP =< 92781 -> 'X';
uts46_map(CP) when 92782 =< CP, CP =< 92783 -> 'V';
uts46_map(CP) when 92784 =< CP, CP =< 92879 -> 'X';
uts46_map(CP) when 92880 =< CP, CP =< 92909 -> 'V';
uts46_map(CP) when 92910 =< CP, CP =< 92911 -> 'X';
uts46_map(CP) when 92912 =< CP, CP =< 92916 -> 'V';
uts46_map(CP) when 92918 =< CP, CP =< 92927 -> 'X';
uts46_map(CP) when 92928 =< CP, CP =< 92982 -> 'V';
uts46_map(CP) when 92983 =< CP, CP =< 92991 -> 'V';
uts46_map(CP) when 92992 =< CP, CP =< 92995 -> 'V';
uts46_map(CP) when 92996 =< CP, CP =< 92997 -> 'V';
uts46_map(CP) when 92998 =< CP, CP =< 93007 -> 'X';
uts46_map(CP) when 93008 =< CP, CP =< 93017 -> 'V';
uts46_map(CP) when 93019 =< CP, CP =< 93025 -> 'V';
uts46_map(CP) when 93027 =< CP, CP =< 93047 -> 'V';
uts46_map(CP) when 93048 =< CP, CP =< 93052 -> 'X';
uts46_map(CP) when 93053 =< CP, CP =< 93071 -> 'V';
uts46_map(CP) when 93072 =< CP, CP =< 93759 -> 'X';
uts46_map(CP) when 93792 =< CP, CP =< 93823 -> 'V';
uts46_map(CP) when 93824 =< CP, CP =< 93850 -> 'V';
uts46_map(CP) when 93851 =< CP, CP =< 93951 -> 'X';
uts46_map(CP) when 93952 =< CP, CP =< 94020 -> 'V';
uts46_map(CP) when 94021 =< CP, CP =< 94026 -> 'V';
uts46_map(CP) when 94027 =< CP, CP =< 94030 -> 'X';
uts46_map(CP) when 94032 =< CP, CP =< 94078 -> 'V';
uts46_map(CP) when 94079 =< CP, CP =< 94087 -> 'V';
uts46_map(CP) when 94088 =< CP, CP =< 94094 -> 'X';
uts46_map(CP) when 94095 =< CP, CP =< 94111 -> 'V';
uts46_map(CP) when 94112 =< CP, CP =< 94175 -> 'X';
uts46_map(CP) when 94181 =< CP, CP =< 94191 -> 'X';
uts46_map(CP) when 94192 =< CP, CP =< 94193 -> 'V';
uts46_map(CP) when 94194 =< CP, CP =< 94207 -> 'X';
uts46_map(CP) when 94208 =< CP, CP =< 100332 -> 'V';
uts46_map(CP) when 100333 =< CP, CP =< 100337 -> 'V';
uts46_map(CP) when 100338 =< CP, CP =< 100343 -> 'V';
uts46_map(CP) when 100344 =< CP, CP =< 100351 -> 'X';
uts46_map(CP) when 100352 =< CP, CP =< 101106 -> 'V';
uts46_map(CP) when 101107 =< CP, CP =< 101589 -> 'V';
uts46_map(CP) when 101590 =< CP, CP =< 101631 -> 'X';
uts46_map(CP) when 101632 =< CP, CP =< 101640 -> 'V';
uts46_map(CP) when 101641 =< CP, CP =< 110591 -> 'X';
uts46_map(CP) when 110592 =< CP, CP =< 110593 -> 'V';
uts46_map(CP) when 110594 =< CP, CP =< 110878 -> 'V';
uts46_map(CP) when 110879 =< CP, CP =< 110927 -> 'X';
uts46_map(CP) when 110928 =< CP, CP =< 110930 -> 'V';
uts46_map(CP) when 110931 =< CP, CP =< 110947 -> 'X';
uts46_map(CP) when 110948 =< CP, CP =< 110951 -> 'V';
uts46_map(CP) when 110952 =< CP, CP =< 110959 -> 'X';
uts46_map(CP) when 110960 =< CP, CP =< 111355 -> 'V';
uts46_map(CP) when 111356 =< CP, CP =< 113663 -> 'X';
uts46_map(CP) when 113664 =< CP, CP =< 113770 -> 'V';
uts46_map(CP) when 113771 =< CP, CP =< 113775 -> 'X';
uts46_map(CP) when 113776 =< CP, CP =< 113788 -> 'V';
uts46_map(CP) when 113789 =< CP, CP =< 113791 -> 'X';
uts46_map(CP) when 113792 =< CP, CP =< 113800 -> 'V';
uts46_map(CP) when 113801 =< CP, CP =< 113807 -> 'X';
uts46_map(CP) when 113808 =< CP, CP =< 113817 -> 'V';
uts46_map(CP) when 113818 =< CP, CP =< 113819 -> 'X';
uts46_map(CP) when 113821 =< CP, CP =< 113822 -> 'V';
uts46_map(CP) when 113824 =< CP, CP =< 113827 -> 'I';
uts46_map(CP) when 113828 =< CP, CP =< 118783 -> 'X';
uts46_map(CP) when 118784 =< CP, CP =< 119029 -> 'V';
uts46_map(CP) when 119030 =< CP, CP =< 119039 -> 'X';
uts46_map(CP) when 119040 =< CP, CP =< 119078 -> 'V';
uts46_map(CP) when 119079 =< CP, CP =< 119080 -> 'X';
uts46_map(CP) when 119082 =< CP, CP =< 119133 -> 'V';
uts46_map(CP) when 119141 =< CP, CP =< 119154 -> 'V';
uts46_map(CP) when 119155 =< CP, CP =< 119162 -> 'X';
uts46_map(CP) when 119163 =< CP, CP =< 119226 -> 'V';
uts46_map(CP) when 119233 =< CP, CP =< 119261 -> 'V';
uts46_map(CP) when 119262 =< CP, CP =< 119272 -> 'V';
uts46_map(CP) when 119273 =< CP, CP =< 119295 -> 'X';
uts46_map(CP) when 119296 =< CP, CP =< 119365 -> 'V';
uts46_map(CP) when 119366 =< CP, CP =< 119519 -> 'X';
uts46_map(CP) when 119520 =< CP, CP =< 119539 -> 'V';
uts46_map(CP) when 119540 =< CP, CP =< 119551 -> 'X';
uts46_map(CP) when 119552 =< CP, CP =< 119638 -> 'V';
uts46_map(CP) when 119639 =< CP, CP =< 119647 -> 'X';
uts46_map(CP) when 119648 =< CP, CP =< 119665 -> 'V';
uts46_map(CP) when 119666 =< CP, CP =< 119672 -> 'V';
uts46_map(CP) when 119673 =< CP, CP =< 119807 -> 'X';
uts46_map(CP) when 119968 =< CP, CP =< 119969 -> 'X';
uts46_map(CP) when 119971 =< CP, CP =< 119972 -> 'X';
uts46_map(CP) when 119975 =< CP, CP =< 119976 -> 'X';
uts46_map(CP) when 120075 =< CP, CP =< 120076 -> 'X';
uts46_map(CP) when 120135 =< CP, CP =< 120137 -> 'X';
uts46_map(CP) when 120486 =< CP, CP =< 120487 -> 'X';
uts46_map(CP) when 120531 =< CP, CP =< 120532 -> {'M', [963]};
uts46_map(CP) when 120589 =< CP, CP =< 120590 -> {'M', [963]};
uts46_map(CP) when 120647 =< CP, CP =< 120648 -> {'M', [963]};
uts46_map(CP) when 120705 =< CP, CP =< 120706 -> {'M', [963]};
uts46_map(CP) when 120763 =< CP, CP =< 120764 -> {'M', [963]};
uts46_map(CP) when 120778 =< CP, CP =< 120779 -> {'M', [989]};
uts46_map(CP) when 120780 =< CP, CP =< 120781 -> 'X';
uts46_map(CP) when 120832 =< CP, CP =< 121343 -> 'V';
uts46_map(CP) when 121344 =< CP, CP =< 121398 -> 'V';
uts46_map(CP) when 121399 =< CP, CP =< 121402 -> 'V';
uts46_map(CP) when 121403 =< CP, CP =< 121452 -> 'V';
uts46_map(CP) when 121453 =< CP, CP =< 121460 -> 'V';
uts46_map(CP) when 121462 =< CP, CP =< 121475 -> 'V';
uts46_map(CP) when 121477 =< CP, CP =< 121483 -> 'V';
uts46_map(CP) when 121484 =< CP, CP =< 121498 -> 'X';
uts46_map(CP) when 121499 =< CP, CP =< 121503 -> 'V';
uts46_map(CP) when 121505 =< CP, CP =< 121519 -> 'V';
uts46_map(CP) when 121520 =< CP, CP =< 122879 -> 'X';
uts46_map(CP) when 122880 =< CP, CP =< 122886 -> 'V';
uts46_map(CP) when 122888 =< CP, CP =< 122904 -> 'V';
uts46_map(CP) when 122905 =< CP, CP =< 122906 -> 'X';
uts46_map(CP) when 122907 =< CP, CP =< 122913 -> 'V';
uts46_map(CP) when 122915 =< CP, CP =< 122916 -> 'V';
uts46_map(CP) when 122918 =< CP, CP =< 122922 -> 'V';
uts46_map(CP) when 122923 =< CP, CP =< 123135 -> 'X';
uts46_map(CP) when 123136 =< CP, CP =< 123180 -> 'V';
uts46_map(CP) when 123181 =< CP, CP =< 123183 -> 'X';
uts46_map(CP) when 123184 =< CP, CP =< 123197 -> 'V';
uts46_map(CP) when 123198 =< CP, CP =< 123199 -> 'X';
uts46_map(CP) when 123200 =< CP, CP =< 123209 -> 'V';
uts46_map(CP) when 123210 =< CP, CP =< 123213 -> 'X';
uts46_map(CP) when 123216 =< CP, CP =< 123583 -> 'X';
uts46_map(CP) when 123584 =< CP, CP =< 123641 -> 'V';
uts46_map(CP) when 123642 =< CP, CP =< 123646 -> 'X';
uts46_map(CP) when 123648 =< CP, CP =< 124927 -> 'X';
uts46_map(CP) when 124928 =< CP, CP =< 125124 -> 'V';
uts46_map(CP) when 125125 =< CP, CP =< 125126 -> 'X';
uts46_map(CP) when 125127 =< CP, CP =< 125135 -> 'V';
uts46_map(CP) when 125136 =< CP, CP =< 125142 -> 'V';
uts46_map(CP) when 125143 =< CP, CP =< 125183 -> 'X';
uts46_map(CP) when 125218 =< CP, CP =< 125258 -> 'V';
uts46_map(CP) when 125260 =< CP, CP =< 125263 -> 'X';
uts46_map(CP) when 125264 =< CP, CP =< 125273 -> 'V';
uts46_map(CP) when 125274 =< CP, CP =< 125277 -> 'X';
uts46_map(CP) when 125278 =< CP, CP =< 125279 -> 'V';
uts46_map(CP) when 125280 =< CP, CP =< 126064 -> 'X';
uts46_map(CP) when 126065 =< CP, CP =< 126132 -> 'V';
uts46_map(CP) when 126133 =< CP, CP =< 126208 -> 'X';
uts46_map(CP) when 126209 =< CP, CP =< 126269 -> 'V';
uts46_map(CP) when 126270 =< CP, CP =< 126463 -> 'X';
uts46_map(CP) when 126501 =< CP, CP =< 126502 -> 'X';
uts46_map(CP) when 126524 =< CP, CP =< 126529 -> 'X';
uts46_map(CP) when 126531 =< CP, CP =< 126534 -> 'X';
uts46_map(CP) when 126549 =< CP, CP =< 126550 -> 'X';
uts46_map(CP) when 126565 =< CP, CP =< 126566 -> 'X';
uts46_map(CP) when 126620 =< CP, CP =< 126624 -> 'X';
uts46_map(CP) when 126652 =< CP, CP =< 126703 -> 'X';
uts46_map(CP) when 126704 =< CP, CP =< 126705 -> 'V';
uts46_map(CP) when 126706 =< CP, CP =< 126975 -> 'X';
uts46_map(CP) when 126976 =< CP, CP =< 127019 -> 'V';
uts46_map(CP) when 127020 =< CP, CP =< 127023 -> 'X';
uts46_map(CP) when 127024 =< CP, CP =< 127123 -> 'V';
uts46_map(CP) when 127124 =< CP, CP =< 127135 -> 'X';
uts46_map(CP) when 127136 =< CP, CP =< 127150 -> 'V';
uts46_map(CP) when 127151 =< CP, CP =< 127152 -> 'X';
uts46_map(CP) when 127153 =< CP, CP =< 127166 -> 'V';
uts46_map(CP) when 127169 =< CP, CP =< 127183 -> 'V';
uts46_map(CP) when 127185 =< CP, CP =< 127199 -> 'V';
uts46_map(CP) when 127200 =< CP, CP =< 127221 -> 'V';
uts46_map(CP) when 127222 =< CP, CP =< 127231 -> 'X';
uts46_map(CP) when 127243 =< CP, CP =< 127244 -> 'V';
uts46_map(CP) when 127245 =< CP, CP =< 127247 -> 'V';
uts46_map(CP) when 127312 =< CP, CP =< 127318 -> 'V';
uts46_map(CP) when 127320 =< CP, CP =< 127326 -> 'V';
uts46_map(CP) when 127328 =< CP, CP =< 127337 -> 'V';
uts46_map(CP) when 127341 =< CP, CP =< 127343 -> 'V';
uts46_map(CP) when 127344 =< CP, CP =< 127352 -> 'V';
uts46_map(CP) when 127355 =< CP, CP =< 127356 -> 'V';
uts46_map(CP) when 127357 =< CP, CP =< 127358 -> 'V';
uts46_map(CP) when 127360 =< CP, CP =< 127369 -> 'V';
uts46_map(CP) when 127370 =< CP, CP =< 127373 -> 'V';
uts46_map(CP) when 127374 =< CP, CP =< 127375 -> 'V';
uts46_map(CP) when 127377 =< CP, CP =< 127386 -> 'V';
uts46_map(CP) when 127387 =< CP, CP =< 127404 -> 'V';
uts46_map(CP) when 127406 =< CP, CP =< 127461 -> 'X';
uts46_map(CP) when 127462 =< CP, CP =< 127487 -> 'V';
uts46_map(CP) when 127491 =< CP, CP =< 127503 -> 'X';
uts46_map(CP) when 127548 =< CP, CP =< 127551 -> 'X';
uts46_map(CP) when 127561 =< CP, CP =< 127567 -> 'X';
uts46_map(CP) when 127570 =< CP, CP =< 127583 -> 'X';
uts46_map(CP) when 127584 =< CP, CP =< 127589 -> 'V';
uts46_map(CP) when 127590 =< CP, CP =< 127743 -> 'X';
uts46_map(CP) when 127744 =< CP, CP =< 127776 -> 'V';
uts46_map(CP) when 127777 =< CP, CP =< 127788 -> 'V';
uts46_map(CP) when 127789 =< CP, CP =< 127791 -> 'V';
uts46_map(CP) when 127792 =< CP, CP =< 127797 -> 'V';
uts46_map(CP) when 127799 =< CP, CP =< 127868 -> 'V';
uts46_map(CP) when 127870 =< CP, CP =< 127871 -> 'V';
uts46_map(CP) when 127872 =< CP, CP =< 127891 -> 'V';
uts46_map(CP) when 127892 =< CP, CP =< 127903 -> 'V';
uts46_map(CP) when 127904 =< CP, CP =< 127940 -> 'V';
uts46_map(CP) when 127942 =< CP, CP =< 127946 -> 'V';
uts46_map(CP) when 127947 =< CP, CP =< 127950 -> 'V';
uts46_map(CP) when 127951 =< CP, CP =< 127955 -> 'V';
uts46_map(CP) when 127956 =< CP, CP =< 127967 -> 'V';
uts46_map(CP) when 127968 =< CP, CP =< 127984 -> 'V';
uts46_map(CP) when 127985 =< CP, CP =< 127991 -> 'V';
uts46_map(CP) when 127992 =< CP, CP =< 127999 -> 'V';
uts46_map(CP) when 128000 =< CP, CP =< 128062 -> 'V';
uts46_map(CP) when 128066 =< CP, CP =< 128247 -> 'V';
uts46_map(CP) when 128249 =< CP, CP =< 128252 -> 'V';
uts46_map(CP) when 128253 =< CP, CP =< 128254 -> 'V';
uts46_map(CP) when 128256 =< CP, CP =< 128317 -> 'V';
uts46_map(CP) when 128318 =< CP, CP =< 128319 -> 'V';
uts46_map(CP) when 128320 =< CP, CP =< 128323 -> 'V';
uts46_map(CP) when 128324 =< CP, CP =< 128330 -> 'V';
uts46_map(CP) when 128331 =< CP, CP =< 128335 -> 'V';
uts46_map(CP) when 128336 =< CP, CP =< 128359 -> 'V';
uts46_map(CP) when 128360 =< CP, CP =< 128377 -> 'V';
uts46_map(CP) when 128379 =< CP, CP =< 128419 -> 'V';
uts46_map(CP) when 128421 =< CP, CP =< 128506 -> 'V';
uts46_map(CP) when 128507 =< CP, CP =< 128511 -> 'V';
uts46_map(CP) when 128513 =< CP, CP =< 128528 -> 'V';
uts46_map(CP) when 128530 =< CP, CP =< 128532 -> 'V';
uts46_map(CP) when 128540 =< CP, CP =< 128542 -> 'V';
uts46_map(CP) when 128544 =< CP, CP =< 128549 -> 'V';
uts46_map(CP) when 128550 =< CP, CP =< 128551 -> 'V';
uts46_map(CP) when 128552 =< CP, CP =< 128555 -> 'V';
uts46_map(CP) when 128558 =< CP, CP =< 128559 -> 'V';
uts46_map(CP) when 128560 =< CP, CP =< 128563 -> 'V';
uts46_map(CP) when 128565 =< CP, CP =< 128576 -> 'V';
uts46_map(CP) when 128577 =< CP, CP =< 128578 -> 'V';
uts46_map(CP) when 128579 =< CP, CP =< 128580 -> 'V';
uts46_map(CP) when 128581 =< CP, CP =< 128591 -> 'V';
uts46_map(CP) when 128592 =< CP, CP =< 128639 -> 'V';
uts46_map(CP) when 128640 =< CP, CP =< 128709 -> 'V';
uts46_map(CP) when 128710 =< CP, CP =< 128719 -> 'V';
uts46_map(CP) when 128721 =< CP, CP =< 128722 -> 'V';
uts46_map(CP) when 128723 =< CP, CP =< 128724 -> 'V';
uts46_map(CP) when 128726 =< CP, CP =< 128727 -> 'V';
uts46_map(CP) when 128728 =< CP, CP =< 128735 -> 'X';
uts46_map(CP) when 128736 =< CP, CP =< 128748 -> 'V';
uts46_map(CP) when 128749 =< CP, CP =< 128751 -> 'X';
uts46_map(CP) when 128752 =< CP, CP =< 128755 -> 'V';
uts46_map(CP) when 128756 =< CP, CP =< 128758 -> 'V';
uts46_map(CP) when 128759 =< CP, CP =< 128760 -> 'V';
uts46_map(CP) when 128763 =< CP, CP =< 128764 -> 'V';
uts46_map(CP) when 128765 =< CP, CP =< 128767 -> 'X';
uts46_map(CP) when 128768 =< CP, CP =< 128883 -> 'V';
uts46_map(CP) when 128884 =< CP, CP =< 128895 -> 'X';
uts46_map(CP) when 128896 =< CP, CP =< 128980 -> 'V';
uts46_map(CP) when 128981 =< CP, CP =< 128984 -> 'V';
uts46_map(CP) when 128985 =< CP, CP =< 128991 -> 'X';
uts46_map(CP) when 128992 =< CP, CP =< 129003 -> 'V';
uts46_map(CP) when 129004 =< CP, CP =< 129023 -> 'X';
uts46_map(CP) when 129024 =< CP, CP =< 129035 -> 'V';
uts46_map(CP) when 129036 =< CP, CP =< 129039 -> 'X';
uts46_map(CP) when 129040 =< CP, CP =< 129095 -> 'V';
uts46_map(CP) when 129096 =< CP, CP =< 129103 -> 'X';
uts46_map(CP) when 129104 =< CP, CP =< 129113 -> 'V';
uts46_map(CP) when 129114 =< CP, CP =< 129119 -> 'X';
uts46_map(CP) when 129120 =< CP, CP =< 129159 -> 'V';
uts46_map(CP) when 129160 =< CP, CP =< 129167 -> 'X';
uts46_map(CP) when 129168 =< CP, CP =< 129197 -> 'V';
uts46_map(CP) when 129198 =< CP, CP =< 129199 -> 'X';
uts46_map(CP) when 129200 =< CP, CP =< 129201 -> 'V';
uts46_map(CP) when 129202 =< CP, CP =< 129279 -> 'X';
uts46_map(CP) when 129280 =< CP, CP =< 129291 -> 'V';
uts46_map(CP) when 129293 =< CP, CP =< 129295 -> 'V';
uts46_map(CP) when 129296 =< CP, CP =< 129304 -> 'V';
uts46_map(CP) when 129305 =< CP, CP =< 129310 -> 'V';
uts46_map(CP) when 129312 =< CP, CP =< 129319 -> 'V';
uts46_map(CP) when 129320 =< CP, CP =< 129327 -> 'V';
uts46_map(CP) when 129329 =< CP, CP =< 129330 -> 'V';
uts46_map(CP) when 129331 =< CP, CP =< 129342 -> 'V';
uts46_map(CP) when 129344 =< CP, CP =< 129355 -> 'V';
uts46_map(CP) when 129357 =< CP, CP =< 129359 -> 'V';
uts46_map(CP) when 129360 =< CP, CP =< 129374 -> 'V';
uts46_map(CP) when 129375 =< CP, CP =< 129387 -> 'V';
uts46_map(CP) when 129388 =< CP, CP =< 129392 -> 'V';
uts46_map(CP) when 129395 =< CP, CP =< 129398 -> 'V';
uts46_map(CP) when 129399 =< CP, CP =< 129400 -> 'V';
uts46_map(CP) when 129404 =< CP, CP =< 129407 -> 'V';
uts46_map(CP) when 129408 =< CP, CP =< 129412 -> 'V';
uts46_map(CP) when 129413 =< CP, CP =< 129425 -> 'V';
uts46_map(CP) when 129426 =< CP, CP =< 129431 -> 'V';
uts46_map(CP) when 129432 =< CP, CP =< 129442 -> 'V';
uts46_map(CP) when 129443 =< CP, CP =< 129444 -> 'V';
uts46_map(CP) when 129445 =< CP, CP =< 129450 -> 'V';
uts46_map(CP) when 129451 =< CP, CP =< 129453 -> 'V';
uts46_map(CP) when 129454 =< CP, CP =< 129455 -> 'V';
uts46_map(CP) when 129456 =< CP, CP =< 129465 -> 'V';
uts46_map(CP) when 129466 =< CP, CP =< 129471 -> 'V';
uts46_map(CP) when 129473 =< CP, CP =< 129474 -> 'V';
uts46_map(CP) when 129475 =< CP, CP =< 129482 -> 'V';
uts46_map(CP) when 129485 =< CP, CP =< 129487 -> 'V';
uts46_map(CP) when 129488 =< CP, CP =< 129510 -> 'V';
uts46_map(CP) when 129511 =< CP, CP =< 129535 -> 'V';
uts46_map(CP) when 129536 =< CP, CP =< 129619 -> 'V';
uts46_map(CP) when 129620 =< CP, CP =< 129631 -> 'X';
uts46_map(CP) when 129632 =< CP, CP =< 129645 -> 'V';
uts46_map(CP) when 129646 =< CP, CP =< 129647 -> 'X';
uts46_map(CP) when 129648 =< CP, CP =< 129651 -> 'V';
uts46_map(CP) when 129653 =< CP, CP =< 129655 -> 'X';
uts46_map(CP) when 129656 =< CP, CP =< 129658 -> 'V';
uts46_map(CP) when 129659 =< CP, CP =< 129663 -> 'X';
uts46_map(CP) when 129664 =< CP, CP =< 129666 -> 'V';
uts46_map(CP) when 129667 =< CP, CP =< 129670 -> 'V';
uts46_map(CP) when 129671 =< CP, CP =< 129679 -> 'X';
uts46_map(CP) when 129680 =< CP, CP =< 129685 -> 'V';
uts46_map(CP) when 129686 =< CP, CP =< 129704 -> 'V';
uts46_map(CP) when 129705 =< CP, CP =< 129711 -> 'X';
uts46_map(CP) when 129712 =< CP, CP =< 129718 -> 'V';
uts46_map(CP) when 129719 =< CP, CP =< 129727 -> 'X';
uts46_map(CP) when 129728 =< CP, CP =< 129730 -> 'V';
uts46_map(CP) when 129731 =< CP, CP =< 129743 -> 'X';
uts46_map(CP) when 129744 =< CP, CP =< 129750 -> 'V';
uts46_map(CP) when 129751 =< CP, CP =< 129791 -> 'X';
uts46_map(CP) when 129792 =< CP, CP =< 129938 -> 'V';
uts46_map(CP) when 129940 =< CP, CP =< 129994 -> 'V';
uts46_map(CP) when 129995 =< CP, CP =< 130031 -> 'X';
uts46_map(CP) when 130042 =< CP, CP =< 131069 -> 'X';
uts46_map(CP) when 131070 =< CP, CP =< 131071 -> 'X';
uts46_map(CP) when 131072 =< CP, CP =< 173782 -> 'V';
uts46_map(CP) when 173783 =< CP, CP =< 173789 -> 'V';
uts46_map(CP) when 173790 =< CP, CP =< 173823 -> 'X';
uts46_map(CP) when 173824 =< CP, CP =< 177972 -> 'V';
uts46_map(CP) when 177973 =< CP, CP =< 177983 -> 'X';
uts46_map(CP) when 177984 =< CP, CP =< 178205 -> 'V';
uts46_map(CP) when 178206 =< CP, CP =< 178207 -> 'X';
uts46_map(CP) when 178208 =< CP, CP =< 183969 -> 'V';
uts46_map(CP) when 183970 =< CP, CP =< 183983 -> 'X';
uts46_map(CP) when 183984 =< CP, CP =< 191456 -> 'V';
uts46_map(CP) when 191457 =< CP, CP =< 194559 -> 'X';
uts46_map(CP) when 194609 =< CP, CP =< 194611 -> {'M', [21375]};
uts46_map(CP) when 194629 =< CP, CP =< 194630 -> {'M', [21892]};
uts46_map(CP) when 194666 =< CP, CP =< 194667 -> {'M', [23358]};
uts46_map(CP) when 194705 =< CP, CP =< 194706 -> {'M', [140081]};
uts46_map(CP) when 194708 =< CP, CP =< 194709 -> {'M', [24354]};
uts46_map(CP) when 194860 =< CP, CP =< 194861 -> {'M', [16056]};
uts46_map(CP) when 194886 =< CP, CP =< 194887 -> {'M', [30495]};
uts46_map(CP) when 194909 =< CP, CP =< 194910 -> {'M', [154279]};
uts46_map(CP) when 195070 =< CP, CP =< 195071 -> {'M', [38923]};
uts46_map(CP) when 195102 =< CP, CP =< 196605 -> 'X';
uts46_map(CP) when 196606 =< CP, CP =< 196607 -> 'X';
uts46_map(CP) when 196608 =< CP, CP =< 201546 -> 'V';
uts46_map(CP) when 201547 =< CP, CP =< 262141 -> 'X';
uts46_map(CP) when 262142 =< CP, CP =< 262143 -> 'X';
uts46_map(CP) when 262144 =< CP, CP =< 327677 -> 'X';
uts46_map(CP) when 327678 =< CP, CP =< 327679 -> 'X';
uts46_map(CP) when 327680 =< CP, CP =< 393213 -> 'X';
uts46_map(CP) when 393214 =< CP, CP =< 393215 -> 'X';
uts46_map(CP) when 393216 =< CP, CP =< 458749 -> 'X';
uts46_map(CP) when 458750 =< CP, CP =< 458751 -> 'X';
uts46_map(CP) when 458752 =< CP, CP =< 524285 -> 'X';
uts46_map(CP) when 524286 =< CP, CP =< 524287 -> 'X';
uts46_map(CP) when 524288 =< CP, CP =< 589821 -> 'X';
uts46_map(CP) when 589822 =< CP, CP =< 589823 -> 'X';
uts46_map(CP) when 589824 =< CP, CP =< 655357 -> 'X';
uts46_map(CP) when 655358 =< CP, CP =< 655359 -> 'X';
uts46_map(CP) when 655360 =< CP, CP =< 720893 -> 'X';
uts46_map(CP) when 720894 =< CP, CP =< 720895 -> 'X';
uts46_map(CP) when 720896 =< CP, CP =< 786429 -> 'X';
uts46_map(CP) when 786430 =< CP, CP =< 786431 -> 'X';
uts46_map(CP) when 786432 =< CP, CP =< 851965 -> 'X';
uts46_map(CP) when 851966 =< CP, CP =< 851967 -> 'X';
uts46_map(CP) when 851968 =< CP, CP =< 917501 -> 'X';
uts46_map(CP) when 917502 =< CP, CP =< 917503 -> 'X';
uts46_map(CP) when 917506 =< CP, CP =< 917535 -> 'X';
uts46_map(CP) when 917536 =< CP, CP =< 917631 -> 'X';
uts46_map(CP) when 917632 =< CP, CP =< 917759 -> 'X';
uts46_map(CP) when 917760 =< CP, CP =< 917999 -> 'I';
uts46_map(CP) when 918000 =< CP, CP =< 983037 -> 'X';
uts46_map(CP) when 983038 =< CP, CP =< 983039 -> 'X';
uts46_map(CP) when 983040 =< CP, CP =< 1048573 -> 'X';
uts46_map(CP) when 1048574 =< CP, CP =< 1048575 -> 'X';
uts46_map(CP) when 1048576 =< CP, CP =< 1114109 -> 'X';
uts46_map(CP) when 1114110 =< CP, CP =< 1114111 -> 'X';
uts46_map(_) -> erlang:error(badarg).
