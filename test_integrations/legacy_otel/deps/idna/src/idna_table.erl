%%
%% this file is generated do not modify
%% see ../uc_spec/gen_idna_table.escript

-module(idna_table).
-compile(compressed).
-export([lookup/1]).
-export([disallowed_p/1, contextj_p/1, contexto_p/1, unassigned_p/1, valid_p/1]).
disallowed_p(CP) -> lookup(CP) == 'DISALLOWED'.
contextj_p(CP) -> lookup(CP) == 'CONTEXTJ'.
contexto_p(CP) -> lookup(CP) == 'CONTEXTO'.
unassigned_p(CP) -> lookup(CP) == 'UNASSIGNED'.
valid_p(CP) -> lookup(CP) == 'PVALID'.
lookup(45) -> 'PVALID';
lookup(183) -> 'CONTEXTO';
lookup(247) -> 'DISALLOWED';
lookup(256) -> 'DISALLOWED';
lookup(257) -> 'PVALID';
lookup(258) -> 'DISALLOWED';
lookup(259) -> 'PVALID';
lookup(260) -> 'DISALLOWED';
lookup(261) -> 'PVALID';
lookup(262) -> 'DISALLOWED';
lookup(263) -> 'PVALID';
lookup(264) -> 'DISALLOWED';
lookup(265) -> 'PVALID';
lookup(266) -> 'DISALLOWED';
lookup(267) -> 'PVALID';
lookup(268) -> 'DISALLOWED';
lookup(269) -> 'PVALID';
lookup(270) -> 'DISALLOWED';
lookup(271) -> 'PVALID';
lookup(272) -> 'DISALLOWED';
lookup(273) -> 'PVALID';
lookup(274) -> 'DISALLOWED';
lookup(275) -> 'PVALID';
lookup(276) -> 'DISALLOWED';
lookup(277) -> 'PVALID';
lookup(278) -> 'DISALLOWED';
lookup(279) -> 'PVALID';
lookup(280) -> 'DISALLOWED';
lookup(281) -> 'PVALID';
lookup(282) -> 'DISALLOWED';
lookup(283) -> 'PVALID';
lookup(284) -> 'DISALLOWED';
lookup(285) -> 'PVALID';
lookup(286) -> 'DISALLOWED';
lookup(287) -> 'PVALID';
lookup(288) -> 'DISALLOWED';
lookup(289) -> 'PVALID';
lookup(290) -> 'DISALLOWED';
lookup(291) -> 'PVALID';
lookup(292) -> 'DISALLOWED';
lookup(293) -> 'PVALID';
lookup(294) -> 'DISALLOWED';
lookup(295) -> 'PVALID';
lookup(296) -> 'DISALLOWED';
lookup(297) -> 'PVALID';
lookup(298) -> 'DISALLOWED';
lookup(299) -> 'PVALID';
lookup(300) -> 'DISALLOWED';
lookup(301) -> 'PVALID';
lookup(302) -> 'DISALLOWED';
lookup(303) -> 'PVALID';
lookup(304) -> 'DISALLOWED';
lookup(305) -> 'PVALID';
lookup(309) -> 'PVALID';
lookup(310) -> 'DISALLOWED';
lookup(313) -> 'DISALLOWED';
lookup(314) -> 'PVALID';
lookup(315) -> 'DISALLOWED';
lookup(316) -> 'PVALID';
lookup(317) -> 'DISALLOWED';
lookup(318) -> 'PVALID';
lookup(322) -> 'PVALID';
lookup(323) -> 'DISALLOWED';
lookup(324) -> 'PVALID';
lookup(325) -> 'DISALLOWED';
lookup(326) -> 'PVALID';
lookup(327) -> 'DISALLOWED';
lookup(328) -> 'PVALID';
lookup(331) -> 'PVALID';
lookup(332) -> 'DISALLOWED';
lookup(333) -> 'PVALID';
lookup(334) -> 'DISALLOWED';
lookup(335) -> 'PVALID';
lookup(336) -> 'DISALLOWED';
lookup(337) -> 'PVALID';
lookup(338) -> 'DISALLOWED';
lookup(339) -> 'PVALID';
lookup(340) -> 'DISALLOWED';
lookup(341) -> 'PVALID';
lookup(342) -> 'DISALLOWED';
lookup(343) -> 'PVALID';
lookup(344) -> 'DISALLOWED';
lookup(345) -> 'PVALID';
lookup(346) -> 'DISALLOWED';
lookup(347) -> 'PVALID';
lookup(348) -> 'DISALLOWED';
lookup(349) -> 'PVALID';
lookup(350) -> 'DISALLOWED';
lookup(351) -> 'PVALID';
lookup(352) -> 'DISALLOWED';
lookup(353) -> 'PVALID';
lookup(354) -> 'DISALLOWED';
lookup(355) -> 'PVALID';
lookup(356) -> 'DISALLOWED';
lookup(357) -> 'PVALID';
lookup(358) -> 'DISALLOWED';
lookup(359) -> 'PVALID';
lookup(360) -> 'DISALLOWED';
lookup(361) -> 'PVALID';
lookup(362) -> 'DISALLOWED';
lookup(363) -> 'PVALID';
lookup(364) -> 'DISALLOWED';
lookup(365) -> 'PVALID';
lookup(366) -> 'DISALLOWED';
lookup(367) -> 'PVALID';
lookup(368) -> 'DISALLOWED';
lookup(369) -> 'PVALID';
lookup(370) -> 'DISALLOWED';
lookup(371) -> 'PVALID';
lookup(372) -> 'DISALLOWED';
lookup(373) -> 'PVALID';
lookup(374) -> 'DISALLOWED';
lookup(375) -> 'PVALID';
lookup(378) -> 'PVALID';
lookup(379) -> 'DISALLOWED';
lookup(380) -> 'PVALID';
lookup(381) -> 'DISALLOWED';
lookup(382) -> 'PVALID';
lookup(383) -> 'DISALLOWED';
lookup(384) -> 'PVALID';
lookup(387) -> 'PVALID';
lookup(388) -> 'DISALLOWED';
lookup(389) -> 'PVALID';
lookup(392) -> 'PVALID';
lookup(402) -> 'PVALID';
lookup(405) -> 'PVALID';
lookup(414) -> 'PVALID';
lookup(417) -> 'PVALID';
lookup(418) -> 'DISALLOWED';
lookup(419) -> 'PVALID';
lookup(420) -> 'DISALLOWED';
lookup(421) -> 'PVALID';
lookup(424) -> 'PVALID';
lookup(425) -> 'DISALLOWED';
lookup(428) -> 'DISALLOWED';
lookup(429) -> 'PVALID';
lookup(432) -> 'PVALID';
lookup(436) -> 'PVALID';
lookup(437) -> 'DISALLOWED';
lookup(438) -> 'PVALID';
lookup(444) -> 'DISALLOWED';
lookup(462) -> 'PVALID';
lookup(463) -> 'DISALLOWED';
lookup(464) -> 'PVALID';
lookup(465) -> 'DISALLOWED';
lookup(466) -> 'PVALID';
lookup(467) -> 'DISALLOWED';
lookup(468) -> 'PVALID';
lookup(469) -> 'DISALLOWED';
lookup(470) -> 'PVALID';
lookup(471) -> 'DISALLOWED';
lookup(472) -> 'PVALID';
lookup(473) -> 'DISALLOWED';
lookup(474) -> 'PVALID';
lookup(475) -> 'DISALLOWED';
lookup(478) -> 'DISALLOWED';
lookup(479) -> 'PVALID';
lookup(480) -> 'DISALLOWED';
lookup(481) -> 'PVALID';
lookup(482) -> 'DISALLOWED';
lookup(483) -> 'PVALID';
lookup(484) -> 'DISALLOWED';
lookup(485) -> 'PVALID';
lookup(486) -> 'DISALLOWED';
lookup(487) -> 'PVALID';
lookup(488) -> 'DISALLOWED';
lookup(489) -> 'PVALID';
lookup(490) -> 'DISALLOWED';
lookup(491) -> 'PVALID';
lookup(492) -> 'DISALLOWED';
lookup(493) -> 'PVALID';
lookup(494) -> 'DISALLOWED';
lookup(501) -> 'PVALID';
lookup(505) -> 'PVALID';
lookup(506) -> 'DISALLOWED';
lookup(507) -> 'PVALID';
lookup(508) -> 'DISALLOWED';
lookup(509) -> 'PVALID';
lookup(510) -> 'DISALLOWED';
lookup(511) -> 'PVALID';
lookup(512) -> 'DISALLOWED';
lookup(513) -> 'PVALID';
lookup(514) -> 'DISALLOWED';
lookup(515) -> 'PVALID';
lookup(516) -> 'DISALLOWED';
lookup(517) -> 'PVALID';
lookup(518) -> 'DISALLOWED';
lookup(519) -> 'PVALID';
lookup(520) -> 'DISALLOWED';
lookup(521) -> 'PVALID';
lookup(522) -> 'DISALLOWED';
lookup(523) -> 'PVALID';
lookup(524) -> 'DISALLOWED';
lookup(525) -> 'PVALID';
lookup(526) -> 'DISALLOWED';
lookup(527) -> 'PVALID';
lookup(528) -> 'DISALLOWED';
lookup(529) -> 'PVALID';
lookup(530) -> 'DISALLOWED';
lookup(531) -> 'PVALID';
lookup(532) -> 'DISALLOWED';
lookup(533) -> 'PVALID';
lookup(534) -> 'DISALLOWED';
lookup(535) -> 'PVALID';
lookup(536) -> 'DISALLOWED';
lookup(537) -> 'PVALID';
lookup(538) -> 'DISALLOWED';
lookup(539) -> 'PVALID';
lookup(540) -> 'DISALLOWED';
lookup(541) -> 'PVALID';
lookup(542) -> 'DISALLOWED';
lookup(543) -> 'PVALID';
lookup(544) -> 'DISALLOWED';
lookup(545) -> 'PVALID';
lookup(546) -> 'DISALLOWED';
lookup(547) -> 'PVALID';
lookup(548) -> 'DISALLOWED';
lookup(549) -> 'PVALID';
lookup(550) -> 'DISALLOWED';
lookup(551) -> 'PVALID';
lookup(552) -> 'DISALLOWED';
lookup(553) -> 'PVALID';
lookup(554) -> 'DISALLOWED';
lookup(555) -> 'PVALID';
lookup(556) -> 'DISALLOWED';
lookup(557) -> 'PVALID';
lookup(558) -> 'DISALLOWED';
lookup(559) -> 'PVALID';
lookup(560) -> 'DISALLOWED';
lookup(561) -> 'PVALID';
lookup(562) -> 'DISALLOWED';
lookup(572) -> 'PVALID';
lookup(577) -> 'DISALLOWED';
lookup(578) -> 'PVALID';
lookup(583) -> 'PVALID';
lookup(584) -> 'DISALLOWED';
lookup(585) -> 'PVALID';
lookup(586) -> 'DISALLOWED';
lookup(587) -> 'PVALID';
lookup(588) -> 'DISALLOWED';
lookup(589) -> 'PVALID';
lookup(590) -> 'DISALLOWED';
lookup(748) -> 'PVALID';
lookup(749) -> 'DISALLOWED';
lookup(750) -> 'PVALID';
lookup(834) -> 'PVALID';
lookup(847) -> 'DISALLOWED';
lookup(880) -> 'DISALLOWED';
lookup(881) -> 'PVALID';
lookup(882) -> 'DISALLOWED';
lookup(883) -> 'PVALID';
lookup(884) -> 'DISALLOWED';
lookup(885) -> 'CONTEXTO';
lookup(886) -> 'DISALLOWED';
lookup(887) -> 'PVALID';
lookup(890) -> 'DISALLOWED';
lookup(907) -> 'UNASSIGNED';
lookup(908) -> 'DISALLOWED';
lookup(909) -> 'UNASSIGNED';
lookup(912) -> 'PVALID';
lookup(930) -> 'UNASSIGNED';
lookup(983) -> 'PVALID';
lookup(984) -> 'DISALLOWED';
lookup(985) -> 'PVALID';
lookup(986) -> 'DISALLOWED';
lookup(987) -> 'PVALID';
lookup(988) -> 'DISALLOWED';
lookup(989) -> 'PVALID';
lookup(990) -> 'DISALLOWED';
lookup(991) -> 'PVALID';
lookup(992) -> 'DISALLOWED';
lookup(993) -> 'PVALID';
lookup(994) -> 'DISALLOWED';
lookup(995) -> 'PVALID';
lookup(996) -> 'DISALLOWED';
lookup(997) -> 'PVALID';
lookup(998) -> 'DISALLOWED';
lookup(999) -> 'PVALID';
lookup(1000) -> 'DISALLOWED';
lookup(1001) -> 'PVALID';
lookup(1002) -> 'DISALLOWED';
lookup(1003) -> 'PVALID';
lookup(1004) -> 'DISALLOWED';
lookup(1005) -> 'PVALID';
lookup(1006) -> 'DISALLOWED';
lookup(1007) -> 'PVALID';
lookup(1011) -> 'PVALID';
lookup(1016) -> 'PVALID';
lookup(1120) -> 'DISALLOWED';
lookup(1121) -> 'PVALID';
lookup(1122) -> 'DISALLOWED';
lookup(1123) -> 'PVALID';
lookup(1124) -> 'DISALLOWED';
lookup(1125) -> 'PVALID';
lookup(1126) -> 'DISALLOWED';
lookup(1127) -> 'PVALID';
lookup(1128) -> 'DISALLOWED';
lookup(1129) -> 'PVALID';
lookup(1130) -> 'DISALLOWED';
lookup(1131) -> 'PVALID';
lookup(1132) -> 'DISALLOWED';
lookup(1133) -> 'PVALID';
lookup(1134) -> 'DISALLOWED';
lookup(1135) -> 'PVALID';
lookup(1136) -> 'DISALLOWED';
lookup(1137) -> 'PVALID';
lookup(1138) -> 'DISALLOWED';
lookup(1139) -> 'PVALID';
lookup(1140) -> 'DISALLOWED';
lookup(1141) -> 'PVALID';
lookup(1142) -> 'DISALLOWED';
lookup(1143) -> 'PVALID';
lookup(1144) -> 'DISALLOWED';
lookup(1145) -> 'PVALID';
lookup(1146) -> 'DISALLOWED';
lookup(1147) -> 'PVALID';
lookup(1148) -> 'DISALLOWED';
lookup(1149) -> 'PVALID';
lookup(1150) -> 'DISALLOWED';
lookup(1151) -> 'PVALID';
lookup(1152) -> 'DISALLOWED';
lookup(1153) -> 'PVALID';
lookup(1154) -> 'DISALLOWED';
lookup(1163) -> 'PVALID';
lookup(1164) -> 'DISALLOWED';
lookup(1165) -> 'PVALID';
lookup(1166) -> 'DISALLOWED';
lookup(1167) -> 'PVALID';
lookup(1168) -> 'DISALLOWED';
lookup(1169) -> 'PVALID';
lookup(1170) -> 'DISALLOWED';
lookup(1171) -> 'PVALID';
lookup(1172) -> 'DISALLOWED';
lookup(1173) -> 'PVALID';
lookup(1174) -> 'DISALLOWED';
lookup(1175) -> 'PVALID';
lookup(1176) -> 'DISALLOWED';
lookup(1177) -> 'PVALID';
lookup(1178) -> 'DISALLOWED';
lookup(1179) -> 'PVALID';
lookup(1180) -> 'DISALLOWED';
lookup(1181) -> 'PVALID';
lookup(1182) -> 'DISALLOWED';
lookup(1183) -> 'PVALID';
lookup(1184) -> 'DISALLOWED';
lookup(1185) -> 'PVALID';
lookup(1186) -> 'DISALLOWED';
lookup(1187) -> 'PVALID';
lookup(1188) -> 'DISALLOWED';
lookup(1189) -> 'PVALID';
lookup(1190) -> 'DISALLOWED';
lookup(1191) -> 'PVALID';
lookup(1192) -> 'DISALLOWED';
lookup(1193) -> 'PVALID';
lookup(1194) -> 'DISALLOWED';
lookup(1195) -> 'PVALID';
lookup(1196) -> 'DISALLOWED';
lookup(1197) -> 'PVALID';
lookup(1198) -> 'DISALLOWED';
lookup(1199) -> 'PVALID';
lookup(1200) -> 'DISALLOWED';
lookup(1201) -> 'PVALID';
lookup(1202) -> 'DISALLOWED';
lookup(1203) -> 'PVALID';
lookup(1204) -> 'DISALLOWED';
lookup(1205) -> 'PVALID';
lookup(1206) -> 'DISALLOWED';
lookup(1207) -> 'PVALID';
lookup(1208) -> 'DISALLOWED';
lookup(1209) -> 'PVALID';
lookup(1210) -> 'DISALLOWED';
lookup(1211) -> 'PVALID';
lookup(1212) -> 'DISALLOWED';
lookup(1213) -> 'PVALID';
lookup(1214) -> 'DISALLOWED';
lookup(1215) -> 'PVALID';
lookup(1218) -> 'PVALID';
lookup(1219) -> 'DISALLOWED';
lookup(1220) -> 'PVALID';
lookup(1221) -> 'DISALLOWED';
lookup(1222) -> 'PVALID';
lookup(1223) -> 'DISALLOWED';
lookup(1224) -> 'PVALID';
lookup(1225) -> 'DISALLOWED';
lookup(1226) -> 'PVALID';
lookup(1227) -> 'DISALLOWED';
lookup(1228) -> 'PVALID';
lookup(1229) -> 'DISALLOWED';
lookup(1232) -> 'DISALLOWED';
lookup(1233) -> 'PVALID';
lookup(1234) -> 'DISALLOWED';
lookup(1235) -> 'PVALID';
lookup(1236) -> 'DISALLOWED';
lookup(1237) -> 'PVALID';
lookup(1238) -> 'DISALLOWED';
lookup(1239) -> 'PVALID';
lookup(1240) -> 'DISALLOWED';
lookup(1241) -> 'PVALID';
lookup(1242) -> 'DISALLOWED';
lookup(1243) -> 'PVALID';
lookup(1244) -> 'DISALLOWED';
lookup(1245) -> 'PVALID';
lookup(1246) -> 'DISALLOWED';
lookup(1247) -> 'PVALID';
lookup(1248) -> 'DISALLOWED';
lookup(1249) -> 'PVALID';
lookup(1250) -> 'DISALLOWED';
lookup(1251) -> 'PVALID';
lookup(1252) -> 'DISALLOWED';
lookup(1253) -> 'PVALID';
lookup(1254) -> 'DISALLOWED';
lookup(1255) -> 'PVALID';
lookup(1256) -> 'DISALLOWED';
lookup(1257) -> 'PVALID';
lookup(1258) -> 'DISALLOWED';
lookup(1259) -> 'PVALID';
lookup(1260) -> 'DISALLOWED';
lookup(1261) -> 'PVALID';
lookup(1262) -> 'DISALLOWED';
lookup(1263) -> 'PVALID';
lookup(1264) -> 'DISALLOWED';
lookup(1265) -> 'PVALID';
lookup(1266) -> 'DISALLOWED';
lookup(1267) -> 'PVALID';
lookup(1268) -> 'DISALLOWED';
lookup(1269) -> 'PVALID';
lookup(1270) -> 'DISALLOWED';
lookup(1271) -> 'PVALID';
lookup(1272) -> 'DISALLOWED';
lookup(1273) -> 'PVALID';
lookup(1274) -> 'DISALLOWED';
lookup(1275) -> 'PVALID';
lookup(1276) -> 'DISALLOWED';
lookup(1277) -> 'PVALID';
lookup(1278) -> 'DISALLOWED';
lookup(1279) -> 'PVALID';
lookup(1280) -> 'DISALLOWED';
lookup(1281) -> 'PVALID';
lookup(1282) -> 'DISALLOWED';
lookup(1283) -> 'PVALID';
lookup(1284) -> 'DISALLOWED';
lookup(1285) -> 'PVALID';
lookup(1286) -> 'DISALLOWED';
lookup(1287) -> 'PVALID';
lookup(1288) -> 'DISALLOWED';
lookup(1289) -> 'PVALID';
lookup(1290) -> 'DISALLOWED';
lookup(1291) -> 'PVALID';
lookup(1292) -> 'DISALLOWED';
lookup(1293) -> 'PVALID';
lookup(1294) -> 'DISALLOWED';
lookup(1295) -> 'PVALID';
lookup(1296) -> 'DISALLOWED';
lookup(1297) -> 'PVALID';
lookup(1298) -> 'DISALLOWED';
lookup(1299) -> 'PVALID';
lookup(1300) -> 'DISALLOWED';
lookup(1301) -> 'PVALID';
lookup(1302) -> 'DISALLOWED';
lookup(1303) -> 'PVALID';
lookup(1304) -> 'DISALLOWED';
lookup(1305) -> 'PVALID';
lookup(1306) -> 'DISALLOWED';
lookup(1307) -> 'PVALID';
lookup(1308) -> 'DISALLOWED';
lookup(1309) -> 'PVALID';
lookup(1310) -> 'DISALLOWED';
lookup(1311) -> 'PVALID';
lookup(1312) -> 'DISALLOWED';
lookup(1313) -> 'PVALID';
lookup(1314) -> 'DISALLOWED';
lookup(1315) -> 'PVALID';
lookup(1316) -> 'DISALLOWED';
lookup(1317) -> 'PVALID';
lookup(1318) -> 'DISALLOWED';
lookup(1319) -> 'PVALID';
lookup(1320) -> 'DISALLOWED';
lookup(1321) -> 'PVALID';
lookup(1322) -> 'DISALLOWED';
lookup(1323) -> 'PVALID';
lookup(1324) -> 'DISALLOWED';
lookup(1325) -> 'PVALID';
lookup(1326) -> 'DISALLOWED';
lookup(1327) -> 'PVALID';
lookup(1328) -> 'UNASSIGNED';
lookup(1369) -> 'PVALID';
lookup(1415) -> 'DISALLOWED';
lookup(1416) -> 'PVALID';
lookup(1424) -> 'UNASSIGNED';
lookup(1470) -> 'DISALLOWED';
lookup(1471) -> 'PVALID';
lookup(1472) -> 'DISALLOWED';
lookup(1475) -> 'DISALLOWED';
lookup(1478) -> 'DISALLOWED';
lookup(1479) -> 'PVALID';
lookup(1565) -> 'UNASSIGNED';
lookup(1600) -> 'DISALLOWED';
lookup(1748) -> 'DISALLOWED';
lookup(1769) -> 'DISALLOWED';
lookup(1806) -> 'UNASSIGNED';
lookup(1807) -> 'DISALLOWED';
lookup(2045) -> 'PVALID';
lookup(2111) -> 'UNASSIGNED';
lookup(2142) -> 'DISALLOWED';
lookup(2143) -> 'UNASSIGNED';
lookup(2229) -> 'UNASSIGNED';
lookup(2274) -> 'DISALLOWED';
lookup(2416) -> 'DISALLOWED';
lookup(2436) -> 'UNASSIGNED';
lookup(2473) -> 'UNASSIGNED';
lookup(2481) -> 'UNASSIGNED';
lookup(2482) -> 'PVALID';
lookup(2519) -> 'PVALID';
lookup(2526) -> 'UNASSIGNED';
lookup(2527) -> 'DISALLOWED';
lookup(2556) -> 'PVALID';
lookup(2557) -> 'DISALLOWED';
lookup(2558) -> 'PVALID';
lookup(2564) -> 'UNASSIGNED';
lookup(2601) -> 'UNASSIGNED';
lookup(2609) -> 'UNASSIGNED';
lookup(2610) -> 'PVALID';
lookup(2611) -> 'DISALLOWED';
lookup(2612) -> 'UNASSIGNED';
lookup(2613) -> 'PVALID';
lookup(2614) -> 'DISALLOWED';
lookup(2615) -> 'UNASSIGNED';
lookup(2620) -> 'PVALID';
lookup(2621) -> 'UNASSIGNED';
lookup(2641) -> 'PVALID';
lookup(2652) -> 'PVALID';
lookup(2653) -> 'UNASSIGNED';
lookup(2654) -> 'DISALLOWED';
lookup(2678) -> 'DISALLOWED';
lookup(2692) -> 'UNASSIGNED';
lookup(2702) -> 'UNASSIGNED';
lookup(2706) -> 'UNASSIGNED';
lookup(2729) -> 'UNASSIGNED';
lookup(2737) -> 'UNASSIGNED';
lookup(2740) -> 'UNASSIGNED';
lookup(2758) -> 'UNASSIGNED';
lookup(2762) -> 'UNASSIGNED';
lookup(2768) -> 'PVALID';
lookup(2816) -> 'UNASSIGNED';
lookup(2820) -> 'UNASSIGNED';
lookup(2857) -> 'UNASSIGNED';
lookup(2865) -> 'UNASSIGNED';
lookup(2868) -> 'UNASSIGNED';
lookup(2910) -> 'UNASSIGNED';
lookup(2928) -> 'DISALLOWED';
lookup(2929) -> 'PVALID';
lookup(2948) -> 'UNASSIGNED';
lookup(2961) -> 'UNASSIGNED';
lookup(2971) -> 'UNASSIGNED';
lookup(2972) -> 'PVALID';
lookup(2973) -> 'UNASSIGNED';
lookup(3017) -> 'UNASSIGNED';
lookup(3024) -> 'PVALID';
lookup(3031) -> 'PVALID';
lookup(3085) -> 'UNASSIGNED';
lookup(3089) -> 'UNASSIGNED';
lookup(3113) -> 'UNASSIGNED';
lookup(3141) -> 'UNASSIGNED';
lookup(3145) -> 'UNASSIGNED';
lookup(3159) -> 'UNASSIGNED';
lookup(3204) -> 'DISALLOWED';
lookup(3213) -> 'UNASSIGNED';
lookup(3217) -> 'UNASSIGNED';
lookup(3241) -> 'UNASSIGNED';
lookup(3252) -> 'UNASSIGNED';
lookup(3269) -> 'UNASSIGNED';
lookup(3273) -> 'UNASSIGNED';
lookup(3294) -> 'PVALID';
lookup(3295) -> 'UNASSIGNED';
lookup(3312) -> 'UNASSIGNED';
lookup(3341) -> 'UNASSIGNED';
lookup(3345) -> 'UNASSIGNED';
lookup(3397) -> 'UNASSIGNED';
lookup(3401) -> 'UNASSIGNED';
lookup(3407) -> 'DISALLOWED';
lookup(3456) -> 'UNASSIGNED';
lookup(3460) -> 'UNASSIGNED';
lookup(3506) -> 'UNASSIGNED';
lookup(3516) -> 'UNASSIGNED';
lookup(3517) -> 'PVALID';
lookup(3530) -> 'PVALID';
lookup(3541) -> 'UNASSIGNED';
lookup(3542) -> 'PVALID';
lookup(3543) -> 'UNASSIGNED';
lookup(3572) -> 'DISALLOWED';
lookup(3635) -> 'DISALLOWED';
lookup(3647) -> 'DISALLOWED';
lookup(3663) -> 'DISALLOWED';
lookup(3715) -> 'UNASSIGNED';
lookup(3716) -> 'PVALID';
lookup(3717) -> 'UNASSIGNED';
lookup(3723) -> 'UNASSIGNED';
lookup(3748) -> 'UNASSIGNED';
lookup(3749) -> 'PVALID';
lookup(3750) -> 'UNASSIGNED';
lookup(3763) -> 'DISALLOWED';
lookup(3781) -> 'UNASSIGNED';
lookup(3782) -> 'PVALID';
lookup(3783) -> 'UNASSIGNED';
lookup(3840) -> 'PVALID';
lookup(3851) -> 'PVALID';
lookup(3893) -> 'PVALID';
lookup(3894) -> 'DISALLOWED';
lookup(3895) -> 'PVALID';
lookup(3896) -> 'DISALLOWED';
lookup(3897) -> 'PVALID';
lookup(3907) -> 'DISALLOWED';
lookup(3912) -> 'UNASSIGNED';
lookup(3917) -> 'DISALLOWED';
lookup(3922) -> 'DISALLOWED';
lookup(3927) -> 'DISALLOWED';
lookup(3932) -> 'DISALLOWED';
lookup(3945) -> 'DISALLOWED';
lookup(3955) -> 'DISALLOWED';
lookup(3956) -> 'PVALID';
lookup(3969) -> 'DISALLOWED';
lookup(3973) -> 'DISALLOWED';
lookup(3987) -> 'DISALLOWED';
lookup(3992) -> 'UNASSIGNED';
lookup(3997) -> 'DISALLOWED';
lookup(4002) -> 'DISALLOWED';
lookup(4007) -> 'DISALLOWED';
lookup(4012) -> 'DISALLOWED';
lookup(4025) -> 'DISALLOWED';
lookup(4029) -> 'UNASSIGNED';
lookup(4038) -> 'PVALID';
lookup(4045) -> 'UNASSIGNED';
lookup(4294) -> 'UNASSIGNED';
lookup(4295) -> 'DISALLOWED';
lookup(4301) -> 'DISALLOWED';
lookup(4681) -> 'UNASSIGNED';
lookup(4695) -> 'UNASSIGNED';
lookup(4696) -> 'PVALID';
lookup(4697) -> 'UNASSIGNED';
lookup(4745) -> 'UNASSIGNED';
lookup(4785) -> 'UNASSIGNED';
lookup(4799) -> 'UNASSIGNED';
lookup(4800) -> 'PVALID';
lookup(4801) -> 'UNASSIGNED';
lookup(4823) -> 'UNASSIGNED';
lookup(4881) -> 'UNASSIGNED';
lookup(5120) -> 'DISALLOWED';
lookup(5760) -> 'DISALLOWED';
lookup(5901) -> 'UNASSIGNED';
lookup(5997) -> 'UNASSIGNED';
lookup(6001) -> 'UNASSIGNED';
lookup(6103) -> 'PVALID';
lookup(6159) -> 'UNASSIGNED';
lookup(6431) -> 'UNASSIGNED';
lookup(6464) -> 'DISALLOWED';
lookup(6618) -> 'DISALLOWED';
lookup(6751) -> 'UNASSIGNED';
lookup(6823) -> 'PVALID';
lookup(6846) -> 'DISALLOWED';
lookup(7379) -> 'DISALLOWED';
lookup(7471) -> 'PVALID';
lookup(7483) -> 'PVALID';
lookup(7502) -> 'PVALID';
lookup(7544) -> 'DISALLOWED';
lookup(7674) -> 'UNASSIGNED';
lookup(7680) -> 'DISALLOWED';
lookup(7681) -> 'PVALID';
lookup(7682) -> 'DISALLOWED';
lookup(7683) -> 'PVALID';
lookup(7684) -> 'DISALLOWED';
lookup(7685) -> 'PVALID';
lookup(7686) -> 'DISALLOWED';
lookup(7687) -> 'PVALID';
lookup(7688) -> 'DISALLOWED';
lookup(7689) -> 'PVALID';
lookup(7690) -> 'DISALLOWED';
lookup(7691) -> 'PVALID';
lookup(7692) -> 'DISALLOWED';
lookup(7693) -> 'PVALID';
lookup(7694) -> 'DISALLOWED';
lookup(7695) -> 'PVALID';
lookup(7696) -> 'DISALLOWED';
lookup(7697) -> 'PVALID';
lookup(7698) -> 'DISALLOWED';
lookup(7699) -> 'PVALID';
lookup(7700) -> 'DISALLOWED';
lookup(7701) -> 'PVALID';
lookup(7702) -> 'DISALLOWED';
lookup(7703) -> 'PVALID';
lookup(7704) -> 'DISALLOWED';
lookup(7705) -> 'PVALID';
lookup(7706) -> 'DISALLOWED';
lookup(7707) -> 'PVALID';
lookup(7708) -> 'DISALLOWED';
lookup(7709) -> 'PVALID';
lookup(7710) -> 'DISALLOWED';
lookup(7711) -> 'PVALID';
lookup(7712) -> 'DISALLOWED';
lookup(7713) -> 'PVALID';
lookup(7714) -> 'DISALLOWED';
lookup(7715) -> 'PVALID';
lookup(7716) -> 'DISALLOWED';
lookup(7717) -> 'PVALID';
lookup(7718) -> 'DISALLOWED';
lookup(7719) -> 'PVALID';
lookup(7720) -> 'DISALLOWED';
lookup(7721) -> 'PVALID';
lookup(7722) -> 'DISALLOWED';
lookup(7723) -> 'PVALID';
lookup(7724) -> 'DISALLOWED';
lookup(7725) -> 'PVALID';
lookup(7726) -> 'DISALLOWED';
lookup(7727) -> 'PVALID';
lookup(7728) -> 'DISALLOWED';
lookup(7729) -> 'PVALID';
lookup(7730) -> 'DISALLOWED';
lookup(7731) -> 'PVALID';
lookup(7732) -> 'DISALLOWED';
lookup(7733) -> 'PVALID';
lookup(7734) -> 'DISALLOWED';
lookup(7735) -> 'PVALID';
lookup(7736) -> 'DISALLOWED';
lookup(7737) -> 'PVALID';
lookup(7738) -> 'DISALLOWED';
lookup(7739) -> 'PVALID';
lookup(7740) -> 'DISALLOWED';
lookup(7741) -> 'PVALID';
lookup(7742) -> 'DISALLOWED';
lookup(7743) -> 'PVALID';
lookup(7744) -> 'DISALLOWED';
lookup(7745) -> 'PVALID';
lookup(7746) -> 'DISALLOWED';
lookup(7747) -> 'PVALID';
lookup(7748) -> 'DISALLOWED';
lookup(7749) -> 'PVALID';
lookup(7750) -> 'DISALLOWED';
lookup(7751) -> 'PVALID';
lookup(7752) -> 'DISALLOWED';
lookup(7753) -> 'PVALID';
lookup(7754) -> 'DISALLOWED';
lookup(7755) -> 'PVALID';
lookup(7756) -> 'DISALLOWED';
lookup(7757) -> 'PVALID';
lookup(7758) -> 'DISALLOWED';
lookup(7759) -> 'PVALID';
lookup(7760) -> 'DISALLOWED';
lookup(7761) -> 'PVALID';
lookup(7762) -> 'DISALLOWED';
lookup(7763) -> 'PVALID';
lookup(7764) -> 'DISALLOWED';
lookup(7765) -> 'PVALID';
lookup(7766) -> 'DISALLOWED';
lookup(7767) -> 'PVALID';
lookup(7768) -> 'DISALLOWED';
lookup(7769) -> 'PVALID';
lookup(7770) -> 'DISALLOWED';
lookup(7771) -> 'PVALID';
lookup(7772) -> 'DISALLOWED';
lookup(7773) -> 'PVALID';
lookup(7774) -> 'DISALLOWED';
lookup(7775) -> 'PVALID';
lookup(7776) -> 'DISALLOWED';
lookup(7777) -> 'PVALID';
lookup(7778) -> 'DISALLOWED';
lookup(7779) -> 'PVALID';
lookup(7780) -> 'DISALLOWED';
lookup(7781) -> 'PVALID';
lookup(7782) -> 'DISALLOWED';
lookup(7783) -> 'PVALID';
lookup(7784) -> 'DISALLOWED';
lookup(7785) -> 'PVALID';
lookup(7786) -> 'DISALLOWED';
lookup(7787) -> 'PVALID';
lookup(7788) -> 'DISALLOWED';
lookup(7789) -> 'PVALID';
lookup(7790) -> 'DISALLOWED';
lookup(7791) -> 'PVALID';
lookup(7792) -> 'DISALLOWED';
lookup(7793) -> 'PVALID';
lookup(7794) -> 'DISALLOWED';
lookup(7795) -> 'PVALID';
lookup(7796) -> 'DISALLOWED';
lookup(7797) -> 'PVALID';
lookup(7798) -> 'DISALLOWED';
lookup(7799) -> 'PVALID';
lookup(7800) -> 'DISALLOWED';
lookup(7801) -> 'PVALID';
lookup(7802) -> 'DISALLOWED';
lookup(7803) -> 'PVALID';
lookup(7804) -> 'DISALLOWED';
lookup(7805) -> 'PVALID';
lookup(7806) -> 'DISALLOWED';
lookup(7807) -> 'PVALID';
lookup(7808) -> 'DISALLOWED';
lookup(7809) -> 'PVALID';
lookup(7810) -> 'DISALLOWED';
lookup(7811) -> 'PVALID';
lookup(7812) -> 'DISALLOWED';
lookup(7813) -> 'PVALID';
lookup(7814) -> 'DISALLOWED';
lookup(7815) -> 'PVALID';
lookup(7816) -> 'DISALLOWED';
lookup(7817) -> 'PVALID';
lookup(7818) -> 'DISALLOWED';
lookup(7819) -> 'PVALID';
lookup(7820) -> 'DISALLOWED';
lookup(7821) -> 'PVALID';
lookup(7822) -> 'DISALLOWED';
lookup(7823) -> 'PVALID';
lookup(7824) -> 'DISALLOWED';
lookup(7825) -> 'PVALID';
lookup(7826) -> 'DISALLOWED';
lookup(7827) -> 'PVALID';
lookup(7828) -> 'DISALLOWED';
lookup(7838) -> 'DISALLOWED';
lookup(7839) -> 'PVALID';
lookup(7840) -> 'DISALLOWED';
lookup(7841) -> 'PVALID';
lookup(7842) -> 'DISALLOWED';
lookup(7843) -> 'PVALID';
lookup(7844) -> 'DISALLOWED';
lookup(7845) -> 'PVALID';
lookup(7846) -> 'DISALLOWED';
lookup(7847) -> 'PVALID';
lookup(7848) -> 'DISALLOWED';
lookup(7849) -> 'PVALID';
lookup(7850) -> 'DISALLOWED';
lookup(7851) -> 'PVALID';
lookup(7852) -> 'DISALLOWED';
lookup(7853) -> 'PVALID';
lookup(7854) -> 'DISALLOWED';
lookup(7855) -> 'PVALID';
lookup(7856) -> 'DISALLOWED';
lookup(7857) -> 'PVALID';
lookup(7858) -> 'DISALLOWED';
lookup(7859) -> 'PVALID';
lookup(7860) -> 'DISALLOWED';
lookup(7861) -> 'PVALID';
lookup(7862) -> 'DISALLOWED';
lookup(7863) -> 'PVALID';
lookup(7864) -> 'DISALLOWED';
lookup(7865) -> 'PVALID';
lookup(7866) -> 'DISALLOWED';
lookup(7867) -> 'PVALID';
lookup(7868) -> 'DISALLOWED';
lookup(7869) -> 'PVALID';
lookup(7870) -> 'DISALLOWED';
lookup(7871) -> 'PVALID';
lookup(7872) -> 'DISALLOWED';
lookup(7873) -> 'PVALID';
lookup(7874) -> 'DISALLOWED';
lookup(7875) -> 'PVALID';
lookup(7876) -> 'DISALLOWED';
lookup(7877) -> 'PVALID';
lookup(7878) -> 'DISALLOWED';
lookup(7879) -> 'PVALID';
lookup(7880) -> 'DISALLOWED';
lookup(7881) -> 'PVALID';
lookup(7882) -> 'DISALLOWED';
lookup(7883) -> 'PVALID';
lookup(7884) -> 'DISALLOWED';
lookup(7885) -> 'PVALID';
lookup(7886) -> 'DISALLOWED';
lookup(7887) -> 'PVALID';
lookup(7888) -> 'DISALLOWED';
lookup(7889) -> 'PVALID';
lookup(7890) -> 'DISALLOWED';
lookup(7891) -> 'PVALID';
lookup(7892) -> 'DISALLOWED';
lookup(7893) -> 'PVALID';
lookup(7894) -> 'DISALLOWED';
lookup(7895) -> 'PVALID';
lookup(7896) -> 'DISALLOWED';
lookup(7897) -> 'PVALID';
lookup(7898) -> 'DISALLOWED';
lookup(7899) -> 'PVALID';
lookup(7900) -> 'DISALLOWED';
lookup(7901) -> 'PVALID';
lookup(7902) -> 'DISALLOWED';
lookup(7903) -> 'PVALID';
lookup(7904) -> 'DISALLOWED';
lookup(7905) -> 'PVALID';
lookup(7906) -> 'DISALLOWED';
lookup(7907) -> 'PVALID';
lookup(7908) -> 'DISALLOWED';
lookup(7909) -> 'PVALID';
lookup(7910) -> 'DISALLOWED';
lookup(7911) -> 'PVALID';
lookup(7912) -> 'DISALLOWED';
lookup(7913) -> 'PVALID';
lookup(7914) -> 'DISALLOWED';
lookup(7915) -> 'PVALID';
lookup(7916) -> 'DISALLOWED';
lookup(7917) -> 'PVALID';
lookup(7918) -> 'DISALLOWED';
lookup(7919) -> 'PVALID';
lookup(7920) -> 'DISALLOWED';
lookup(7921) -> 'PVALID';
lookup(7922) -> 'DISALLOWED';
lookup(7923) -> 'PVALID';
lookup(7924) -> 'DISALLOWED';
lookup(7925) -> 'PVALID';
lookup(7926) -> 'DISALLOWED';
lookup(7927) -> 'PVALID';
lookup(7928) -> 'DISALLOWED';
lookup(7929) -> 'PVALID';
lookup(7930) -> 'DISALLOWED';
lookup(7931) -> 'PVALID';
lookup(7932) -> 'DISALLOWED';
lookup(7933) -> 'PVALID';
lookup(7934) -> 'DISALLOWED';
lookup(8024) -> 'UNASSIGNED';
lookup(8025) -> 'DISALLOWED';
lookup(8026) -> 'UNASSIGNED';
lookup(8027) -> 'DISALLOWED';
lookup(8028) -> 'UNASSIGNED';
lookup(8029) -> 'DISALLOWED';
lookup(8030) -> 'UNASSIGNED';
lookup(8031) -> 'DISALLOWED';
lookup(8048) -> 'PVALID';
lookup(8049) -> 'DISALLOWED';
lookup(8050) -> 'PVALID';
lookup(8051) -> 'DISALLOWED';
lookup(8052) -> 'PVALID';
lookup(8053) -> 'DISALLOWED';
lookup(8054) -> 'PVALID';
lookup(8055) -> 'DISALLOWED';
lookup(8056) -> 'PVALID';
lookup(8057) -> 'DISALLOWED';
lookup(8058) -> 'PVALID';
lookup(8059) -> 'DISALLOWED';
lookup(8060) -> 'PVALID';
lookup(8061) -> 'DISALLOWED';
lookup(8117) -> 'UNASSIGNED';
lookup(8118) -> 'PVALID';
lookup(8133) -> 'UNASSIGNED';
lookup(8134) -> 'PVALID';
lookup(8147) -> 'DISALLOWED';
lookup(8156) -> 'UNASSIGNED';
lookup(8163) -> 'DISALLOWED';
lookup(8181) -> 'UNASSIGNED';
lookup(8182) -> 'PVALID';
lookup(8191) -> 'UNASSIGNED';
lookup(8293) -> 'UNASSIGNED';
lookup(8335) -> 'UNASSIGNED';
lookup(8526) -> 'PVALID';
lookup(8580) -> 'PVALID';
lookup(11158) -> 'UNASSIGNED';
lookup(11311) -> 'UNASSIGNED';
lookup(11359) -> 'UNASSIGNED';
lookup(11360) -> 'DISALLOWED';
lookup(11361) -> 'PVALID';
lookup(11367) -> 'DISALLOWED';
lookup(11368) -> 'PVALID';
lookup(11369) -> 'DISALLOWED';
lookup(11370) -> 'PVALID';
lookup(11371) -> 'DISALLOWED';
lookup(11372) -> 'PVALID';
lookup(11377) -> 'PVALID';
lookup(11378) -> 'DISALLOWED';
lookup(11381) -> 'DISALLOWED';
lookup(11393) -> 'PVALID';
lookup(11394) -> 'DISALLOWED';
lookup(11395) -> 'PVALID';
lookup(11396) -> 'DISALLOWED';
lookup(11397) -> 'PVALID';
lookup(11398) -> 'DISALLOWED';
lookup(11399) -> 'PVALID';
lookup(11400) -> 'DISALLOWED';
lookup(11401) -> 'PVALID';
lookup(11402) -> 'DISALLOWED';
lookup(11403) -> 'PVALID';
lookup(11404) -> 'DISALLOWED';
lookup(11405) -> 'PVALID';
lookup(11406) -> 'DISALLOWED';
lookup(11407) -> 'PVALID';
lookup(11408) -> 'DISALLOWED';
lookup(11409) -> 'PVALID';
lookup(11410) -> 'DISALLOWED';
lookup(11411) -> 'PVALID';
lookup(11412) -> 'DISALLOWED';
lookup(11413) -> 'PVALID';
lookup(11414) -> 'DISALLOWED';
lookup(11415) -> 'PVALID';
lookup(11416) -> 'DISALLOWED';
lookup(11417) -> 'PVALID';
lookup(11418) -> 'DISALLOWED';
lookup(11419) -> 'PVALID';
lookup(11420) -> 'DISALLOWED';
lookup(11421) -> 'PVALID';
lookup(11422) -> 'DISALLOWED';
lookup(11423) -> 'PVALID';
lookup(11424) -> 'DISALLOWED';
lookup(11425) -> 'PVALID';
lookup(11426) -> 'DISALLOWED';
lookup(11427) -> 'PVALID';
lookup(11428) -> 'DISALLOWED';
lookup(11429) -> 'PVALID';
lookup(11430) -> 'DISALLOWED';
lookup(11431) -> 'PVALID';
lookup(11432) -> 'DISALLOWED';
lookup(11433) -> 'PVALID';
lookup(11434) -> 'DISALLOWED';
lookup(11435) -> 'PVALID';
lookup(11436) -> 'DISALLOWED';
lookup(11437) -> 'PVALID';
lookup(11438) -> 'DISALLOWED';
lookup(11439) -> 'PVALID';
lookup(11440) -> 'DISALLOWED';
lookup(11441) -> 'PVALID';
lookup(11442) -> 'DISALLOWED';
lookup(11443) -> 'PVALID';
lookup(11444) -> 'DISALLOWED';
lookup(11445) -> 'PVALID';
lookup(11446) -> 'DISALLOWED';
lookup(11447) -> 'PVALID';
lookup(11448) -> 'DISALLOWED';
lookup(11449) -> 'PVALID';
lookup(11450) -> 'DISALLOWED';
lookup(11451) -> 'PVALID';
lookup(11452) -> 'DISALLOWED';
lookup(11453) -> 'PVALID';
lookup(11454) -> 'DISALLOWED';
lookup(11455) -> 'PVALID';
lookup(11456) -> 'DISALLOWED';
lookup(11457) -> 'PVALID';
lookup(11458) -> 'DISALLOWED';
lookup(11459) -> 'PVALID';
lookup(11460) -> 'DISALLOWED';
lookup(11461) -> 'PVALID';
lookup(11462) -> 'DISALLOWED';
lookup(11463) -> 'PVALID';
lookup(11464) -> 'DISALLOWED';
lookup(11465) -> 'PVALID';
lookup(11466) -> 'DISALLOWED';
lookup(11467) -> 'PVALID';
lookup(11468) -> 'DISALLOWED';
lookup(11469) -> 'PVALID';
lookup(11470) -> 'DISALLOWED';
lookup(11471) -> 'PVALID';
lookup(11472) -> 'DISALLOWED';
lookup(11473) -> 'PVALID';
lookup(11474) -> 'DISALLOWED';
lookup(11475) -> 'PVALID';
lookup(11476) -> 'DISALLOWED';
lookup(11477) -> 'PVALID';
lookup(11478) -> 'DISALLOWED';
lookup(11479) -> 'PVALID';
lookup(11480) -> 'DISALLOWED';
lookup(11481) -> 'PVALID';
lookup(11482) -> 'DISALLOWED';
lookup(11483) -> 'PVALID';
lookup(11484) -> 'DISALLOWED';
lookup(11485) -> 'PVALID';
lookup(11486) -> 'DISALLOWED';
lookup(11487) -> 'PVALID';
lookup(11488) -> 'DISALLOWED';
lookup(11489) -> 'PVALID';
lookup(11490) -> 'DISALLOWED';
lookup(11500) -> 'PVALID';
lookup(11501) -> 'DISALLOWED';
lookup(11506) -> 'DISALLOWED';
lookup(11507) -> 'PVALID';
lookup(11558) -> 'UNASSIGNED';
lookup(11559) -> 'PVALID';
lookup(11565) -> 'PVALID';
lookup(11687) -> 'UNASSIGNED';
lookup(11695) -> 'UNASSIGNED';
lookup(11703) -> 'UNASSIGNED';
lookup(11711) -> 'UNASSIGNED';
lookup(11719) -> 'UNASSIGNED';
lookup(11727) -> 'UNASSIGNED';
lookup(11735) -> 'UNASSIGNED';
lookup(11743) -> 'UNASSIGNED';
lookup(11823) -> 'PVALID';
lookup(11930) -> 'UNASSIGNED';
lookup(12348) -> 'PVALID';
lookup(12352) -> 'UNASSIGNED';
lookup(12539) -> 'CONTEXTO';
lookup(12543) -> 'DISALLOWED';
lookup(12592) -> 'UNASSIGNED';
lookup(12687) -> 'UNASSIGNED';
lookup(12831) -> 'UNASSIGNED';
lookup(42560) -> 'DISALLOWED';
lookup(42561) -> 'PVALID';
lookup(42562) -> 'DISALLOWED';
lookup(42563) -> 'PVALID';
lookup(42564) -> 'DISALLOWED';
lookup(42565) -> 'PVALID';
lookup(42566) -> 'DISALLOWED';
lookup(42567) -> 'PVALID';
lookup(42568) -> 'DISALLOWED';
lookup(42569) -> 'PVALID';
lookup(42570) -> 'DISALLOWED';
lookup(42571) -> 'PVALID';
lookup(42572) -> 'DISALLOWED';
lookup(42573) -> 'PVALID';
lookup(42574) -> 'DISALLOWED';
lookup(42575) -> 'PVALID';
lookup(42576) -> 'DISALLOWED';
lookup(42577) -> 'PVALID';
lookup(42578) -> 'DISALLOWED';
lookup(42579) -> 'PVALID';
lookup(42580) -> 'DISALLOWED';
lookup(42581) -> 'PVALID';
lookup(42582) -> 'DISALLOWED';
lookup(42583) -> 'PVALID';
lookup(42584) -> 'DISALLOWED';
lookup(42585) -> 'PVALID';
lookup(42586) -> 'DISALLOWED';
lookup(42587) -> 'PVALID';
lookup(42588) -> 'DISALLOWED';
lookup(42589) -> 'PVALID';
lookup(42590) -> 'DISALLOWED';
lookup(42591) -> 'PVALID';
lookup(42592) -> 'DISALLOWED';
lookup(42593) -> 'PVALID';
lookup(42594) -> 'DISALLOWED';
lookup(42595) -> 'PVALID';
lookup(42596) -> 'DISALLOWED';
lookup(42597) -> 'PVALID';
lookup(42598) -> 'DISALLOWED';
lookup(42599) -> 'PVALID';
lookup(42600) -> 'DISALLOWED';
lookup(42601) -> 'PVALID';
lookup(42602) -> 'DISALLOWED';
lookup(42603) -> 'PVALID';
lookup(42604) -> 'DISALLOWED';
lookup(42622) -> 'DISALLOWED';
lookup(42623) -> 'PVALID';
lookup(42624) -> 'DISALLOWED';
lookup(42625) -> 'PVALID';
lookup(42626) -> 'DISALLOWED';
lookup(42627) -> 'PVALID';
lookup(42628) -> 'DISALLOWED';
lookup(42629) -> 'PVALID';
lookup(42630) -> 'DISALLOWED';
lookup(42631) -> 'PVALID';
lookup(42632) -> 'DISALLOWED';
lookup(42633) -> 'PVALID';
lookup(42634) -> 'DISALLOWED';
lookup(42635) -> 'PVALID';
lookup(42636) -> 'DISALLOWED';
lookup(42637) -> 'PVALID';
lookup(42638) -> 'DISALLOWED';
lookup(42639) -> 'PVALID';
lookup(42640) -> 'DISALLOWED';
lookup(42641) -> 'PVALID';
lookup(42642) -> 'DISALLOWED';
lookup(42643) -> 'PVALID';
lookup(42644) -> 'DISALLOWED';
lookup(42645) -> 'PVALID';
lookup(42646) -> 'DISALLOWED';
lookup(42647) -> 'PVALID';
lookup(42648) -> 'DISALLOWED';
lookup(42649) -> 'PVALID';
lookup(42650) -> 'DISALLOWED';
lookup(42651) -> 'PVALID';
lookup(42787) -> 'PVALID';
lookup(42788) -> 'DISALLOWED';
lookup(42789) -> 'PVALID';
lookup(42790) -> 'DISALLOWED';
lookup(42791) -> 'PVALID';
lookup(42792) -> 'DISALLOWED';
lookup(42793) -> 'PVALID';
lookup(42794) -> 'DISALLOWED';
lookup(42795) -> 'PVALID';
lookup(42796) -> 'DISALLOWED';
lookup(42797) -> 'PVALID';
lookup(42798) -> 'DISALLOWED';
lookup(42802) -> 'DISALLOWED';
lookup(42803) -> 'PVALID';
lookup(42804) -> 'DISALLOWED';
lookup(42805) -> 'PVALID';
lookup(42806) -> 'DISALLOWED';
lookup(42807) -> 'PVALID';
lookup(42808) -> 'DISALLOWED';
lookup(42809) -> 'PVALID';
lookup(42810) -> 'DISALLOWED';
lookup(42811) -> 'PVALID';
lookup(42812) -> 'DISALLOWED';
lookup(42813) -> 'PVALID';
lookup(42814) -> 'DISALLOWED';
lookup(42815) -> 'PVALID';
lookup(42816) -> 'DISALLOWED';
lookup(42817) -> 'PVALID';
lookup(42818) -> 'DISALLOWED';
lookup(42819) -> 'PVALID';
lookup(42820) -> 'DISALLOWED';
lookup(42821) -> 'PVALID';
lookup(42822) -> 'DISALLOWED';
lookup(42823) -> 'PVALID';
lookup(42824) -> 'DISALLOWED';
lookup(42825) -> 'PVALID';
lookup(42826) -> 'DISALLOWED';
lookup(42827) -> 'PVALID';
lookup(42828) -> 'DISALLOWED';
lookup(42829) -> 'PVALID';
lookup(42830) -> 'DISALLOWED';
lookup(42831) -> 'PVALID';
lookup(42832) -> 'DISALLOWED';
lookup(42833) -> 'PVALID';
lookup(42834) -> 'DISALLOWED';
lookup(42835) -> 'PVALID';
lookup(42836) -> 'DISALLOWED';
lookup(42837) -> 'PVALID';
lookup(42838) -> 'DISALLOWED';
lookup(42839) -> 'PVALID';
lookup(42840) -> 'DISALLOWED';
lookup(42841) -> 'PVALID';
lookup(42842) -> 'DISALLOWED';
lookup(42843) -> 'PVALID';
lookup(42844) -> 'DISALLOWED';
lookup(42845) -> 'PVALID';
lookup(42846) -> 'DISALLOWED';
lookup(42847) -> 'PVALID';
lookup(42848) -> 'DISALLOWED';
lookup(42849) -> 'PVALID';
lookup(42850) -> 'DISALLOWED';
lookup(42851) -> 'PVALID';
lookup(42852) -> 'DISALLOWED';
lookup(42853) -> 'PVALID';
lookup(42854) -> 'DISALLOWED';
lookup(42855) -> 'PVALID';
lookup(42856) -> 'DISALLOWED';
lookup(42857) -> 'PVALID';
lookup(42858) -> 'DISALLOWED';
lookup(42859) -> 'PVALID';
lookup(42860) -> 'DISALLOWED';
lookup(42861) -> 'PVALID';
lookup(42862) -> 'DISALLOWED';
lookup(42863) -> 'PVALID';
lookup(42864) -> 'DISALLOWED';
lookup(42873) -> 'DISALLOWED';
lookup(42874) -> 'PVALID';
lookup(42875) -> 'DISALLOWED';
lookup(42876) -> 'PVALID';
lookup(42879) -> 'PVALID';
lookup(42880) -> 'DISALLOWED';
lookup(42881) -> 'PVALID';
lookup(42882) -> 'DISALLOWED';
lookup(42883) -> 'PVALID';
lookup(42884) -> 'DISALLOWED';
lookup(42885) -> 'PVALID';
lookup(42886) -> 'DISALLOWED';
lookup(42892) -> 'PVALID';
lookup(42893) -> 'DISALLOWED';
lookup(42896) -> 'DISALLOWED';
lookup(42897) -> 'PVALID';
lookup(42898) -> 'DISALLOWED';
lookup(42902) -> 'DISALLOWED';
lookup(42903) -> 'PVALID';
lookup(42904) -> 'DISALLOWED';
lookup(42905) -> 'PVALID';
lookup(42906) -> 'DISALLOWED';
lookup(42907) -> 'PVALID';
lookup(42908) -> 'DISALLOWED';
lookup(42909) -> 'PVALID';
lookup(42910) -> 'DISALLOWED';
lookup(42911) -> 'PVALID';
lookup(42912) -> 'DISALLOWED';
lookup(42913) -> 'PVALID';
lookup(42914) -> 'DISALLOWED';
lookup(42915) -> 'PVALID';
lookup(42916) -> 'DISALLOWED';
lookup(42917) -> 'PVALID';
lookup(42918) -> 'DISALLOWED';
lookup(42919) -> 'PVALID';
lookup(42920) -> 'DISALLOWED';
lookup(42921) -> 'PVALID';
lookup(42927) -> 'PVALID';
lookup(42933) -> 'PVALID';
lookup(42934) -> 'DISALLOWED';
lookup(42935) -> 'PVALID';
lookup(42936) -> 'DISALLOWED';
lookup(42937) -> 'PVALID';
lookup(42938) -> 'DISALLOWED';
lookup(42939) -> 'PVALID';
lookup(42940) -> 'DISALLOWED';
lookup(42941) -> 'PVALID';
lookup(42942) -> 'DISALLOWED';
lookup(42943) -> 'PVALID';
lookup(42946) -> 'DISALLOWED';
lookup(42947) -> 'PVALID';
lookup(42952) -> 'PVALID';
lookup(42953) -> 'DISALLOWED';
lookup(42954) -> 'PVALID';
lookup(42997) -> 'DISALLOWED';
lookup(43052) -> 'PVALID';
lookup(43259) -> 'PVALID';
lookup(43260) -> 'DISALLOWED';
lookup(43470) -> 'UNASSIGNED';
lookup(43519) -> 'UNASSIGNED';
lookup(43815) -> 'UNASSIGNED';
lookup(43823) -> 'UNASSIGNED';
lookup(44011) -> 'DISALLOWED';
lookup(64016) -> 'DISALLOWED';
lookup(64017) -> 'PVALID';
lookup(64018) -> 'DISALLOWED';
lookup(64031) -> 'PVALID';
lookup(64032) -> 'DISALLOWED';
lookup(64033) -> 'PVALID';
lookup(64034) -> 'DISALLOWED';
lookup(64285) -> 'DISALLOWED';
lookup(64286) -> 'PVALID';
lookup(64311) -> 'UNASSIGNED';
lookup(64317) -> 'UNASSIGNED';
lookup(64318) -> 'DISALLOWED';
lookup(64319) -> 'UNASSIGNED';
lookup(64322) -> 'UNASSIGNED';
lookup(64325) -> 'UNASSIGNED';
lookup(65107) -> 'UNASSIGNED';
lookup(65127) -> 'UNASSIGNED';
lookup(65139) -> 'PVALID';
lookup(65140) -> 'DISALLOWED';
lookup(65141) -> 'UNASSIGNED';
lookup(65279) -> 'DISALLOWED';
lookup(65280) -> 'UNASSIGNED';
lookup(65511) -> 'UNASSIGNED';
lookup(65548) -> 'UNASSIGNED';
lookup(65575) -> 'UNASSIGNED';
lookup(65595) -> 'UNASSIGNED';
lookup(65598) -> 'UNASSIGNED';
lookup(65935) -> 'UNASSIGNED';
lookup(65952) -> 'DISALLOWED';
lookup(66045) -> 'PVALID';
lookup(66272) -> 'PVALID';
lookup(66369) -> 'DISALLOWED';
lookup(66378) -> 'DISALLOWED';
lookup(66462) -> 'UNASSIGNED';
lookup(66463) -> 'DISALLOWED';
lookup(66927) -> 'DISALLOWED';
lookup(67592) -> 'PVALID';
lookup(67593) -> 'UNASSIGNED';
lookup(67638) -> 'UNASSIGNED';
lookup(67644) -> 'PVALID';
lookup(67670) -> 'UNASSIGNED';
lookup(67827) -> 'UNASSIGNED';
lookup(67871) -> 'DISALLOWED';
lookup(67903) -> 'DISALLOWED';
lookup(68100) -> 'UNASSIGNED';
lookup(68116) -> 'UNASSIGNED';
lookup(68120) -> 'UNASSIGNED';
lookup(68159) -> 'PVALID';
lookup(68296) -> 'DISALLOWED';
lookup(69247) -> 'UNASSIGNED';
lookup(69290) -> 'UNASSIGNED';
lookup(69293) -> 'DISALLOWED';
lookup(69415) -> 'PVALID';
lookup(69837) -> 'DISALLOWED';
lookup(69941) -> 'UNASSIGNED';
lookup(70006) -> 'PVALID';
lookup(70093) -> 'DISALLOWED';
lookup(70107) -> 'DISALLOWED';
lookup(70108) -> 'PVALID';
lookup(70112) -> 'UNASSIGNED';
lookup(70162) -> 'UNASSIGNED';
lookup(70206) -> 'PVALID';
lookup(70279) -> 'UNASSIGNED';
lookup(70280) -> 'PVALID';
lookup(70281) -> 'UNASSIGNED';
lookup(70286) -> 'UNASSIGNED';
lookup(70302) -> 'UNASSIGNED';
lookup(70313) -> 'DISALLOWED';
lookup(70404) -> 'UNASSIGNED';
lookup(70441) -> 'UNASSIGNED';
lookup(70449) -> 'UNASSIGNED';
lookup(70452) -> 'UNASSIGNED';
lookup(70458) -> 'UNASSIGNED';
lookup(70480) -> 'PVALID';
lookup(70487) -> 'PVALID';
lookup(70748) -> 'UNASSIGNED';
lookup(70749) -> 'DISALLOWED';
lookup(70854) -> 'DISALLOWED';
lookup(70855) -> 'PVALID';
lookup(71236) -> 'PVALID';
lookup(71739) -> 'DISALLOWED';
lookup(71945) -> 'PVALID';
lookup(71956) -> 'UNASSIGNED';
lookup(71959) -> 'UNASSIGNED';
lookup(71990) -> 'UNASSIGNED';
lookup(72162) -> 'DISALLOWED';
lookup(72263) -> 'PVALID';
lookup(72349) -> 'PVALID';
lookup(72713) -> 'UNASSIGNED';
lookup(72759) -> 'UNASSIGNED';
lookup(72872) -> 'UNASSIGNED';
lookup(72967) -> 'UNASSIGNED';
lookup(72970) -> 'UNASSIGNED';
lookup(73018) -> 'PVALID';
lookup(73019) -> 'UNASSIGNED';
lookup(73022) -> 'UNASSIGNED';
lookup(73062) -> 'UNASSIGNED';
lookup(73065) -> 'UNASSIGNED';
lookup(73103) -> 'UNASSIGNED';
lookup(73106) -> 'UNASSIGNED';
lookup(73648) -> 'PVALID';
lookup(73727) -> 'DISALLOWED';
lookup(74863) -> 'UNASSIGNED';
lookup(78895) -> 'UNASSIGNED';
lookup(92767) -> 'UNASSIGNED';
lookup(92917) -> 'DISALLOWED';
lookup(93018) -> 'UNASSIGNED';
lookup(93026) -> 'UNASSIGNED';
lookup(94178) -> 'DISALLOWED';
lookup(113820) -> 'DISALLOWED';
lookup(119893) -> 'UNASSIGNED';
lookup(119965) -> 'UNASSIGNED';
lookup(119970) -> 'DISALLOWED';
lookup(119981) -> 'UNASSIGNED';
lookup(119994) -> 'UNASSIGNED';
lookup(119995) -> 'DISALLOWED';
lookup(119996) -> 'UNASSIGNED';
lookup(120004) -> 'UNASSIGNED';
lookup(120070) -> 'UNASSIGNED';
lookup(120085) -> 'UNASSIGNED';
lookup(120093) -> 'UNASSIGNED';
lookup(120122) -> 'UNASSIGNED';
lookup(120127) -> 'UNASSIGNED';
lookup(120133) -> 'UNASSIGNED';
lookup(120134) -> 'DISALLOWED';
lookup(120145) -> 'UNASSIGNED';
lookup(121461) -> 'PVALID';
lookup(121476) -> 'PVALID';
lookup(121504) -> 'UNASSIGNED';
lookup(122887) -> 'UNASSIGNED';
lookup(122914) -> 'UNASSIGNED';
lookup(122917) -> 'UNASSIGNED';
lookup(123214) -> 'PVALID';
lookup(123215) -> 'DISALLOWED';
lookup(123647) -> 'DISALLOWED';
lookup(126468) -> 'UNASSIGNED';
lookup(126496) -> 'UNASSIGNED';
lookup(126499) -> 'UNASSIGNED';
lookup(126500) -> 'DISALLOWED';
lookup(126503) -> 'DISALLOWED';
lookup(126504) -> 'UNASSIGNED';
lookup(126515) -> 'UNASSIGNED';
lookup(126520) -> 'UNASSIGNED';
lookup(126521) -> 'DISALLOWED';
lookup(126522) -> 'UNASSIGNED';
lookup(126523) -> 'DISALLOWED';
lookup(126530) -> 'DISALLOWED';
lookup(126535) -> 'DISALLOWED';
lookup(126536) -> 'UNASSIGNED';
lookup(126537) -> 'DISALLOWED';
lookup(126538) -> 'UNASSIGNED';
lookup(126539) -> 'DISALLOWED';
lookup(126540) -> 'UNASSIGNED';
lookup(126544) -> 'UNASSIGNED';
lookup(126547) -> 'UNASSIGNED';
lookup(126548) -> 'DISALLOWED';
lookup(126551) -> 'DISALLOWED';
lookup(126552) -> 'UNASSIGNED';
lookup(126553) -> 'DISALLOWED';
lookup(126554) -> 'UNASSIGNED';
lookup(126555) -> 'DISALLOWED';
lookup(126556) -> 'UNASSIGNED';
lookup(126557) -> 'DISALLOWED';
lookup(126558) -> 'UNASSIGNED';
lookup(126559) -> 'DISALLOWED';
lookup(126560) -> 'UNASSIGNED';
lookup(126563) -> 'UNASSIGNED';
lookup(126564) -> 'DISALLOWED';
lookup(126571) -> 'UNASSIGNED';
lookup(126579) -> 'UNASSIGNED';
lookup(126584) -> 'UNASSIGNED';
lookup(126589) -> 'UNASSIGNED';
lookup(126590) -> 'DISALLOWED';
lookup(126591) -> 'UNASSIGNED';
lookup(126602) -> 'UNASSIGNED';
lookup(126628) -> 'UNASSIGNED';
lookup(126634) -> 'UNASSIGNED';
lookup(127168) -> 'UNASSIGNED';
lookup(127184) -> 'UNASSIGNED';
lookup(129401) -> 'UNASSIGNED';
lookup(129484) -> 'UNASSIGNED';
lookup(129939) -> 'UNASSIGNED';
lookup(917504) -> 'UNASSIGNED';
lookup(917505) -> 'DISALLOWED';
lookup(CP) when 0 =< CP, CP =< 44 -> 'DISALLOWED';
lookup(CP) when 46 =< CP, CP =< 47 -> 'DISALLOWED';
lookup(CP) when 48 =< CP, CP =< 57 -> 'PVALID';
lookup(CP) when 58 =< CP, CP =< 96 -> 'DISALLOWED';
lookup(CP) when 97 =< CP, CP =< 122 -> 'PVALID';
lookup(CP) when 123 =< CP, CP =< 182 -> 'DISALLOWED';
lookup(CP) when 184 =< CP, CP =< 222 -> 'DISALLOWED';
lookup(CP) when 223 =< CP, CP =< 246 -> 'PVALID';
lookup(CP) when 248 =< CP, CP =< 255 -> 'PVALID';
lookup(CP) when 306 =< CP, CP =< 308 -> 'DISALLOWED';
lookup(CP) when 311 =< CP, CP =< 312 -> 'PVALID';
lookup(CP) when 319 =< CP, CP =< 321 -> 'DISALLOWED';
lookup(CP) when 329 =< CP, CP =< 330 -> 'DISALLOWED';
lookup(CP) when 376 =< CP, CP =< 377 -> 'DISALLOWED';
lookup(CP) when 385 =< CP, CP =< 386 -> 'DISALLOWED';
lookup(CP) when 390 =< CP, CP =< 391 -> 'DISALLOWED';
lookup(CP) when 393 =< CP, CP =< 395 -> 'DISALLOWED';
lookup(CP) when 396 =< CP, CP =< 397 -> 'PVALID';
lookup(CP) when 398 =< CP, CP =< 401 -> 'DISALLOWED';
lookup(CP) when 403 =< CP, CP =< 404 -> 'DISALLOWED';
lookup(CP) when 406 =< CP, CP =< 408 -> 'DISALLOWED';
lookup(CP) when 409 =< CP, CP =< 411 -> 'PVALID';
lookup(CP) when 412 =< CP, CP =< 413 -> 'DISALLOWED';
lookup(CP) when 415 =< CP, CP =< 416 -> 'DISALLOWED';
lookup(CP) when 422 =< CP, CP =< 423 -> 'DISALLOWED';
lookup(CP) when 426 =< CP, CP =< 427 -> 'PVALID';
lookup(CP) when 430 =< CP, CP =< 431 -> 'DISALLOWED';
lookup(CP) when 433 =< CP, CP =< 435 -> 'DISALLOWED';
lookup(CP) when 439 =< CP, CP =< 440 -> 'DISALLOWED';
lookup(CP) when 441 =< CP, CP =< 443 -> 'PVALID';
lookup(CP) when 445 =< CP, CP =< 451 -> 'PVALID';
lookup(CP) when 452 =< CP, CP =< 461 -> 'DISALLOWED';
lookup(CP) when 476 =< CP, CP =< 477 -> 'PVALID';
lookup(CP) when 495 =< CP, CP =< 496 -> 'PVALID';
lookup(CP) when 497 =< CP, CP =< 500 -> 'DISALLOWED';
lookup(CP) when 502 =< CP, CP =< 504 -> 'DISALLOWED';
lookup(CP) when 563 =< CP, CP =< 569 -> 'PVALID';
lookup(CP) when 570 =< CP, CP =< 571 -> 'DISALLOWED';
lookup(CP) when 573 =< CP, CP =< 574 -> 'DISALLOWED';
lookup(CP) when 575 =< CP, CP =< 576 -> 'PVALID';
lookup(CP) when 579 =< CP, CP =< 582 -> 'DISALLOWED';
lookup(CP) when 591 =< CP, CP =< 687 -> 'PVALID';
lookup(CP) when 688 =< CP, CP =< 696 -> 'DISALLOWED';
lookup(CP) when 697 =< CP, CP =< 705 -> 'PVALID';
lookup(CP) when 706 =< CP, CP =< 709 -> 'DISALLOWED';
lookup(CP) when 710 =< CP, CP =< 721 -> 'PVALID';
lookup(CP) when 722 =< CP, CP =< 747 -> 'DISALLOWED';
lookup(CP) when 751 =< CP, CP =< 767 -> 'DISALLOWED';
lookup(CP) when 768 =< CP, CP =< 831 -> 'PVALID';
lookup(CP) when 832 =< CP, CP =< 833 -> 'DISALLOWED';
lookup(CP) when 835 =< CP, CP =< 837 -> 'DISALLOWED';
lookup(CP) when 838 =< CP, CP =< 846 -> 'PVALID';
lookup(CP) when 848 =< CP, CP =< 879 -> 'PVALID';
lookup(CP) when 888 =< CP, CP =< 889 -> 'UNASSIGNED';
lookup(CP) when 891 =< CP, CP =< 893 -> 'PVALID';
lookup(CP) when 894 =< CP, CP =< 895 -> 'DISALLOWED';
lookup(CP) when 896 =< CP, CP =< 899 -> 'UNASSIGNED';
lookup(CP) when 900 =< CP, CP =< 906 -> 'DISALLOWED';
lookup(CP) when 910 =< CP, CP =< 911 -> 'DISALLOWED';
lookup(CP) when 913 =< CP, CP =< 929 -> 'DISALLOWED';
lookup(CP) when 931 =< CP, CP =< 939 -> 'DISALLOWED';
lookup(CP) when 940 =< CP, CP =< 974 -> 'PVALID';
lookup(CP) when 975 =< CP, CP =< 982 -> 'DISALLOWED';
lookup(CP) when 1008 =< CP, CP =< 1010 -> 'DISALLOWED';
lookup(CP) when 1012 =< CP, CP =< 1015 -> 'DISALLOWED';
lookup(CP) when 1017 =< CP, CP =< 1018 -> 'DISALLOWED';
lookup(CP) when 1019 =< CP, CP =< 1020 -> 'PVALID';
lookup(CP) when 1021 =< CP, CP =< 1071 -> 'DISALLOWED';
lookup(CP) when 1072 =< CP, CP =< 1119 -> 'PVALID';
lookup(CP) when 1155 =< CP, CP =< 1159 -> 'PVALID';
lookup(CP) when 1160 =< CP, CP =< 1162 -> 'DISALLOWED';
lookup(CP) when 1216 =< CP, CP =< 1217 -> 'DISALLOWED';
lookup(CP) when 1230 =< CP, CP =< 1231 -> 'PVALID';
lookup(CP) when 1329 =< CP, CP =< 1366 -> 'DISALLOWED';
lookup(CP) when 1367 =< CP, CP =< 1368 -> 'UNASSIGNED';
lookup(CP) when 1370 =< CP, CP =< 1375 -> 'DISALLOWED';
lookup(CP) when 1376 =< CP, CP =< 1414 -> 'PVALID';
lookup(CP) when 1417 =< CP, CP =< 1418 -> 'DISALLOWED';
lookup(CP) when 1419 =< CP, CP =< 1420 -> 'UNASSIGNED';
lookup(CP) when 1421 =< CP, CP =< 1423 -> 'DISALLOWED';
lookup(CP) when 1425 =< CP, CP =< 1469 -> 'PVALID';
lookup(CP) when 1473 =< CP, CP =< 1474 -> 'PVALID';
lookup(CP) when 1476 =< CP, CP =< 1477 -> 'PVALID';
lookup(CP) when 1480 =< CP, CP =< 1487 -> 'UNASSIGNED';
lookup(CP) when 1488 =< CP, CP =< 1514 -> 'PVALID';
lookup(CP) when 1515 =< CP, CP =< 1518 -> 'UNASSIGNED';
lookup(CP) when 1519 =< CP, CP =< 1522 -> 'PVALID';
lookup(CP) when 1523 =< CP, CP =< 1524 -> 'CONTEXTO';
lookup(CP) when 1525 =< CP, CP =< 1535 -> 'UNASSIGNED';
lookup(CP) when 1536 =< CP, CP =< 1551 -> 'DISALLOWED';
lookup(CP) when 1552 =< CP, CP =< 1562 -> 'PVALID';
lookup(CP) when 1563 =< CP, CP =< 1564 -> 'DISALLOWED';
lookup(CP) when 1566 =< CP, CP =< 1567 -> 'DISALLOWED';
lookup(CP) when 1568 =< CP, CP =< 1599 -> 'PVALID';
lookup(CP) when 1601 =< CP, CP =< 1631 -> 'PVALID';
lookup(CP) when 1632 =< CP, CP =< 1641 -> 'CONTEXTO';
lookup(CP) when 1642 =< CP, CP =< 1645 -> 'DISALLOWED';
lookup(CP) when 1646 =< CP, CP =< 1652 -> 'PVALID';
lookup(CP) when 1653 =< CP, CP =< 1656 -> 'DISALLOWED';
lookup(CP) when 1657 =< CP, CP =< 1747 -> 'PVALID';
lookup(CP) when 1749 =< CP, CP =< 1756 -> 'PVALID';
lookup(CP) when 1757 =< CP, CP =< 1758 -> 'DISALLOWED';
lookup(CP) when 1759 =< CP, CP =< 1768 -> 'PVALID';
lookup(CP) when 1770 =< CP, CP =< 1775 -> 'PVALID';
lookup(CP) when 1776 =< CP, CP =< 1785 -> 'CONTEXTO';
lookup(CP) when 1786 =< CP, CP =< 1791 -> 'PVALID';
lookup(CP) when 1792 =< CP, CP =< 1805 -> 'DISALLOWED';
lookup(CP) when 1808 =< CP, CP =< 1866 -> 'PVALID';
lookup(CP) when 1867 =< CP, CP =< 1868 -> 'UNASSIGNED';
lookup(CP) when 1869 =< CP, CP =< 1969 -> 'PVALID';
lookup(CP) when 1970 =< CP, CP =< 1983 -> 'UNASSIGNED';
lookup(CP) when 1984 =< CP, CP =< 2037 -> 'PVALID';
lookup(CP) when 2038 =< CP, CP =< 2042 -> 'DISALLOWED';
lookup(CP) when 2043 =< CP, CP =< 2044 -> 'UNASSIGNED';
lookup(CP) when 2046 =< CP, CP =< 2047 -> 'DISALLOWED';
lookup(CP) when 2048 =< CP, CP =< 2093 -> 'PVALID';
lookup(CP) when 2094 =< CP, CP =< 2095 -> 'UNASSIGNED';
lookup(CP) when 2096 =< CP, CP =< 2110 -> 'DISALLOWED';
lookup(CP) when 2112 =< CP, CP =< 2139 -> 'PVALID';
lookup(CP) when 2140 =< CP, CP =< 2141 -> 'UNASSIGNED';
lookup(CP) when 2144 =< CP, CP =< 2154 -> 'PVALID';
lookup(CP) when 2155 =< CP, CP =< 2207 -> 'UNASSIGNED';
lookup(CP) when 2208 =< CP, CP =< 2228 -> 'PVALID';
lookup(CP) when 2230 =< CP, CP =< 2247 -> 'PVALID';
lookup(CP) when 2248 =< CP, CP =< 2258 -> 'UNASSIGNED';
lookup(CP) when 2259 =< CP, CP =< 2273 -> 'PVALID';
lookup(CP) when 2275 =< CP, CP =< 2391 -> 'PVALID';
lookup(CP) when 2392 =< CP, CP =< 2399 -> 'DISALLOWED';
lookup(CP) when 2400 =< CP, CP =< 2403 -> 'PVALID';
lookup(CP) when 2404 =< CP, CP =< 2405 -> 'DISALLOWED';
lookup(CP) when 2406 =< CP, CP =< 2415 -> 'PVALID';
lookup(CP) when 2417 =< CP, CP =< 2435 -> 'PVALID';
lookup(CP) when 2437 =< CP, CP =< 2444 -> 'PVALID';
lookup(CP) when 2445 =< CP, CP =< 2446 -> 'UNASSIGNED';
lookup(CP) when 2447 =< CP, CP =< 2448 -> 'PVALID';
lookup(CP) when 2449 =< CP, CP =< 2450 -> 'UNASSIGNED';
lookup(CP) when 2451 =< CP, CP =< 2472 -> 'PVALID';
lookup(CP) when 2474 =< CP, CP =< 2480 -> 'PVALID';
lookup(CP) when 2483 =< CP, CP =< 2485 -> 'UNASSIGNED';
lookup(CP) when 2486 =< CP, CP =< 2489 -> 'PVALID';
lookup(CP) when 2490 =< CP, CP =< 2491 -> 'UNASSIGNED';
lookup(CP) when 2492 =< CP, CP =< 2500 -> 'PVALID';
lookup(CP) when 2501 =< CP, CP =< 2502 -> 'UNASSIGNED';
lookup(CP) when 2503 =< CP, CP =< 2504 -> 'PVALID';
lookup(CP) when 2505 =< CP, CP =< 2506 -> 'UNASSIGNED';
lookup(CP) when 2507 =< CP, CP =< 2510 -> 'PVALID';
lookup(CP) when 2511 =< CP, CP =< 2518 -> 'UNASSIGNED';
lookup(CP) when 2520 =< CP, CP =< 2523 -> 'UNASSIGNED';
lookup(CP) when 2524 =< CP, CP =< 2525 -> 'DISALLOWED';
lookup(CP) when 2528 =< CP, CP =< 2531 -> 'PVALID';
lookup(CP) when 2532 =< CP, CP =< 2533 -> 'UNASSIGNED';
lookup(CP) when 2534 =< CP, CP =< 2545 -> 'PVALID';
lookup(CP) when 2546 =< CP, CP =< 2555 -> 'DISALLOWED';
lookup(CP) when 2559 =< CP, CP =< 2560 -> 'UNASSIGNED';
lookup(CP) when 2561 =< CP, CP =< 2563 -> 'PVALID';
lookup(CP) when 2565 =< CP, CP =< 2570 -> 'PVALID';
lookup(CP) when 2571 =< CP, CP =< 2574 -> 'UNASSIGNED';
lookup(CP) when 2575 =< CP, CP =< 2576 -> 'PVALID';
lookup(CP) when 2577 =< CP, CP =< 2578 -> 'UNASSIGNED';
lookup(CP) when 2579 =< CP, CP =< 2600 -> 'PVALID';
lookup(CP) when 2602 =< CP, CP =< 2608 -> 'PVALID';
lookup(CP) when 2616 =< CP, CP =< 2617 -> 'PVALID';
lookup(CP) when 2618 =< CP, CP =< 2619 -> 'UNASSIGNED';
lookup(CP) when 2622 =< CP, CP =< 2626 -> 'PVALID';
lookup(CP) when 2627 =< CP, CP =< 2630 -> 'UNASSIGNED';
lookup(CP) when 2631 =< CP, CP =< 2632 -> 'PVALID';
lookup(CP) when 2633 =< CP, CP =< 2634 -> 'UNASSIGNED';
lookup(CP) when 2635 =< CP, CP =< 2637 -> 'PVALID';
lookup(CP) when 2638 =< CP, CP =< 2640 -> 'UNASSIGNED';
lookup(CP) when 2642 =< CP, CP =< 2648 -> 'UNASSIGNED';
lookup(CP) when 2649 =< CP, CP =< 2651 -> 'DISALLOWED';
lookup(CP) when 2655 =< CP, CP =< 2661 -> 'UNASSIGNED';
lookup(CP) when 2662 =< CP, CP =< 2677 -> 'PVALID';
lookup(CP) when 2679 =< CP, CP =< 2688 -> 'UNASSIGNED';
lookup(CP) when 2689 =< CP, CP =< 2691 -> 'PVALID';
lookup(CP) when 2693 =< CP, CP =< 2701 -> 'PVALID';
lookup(CP) when 2703 =< CP, CP =< 2705 -> 'PVALID';
lookup(CP) when 2707 =< CP, CP =< 2728 -> 'PVALID';
lookup(CP) when 2730 =< CP, CP =< 2736 -> 'PVALID';
lookup(CP) when 2738 =< CP, CP =< 2739 -> 'PVALID';
lookup(CP) when 2741 =< CP, CP =< 2745 -> 'PVALID';
lookup(CP) when 2746 =< CP, CP =< 2747 -> 'UNASSIGNED';
lookup(CP) when 2748 =< CP, CP =< 2757 -> 'PVALID';
lookup(CP) when 2759 =< CP, CP =< 2761 -> 'PVALID';
lookup(CP) when 2763 =< CP, CP =< 2765 -> 'PVALID';
lookup(CP) when 2766 =< CP, CP =< 2767 -> 'UNASSIGNED';
lookup(CP) when 2769 =< CP, CP =< 2783 -> 'UNASSIGNED';
lookup(CP) when 2784 =< CP, CP =< 2787 -> 'PVALID';
lookup(CP) when 2788 =< CP, CP =< 2789 -> 'UNASSIGNED';
lookup(CP) when 2790 =< CP, CP =< 2799 -> 'PVALID';
lookup(CP) when 2800 =< CP, CP =< 2801 -> 'DISALLOWED';
lookup(CP) when 2802 =< CP, CP =< 2808 -> 'UNASSIGNED';
lookup(CP) when 2809 =< CP, CP =< 2815 -> 'PVALID';
lookup(CP) when 2817 =< CP, CP =< 2819 -> 'PVALID';
lookup(CP) when 2821 =< CP, CP =< 2828 -> 'PVALID';
lookup(CP) when 2829 =< CP, CP =< 2830 -> 'UNASSIGNED';
lookup(CP) when 2831 =< CP, CP =< 2832 -> 'PVALID';
lookup(CP) when 2833 =< CP, CP =< 2834 -> 'UNASSIGNED';
lookup(CP) when 2835 =< CP, CP =< 2856 -> 'PVALID';
lookup(CP) when 2858 =< CP, CP =< 2864 -> 'PVALID';
lookup(CP) when 2866 =< CP, CP =< 2867 -> 'PVALID';
lookup(CP) when 2869 =< CP, CP =< 2873 -> 'PVALID';
lookup(CP) when 2874 =< CP, CP =< 2875 -> 'UNASSIGNED';
lookup(CP) when 2876 =< CP, CP =< 2884 -> 'PVALID';
lookup(CP) when 2885 =< CP, CP =< 2886 -> 'UNASSIGNED';
lookup(CP) when 2887 =< CP, CP =< 2888 -> 'PVALID';
lookup(CP) when 2889 =< CP, CP =< 2890 -> 'UNASSIGNED';
lookup(CP) when 2891 =< CP, CP =< 2893 -> 'PVALID';
lookup(CP) when 2894 =< CP, CP =< 2900 -> 'UNASSIGNED';
lookup(CP) when 2901 =< CP, CP =< 2903 -> 'PVALID';
lookup(CP) when 2904 =< CP, CP =< 2907 -> 'UNASSIGNED';
lookup(CP) when 2908 =< CP, CP =< 2909 -> 'DISALLOWED';
lookup(CP) when 2911 =< CP, CP =< 2915 -> 'PVALID';
lookup(CP) when 2916 =< CP, CP =< 2917 -> 'UNASSIGNED';
lookup(CP) when 2918 =< CP, CP =< 2927 -> 'PVALID';
lookup(CP) when 2930 =< CP, CP =< 2935 -> 'DISALLOWED';
lookup(CP) when 2936 =< CP, CP =< 2945 -> 'UNASSIGNED';
lookup(CP) when 2946 =< CP, CP =< 2947 -> 'PVALID';
lookup(CP) when 2949 =< CP, CP =< 2954 -> 'PVALID';
lookup(CP) when 2955 =< CP, CP =< 2957 -> 'UNASSIGNED';
lookup(CP) when 2958 =< CP, CP =< 2960 -> 'PVALID';
lookup(CP) when 2962 =< CP, CP =< 2965 -> 'PVALID';
lookup(CP) when 2966 =< CP, CP =< 2968 -> 'UNASSIGNED';
lookup(CP) when 2969 =< CP, CP =< 2970 -> 'PVALID';
lookup(CP) when 2974 =< CP, CP =< 2975 -> 'PVALID';
lookup(CP) when 2976 =< CP, CP =< 2978 -> 'UNASSIGNED';
lookup(CP) when 2979 =< CP, CP =< 2980 -> 'PVALID';
lookup(CP) when 2981 =< CP, CP =< 2983 -> 'UNASSIGNED';
lookup(CP) when 2984 =< CP, CP =< 2986 -> 'PVALID';
lookup(CP) when 2987 =< CP, CP =< 2989 -> 'UNASSIGNED';
lookup(CP) when 2990 =< CP, CP =< 3001 -> 'PVALID';
lookup(CP) when 3002 =< CP, CP =< 3005 -> 'UNASSIGNED';
lookup(CP) when 3006 =< CP, CP =< 3010 -> 'PVALID';
lookup(CP) when 3011 =< CP, CP =< 3013 -> 'UNASSIGNED';
lookup(CP) when 3014 =< CP, CP =< 3016 -> 'PVALID';
lookup(CP) when 3018 =< CP, CP =< 3021 -> 'PVALID';
lookup(CP) when 3022 =< CP, CP =< 3023 -> 'UNASSIGNED';
lookup(CP) when 3025 =< CP, CP =< 3030 -> 'UNASSIGNED';
lookup(CP) when 3032 =< CP, CP =< 3045 -> 'UNASSIGNED';
lookup(CP) when 3046 =< CP, CP =< 3055 -> 'PVALID';
lookup(CP) when 3056 =< CP, CP =< 3066 -> 'DISALLOWED';
lookup(CP) when 3067 =< CP, CP =< 3071 -> 'UNASSIGNED';
lookup(CP) when 3072 =< CP, CP =< 3084 -> 'PVALID';
lookup(CP) when 3086 =< CP, CP =< 3088 -> 'PVALID';
lookup(CP) when 3090 =< CP, CP =< 3112 -> 'PVALID';
lookup(CP) when 3114 =< CP, CP =< 3129 -> 'PVALID';
lookup(CP) when 3130 =< CP, CP =< 3132 -> 'UNASSIGNED';
lookup(CP) when 3133 =< CP, CP =< 3140 -> 'PVALID';
lookup(CP) when 3142 =< CP, CP =< 3144 -> 'PVALID';
lookup(CP) when 3146 =< CP, CP =< 3149 -> 'PVALID';
lookup(CP) when 3150 =< CP, CP =< 3156 -> 'UNASSIGNED';
lookup(CP) when 3157 =< CP, CP =< 3158 -> 'PVALID';
lookup(CP) when 3160 =< CP, CP =< 3162 -> 'PVALID';
lookup(CP) when 3163 =< CP, CP =< 3167 -> 'UNASSIGNED';
lookup(CP) when 3168 =< CP, CP =< 3171 -> 'PVALID';
lookup(CP) when 3172 =< CP, CP =< 3173 -> 'UNASSIGNED';
lookup(CP) when 3174 =< CP, CP =< 3183 -> 'PVALID';
lookup(CP) when 3184 =< CP, CP =< 3190 -> 'UNASSIGNED';
lookup(CP) when 3191 =< CP, CP =< 3199 -> 'DISALLOWED';
lookup(CP) when 3200 =< CP, CP =< 3203 -> 'PVALID';
lookup(CP) when 3205 =< CP, CP =< 3212 -> 'PVALID';
lookup(CP) when 3214 =< CP, CP =< 3216 -> 'PVALID';
lookup(CP) when 3218 =< CP, CP =< 3240 -> 'PVALID';
lookup(CP) when 3242 =< CP, CP =< 3251 -> 'PVALID';
lookup(CP) when 3253 =< CP, CP =< 3257 -> 'PVALID';
lookup(CP) when 3258 =< CP, CP =< 3259 -> 'UNASSIGNED';
lookup(CP) when 3260 =< CP, CP =< 3268 -> 'PVALID';
lookup(CP) when 3270 =< CP, CP =< 3272 -> 'PVALID';
lookup(CP) when 3274 =< CP, CP =< 3277 -> 'PVALID';
lookup(CP) when 3278 =< CP, CP =< 3284 -> 'UNASSIGNED';
lookup(CP) when 3285 =< CP, CP =< 3286 -> 'PVALID';
lookup(CP) when 3287 =< CP, CP =< 3293 -> 'UNASSIGNED';
lookup(CP) when 3296 =< CP, CP =< 3299 -> 'PVALID';
lookup(CP) when 3300 =< CP, CP =< 3301 -> 'UNASSIGNED';
lookup(CP) when 3302 =< CP, CP =< 3311 -> 'PVALID';
lookup(CP) when 3313 =< CP, CP =< 3314 -> 'PVALID';
lookup(CP) when 3315 =< CP, CP =< 3327 -> 'UNASSIGNED';
lookup(CP) when 3328 =< CP, CP =< 3340 -> 'PVALID';
lookup(CP) when 3342 =< CP, CP =< 3344 -> 'PVALID';
lookup(CP) when 3346 =< CP, CP =< 3396 -> 'PVALID';
lookup(CP) when 3398 =< CP, CP =< 3400 -> 'PVALID';
lookup(CP) when 3402 =< CP, CP =< 3406 -> 'PVALID';
lookup(CP) when 3408 =< CP, CP =< 3411 -> 'UNASSIGNED';
lookup(CP) when 3412 =< CP, CP =< 3415 -> 'PVALID';
lookup(CP) when 3416 =< CP, CP =< 3422 -> 'DISALLOWED';
lookup(CP) when 3423 =< CP, CP =< 3427 -> 'PVALID';
lookup(CP) when 3428 =< CP, CP =< 3429 -> 'UNASSIGNED';
lookup(CP) when 3430 =< CP, CP =< 3439 -> 'PVALID';
lookup(CP) when 3440 =< CP, CP =< 3449 -> 'DISALLOWED';
lookup(CP) when 3450 =< CP, CP =< 3455 -> 'PVALID';
lookup(CP) when 3457 =< CP, CP =< 3459 -> 'PVALID';
lookup(CP) when 3461 =< CP, CP =< 3478 -> 'PVALID';
lookup(CP) when 3479 =< CP, CP =< 3481 -> 'UNASSIGNED';
lookup(CP) when 3482 =< CP, CP =< 3505 -> 'PVALID';
lookup(CP) when 3507 =< CP, CP =< 3515 -> 'PVALID';
lookup(CP) when 3518 =< CP, CP =< 3519 -> 'UNASSIGNED';
lookup(CP) when 3520 =< CP, CP =< 3526 -> 'PVALID';
lookup(CP) when 3527 =< CP, CP =< 3529 -> 'UNASSIGNED';
lookup(CP) when 3531 =< CP, CP =< 3534 -> 'UNASSIGNED';
lookup(CP) when 3535 =< CP, CP =< 3540 -> 'PVALID';
lookup(CP) when 3544 =< CP, CP =< 3551 -> 'PVALID';
lookup(CP) when 3552 =< CP, CP =< 3557 -> 'UNASSIGNED';
lookup(CP) when 3558 =< CP, CP =< 3567 -> 'PVALID';
lookup(CP) when 3568 =< CP, CP =< 3569 -> 'UNASSIGNED';
lookup(CP) when 3570 =< CP, CP =< 3571 -> 'PVALID';
lookup(CP) when 3573 =< CP, CP =< 3584 -> 'UNASSIGNED';
lookup(CP) when 3585 =< CP, CP =< 3634 -> 'PVALID';
lookup(CP) when 3636 =< CP, CP =< 3642 -> 'PVALID';
lookup(CP) when 3643 =< CP, CP =< 3646 -> 'UNASSIGNED';
lookup(CP) when 3648 =< CP, CP =< 3662 -> 'PVALID';
lookup(CP) when 3664 =< CP, CP =< 3673 -> 'PVALID';
lookup(CP) when 3674 =< CP, CP =< 3675 -> 'DISALLOWED';
lookup(CP) when 3676 =< CP, CP =< 3712 -> 'UNASSIGNED';
lookup(CP) when 3713 =< CP, CP =< 3714 -> 'PVALID';
lookup(CP) when 3718 =< CP, CP =< 3722 -> 'PVALID';
lookup(CP) when 3724 =< CP, CP =< 3747 -> 'PVALID';
lookup(CP) when 3751 =< CP, CP =< 3762 -> 'PVALID';
lookup(CP) when 3764 =< CP, CP =< 3773 -> 'PVALID';
lookup(CP) when 3774 =< CP, CP =< 3775 -> 'UNASSIGNED';
lookup(CP) when 3776 =< CP, CP =< 3780 -> 'PVALID';
lookup(CP) when 3784 =< CP, CP =< 3789 -> 'PVALID';
lookup(CP) when 3790 =< CP, CP =< 3791 -> 'UNASSIGNED';
lookup(CP) when 3792 =< CP, CP =< 3801 -> 'PVALID';
lookup(CP) when 3802 =< CP, CP =< 3803 -> 'UNASSIGNED';
lookup(CP) when 3804 =< CP, CP =< 3805 -> 'DISALLOWED';
lookup(CP) when 3806 =< CP, CP =< 3807 -> 'PVALID';
lookup(CP) when 3808 =< CP, CP =< 3839 -> 'UNASSIGNED';
lookup(CP) when 3841 =< CP, CP =< 3850 -> 'DISALLOWED';
lookup(CP) when 3852 =< CP, CP =< 3863 -> 'DISALLOWED';
lookup(CP) when 3864 =< CP, CP =< 3865 -> 'PVALID';
lookup(CP) when 3866 =< CP, CP =< 3871 -> 'DISALLOWED';
lookup(CP) when 3872 =< CP, CP =< 3881 -> 'PVALID';
lookup(CP) when 3882 =< CP, CP =< 3892 -> 'DISALLOWED';
lookup(CP) when 3898 =< CP, CP =< 3901 -> 'DISALLOWED';
lookup(CP) when 3902 =< CP, CP =< 3906 -> 'PVALID';
lookup(CP) when 3908 =< CP, CP =< 3911 -> 'PVALID';
lookup(CP) when 3913 =< CP, CP =< 3916 -> 'PVALID';
lookup(CP) when 3918 =< CP, CP =< 3921 -> 'PVALID';
lookup(CP) when 3923 =< CP, CP =< 3926 -> 'PVALID';
lookup(CP) when 3928 =< CP, CP =< 3931 -> 'PVALID';
lookup(CP) when 3933 =< CP, CP =< 3944 -> 'PVALID';
lookup(CP) when 3946 =< CP, CP =< 3948 -> 'PVALID';
lookup(CP) when 3949 =< CP, CP =< 3952 -> 'UNASSIGNED';
lookup(CP) when 3953 =< CP, CP =< 3954 -> 'PVALID';
lookup(CP) when 3957 =< CP, CP =< 3961 -> 'DISALLOWED';
lookup(CP) when 3962 =< CP, CP =< 3968 -> 'PVALID';
lookup(CP) when 3970 =< CP, CP =< 3972 -> 'PVALID';
lookup(CP) when 3974 =< CP, CP =< 3986 -> 'PVALID';
lookup(CP) when 3988 =< CP, CP =< 3991 -> 'PVALID';
lookup(CP) when 3993 =< CP, CP =< 3996 -> 'PVALID';
lookup(CP) when 3998 =< CP, CP =< 4001 -> 'PVALID';
lookup(CP) when 4003 =< CP, CP =< 4006 -> 'PVALID';
lookup(CP) when 4008 =< CP, CP =< 4011 -> 'PVALID';
lookup(CP) when 4013 =< CP, CP =< 4024 -> 'PVALID';
lookup(CP) when 4026 =< CP, CP =< 4028 -> 'PVALID';
lookup(CP) when 4030 =< CP, CP =< 4037 -> 'DISALLOWED';
lookup(CP) when 4039 =< CP, CP =< 4044 -> 'DISALLOWED';
lookup(CP) when 4046 =< CP, CP =< 4058 -> 'DISALLOWED';
lookup(CP) when 4059 =< CP, CP =< 4095 -> 'UNASSIGNED';
lookup(CP) when 4096 =< CP, CP =< 4169 -> 'PVALID';
lookup(CP) when 4170 =< CP, CP =< 4175 -> 'DISALLOWED';
lookup(CP) when 4176 =< CP, CP =< 4253 -> 'PVALID';
lookup(CP) when 4254 =< CP, CP =< 4293 -> 'DISALLOWED';
lookup(CP) when 4296 =< CP, CP =< 4300 -> 'UNASSIGNED';
lookup(CP) when 4302 =< CP, CP =< 4303 -> 'UNASSIGNED';
lookup(CP) when 4304 =< CP, CP =< 4346 -> 'PVALID';
lookup(CP) when 4347 =< CP, CP =< 4348 -> 'DISALLOWED';
lookup(CP) when 4349 =< CP, CP =< 4351 -> 'PVALID';
lookup(CP) when 4352 =< CP, CP =< 4607 -> 'DISALLOWED';
lookup(CP) when 4608 =< CP, CP =< 4680 -> 'PVALID';
lookup(CP) when 4682 =< CP, CP =< 4685 -> 'PVALID';
lookup(CP) when 4686 =< CP, CP =< 4687 -> 'UNASSIGNED';
lookup(CP) when 4688 =< CP, CP =< 4694 -> 'PVALID';
lookup(CP) when 4698 =< CP, CP =< 4701 -> 'PVALID';
lookup(CP) when 4702 =< CP, CP =< 4703 -> 'UNASSIGNED';
lookup(CP) when 4704 =< CP, CP =< 4744 -> 'PVALID';
lookup(CP) when 4746 =< CP, CP =< 4749 -> 'PVALID';
lookup(CP) when 4750 =< CP, CP =< 4751 -> 'UNASSIGNED';
lookup(CP) when 4752 =< CP, CP =< 4784 -> 'PVALID';
lookup(CP) when 4786 =< CP, CP =< 4789 -> 'PVALID';
lookup(CP) when 4790 =< CP, CP =< 4791 -> 'UNASSIGNED';
lookup(CP) when 4792 =< CP, CP =< 4798 -> 'PVALID';
lookup(CP) when 4802 =< CP, CP =< 4805 -> 'PVALID';
lookup(CP) when 4806 =< CP, CP =< 4807 -> 'UNASSIGNED';
lookup(CP) when 4808 =< CP, CP =< 4822 -> 'PVALID';
lookup(CP) when 4824 =< CP, CP =< 4880 -> 'PVALID';
lookup(CP) when 4882 =< CP, CP =< 4885 -> 'PVALID';
lookup(CP) when 4886 =< CP, CP =< 4887 -> 'UNASSIGNED';
lookup(CP) when 4888 =< CP, CP =< 4954 -> 'PVALID';
lookup(CP) when 4955 =< CP, CP =< 4956 -> 'UNASSIGNED';
lookup(CP) when 4957 =< CP, CP =< 4959 -> 'PVALID';
lookup(CP) when 4960 =< CP, CP =< 4988 -> 'DISALLOWED';
lookup(CP) when 4989 =< CP, CP =< 4991 -> 'UNASSIGNED';
lookup(CP) when 4992 =< CP, CP =< 5007 -> 'PVALID';
lookup(CP) when 5008 =< CP, CP =< 5017 -> 'DISALLOWED';
lookup(CP) when 5018 =< CP, CP =< 5023 -> 'UNASSIGNED';
lookup(CP) when 5024 =< CP, CP =< 5109 -> 'PVALID';
lookup(CP) when 5110 =< CP, CP =< 5111 -> 'UNASSIGNED';
lookup(CP) when 5112 =< CP, CP =< 5117 -> 'DISALLOWED';
lookup(CP) when 5118 =< CP, CP =< 5119 -> 'UNASSIGNED';
lookup(CP) when 5121 =< CP, CP =< 5740 -> 'PVALID';
lookup(CP) when 5741 =< CP, CP =< 5742 -> 'DISALLOWED';
lookup(CP) when 5743 =< CP, CP =< 5759 -> 'PVALID';
lookup(CP) when 5761 =< CP, CP =< 5786 -> 'PVALID';
lookup(CP) when 5787 =< CP, CP =< 5788 -> 'DISALLOWED';
lookup(CP) when 5789 =< CP, CP =< 5791 -> 'UNASSIGNED';
lookup(CP) when 5792 =< CP, CP =< 5866 -> 'PVALID';
lookup(CP) when 5867 =< CP, CP =< 5872 -> 'DISALLOWED';
lookup(CP) when 5873 =< CP, CP =< 5880 -> 'PVALID';
lookup(CP) when 5881 =< CP, CP =< 5887 -> 'UNASSIGNED';
lookup(CP) when 5888 =< CP, CP =< 5900 -> 'PVALID';
lookup(CP) when 5902 =< CP, CP =< 5908 -> 'PVALID';
lookup(CP) when 5909 =< CP, CP =< 5919 -> 'UNASSIGNED';
lookup(CP) when 5920 =< CP, CP =< 5940 -> 'PVALID';
lookup(CP) when 5941 =< CP, CP =< 5942 -> 'DISALLOWED';
lookup(CP) when 5943 =< CP, CP =< 5951 -> 'UNASSIGNED';
lookup(CP) when 5952 =< CP, CP =< 5971 -> 'PVALID';
lookup(CP) when 5972 =< CP, CP =< 5983 -> 'UNASSIGNED';
lookup(CP) when 5984 =< CP, CP =< 5996 -> 'PVALID';
lookup(CP) when 5998 =< CP, CP =< 6000 -> 'PVALID';
lookup(CP) when 6002 =< CP, CP =< 6003 -> 'PVALID';
lookup(CP) when 6004 =< CP, CP =< 6015 -> 'UNASSIGNED';
lookup(CP) when 6016 =< CP, CP =< 6067 -> 'PVALID';
lookup(CP) when 6068 =< CP, CP =< 6069 -> 'DISALLOWED';
lookup(CP) when 6070 =< CP, CP =< 6099 -> 'PVALID';
lookup(CP) when 6100 =< CP, CP =< 6102 -> 'DISALLOWED';
lookup(CP) when 6104 =< CP, CP =< 6107 -> 'DISALLOWED';
lookup(CP) when 6108 =< CP, CP =< 6109 -> 'PVALID';
lookup(CP) when 6110 =< CP, CP =< 6111 -> 'UNASSIGNED';
lookup(CP) when 6112 =< CP, CP =< 6121 -> 'PVALID';
lookup(CP) when 6122 =< CP, CP =< 6127 -> 'UNASSIGNED';
lookup(CP) when 6128 =< CP, CP =< 6137 -> 'DISALLOWED';
lookup(CP) when 6138 =< CP, CP =< 6143 -> 'UNASSIGNED';
lookup(CP) when 6144 =< CP, CP =< 6158 -> 'DISALLOWED';
lookup(CP) when 6160 =< CP, CP =< 6169 -> 'PVALID';
lookup(CP) when 6170 =< CP, CP =< 6175 -> 'UNASSIGNED';
lookup(CP) when 6176 =< CP, CP =< 6264 -> 'PVALID';
lookup(CP) when 6265 =< CP, CP =< 6271 -> 'UNASSIGNED';
lookup(CP) when 6272 =< CP, CP =< 6314 -> 'PVALID';
lookup(CP) when 6315 =< CP, CP =< 6319 -> 'UNASSIGNED';
lookup(CP) when 6320 =< CP, CP =< 6389 -> 'PVALID';
lookup(CP) when 6390 =< CP, CP =< 6399 -> 'UNASSIGNED';
lookup(CP) when 6400 =< CP, CP =< 6430 -> 'PVALID';
lookup(CP) when 6432 =< CP, CP =< 6443 -> 'PVALID';
lookup(CP) when 6444 =< CP, CP =< 6447 -> 'UNASSIGNED';
lookup(CP) when 6448 =< CP, CP =< 6459 -> 'PVALID';
lookup(CP) when 6460 =< CP, CP =< 6463 -> 'UNASSIGNED';
lookup(CP) when 6465 =< CP, CP =< 6467 -> 'UNASSIGNED';
lookup(CP) when 6468 =< CP, CP =< 6469 -> 'DISALLOWED';
lookup(CP) when 6470 =< CP, CP =< 6509 -> 'PVALID';
lookup(CP) when 6510 =< CP, CP =< 6511 -> 'UNASSIGNED';
lookup(CP) when 6512 =< CP, CP =< 6516 -> 'PVALID';
lookup(CP) when 6517 =< CP, CP =< 6527 -> 'UNASSIGNED';
lookup(CP) when 6528 =< CP, CP =< 6571 -> 'PVALID';
lookup(CP) when 6572 =< CP, CP =< 6575 -> 'UNASSIGNED';
lookup(CP) when 6576 =< CP, CP =< 6601 -> 'PVALID';
lookup(CP) when 6602 =< CP, CP =< 6607 -> 'UNASSIGNED';
lookup(CP) when 6608 =< CP, CP =< 6617 -> 'PVALID';
lookup(CP) when 6619 =< CP, CP =< 6621 -> 'UNASSIGNED';
lookup(CP) when 6622 =< CP, CP =< 6655 -> 'DISALLOWED';
lookup(CP) when 6656 =< CP, CP =< 6683 -> 'PVALID';
lookup(CP) when 6684 =< CP, CP =< 6685 -> 'UNASSIGNED';
lookup(CP) when 6686 =< CP, CP =< 6687 -> 'DISALLOWED';
lookup(CP) when 6688 =< CP, CP =< 6750 -> 'PVALID';
lookup(CP) when 6752 =< CP, CP =< 6780 -> 'PVALID';
lookup(CP) when 6781 =< CP, CP =< 6782 -> 'UNASSIGNED';
lookup(CP) when 6783 =< CP, CP =< 6793 -> 'PVALID';
lookup(CP) when 6794 =< CP, CP =< 6799 -> 'UNASSIGNED';
lookup(CP) when 6800 =< CP, CP =< 6809 -> 'PVALID';
lookup(CP) when 6810 =< CP, CP =< 6815 -> 'UNASSIGNED';
lookup(CP) when 6816 =< CP, CP =< 6822 -> 'DISALLOWED';
lookup(CP) when 6824 =< CP, CP =< 6829 -> 'DISALLOWED';
lookup(CP) when 6830 =< CP, CP =< 6831 -> 'UNASSIGNED';
lookup(CP) when 6832 =< CP, CP =< 6845 -> 'PVALID';
lookup(CP) when 6847 =< CP, CP =< 6848 -> 'PVALID';
lookup(CP) when 6849 =< CP, CP =< 6911 -> 'UNASSIGNED';
lookup(CP) when 6912 =< CP, CP =< 6987 -> 'PVALID';
lookup(CP) when 6988 =< CP, CP =< 6991 -> 'UNASSIGNED';
lookup(CP) when 6992 =< CP, CP =< 7001 -> 'PVALID';
lookup(CP) when 7002 =< CP, CP =< 7018 -> 'DISALLOWED';
lookup(CP) when 7019 =< CP, CP =< 7027 -> 'PVALID';
lookup(CP) when 7028 =< CP, CP =< 7036 -> 'DISALLOWED';
lookup(CP) when 7037 =< CP, CP =< 7039 -> 'UNASSIGNED';
lookup(CP) when 7040 =< CP, CP =< 7155 -> 'PVALID';
lookup(CP) when 7156 =< CP, CP =< 7163 -> 'UNASSIGNED';
lookup(CP) when 7164 =< CP, CP =< 7167 -> 'DISALLOWED';
lookup(CP) when 7168 =< CP, CP =< 7223 -> 'PVALID';
lookup(CP) when 7224 =< CP, CP =< 7226 -> 'UNASSIGNED';
lookup(CP) when 7227 =< CP, CP =< 7231 -> 'DISALLOWED';
lookup(CP) when 7232 =< CP, CP =< 7241 -> 'PVALID';
lookup(CP) when 7242 =< CP, CP =< 7244 -> 'UNASSIGNED';
lookup(CP) when 7245 =< CP, CP =< 7293 -> 'PVALID';
lookup(CP) when 7294 =< CP, CP =< 7304 -> 'DISALLOWED';
lookup(CP) when 7305 =< CP, CP =< 7311 -> 'UNASSIGNED';
lookup(CP) when 7312 =< CP, CP =< 7354 -> 'DISALLOWED';
lookup(CP) when 7355 =< CP, CP =< 7356 -> 'UNASSIGNED';
lookup(CP) when 7357 =< CP, CP =< 7367 -> 'DISALLOWED';
lookup(CP) when 7368 =< CP, CP =< 7375 -> 'UNASSIGNED';
lookup(CP) when 7376 =< CP, CP =< 7378 -> 'PVALID';
lookup(CP) when 7380 =< CP, CP =< 7418 -> 'PVALID';
lookup(CP) when 7419 =< CP, CP =< 7423 -> 'UNASSIGNED';
lookup(CP) when 7424 =< CP, CP =< 7467 -> 'PVALID';
lookup(CP) when 7468 =< CP, CP =< 7470 -> 'DISALLOWED';
lookup(CP) when 7472 =< CP, CP =< 7482 -> 'DISALLOWED';
lookup(CP) when 7484 =< CP, CP =< 7501 -> 'DISALLOWED';
lookup(CP) when 7503 =< CP, CP =< 7530 -> 'DISALLOWED';
lookup(CP) when 7531 =< CP, CP =< 7543 -> 'PVALID';
lookup(CP) when 7545 =< CP, CP =< 7578 -> 'PVALID';
lookup(CP) when 7579 =< CP, CP =< 7615 -> 'DISALLOWED';
lookup(CP) when 7616 =< CP, CP =< 7673 -> 'PVALID';
lookup(CP) when 7675 =< CP, CP =< 7679 -> 'PVALID';
lookup(CP) when 7829 =< CP, CP =< 7833 -> 'PVALID';
lookup(CP) when 7834 =< CP, CP =< 7835 -> 'DISALLOWED';
lookup(CP) when 7836 =< CP, CP =< 7837 -> 'PVALID';
lookup(CP) when 7935 =< CP, CP =< 7943 -> 'PVALID';
lookup(CP) when 7944 =< CP, CP =< 7951 -> 'DISALLOWED';
lookup(CP) when 7952 =< CP, CP =< 7957 -> 'PVALID';
lookup(CP) when 7958 =< CP, CP =< 7959 -> 'UNASSIGNED';
lookup(CP) when 7960 =< CP, CP =< 7965 -> 'DISALLOWED';
lookup(CP) when 7966 =< CP, CP =< 7967 -> 'UNASSIGNED';
lookup(CP) when 7968 =< CP, CP =< 7975 -> 'PVALID';
lookup(CP) when 7976 =< CP, CP =< 7983 -> 'DISALLOWED';
lookup(CP) when 7984 =< CP, CP =< 7991 -> 'PVALID';
lookup(CP) when 7992 =< CP, CP =< 7999 -> 'DISALLOWED';
lookup(CP) when 8000 =< CP, CP =< 8005 -> 'PVALID';
lookup(CP) when 8006 =< CP, CP =< 8007 -> 'UNASSIGNED';
lookup(CP) when 8008 =< CP, CP =< 8013 -> 'DISALLOWED';
lookup(CP) when 8014 =< CP, CP =< 8015 -> 'UNASSIGNED';
lookup(CP) when 8016 =< CP, CP =< 8023 -> 'PVALID';
lookup(CP) when 8032 =< CP, CP =< 8039 -> 'PVALID';
lookup(CP) when 8040 =< CP, CP =< 8047 -> 'DISALLOWED';
lookup(CP) when 8062 =< CP, CP =< 8063 -> 'UNASSIGNED';
lookup(CP) when 8064 =< CP, CP =< 8111 -> 'DISALLOWED';
lookup(CP) when 8112 =< CP, CP =< 8113 -> 'PVALID';
lookup(CP) when 8114 =< CP, CP =< 8116 -> 'DISALLOWED';
lookup(CP) when 8119 =< CP, CP =< 8132 -> 'DISALLOWED';
lookup(CP) when 8135 =< CP, CP =< 8143 -> 'DISALLOWED';
lookup(CP) when 8144 =< CP, CP =< 8146 -> 'PVALID';
lookup(CP) when 8148 =< CP, CP =< 8149 -> 'UNASSIGNED';
lookup(CP) when 8150 =< CP, CP =< 8151 -> 'PVALID';
lookup(CP) when 8152 =< CP, CP =< 8155 -> 'DISALLOWED';
lookup(CP) when 8157 =< CP, CP =< 8159 -> 'DISALLOWED';
lookup(CP) when 8160 =< CP, CP =< 8162 -> 'PVALID';
lookup(CP) when 8164 =< CP, CP =< 8167 -> 'PVALID';
lookup(CP) when 8168 =< CP, CP =< 8175 -> 'DISALLOWED';
lookup(CP) when 8176 =< CP, CP =< 8177 -> 'UNASSIGNED';
lookup(CP) when 8178 =< CP, CP =< 8180 -> 'DISALLOWED';
lookup(CP) when 8183 =< CP, CP =< 8190 -> 'DISALLOWED';
lookup(CP) when 8192 =< CP, CP =< 8203 -> 'DISALLOWED';
lookup(CP) when 8204 =< CP, CP =< 8205 -> 'CONTEXTJ';
lookup(CP) when 8206 =< CP, CP =< 8292 -> 'DISALLOWED';
lookup(CP) when 8294 =< CP, CP =< 8305 -> 'DISALLOWED';
lookup(CP) when 8306 =< CP, CP =< 8307 -> 'UNASSIGNED';
lookup(CP) when 8308 =< CP, CP =< 8334 -> 'DISALLOWED';
lookup(CP) when 8336 =< CP, CP =< 8348 -> 'DISALLOWED';
lookup(CP) when 8349 =< CP, CP =< 8351 -> 'UNASSIGNED';
lookup(CP) when 8352 =< CP, CP =< 8383 -> 'DISALLOWED';
lookup(CP) when 8384 =< CP, CP =< 8399 -> 'UNASSIGNED';
lookup(CP) when 8400 =< CP, CP =< 8432 -> 'DISALLOWED';
lookup(CP) when 8433 =< CP, CP =< 8447 -> 'UNASSIGNED';
lookup(CP) when 8448 =< CP, CP =< 8525 -> 'DISALLOWED';
lookup(CP) when 8527 =< CP, CP =< 8579 -> 'DISALLOWED';
lookup(CP) when 8581 =< CP, CP =< 8587 -> 'DISALLOWED';
lookup(CP) when 8588 =< CP, CP =< 8591 -> 'UNASSIGNED';
lookup(CP) when 8592 =< CP, CP =< 9254 -> 'DISALLOWED';
lookup(CP) when 9255 =< CP, CP =< 9279 -> 'UNASSIGNED';
lookup(CP) when 9280 =< CP, CP =< 9290 -> 'DISALLOWED';
lookup(CP) when 9291 =< CP, CP =< 9311 -> 'UNASSIGNED';
lookup(CP) when 9312 =< CP, CP =< 11123 -> 'DISALLOWED';
lookup(CP) when 11124 =< CP, CP =< 11125 -> 'UNASSIGNED';
lookup(CP) when 11126 =< CP, CP =< 11157 -> 'DISALLOWED';
lookup(CP) when 11159 =< CP, CP =< 11310 -> 'DISALLOWED';
lookup(CP) when 11312 =< CP, CP =< 11358 -> 'PVALID';
lookup(CP) when 11362 =< CP, CP =< 11364 -> 'DISALLOWED';
lookup(CP) when 11365 =< CP, CP =< 11366 -> 'PVALID';
lookup(CP) when 11373 =< CP, CP =< 11376 -> 'DISALLOWED';
lookup(CP) when 11379 =< CP, CP =< 11380 -> 'PVALID';
lookup(CP) when 11382 =< CP, CP =< 11387 -> 'PVALID';
lookup(CP) when 11388 =< CP, CP =< 11392 -> 'DISALLOWED';
lookup(CP) when 11491 =< CP, CP =< 11492 -> 'PVALID';
lookup(CP) when 11493 =< CP, CP =< 11499 -> 'DISALLOWED';
lookup(CP) when 11502 =< CP, CP =< 11505 -> 'PVALID';
lookup(CP) when 11508 =< CP, CP =< 11512 -> 'UNASSIGNED';
lookup(CP) when 11513 =< CP, CP =< 11519 -> 'DISALLOWED';
lookup(CP) when 11520 =< CP, CP =< 11557 -> 'PVALID';
lookup(CP) when 11560 =< CP, CP =< 11564 -> 'UNASSIGNED';
lookup(CP) when 11566 =< CP, CP =< 11567 -> 'UNASSIGNED';
lookup(CP) when 11568 =< CP, CP =< 11623 -> 'PVALID';
lookup(CP) when 11624 =< CP, CP =< 11630 -> 'UNASSIGNED';
lookup(CP) when 11631 =< CP, CP =< 11632 -> 'DISALLOWED';
lookup(CP) when 11633 =< CP, CP =< 11646 -> 'UNASSIGNED';
lookup(CP) when 11647 =< CP, CP =< 11670 -> 'PVALID';
lookup(CP) when 11671 =< CP, CP =< 11679 -> 'UNASSIGNED';
lookup(CP) when 11680 =< CP, CP =< 11686 -> 'PVALID';
lookup(CP) when 11688 =< CP, CP =< 11694 -> 'PVALID';
lookup(CP) when 11696 =< CP, CP =< 11702 -> 'PVALID';
lookup(CP) when 11704 =< CP, CP =< 11710 -> 'PVALID';
lookup(CP) when 11712 =< CP, CP =< 11718 -> 'PVALID';
lookup(CP) when 11720 =< CP, CP =< 11726 -> 'PVALID';
lookup(CP) when 11728 =< CP, CP =< 11734 -> 'PVALID';
lookup(CP) when 11736 =< CP, CP =< 11742 -> 'PVALID';
lookup(CP) when 11744 =< CP, CP =< 11775 -> 'PVALID';
lookup(CP) when 11776 =< CP, CP =< 11822 -> 'DISALLOWED';
lookup(CP) when 11824 =< CP, CP =< 11858 -> 'DISALLOWED';
lookup(CP) when 11859 =< CP, CP =< 11903 -> 'UNASSIGNED';
lookup(CP) when 11904 =< CP, CP =< 11929 -> 'DISALLOWED';
lookup(CP) when 11931 =< CP, CP =< 12019 -> 'DISALLOWED';
lookup(CP) when 12020 =< CP, CP =< 12031 -> 'UNASSIGNED';
lookup(CP) when 12032 =< CP, CP =< 12245 -> 'DISALLOWED';
lookup(CP) when 12246 =< CP, CP =< 12271 -> 'UNASSIGNED';
lookup(CP) when 12272 =< CP, CP =< 12283 -> 'DISALLOWED';
lookup(CP) when 12284 =< CP, CP =< 12287 -> 'UNASSIGNED';
lookup(CP) when 12288 =< CP, CP =< 12292 -> 'DISALLOWED';
lookup(CP) when 12293 =< CP, CP =< 12295 -> 'PVALID';
lookup(CP) when 12296 =< CP, CP =< 12329 -> 'DISALLOWED';
lookup(CP) when 12330 =< CP, CP =< 12333 -> 'PVALID';
lookup(CP) when 12334 =< CP, CP =< 12347 -> 'DISALLOWED';
lookup(CP) when 12349 =< CP, CP =< 12351 -> 'DISALLOWED';
lookup(CP) when 12353 =< CP, CP =< 12438 -> 'PVALID';
lookup(CP) when 12439 =< CP, CP =< 12440 -> 'UNASSIGNED';
lookup(CP) when 12441 =< CP, CP =< 12442 -> 'PVALID';
lookup(CP) when 12443 =< CP, CP =< 12444 -> 'DISALLOWED';
lookup(CP) when 12445 =< CP, CP =< 12446 -> 'PVALID';
lookup(CP) when 12447 =< CP, CP =< 12448 -> 'DISALLOWED';
lookup(CP) when 12449 =< CP, CP =< 12538 -> 'PVALID';
lookup(CP) when 12540 =< CP, CP =< 12542 -> 'PVALID';
lookup(CP) when 12544 =< CP, CP =< 12548 -> 'UNASSIGNED';
lookup(CP) when 12549 =< CP, CP =< 12591 -> 'PVALID';
lookup(CP) when 12593 =< CP, CP =< 12686 -> 'DISALLOWED';
lookup(CP) when 12688 =< CP, CP =< 12703 -> 'DISALLOWED';
lookup(CP) when 12704 =< CP, CP =< 12735 -> 'PVALID';
lookup(CP) when 12736 =< CP, CP =< 12771 -> 'DISALLOWED';
lookup(CP) when 12772 =< CP, CP =< 12783 -> 'UNASSIGNED';
lookup(CP) when 12784 =< CP, CP =< 12799 -> 'PVALID';
lookup(CP) when 12800 =< CP, CP =< 12830 -> 'DISALLOWED';
lookup(CP) when 12832 =< CP, CP =< 13311 -> 'DISALLOWED';
lookup(CP) when 13312 =< CP, CP =< 19903 -> 'PVALID';
lookup(CP) when 19904 =< CP, CP =< 19967 -> 'DISALLOWED';
lookup(CP) when 19968 =< CP, CP =< 40956 -> 'PVALID';
lookup(CP) when 40957 =< CP, CP =< 40959 -> 'UNASSIGNED';
lookup(CP) when 40960 =< CP, CP =< 42124 -> 'PVALID';
lookup(CP) when 42125 =< CP, CP =< 42127 -> 'UNASSIGNED';
lookup(CP) when 42128 =< CP, CP =< 42182 -> 'DISALLOWED';
lookup(CP) when 42183 =< CP, CP =< 42191 -> 'UNASSIGNED';
lookup(CP) when 42192 =< CP, CP =< 42237 -> 'PVALID';
lookup(CP) when 42238 =< CP, CP =< 42239 -> 'DISALLOWED';
lookup(CP) when 42240 =< CP, CP =< 42508 -> 'PVALID';
lookup(CP) when 42509 =< CP, CP =< 42511 -> 'DISALLOWED';
lookup(CP) when 42512 =< CP, CP =< 42539 -> 'PVALID';
lookup(CP) when 42540 =< CP, CP =< 42559 -> 'UNASSIGNED';
lookup(CP) when 42605 =< CP, CP =< 42607 -> 'PVALID';
lookup(CP) when 42608 =< CP, CP =< 42611 -> 'DISALLOWED';
lookup(CP) when 42612 =< CP, CP =< 42621 -> 'PVALID';
lookup(CP) when 42652 =< CP, CP =< 42653 -> 'DISALLOWED';
lookup(CP) when 42654 =< CP, CP =< 42725 -> 'PVALID';
lookup(CP) when 42726 =< CP, CP =< 42735 -> 'DISALLOWED';
lookup(CP) when 42736 =< CP, CP =< 42737 -> 'PVALID';
lookup(CP) when 42738 =< CP, CP =< 42743 -> 'DISALLOWED';
lookup(CP) when 42744 =< CP, CP =< 42751 -> 'UNASSIGNED';
lookup(CP) when 42752 =< CP, CP =< 42774 -> 'DISALLOWED';
lookup(CP) when 42775 =< CP, CP =< 42783 -> 'PVALID';
lookup(CP) when 42784 =< CP, CP =< 42786 -> 'DISALLOWED';
lookup(CP) when 42799 =< CP, CP =< 42801 -> 'PVALID';
lookup(CP) when 42865 =< CP, CP =< 42872 -> 'PVALID';
lookup(CP) when 42877 =< CP, CP =< 42878 -> 'DISALLOWED';
lookup(CP) when 42887 =< CP, CP =< 42888 -> 'PVALID';
lookup(CP) when 42889 =< CP, CP =< 42891 -> 'DISALLOWED';
lookup(CP) when 42894 =< CP, CP =< 42895 -> 'PVALID';
lookup(CP) when 42899 =< CP, CP =< 42901 -> 'PVALID';
lookup(CP) when 42922 =< CP, CP =< 42926 -> 'DISALLOWED';
lookup(CP) when 42928 =< CP, CP =< 42932 -> 'DISALLOWED';
lookup(CP) when 42944 =< CP, CP =< 42945 -> 'UNASSIGNED';
lookup(CP) when 42948 =< CP, CP =< 42951 -> 'DISALLOWED';
lookup(CP) when 42955 =< CP, CP =< 42996 -> 'UNASSIGNED';
lookup(CP) when 42998 =< CP, CP =< 42999 -> 'PVALID';
lookup(CP) when 43000 =< CP, CP =< 43001 -> 'DISALLOWED';
lookup(CP) when 43002 =< CP, CP =< 43047 -> 'PVALID';
lookup(CP) when 43048 =< CP, CP =< 43051 -> 'DISALLOWED';
lookup(CP) when 43053 =< CP, CP =< 43055 -> 'UNASSIGNED';
lookup(CP) when 43056 =< CP, CP =< 43065 -> 'DISALLOWED';
lookup(CP) when 43066 =< CP, CP =< 43071 -> 'UNASSIGNED';
lookup(CP) when 43072 =< CP, CP =< 43123 -> 'PVALID';
lookup(CP) when 43124 =< CP, CP =< 43127 -> 'DISALLOWED';
lookup(CP) when 43128 =< CP, CP =< 43135 -> 'UNASSIGNED';
lookup(CP) when 43136 =< CP, CP =< 43205 -> 'PVALID';
lookup(CP) when 43206 =< CP, CP =< 43213 -> 'UNASSIGNED';
lookup(CP) when 43214 =< CP, CP =< 43215 -> 'DISALLOWED';
lookup(CP) when 43216 =< CP, CP =< 43225 -> 'PVALID';
lookup(CP) when 43226 =< CP, CP =< 43231 -> 'UNASSIGNED';
lookup(CP) when 43232 =< CP, CP =< 43255 -> 'PVALID';
lookup(CP) when 43256 =< CP, CP =< 43258 -> 'DISALLOWED';
lookup(CP) when 43261 =< CP, CP =< 43309 -> 'PVALID';
lookup(CP) when 43310 =< CP, CP =< 43311 -> 'DISALLOWED';
lookup(CP) when 43312 =< CP, CP =< 43347 -> 'PVALID';
lookup(CP) when 43348 =< CP, CP =< 43358 -> 'UNASSIGNED';
lookup(CP) when 43359 =< CP, CP =< 43388 -> 'DISALLOWED';
lookup(CP) when 43389 =< CP, CP =< 43391 -> 'UNASSIGNED';
lookup(CP) when 43392 =< CP, CP =< 43456 -> 'PVALID';
lookup(CP) when 43457 =< CP, CP =< 43469 -> 'DISALLOWED';
lookup(CP) when 43471 =< CP, CP =< 43481 -> 'PVALID';
lookup(CP) when 43482 =< CP, CP =< 43485 -> 'UNASSIGNED';
lookup(CP) when 43486 =< CP, CP =< 43487 -> 'DISALLOWED';
lookup(CP) when 43488 =< CP, CP =< 43518 -> 'PVALID';
lookup(CP) when 43520 =< CP, CP =< 43574 -> 'PVALID';
lookup(CP) when 43575 =< CP, CP =< 43583 -> 'UNASSIGNED';
lookup(CP) when 43584 =< CP, CP =< 43597 -> 'PVALID';
lookup(CP) when 43598 =< CP, CP =< 43599 -> 'UNASSIGNED';
lookup(CP) when 43600 =< CP, CP =< 43609 -> 'PVALID';
lookup(CP) when 43610 =< CP, CP =< 43611 -> 'UNASSIGNED';
lookup(CP) when 43612 =< CP, CP =< 43615 -> 'DISALLOWED';
lookup(CP) when 43616 =< CP, CP =< 43638 -> 'PVALID';
lookup(CP) when 43639 =< CP, CP =< 43641 -> 'DISALLOWED';
lookup(CP) when 43642 =< CP, CP =< 43714 -> 'PVALID';
lookup(CP) when 43715 =< CP, CP =< 43738 -> 'UNASSIGNED';
lookup(CP) when 43739 =< CP, CP =< 43741 -> 'PVALID';
lookup(CP) when 43742 =< CP, CP =< 43743 -> 'DISALLOWED';
lookup(CP) when 43744 =< CP, CP =< 43759 -> 'PVALID';
lookup(CP) when 43760 =< CP, CP =< 43761 -> 'DISALLOWED';
lookup(CP) when 43762 =< CP, CP =< 43766 -> 'PVALID';
lookup(CP) when 43767 =< CP, CP =< 43776 -> 'UNASSIGNED';
lookup(CP) when 43777 =< CP, CP =< 43782 -> 'PVALID';
lookup(CP) when 43783 =< CP, CP =< 43784 -> 'UNASSIGNED';
lookup(CP) when 43785 =< CP, CP =< 43790 -> 'PVALID';
lookup(CP) when 43791 =< CP, CP =< 43792 -> 'UNASSIGNED';
lookup(CP) when 43793 =< CP, CP =< 43798 -> 'PVALID';
lookup(CP) when 43799 =< CP, CP =< 43807 -> 'UNASSIGNED';
lookup(CP) when 43808 =< CP, CP =< 43814 -> 'PVALID';
lookup(CP) when 43816 =< CP, CP =< 43822 -> 'PVALID';
lookup(CP) when 43824 =< CP, CP =< 43866 -> 'PVALID';
lookup(CP) when 43867 =< CP, CP =< 43871 -> 'DISALLOWED';
lookup(CP) when 43872 =< CP, CP =< 43880 -> 'PVALID';
lookup(CP) when 43881 =< CP, CP =< 43883 -> 'DISALLOWED';
lookup(CP) when 43884 =< CP, CP =< 43887 -> 'UNASSIGNED';
lookup(CP) when 43888 =< CP, CP =< 43967 -> 'DISALLOWED';
lookup(CP) when 43968 =< CP, CP =< 44010 -> 'PVALID';
lookup(CP) when 44012 =< CP, CP =< 44013 -> 'PVALID';
lookup(CP) when 44014 =< CP, CP =< 44015 -> 'UNASSIGNED';
lookup(CP) when 44016 =< CP, CP =< 44025 -> 'PVALID';
lookup(CP) when 44026 =< CP, CP =< 44031 -> 'UNASSIGNED';
lookup(CP) when 44032 =< CP, CP =< 55203 -> 'PVALID';
lookup(CP) when 55204 =< CP, CP =< 55215 -> 'UNASSIGNED';
lookup(CP) when 55216 =< CP, CP =< 55238 -> 'DISALLOWED';
lookup(CP) when 55239 =< CP, CP =< 55242 -> 'UNASSIGNED';
lookup(CP) when 55243 =< CP, CP =< 55291 -> 'DISALLOWED';
lookup(CP) when 55292 =< CP, CP =< 55295 -> 'UNASSIGNED';
lookup(CP) when 55296 =< CP, CP =< 64013 -> 'DISALLOWED';
lookup(CP) when 64014 =< CP, CP =< 64015 -> 'PVALID';
lookup(CP) when 64019 =< CP, CP =< 64020 -> 'PVALID';
lookup(CP) when 64021 =< CP, CP =< 64030 -> 'DISALLOWED';
lookup(CP) when 64035 =< CP, CP =< 64036 -> 'PVALID';
lookup(CP) when 64037 =< CP, CP =< 64038 -> 'DISALLOWED';
lookup(CP) when 64039 =< CP, CP =< 64041 -> 'PVALID';
lookup(CP) when 64042 =< CP, CP =< 64109 -> 'DISALLOWED';
lookup(CP) when 64110 =< CP, CP =< 64111 -> 'UNASSIGNED';
lookup(CP) when 64112 =< CP, CP =< 64217 -> 'DISALLOWED';
lookup(CP) when 64218 =< CP, CP =< 64255 -> 'UNASSIGNED';
lookup(CP) when 64256 =< CP, CP =< 64262 -> 'DISALLOWED';
lookup(CP) when 64263 =< CP, CP =< 64274 -> 'UNASSIGNED';
lookup(CP) when 64275 =< CP, CP =< 64279 -> 'DISALLOWED';
lookup(CP) when 64280 =< CP, CP =< 64284 -> 'UNASSIGNED';
lookup(CP) when 64287 =< CP, CP =< 64310 -> 'DISALLOWED';
lookup(CP) when 64312 =< CP, CP =< 64316 -> 'DISALLOWED';
lookup(CP) when 64320 =< CP, CP =< 64321 -> 'DISALLOWED';
lookup(CP) when 64323 =< CP, CP =< 64324 -> 'DISALLOWED';
lookup(CP) when 64326 =< CP, CP =< 64449 -> 'DISALLOWED';
lookup(CP) when 64450 =< CP, CP =< 64466 -> 'UNASSIGNED';
lookup(CP) when 64467 =< CP, CP =< 64831 -> 'DISALLOWED';
lookup(CP) when 64832 =< CP, CP =< 64847 -> 'UNASSIGNED';
lookup(CP) when 64848 =< CP, CP =< 64911 -> 'DISALLOWED';
lookup(CP) when 64912 =< CP, CP =< 64913 -> 'UNASSIGNED';
lookup(CP) when 64914 =< CP, CP =< 64967 -> 'DISALLOWED';
lookup(CP) when 64968 =< CP, CP =< 64975 -> 'UNASSIGNED';
lookup(CP) when 64976 =< CP, CP =< 65021 -> 'DISALLOWED';
lookup(CP) when 65022 =< CP, CP =< 65023 -> 'UNASSIGNED';
lookup(CP) when 65024 =< CP, CP =< 65049 -> 'DISALLOWED';
lookup(CP) when 65050 =< CP, CP =< 65055 -> 'UNASSIGNED';
lookup(CP) when 65056 =< CP, CP =< 65071 -> 'PVALID';
lookup(CP) when 65072 =< CP, CP =< 65106 -> 'DISALLOWED';
lookup(CP) when 65108 =< CP, CP =< 65126 -> 'DISALLOWED';
lookup(CP) when 65128 =< CP, CP =< 65131 -> 'DISALLOWED';
lookup(CP) when 65132 =< CP, CP =< 65135 -> 'UNASSIGNED';
lookup(CP) when 65136 =< CP, CP =< 65138 -> 'DISALLOWED';
lookup(CP) when 65142 =< CP, CP =< 65276 -> 'DISALLOWED';
lookup(CP) when 65277 =< CP, CP =< 65278 -> 'UNASSIGNED';
lookup(CP) when 65281 =< CP, CP =< 65470 -> 'DISALLOWED';
lookup(CP) when 65471 =< CP, CP =< 65473 -> 'UNASSIGNED';
lookup(CP) when 65474 =< CP, CP =< 65479 -> 'DISALLOWED';
lookup(CP) when 65480 =< CP, CP =< 65481 -> 'UNASSIGNED';
lookup(CP) when 65482 =< CP, CP =< 65487 -> 'DISALLOWED';
lookup(CP) when 65488 =< CP, CP =< 65489 -> 'UNASSIGNED';
lookup(CP) when 65490 =< CP, CP =< 65495 -> 'DISALLOWED';
lookup(CP) when 65496 =< CP, CP =< 65497 -> 'UNASSIGNED';
lookup(CP) when 65498 =< CP, CP =< 65500 -> 'DISALLOWED';
lookup(CP) when 65501 =< CP, CP =< 65503 -> 'UNASSIGNED';
lookup(CP) when 65504 =< CP, CP =< 65510 -> 'DISALLOWED';
lookup(CP) when 65512 =< CP, CP =< 65518 -> 'DISALLOWED';
lookup(CP) when 65519 =< CP, CP =< 65528 -> 'UNASSIGNED';
lookup(CP) when 65529 =< CP, CP =< 65535 -> 'DISALLOWED';
lookup(CP) when 65536 =< CP, CP =< 65547 -> 'PVALID';
lookup(CP) when 65549 =< CP, CP =< 65574 -> 'PVALID';
lookup(CP) when 65576 =< CP, CP =< 65594 -> 'PVALID';
lookup(CP) when 65596 =< CP, CP =< 65597 -> 'PVALID';
lookup(CP) when 65599 =< CP, CP =< 65613 -> 'PVALID';
lookup(CP) when 65614 =< CP, CP =< 65615 -> 'UNASSIGNED';
lookup(CP) when 65616 =< CP, CP =< 65629 -> 'PVALID';
lookup(CP) when 65630 =< CP, CP =< 65663 -> 'UNASSIGNED';
lookup(CP) when 65664 =< CP, CP =< 65786 -> 'PVALID';
lookup(CP) when 65787 =< CP, CP =< 65791 -> 'UNASSIGNED';
lookup(CP) when 65792 =< CP, CP =< 65794 -> 'DISALLOWED';
lookup(CP) when 65795 =< CP, CP =< 65798 -> 'UNASSIGNED';
lookup(CP) when 65799 =< CP, CP =< 65843 -> 'DISALLOWED';
lookup(CP) when 65844 =< CP, CP =< 65846 -> 'UNASSIGNED';
lookup(CP) when 65847 =< CP, CP =< 65934 -> 'DISALLOWED';
lookup(CP) when 65936 =< CP, CP =< 65948 -> 'DISALLOWED';
lookup(CP) when 65949 =< CP, CP =< 65951 -> 'UNASSIGNED';
lookup(CP) when 65953 =< CP, CP =< 65999 -> 'UNASSIGNED';
lookup(CP) when 66000 =< CP, CP =< 66044 -> 'DISALLOWED';
lookup(CP) when 66046 =< CP, CP =< 66175 -> 'UNASSIGNED';
lookup(CP) when 66176 =< CP, CP =< 66204 -> 'PVALID';
lookup(CP) when 66205 =< CP, CP =< 66207 -> 'UNASSIGNED';
lookup(CP) when 66208 =< CP, CP =< 66256 -> 'PVALID';
lookup(CP) when 66257 =< CP, CP =< 66271 -> 'UNASSIGNED';
lookup(CP) when 66273 =< CP, CP =< 66299 -> 'DISALLOWED';
lookup(CP) when 66300 =< CP, CP =< 66303 -> 'UNASSIGNED';
lookup(CP) when 66304 =< CP, CP =< 66335 -> 'PVALID';
lookup(CP) when 66336 =< CP, CP =< 66339 -> 'DISALLOWED';
lookup(CP) when 66340 =< CP, CP =< 66348 -> 'UNASSIGNED';
lookup(CP) when 66349 =< CP, CP =< 66368 -> 'PVALID';
lookup(CP) when 66370 =< CP, CP =< 66377 -> 'PVALID';
lookup(CP) when 66379 =< CP, CP =< 66383 -> 'UNASSIGNED';
lookup(CP) when 66384 =< CP, CP =< 66426 -> 'PVALID';
lookup(CP) when 66427 =< CP, CP =< 66431 -> 'UNASSIGNED';
lookup(CP) when 66432 =< CP, CP =< 66461 -> 'PVALID';
lookup(CP) when 66464 =< CP, CP =< 66499 -> 'PVALID';
lookup(CP) when 66500 =< CP, CP =< 66503 -> 'UNASSIGNED';
lookup(CP) when 66504 =< CP, CP =< 66511 -> 'PVALID';
lookup(CP) when 66512 =< CP, CP =< 66517 -> 'DISALLOWED';
lookup(CP) when 66518 =< CP, CP =< 66559 -> 'UNASSIGNED';
lookup(CP) when 66560 =< CP, CP =< 66599 -> 'DISALLOWED';
lookup(CP) when 66600 =< CP, CP =< 66717 -> 'PVALID';
lookup(CP) when 66718 =< CP, CP =< 66719 -> 'UNASSIGNED';
lookup(CP) when 66720 =< CP, CP =< 66729 -> 'PVALID';
lookup(CP) when 66730 =< CP, CP =< 66735 -> 'UNASSIGNED';
lookup(CP) when 66736 =< CP, CP =< 66771 -> 'DISALLOWED';
lookup(CP) when 66772 =< CP, CP =< 66775 -> 'UNASSIGNED';
lookup(CP) when 66776 =< CP, CP =< 66811 -> 'PVALID';
lookup(CP) when 66812 =< CP, CP =< 66815 -> 'UNASSIGNED';
lookup(CP) when 66816 =< CP, CP =< 66855 -> 'PVALID';
lookup(CP) when 66856 =< CP, CP =< 66863 -> 'UNASSIGNED';
lookup(CP) when 66864 =< CP, CP =< 66915 -> 'PVALID';
lookup(CP) when 66916 =< CP, CP =< 66926 -> 'UNASSIGNED';
lookup(CP) when 66928 =< CP, CP =< 67071 -> 'UNASSIGNED';
lookup(CP) when 67072 =< CP, CP =< 67382 -> 'PVALID';
lookup(CP) when 67383 =< CP, CP =< 67391 -> 'UNASSIGNED';
lookup(CP) when 67392 =< CP, CP =< 67413 -> 'PVALID';
lookup(CP) when 67414 =< CP, CP =< 67423 -> 'UNASSIGNED';
lookup(CP) when 67424 =< CP, CP =< 67431 -> 'PVALID';
lookup(CP) when 67432 =< CP, CP =< 67583 -> 'UNASSIGNED';
lookup(CP) when 67584 =< CP, CP =< 67589 -> 'PVALID';
lookup(CP) when 67590 =< CP, CP =< 67591 -> 'UNASSIGNED';
lookup(CP) when 67594 =< CP, CP =< 67637 -> 'PVALID';
lookup(CP) when 67639 =< CP, CP =< 67640 -> 'PVALID';
lookup(CP) when 67641 =< CP, CP =< 67643 -> 'UNASSIGNED';
lookup(CP) when 67645 =< CP, CP =< 67646 -> 'UNASSIGNED';
lookup(CP) when 67647 =< CP, CP =< 67669 -> 'PVALID';
lookup(CP) when 67671 =< CP, CP =< 67679 -> 'DISALLOWED';
lookup(CP) when 67680 =< CP, CP =< 67702 -> 'PVALID';
lookup(CP) when 67703 =< CP, CP =< 67711 -> 'DISALLOWED';
lookup(CP) when 67712 =< CP, CP =< 67742 -> 'PVALID';
lookup(CP) when 67743 =< CP, CP =< 67750 -> 'UNASSIGNED';
lookup(CP) when 67751 =< CP, CP =< 67759 -> 'DISALLOWED';
lookup(CP) when 67760 =< CP, CP =< 67807 -> 'UNASSIGNED';
lookup(CP) when 67808 =< CP, CP =< 67826 -> 'PVALID';
lookup(CP) when 67828 =< CP, CP =< 67829 -> 'PVALID';
lookup(CP) when 67830 =< CP, CP =< 67834 -> 'UNASSIGNED';
lookup(CP) when 67835 =< CP, CP =< 67839 -> 'DISALLOWED';
lookup(CP) when 67840 =< CP, CP =< 67861 -> 'PVALID';
lookup(CP) when 67862 =< CP, CP =< 67867 -> 'DISALLOWED';
lookup(CP) when 67868 =< CP, CP =< 67870 -> 'UNASSIGNED';
lookup(CP) when 67872 =< CP, CP =< 67897 -> 'PVALID';
lookup(CP) when 67898 =< CP, CP =< 67902 -> 'UNASSIGNED';
lookup(CP) when 67904 =< CP, CP =< 67967 -> 'UNASSIGNED';
lookup(CP) when 67968 =< CP, CP =< 68023 -> 'PVALID';
lookup(CP) when 68024 =< CP, CP =< 68027 -> 'UNASSIGNED';
lookup(CP) when 68028 =< CP, CP =< 68029 -> 'DISALLOWED';
lookup(CP) when 68030 =< CP, CP =< 68031 -> 'PVALID';
lookup(CP) when 68032 =< CP, CP =< 68047 -> 'DISALLOWED';
lookup(CP) when 68048 =< CP, CP =< 68049 -> 'UNASSIGNED';
lookup(CP) when 68050 =< CP, CP =< 68095 -> 'DISALLOWED';
lookup(CP) when 68096 =< CP, CP =< 68099 -> 'PVALID';
lookup(CP) when 68101 =< CP, CP =< 68102 -> 'PVALID';
lookup(CP) when 68103 =< CP, CP =< 68107 -> 'UNASSIGNED';
lookup(CP) when 68108 =< CP, CP =< 68115 -> 'PVALID';
lookup(CP) when 68117 =< CP, CP =< 68119 -> 'PVALID';
lookup(CP) when 68121 =< CP, CP =< 68149 -> 'PVALID';
lookup(CP) when 68150 =< CP, CP =< 68151 -> 'UNASSIGNED';
lookup(CP) when 68152 =< CP, CP =< 68154 -> 'PVALID';
lookup(CP) when 68155 =< CP, CP =< 68158 -> 'UNASSIGNED';
lookup(CP) when 68160 =< CP, CP =< 68168 -> 'DISALLOWED';
lookup(CP) when 68169 =< CP, CP =< 68175 -> 'UNASSIGNED';
lookup(CP) when 68176 =< CP, CP =< 68184 -> 'DISALLOWED';
lookup(CP) when 68185 =< CP, CP =< 68191 -> 'UNASSIGNED';
lookup(CP) when 68192 =< CP, CP =< 68220 -> 'PVALID';
lookup(CP) when 68221 =< CP, CP =< 68223 -> 'DISALLOWED';
lookup(CP) when 68224 =< CP, CP =< 68252 -> 'PVALID';
lookup(CP) when 68253 =< CP, CP =< 68255 -> 'DISALLOWED';
lookup(CP) when 68256 =< CP, CP =< 68287 -> 'UNASSIGNED';
lookup(CP) when 68288 =< CP, CP =< 68295 -> 'PVALID';
lookup(CP) when 68297 =< CP, CP =< 68326 -> 'PVALID';
lookup(CP) when 68327 =< CP, CP =< 68330 -> 'UNASSIGNED';
lookup(CP) when 68331 =< CP, CP =< 68342 -> 'DISALLOWED';
lookup(CP) when 68343 =< CP, CP =< 68351 -> 'UNASSIGNED';
lookup(CP) when 68352 =< CP, CP =< 68405 -> 'PVALID';
lookup(CP) when 68406 =< CP, CP =< 68408 -> 'UNASSIGNED';
lookup(CP) when 68409 =< CP, CP =< 68415 -> 'DISALLOWED';
lookup(CP) when 68416 =< CP, CP =< 68437 -> 'PVALID';
lookup(CP) when 68438 =< CP, CP =< 68439 -> 'UNASSIGNED';
lookup(CP) when 68440 =< CP, CP =< 68447 -> 'DISALLOWED';
lookup(CP) when 68448 =< CP, CP =< 68466 -> 'PVALID';
lookup(CP) when 68467 =< CP, CP =< 68471 -> 'UNASSIGNED';
lookup(CP) when 68472 =< CP, CP =< 68479 -> 'DISALLOWED';
lookup(CP) when 68480 =< CP, CP =< 68497 -> 'PVALID';
lookup(CP) when 68498 =< CP, CP =< 68504 -> 'UNASSIGNED';
lookup(CP) when 68505 =< CP, CP =< 68508 -> 'DISALLOWED';
lookup(CP) when 68509 =< CP, CP =< 68520 -> 'UNASSIGNED';
lookup(CP) when 68521 =< CP, CP =< 68527 -> 'DISALLOWED';
lookup(CP) when 68528 =< CP, CP =< 68607 -> 'UNASSIGNED';
lookup(CP) when 68608 =< CP, CP =< 68680 -> 'PVALID';
lookup(CP) when 68681 =< CP, CP =< 68735 -> 'UNASSIGNED';
lookup(CP) when 68736 =< CP, CP =< 68786 -> 'DISALLOWED';
lookup(CP) when 68787 =< CP, CP =< 68799 -> 'UNASSIGNED';
lookup(CP) when 68800 =< CP, CP =< 68850 -> 'PVALID';
lookup(CP) when 68851 =< CP, CP =< 68857 -> 'UNASSIGNED';
lookup(CP) when 68858 =< CP, CP =< 68863 -> 'DISALLOWED';
lookup(CP) when 68864 =< CP, CP =< 68903 -> 'PVALID';
lookup(CP) when 68904 =< CP, CP =< 68911 -> 'UNASSIGNED';
lookup(CP) when 68912 =< CP, CP =< 68921 -> 'PVALID';
lookup(CP) when 68922 =< CP, CP =< 69215 -> 'UNASSIGNED';
lookup(CP) when 69216 =< CP, CP =< 69246 -> 'DISALLOWED';
lookup(CP) when 69248 =< CP, CP =< 69289 -> 'PVALID';
lookup(CP) when 69291 =< CP, CP =< 69292 -> 'PVALID';
lookup(CP) when 69294 =< CP, CP =< 69295 -> 'UNASSIGNED';
lookup(CP) when 69296 =< CP, CP =< 69297 -> 'PVALID';
lookup(CP) when 69298 =< CP, CP =< 69375 -> 'UNASSIGNED';
lookup(CP) when 69376 =< CP, CP =< 69404 -> 'PVALID';
lookup(CP) when 69405 =< CP, CP =< 69414 -> 'DISALLOWED';
lookup(CP) when 69416 =< CP, CP =< 69423 -> 'UNASSIGNED';
lookup(CP) when 69424 =< CP, CP =< 69456 -> 'PVALID';
lookup(CP) when 69457 =< CP, CP =< 69465 -> 'DISALLOWED';
lookup(CP) when 69466 =< CP, CP =< 69551 -> 'UNASSIGNED';
lookup(CP) when 69552 =< CP, CP =< 69572 -> 'PVALID';
lookup(CP) when 69573 =< CP, CP =< 69579 -> 'DISALLOWED';
lookup(CP) when 69580 =< CP, CP =< 69599 -> 'UNASSIGNED';
lookup(CP) when 69600 =< CP, CP =< 69622 -> 'PVALID';
lookup(CP) when 69623 =< CP, CP =< 69631 -> 'UNASSIGNED';
lookup(CP) when 69632 =< CP, CP =< 69702 -> 'PVALID';
lookup(CP) when 69703 =< CP, CP =< 69709 -> 'DISALLOWED';
lookup(CP) when 69710 =< CP, CP =< 69713 -> 'UNASSIGNED';
lookup(CP) when 69714 =< CP, CP =< 69733 -> 'DISALLOWED';
lookup(CP) when 69734 =< CP, CP =< 69743 -> 'PVALID';
lookup(CP) when 69744 =< CP, CP =< 69758 -> 'UNASSIGNED';
lookup(CP) when 69759 =< CP, CP =< 69818 -> 'PVALID';
lookup(CP) when 69819 =< CP, CP =< 69825 -> 'DISALLOWED';
lookup(CP) when 69826 =< CP, CP =< 69836 -> 'UNASSIGNED';
lookup(CP) when 69838 =< CP, CP =< 69839 -> 'UNASSIGNED';
lookup(CP) when 69840 =< CP, CP =< 69864 -> 'PVALID';
lookup(CP) when 69865 =< CP, CP =< 69871 -> 'UNASSIGNED';
lookup(CP) when 69872 =< CP, CP =< 69881 -> 'PVALID';
lookup(CP) when 69882 =< CP, CP =< 69887 -> 'UNASSIGNED';
lookup(CP) when 69888 =< CP, CP =< 69940 -> 'PVALID';
lookup(CP) when 69942 =< CP, CP =< 69951 -> 'PVALID';
lookup(CP) when 69952 =< CP, CP =< 69955 -> 'DISALLOWED';
lookup(CP) when 69956 =< CP, CP =< 69959 -> 'PVALID';
lookup(CP) when 69960 =< CP, CP =< 69967 -> 'UNASSIGNED';
lookup(CP) when 69968 =< CP, CP =< 70003 -> 'PVALID';
lookup(CP) when 70004 =< CP, CP =< 70005 -> 'DISALLOWED';
lookup(CP) when 70007 =< CP, CP =< 70015 -> 'UNASSIGNED';
lookup(CP) when 70016 =< CP, CP =< 70084 -> 'PVALID';
lookup(CP) when 70085 =< CP, CP =< 70088 -> 'DISALLOWED';
lookup(CP) when 70089 =< CP, CP =< 70092 -> 'PVALID';
lookup(CP) when 70094 =< CP, CP =< 70106 -> 'PVALID';
lookup(CP) when 70109 =< CP, CP =< 70111 -> 'DISALLOWED';
lookup(CP) when 70113 =< CP, CP =< 70132 -> 'DISALLOWED';
lookup(CP) when 70133 =< CP, CP =< 70143 -> 'UNASSIGNED';
lookup(CP) when 70144 =< CP, CP =< 70161 -> 'PVALID';
lookup(CP) when 70163 =< CP, CP =< 70199 -> 'PVALID';
lookup(CP) when 70200 =< CP, CP =< 70205 -> 'DISALLOWED';
lookup(CP) when 70207 =< CP, CP =< 70271 -> 'UNASSIGNED';
lookup(CP) when 70272 =< CP, CP =< 70278 -> 'PVALID';
lookup(CP) when 70282 =< CP, CP =< 70285 -> 'PVALID';
lookup(CP) when 70287 =< CP, CP =< 70301 -> 'PVALID';
lookup(CP) when 70303 =< CP, CP =< 70312 -> 'PVALID';
lookup(CP) when 70314 =< CP, CP =< 70319 -> 'UNASSIGNED';
lookup(CP) when 70320 =< CP, CP =< 70378 -> 'PVALID';
lookup(CP) when 70379 =< CP, CP =< 70383 -> 'UNASSIGNED';
lookup(CP) when 70384 =< CP, CP =< 70393 -> 'PVALID';
lookup(CP) when 70394 =< CP, CP =< 70399 -> 'UNASSIGNED';
lookup(CP) when 70400 =< CP, CP =< 70403 -> 'PVALID';
lookup(CP) when 70405 =< CP, CP =< 70412 -> 'PVALID';
lookup(CP) when 70413 =< CP, CP =< 70414 -> 'UNASSIGNED';
lookup(CP) when 70415 =< CP, CP =< 70416 -> 'PVALID';
lookup(CP) when 70417 =< CP, CP =< 70418 -> 'UNASSIGNED';
lookup(CP) when 70419 =< CP, CP =< 70440 -> 'PVALID';
lookup(CP) when 70442 =< CP, CP =< 70448 -> 'PVALID';
lookup(CP) when 70450 =< CP, CP =< 70451 -> 'PVALID';
lookup(CP) when 70453 =< CP, CP =< 70457 -> 'PVALID';
lookup(CP) when 70459 =< CP, CP =< 70468 -> 'PVALID';
lookup(CP) when 70469 =< CP, CP =< 70470 -> 'UNASSIGNED';
lookup(CP) when 70471 =< CP, CP =< 70472 -> 'PVALID';
lookup(CP) when 70473 =< CP, CP =< 70474 -> 'UNASSIGNED';
lookup(CP) when 70475 =< CP, CP =< 70477 -> 'PVALID';
lookup(CP) when 70478 =< CP, CP =< 70479 -> 'UNASSIGNED';
lookup(CP) when 70481 =< CP, CP =< 70486 -> 'UNASSIGNED';
lookup(CP) when 70488 =< CP, CP =< 70492 -> 'UNASSIGNED';
lookup(CP) when 70493 =< CP, CP =< 70499 -> 'PVALID';
lookup(CP) when 70500 =< CP, CP =< 70501 -> 'UNASSIGNED';
lookup(CP) when 70502 =< CP, CP =< 70508 -> 'PVALID';
lookup(CP) when 70509 =< CP, CP =< 70511 -> 'UNASSIGNED';
lookup(CP) when 70512 =< CP, CP =< 70516 -> 'PVALID';
lookup(CP) when 70517 =< CP, CP =< 70655 -> 'UNASSIGNED';
lookup(CP) when 70656 =< CP, CP =< 70730 -> 'PVALID';
lookup(CP) when 70731 =< CP, CP =< 70735 -> 'DISALLOWED';
lookup(CP) when 70736 =< CP, CP =< 70745 -> 'PVALID';
lookup(CP) when 70746 =< CP, CP =< 70747 -> 'DISALLOWED';
lookup(CP) when 70750 =< CP, CP =< 70753 -> 'PVALID';
lookup(CP) when 70754 =< CP, CP =< 70783 -> 'UNASSIGNED';
lookup(CP) when 70784 =< CP, CP =< 70853 -> 'PVALID';
lookup(CP) when 70856 =< CP, CP =< 70863 -> 'UNASSIGNED';
lookup(CP) when 70864 =< CP, CP =< 70873 -> 'PVALID';
lookup(CP) when 70874 =< CP, CP =< 71039 -> 'UNASSIGNED';
lookup(CP) when 71040 =< CP, CP =< 71093 -> 'PVALID';
lookup(CP) when 71094 =< CP, CP =< 71095 -> 'UNASSIGNED';
lookup(CP) when 71096 =< CP, CP =< 71104 -> 'PVALID';
lookup(CP) when 71105 =< CP, CP =< 71127 -> 'DISALLOWED';
lookup(CP) when 71128 =< CP, CP =< 71133 -> 'PVALID';
lookup(CP) when 71134 =< CP, CP =< 71167 -> 'UNASSIGNED';
lookup(CP) when 71168 =< CP, CP =< 71232 -> 'PVALID';
lookup(CP) when 71233 =< CP, CP =< 71235 -> 'DISALLOWED';
lookup(CP) when 71237 =< CP, CP =< 71247 -> 'UNASSIGNED';
lookup(CP) when 71248 =< CP, CP =< 71257 -> 'PVALID';
lookup(CP) when 71258 =< CP, CP =< 71263 -> 'UNASSIGNED';
lookup(CP) when 71264 =< CP, CP =< 71276 -> 'DISALLOWED';
lookup(CP) when 71277 =< CP, CP =< 71295 -> 'UNASSIGNED';
lookup(CP) when 71296 =< CP, CP =< 71352 -> 'PVALID';
lookup(CP) when 71353 =< CP, CP =< 71359 -> 'UNASSIGNED';
lookup(CP) when 71360 =< CP, CP =< 71369 -> 'PVALID';
lookup(CP) when 71370 =< CP, CP =< 71423 -> 'UNASSIGNED';
lookup(CP) when 71424 =< CP, CP =< 71450 -> 'PVALID';
lookup(CP) when 71451 =< CP, CP =< 71452 -> 'UNASSIGNED';
lookup(CP) when 71453 =< CP, CP =< 71467 -> 'PVALID';
lookup(CP) when 71468 =< CP, CP =< 71471 -> 'UNASSIGNED';
lookup(CP) when 71472 =< CP, CP =< 71481 -> 'PVALID';
lookup(CP) when 71482 =< CP, CP =< 71487 -> 'DISALLOWED';
lookup(CP) when 71488 =< CP, CP =< 71679 -> 'UNASSIGNED';
lookup(CP) when 71680 =< CP, CP =< 71738 -> 'PVALID';
lookup(CP) when 71740 =< CP, CP =< 71839 -> 'UNASSIGNED';
lookup(CP) when 71840 =< CP, CP =< 71871 -> 'DISALLOWED';
lookup(CP) when 71872 =< CP, CP =< 71913 -> 'PVALID';
lookup(CP) when 71914 =< CP, CP =< 71922 -> 'DISALLOWED';
lookup(CP) when 71923 =< CP, CP =< 71934 -> 'UNASSIGNED';
lookup(CP) when 71935 =< CP, CP =< 71942 -> 'PVALID';
lookup(CP) when 71943 =< CP, CP =< 71944 -> 'UNASSIGNED';
lookup(CP) when 71946 =< CP, CP =< 71947 -> 'UNASSIGNED';
lookup(CP) when 71948 =< CP, CP =< 71955 -> 'PVALID';
lookup(CP) when 71957 =< CP, CP =< 71958 -> 'PVALID';
lookup(CP) when 71960 =< CP, CP =< 71989 -> 'PVALID';
lookup(CP) when 71991 =< CP, CP =< 71992 -> 'PVALID';
lookup(CP) when 71993 =< CP, CP =< 71994 -> 'UNASSIGNED';
lookup(CP) when 71995 =< CP, CP =< 72003 -> 'PVALID';
lookup(CP) when 72004 =< CP, CP =< 72006 -> 'DISALLOWED';
lookup(CP) when 72007 =< CP, CP =< 72015 -> 'UNASSIGNED';
lookup(CP) when 72016 =< CP, CP =< 72025 -> 'PVALID';
lookup(CP) when 72026 =< CP, CP =< 72095 -> 'UNASSIGNED';
lookup(CP) when 72096 =< CP, CP =< 72103 -> 'PVALID';
lookup(CP) when 72104 =< CP, CP =< 72105 -> 'UNASSIGNED';
lookup(CP) when 72106 =< CP, CP =< 72151 -> 'PVALID';
lookup(CP) when 72152 =< CP, CP =< 72153 -> 'UNASSIGNED';
lookup(CP) when 72154 =< CP, CP =< 72161 -> 'PVALID';
lookup(CP) when 72163 =< CP, CP =< 72164 -> 'PVALID';
lookup(CP) when 72165 =< CP, CP =< 72191 -> 'UNASSIGNED';
lookup(CP) when 72192 =< CP, CP =< 72254 -> 'PVALID';
lookup(CP) when 72255 =< CP, CP =< 72262 -> 'DISALLOWED';
lookup(CP) when 72264 =< CP, CP =< 72271 -> 'UNASSIGNED';
lookup(CP) when 72272 =< CP, CP =< 72345 -> 'PVALID';
lookup(CP) when 72346 =< CP, CP =< 72348 -> 'DISALLOWED';
lookup(CP) when 72350 =< CP, CP =< 72354 -> 'DISALLOWED';
lookup(CP) when 72355 =< CP, CP =< 72383 -> 'UNASSIGNED';
lookup(CP) when 72384 =< CP, CP =< 72440 -> 'PVALID';
lookup(CP) when 72441 =< CP, CP =< 72703 -> 'UNASSIGNED';
lookup(CP) when 72704 =< CP, CP =< 72712 -> 'PVALID';
lookup(CP) when 72714 =< CP, CP =< 72758 -> 'PVALID';
lookup(CP) when 72760 =< CP, CP =< 72768 -> 'PVALID';
lookup(CP) when 72769 =< CP, CP =< 72773 -> 'DISALLOWED';
lookup(CP) when 72774 =< CP, CP =< 72783 -> 'UNASSIGNED';
lookup(CP) when 72784 =< CP, CP =< 72793 -> 'PVALID';
lookup(CP) when 72794 =< CP, CP =< 72812 -> 'DISALLOWED';
lookup(CP) when 72813 =< CP, CP =< 72815 -> 'UNASSIGNED';
lookup(CP) when 72816 =< CP, CP =< 72817 -> 'DISALLOWED';
lookup(CP) when 72818 =< CP, CP =< 72847 -> 'PVALID';
lookup(CP) when 72848 =< CP, CP =< 72849 -> 'UNASSIGNED';
lookup(CP) when 72850 =< CP, CP =< 72871 -> 'PVALID';
lookup(CP) when 72873 =< CP, CP =< 72886 -> 'PVALID';
lookup(CP) when 72887 =< CP, CP =< 72959 -> 'UNASSIGNED';
lookup(CP) when 72960 =< CP, CP =< 72966 -> 'PVALID';
lookup(CP) when 72968 =< CP, CP =< 72969 -> 'PVALID';
lookup(CP) when 72971 =< CP, CP =< 73014 -> 'PVALID';
lookup(CP) when 73015 =< CP, CP =< 73017 -> 'UNASSIGNED';
lookup(CP) when 73020 =< CP, CP =< 73021 -> 'PVALID';
lookup(CP) when 73023 =< CP, CP =< 73031 -> 'PVALID';
lookup(CP) when 73032 =< CP, CP =< 73039 -> 'UNASSIGNED';
lookup(CP) when 73040 =< CP, CP =< 73049 -> 'PVALID';
lookup(CP) when 73050 =< CP, CP =< 73055 -> 'UNASSIGNED';
lookup(CP) when 73056 =< CP, CP =< 73061 -> 'PVALID';
lookup(CP) when 73063 =< CP, CP =< 73064 -> 'PVALID';
lookup(CP) when 73066 =< CP, CP =< 73102 -> 'PVALID';
lookup(CP) when 73104 =< CP, CP =< 73105 -> 'PVALID';
lookup(CP) when 73107 =< CP, CP =< 73112 -> 'PVALID';
lookup(CP) when 73113 =< CP, CP =< 73119 -> 'UNASSIGNED';
lookup(CP) when 73120 =< CP, CP =< 73129 -> 'PVALID';
lookup(CP) when 73130 =< CP, CP =< 73439 -> 'UNASSIGNED';
lookup(CP) when 73440 =< CP, CP =< 73462 -> 'PVALID';
lookup(CP) when 73463 =< CP, CP =< 73464 -> 'DISALLOWED';
lookup(CP) when 73465 =< CP, CP =< 73647 -> 'UNASSIGNED';
lookup(CP) when 73649 =< CP, CP =< 73663 -> 'UNASSIGNED';
lookup(CP) when 73664 =< CP, CP =< 73713 -> 'DISALLOWED';
lookup(CP) when 73714 =< CP, CP =< 73726 -> 'UNASSIGNED';
lookup(CP) when 73728 =< CP, CP =< 74649 -> 'PVALID';
lookup(CP) when 74650 =< CP, CP =< 74751 -> 'UNASSIGNED';
lookup(CP) when 74752 =< CP, CP =< 74862 -> 'DISALLOWED';
lookup(CP) when 74864 =< CP, CP =< 74868 -> 'DISALLOWED';
lookup(CP) when 74869 =< CP, CP =< 74879 -> 'UNASSIGNED';
lookup(CP) when 74880 =< CP, CP =< 75075 -> 'PVALID';
lookup(CP) when 75076 =< CP, CP =< 77823 -> 'UNASSIGNED';
lookup(CP) when 77824 =< CP, CP =< 78894 -> 'PVALID';
lookup(CP) when 78896 =< CP, CP =< 78904 -> 'DISALLOWED';
lookup(CP) when 78905 =< CP, CP =< 82943 -> 'UNASSIGNED';
lookup(CP) when 82944 =< CP, CP =< 83526 -> 'PVALID';
lookup(CP) when 83527 =< CP, CP =< 92159 -> 'UNASSIGNED';
lookup(CP) when 92160 =< CP, CP =< 92728 -> 'PVALID';
lookup(CP) when 92729 =< CP, CP =< 92735 -> 'UNASSIGNED';
lookup(CP) when 92736 =< CP, CP =< 92766 -> 'PVALID';
lookup(CP) when 92768 =< CP, CP =< 92777 -> 'PVALID';
lookup(CP) when 92778 =< CP, CP =< 92781 -> 'UNASSIGNED';
lookup(CP) when 92782 =< CP, CP =< 92783 -> 'DISALLOWED';
lookup(CP) when 92784 =< CP, CP =< 92879 -> 'UNASSIGNED';
lookup(CP) when 92880 =< CP, CP =< 92909 -> 'PVALID';
lookup(CP) when 92910 =< CP, CP =< 92911 -> 'UNASSIGNED';
lookup(CP) when 92912 =< CP, CP =< 92916 -> 'PVALID';
lookup(CP) when 92918 =< CP, CP =< 92927 -> 'UNASSIGNED';
lookup(CP) when 92928 =< CP, CP =< 92982 -> 'PVALID';
lookup(CP) when 92983 =< CP, CP =< 92991 -> 'DISALLOWED';
lookup(CP) when 92992 =< CP, CP =< 92995 -> 'PVALID';
lookup(CP) when 92996 =< CP, CP =< 92997 -> 'DISALLOWED';
lookup(CP) when 92998 =< CP, CP =< 93007 -> 'UNASSIGNED';
lookup(CP) when 93008 =< CP, CP =< 93017 -> 'PVALID';
lookup(CP) when 93019 =< CP, CP =< 93025 -> 'DISALLOWED';
lookup(CP) when 93027 =< CP, CP =< 93047 -> 'PVALID';
lookup(CP) when 93048 =< CP, CP =< 93052 -> 'UNASSIGNED';
lookup(CP) when 93053 =< CP, CP =< 93071 -> 'PVALID';
lookup(CP) when 93072 =< CP, CP =< 93759 -> 'UNASSIGNED';
lookup(CP) when 93760 =< CP, CP =< 93791 -> 'DISALLOWED';
lookup(CP) when 93792 =< CP, CP =< 93823 -> 'PVALID';
lookup(CP) when 93824 =< CP, CP =< 93850 -> 'DISALLOWED';
lookup(CP) when 93851 =< CP, CP =< 93951 -> 'UNASSIGNED';
lookup(CP) when 93952 =< CP, CP =< 94026 -> 'PVALID';
lookup(CP) when 94027 =< CP, CP =< 94030 -> 'UNASSIGNED';
lookup(CP) when 94031 =< CP, CP =< 94087 -> 'PVALID';
lookup(CP) when 94088 =< CP, CP =< 94094 -> 'UNASSIGNED';
lookup(CP) when 94095 =< CP, CP =< 94111 -> 'PVALID';
lookup(CP) when 94112 =< CP, CP =< 94175 -> 'UNASSIGNED';
lookup(CP) when 94176 =< CP, CP =< 94177 -> 'PVALID';
lookup(CP) when 94179 =< CP, CP =< 94180 -> 'PVALID';
lookup(CP) when 94181 =< CP, CP =< 94191 -> 'UNASSIGNED';
lookup(CP) when 94192 =< CP, CP =< 94193 -> 'PVALID';
lookup(CP) when 94194 =< CP, CP =< 94207 -> 'UNASSIGNED';
lookup(CP) when 94208 =< CP, CP =< 100343 -> 'PVALID';
lookup(CP) when 100344 =< CP, CP =< 100351 -> 'UNASSIGNED';
lookup(CP) when 100352 =< CP, CP =< 101589 -> 'PVALID';
lookup(CP) when 101590 =< CP, CP =< 101631 -> 'UNASSIGNED';
lookup(CP) when 101632 =< CP, CP =< 101640 -> 'PVALID';
lookup(CP) when 101641 =< CP, CP =< 110591 -> 'UNASSIGNED';
lookup(CP) when 110592 =< CP, CP =< 110878 -> 'PVALID';
lookup(CP) when 110879 =< CP, CP =< 110927 -> 'UNASSIGNED';
lookup(CP) when 110928 =< CP, CP =< 110930 -> 'PVALID';
lookup(CP) when 110931 =< CP, CP =< 110947 -> 'UNASSIGNED';
lookup(CP) when 110948 =< CP, CP =< 110951 -> 'PVALID';
lookup(CP) when 110952 =< CP, CP =< 110959 -> 'UNASSIGNED';
lookup(CP) when 110960 =< CP, CP =< 111355 -> 'PVALID';
lookup(CP) when 111356 =< CP, CP =< 113663 -> 'UNASSIGNED';
lookup(CP) when 113664 =< CP, CP =< 113770 -> 'PVALID';
lookup(CP) when 113771 =< CP, CP =< 113775 -> 'UNASSIGNED';
lookup(CP) when 113776 =< CP, CP =< 113788 -> 'PVALID';
lookup(CP) when 113789 =< CP, CP =< 113791 -> 'UNASSIGNED';
lookup(CP) when 113792 =< CP, CP =< 113800 -> 'PVALID';
lookup(CP) when 113801 =< CP, CP =< 113807 -> 'UNASSIGNED';
lookup(CP) when 113808 =< CP, CP =< 113817 -> 'PVALID';
lookup(CP) when 113818 =< CP, CP =< 113819 -> 'UNASSIGNED';
lookup(CP) when 113821 =< CP, CP =< 113822 -> 'PVALID';
lookup(CP) when 113823 =< CP, CP =< 113827 -> 'DISALLOWED';
lookup(CP) when 113828 =< CP, CP =< 118783 -> 'UNASSIGNED';
lookup(CP) when 118784 =< CP, CP =< 119029 -> 'DISALLOWED';
lookup(CP) when 119030 =< CP, CP =< 119039 -> 'UNASSIGNED';
lookup(CP) when 119040 =< CP, CP =< 119078 -> 'DISALLOWED';
lookup(CP) when 119079 =< CP, CP =< 119080 -> 'UNASSIGNED';
lookup(CP) when 119081 =< CP, CP =< 119272 -> 'DISALLOWED';
lookup(CP) when 119273 =< CP, CP =< 119295 -> 'UNASSIGNED';
lookup(CP) when 119296 =< CP, CP =< 119365 -> 'DISALLOWED';
lookup(CP) when 119366 =< CP, CP =< 119519 -> 'UNASSIGNED';
lookup(CP) when 119520 =< CP, CP =< 119539 -> 'DISALLOWED';
lookup(CP) when 119540 =< CP, CP =< 119551 -> 'UNASSIGNED';
lookup(CP) when 119552 =< CP, CP =< 119638 -> 'DISALLOWED';
lookup(CP) when 119639 =< CP, CP =< 119647 -> 'UNASSIGNED';
lookup(CP) when 119648 =< CP, CP =< 119672 -> 'DISALLOWED';
lookup(CP) when 119673 =< CP, CP =< 119807 -> 'UNASSIGNED';
lookup(CP) when 119808 =< CP, CP =< 119892 -> 'DISALLOWED';
lookup(CP) when 119894 =< CP, CP =< 119964 -> 'DISALLOWED';
lookup(CP) when 119966 =< CP, CP =< 119967 -> 'DISALLOWED';
lookup(CP) when 119968 =< CP, CP =< 119969 -> 'UNASSIGNED';
lookup(CP) when 119971 =< CP, CP =< 119972 -> 'UNASSIGNED';
lookup(CP) when 119973 =< CP, CP =< 119974 -> 'DISALLOWED';
lookup(CP) when 119975 =< CP, CP =< 119976 -> 'UNASSIGNED';
lookup(CP) when 119977 =< CP, CP =< 119980 -> 'DISALLOWED';
lookup(CP) when 119982 =< CP, CP =< 119993 -> 'DISALLOWED';
lookup(CP) when 119997 =< CP, CP =< 120003 -> 'DISALLOWED';
lookup(CP) when 120005 =< CP, CP =< 120069 -> 'DISALLOWED';
lookup(CP) when 120071 =< CP, CP =< 120074 -> 'DISALLOWED';
lookup(CP) when 120075 =< CP, CP =< 120076 -> 'UNASSIGNED';
lookup(CP) when 120077 =< CP, CP =< 120084 -> 'DISALLOWED';
lookup(CP) when 120086 =< CP, CP =< 120092 -> 'DISALLOWED';
lookup(CP) when 120094 =< CP, CP =< 120121 -> 'DISALLOWED';
lookup(CP) when 120123 =< CP, CP =< 120126 -> 'DISALLOWED';
lookup(CP) when 120128 =< CP, CP =< 120132 -> 'DISALLOWED';
lookup(CP) when 120135 =< CP, CP =< 120137 -> 'UNASSIGNED';
lookup(CP) when 120138 =< CP, CP =< 120144 -> 'DISALLOWED';
lookup(CP) when 120146 =< CP, CP =< 120485 -> 'DISALLOWED';
lookup(CP) when 120486 =< CP, CP =< 120487 -> 'UNASSIGNED';
lookup(CP) when 120488 =< CP, CP =< 120779 -> 'DISALLOWED';
lookup(CP) when 120780 =< CP, CP =< 120781 -> 'UNASSIGNED';
lookup(CP) when 120782 =< CP, CP =< 121343 -> 'DISALLOWED';
lookup(CP) when 121344 =< CP, CP =< 121398 -> 'PVALID';
lookup(CP) when 121399 =< CP, CP =< 121402 -> 'DISALLOWED';
lookup(CP) when 121403 =< CP, CP =< 121452 -> 'PVALID';
lookup(CP) when 121453 =< CP, CP =< 121460 -> 'DISALLOWED';
lookup(CP) when 121462 =< CP, CP =< 121475 -> 'DISALLOWED';
lookup(CP) when 121477 =< CP, CP =< 121483 -> 'DISALLOWED';
lookup(CP) when 121484 =< CP, CP =< 121498 -> 'UNASSIGNED';
lookup(CP) when 121499 =< CP, CP =< 121503 -> 'PVALID';
lookup(CP) when 121505 =< CP, CP =< 121519 -> 'PVALID';
lookup(CP) when 121520 =< CP, CP =< 122879 -> 'UNASSIGNED';
lookup(CP) when 122880 =< CP, CP =< 122886 -> 'PVALID';
lookup(CP) when 122888 =< CP, CP =< 122904 -> 'PVALID';
lookup(CP) when 122905 =< CP, CP =< 122906 -> 'UNASSIGNED';
lookup(CP) when 122907 =< CP, CP =< 122913 -> 'PVALID';
lookup(CP) when 122915 =< CP, CP =< 122916 -> 'PVALID';
lookup(CP) when 122918 =< CP, CP =< 122922 -> 'PVALID';
lookup(CP) when 122923 =< CP, CP =< 123135 -> 'UNASSIGNED';
lookup(CP) when 123136 =< CP, CP =< 123180 -> 'PVALID';
lookup(CP) when 123181 =< CP, CP =< 123183 -> 'UNASSIGNED';
lookup(CP) when 123184 =< CP, CP =< 123197 -> 'PVALID';
lookup(CP) when 123198 =< CP, CP =< 123199 -> 'UNASSIGNED';
lookup(CP) when 123200 =< CP, CP =< 123209 -> 'PVALID';
lookup(CP) when 123210 =< CP, CP =< 123213 -> 'UNASSIGNED';
lookup(CP) when 123216 =< CP, CP =< 123583 -> 'UNASSIGNED';
lookup(CP) when 123584 =< CP, CP =< 123641 -> 'PVALID';
lookup(CP) when 123642 =< CP, CP =< 123646 -> 'UNASSIGNED';
lookup(CP) when 123648 =< CP, CP =< 124927 -> 'UNASSIGNED';
lookup(CP) when 124928 =< CP, CP =< 125124 -> 'PVALID';
lookup(CP) when 125125 =< CP, CP =< 125126 -> 'UNASSIGNED';
lookup(CP) when 125127 =< CP, CP =< 125135 -> 'DISALLOWED';
lookup(CP) when 125136 =< CP, CP =< 125142 -> 'PVALID';
lookup(CP) when 125143 =< CP, CP =< 125183 -> 'UNASSIGNED';
lookup(CP) when 125184 =< CP, CP =< 125217 -> 'DISALLOWED';
lookup(CP) when 125218 =< CP, CP =< 125259 -> 'PVALID';
lookup(CP) when 125260 =< CP, CP =< 125263 -> 'UNASSIGNED';
lookup(CP) when 125264 =< CP, CP =< 125273 -> 'PVALID';
lookup(CP) when 125274 =< CP, CP =< 125277 -> 'UNASSIGNED';
lookup(CP) when 125278 =< CP, CP =< 125279 -> 'DISALLOWED';
lookup(CP) when 125280 =< CP, CP =< 126064 -> 'UNASSIGNED';
lookup(CP) when 126065 =< CP, CP =< 126132 -> 'DISALLOWED';
lookup(CP) when 126133 =< CP, CP =< 126208 -> 'UNASSIGNED';
lookup(CP) when 126209 =< CP, CP =< 126269 -> 'DISALLOWED';
lookup(CP) when 126270 =< CP, CP =< 126463 -> 'UNASSIGNED';
lookup(CP) when 126464 =< CP, CP =< 126467 -> 'DISALLOWED';
lookup(CP) when 126469 =< CP, CP =< 126495 -> 'DISALLOWED';
lookup(CP) when 126497 =< CP, CP =< 126498 -> 'DISALLOWED';
lookup(CP) when 126501 =< CP, CP =< 126502 -> 'UNASSIGNED';
lookup(CP) when 126505 =< CP, CP =< 126514 -> 'DISALLOWED';
lookup(CP) when 126516 =< CP, CP =< 126519 -> 'DISALLOWED';
lookup(CP) when 126524 =< CP, CP =< 126529 -> 'UNASSIGNED';
lookup(CP) when 126531 =< CP, CP =< 126534 -> 'UNASSIGNED';
lookup(CP) when 126541 =< CP, CP =< 126543 -> 'DISALLOWED';
lookup(CP) when 126545 =< CP, CP =< 126546 -> 'DISALLOWED';
lookup(CP) when 126549 =< CP, CP =< 126550 -> 'UNASSIGNED';
lookup(CP) when 126561 =< CP, CP =< 126562 -> 'DISALLOWED';
lookup(CP) when 126565 =< CP, CP =< 126566 -> 'UNASSIGNED';
lookup(CP) when 126567 =< CP, CP =< 126570 -> 'DISALLOWED';
lookup(CP) when 126572 =< CP, CP =< 126578 -> 'DISALLOWED';
lookup(CP) when 126580 =< CP, CP =< 126583 -> 'DISALLOWED';
lookup(CP) when 126585 =< CP, CP =< 126588 -> 'DISALLOWED';
lookup(CP) when 126592 =< CP, CP =< 126601 -> 'DISALLOWED';
lookup(CP) when 126603 =< CP, CP =< 126619 -> 'DISALLOWED';
lookup(CP) when 126620 =< CP, CP =< 126624 -> 'UNASSIGNED';
lookup(CP) when 126625 =< CP, CP =< 126627 -> 'DISALLOWED';
lookup(CP) when 126629 =< CP, CP =< 126633 -> 'DISALLOWED';
lookup(CP) when 126635 =< CP, CP =< 126651 -> 'DISALLOWED';
lookup(CP) when 126652 =< CP, CP =< 126703 -> 'UNASSIGNED';
lookup(CP) when 126704 =< CP, CP =< 126705 -> 'DISALLOWED';
lookup(CP) when 126706 =< CP, CP =< 126975 -> 'UNASSIGNED';
lookup(CP) when 126976 =< CP, CP =< 127019 -> 'DISALLOWED';
lookup(CP) when 127020 =< CP, CP =< 127023 -> 'UNASSIGNED';
lookup(CP) when 127024 =< CP, CP =< 127123 -> 'DISALLOWED';
lookup(CP) when 127124 =< CP, CP =< 127135 -> 'UNASSIGNED';
lookup(CP) when 127136 =< CP, CP =< 127150 -> 'DISALLOWED';
lookup(CP) when 127151 =< CP, CP =< 127152 -> 'UNASSIGNED';
lookup(CP) when 127153 =< CP, CP =< 127167 -> 'DISALLOWED';
lookup(CP) when 127169 =< CP, CP =< 127183 -> 'DISALLOWED';
lookup(CP) when 127185 =< CP, CP =< 127221 -> 'DISALLOWED';
lookup(CP) when 127222 =< CP, CP =< 127231 -> 'UNASSIGNED';
lookup(CP) when 127232 =< CP, CP =< 127405 -> 'DISALLOWED';
lookup(CP) when 127406 =< CP, CP =< 127461 -> 'UNASSIGNED';
lookup(CP) when 127462 =< CP, CP =< 127490 -> 'DISALLOWED';
lookup(CP) when 127491 =< CP, CP =< 127503 -> 'UNASSIGNED';
lookup(CP) when 127504 =< CP, CP =< 127547 -> 'DISALLOWED';
lookup(CP) when 127548 =< CP, CP =< 127551 -> 'UNASSIGNED';
lookup(CP) when 127552 =< CP, CP =< 127560 -> 'DISALLOWED';
lookup(CP) when 127561 =< CP, CP =< 127567 -> 'UNASSIGNED';
lookup(CP) when 127568 =< CP, CP =< 127569 -> 'DISALLOWED';
lookup(CP) when 127570 =< CP, CP =< 127583 -> 'UNASSIGNED';
lookup(CP) when 127584 =< CP, CP =< 127589 -> 'DISALLOWED';
lookup(CP) when 127590 =< CP, CP =< 127743 -> 'UNASSIGNED';
lookup(CP) when 127744 =< CP, CP =< 128727 -> 'DISALLOWED';
lookup(CP) when 128728 =< CP, CP =< 128735 -> 'UNASSIGNED';
lookup(CP) when 128736 =< CP, CP =< 128748 -> 'DISALLOWED';
lookup(CP) when 128749 =< CP, CP =< 128751 -> 'UNASSIGNED';
lookup(CP) when 128752 =< CP, CP =< 128764 -> 'DISALLOWED';
lookup(CP) when 128765 =< CP, CP =< 128767 -> 'UNASSIGNED';
lookup(CP) when 128768 =< CP, CP =< 128883 -> 'DISALLOWED';
lookup(CP) when 128884 =< CP, CP =< 128895 -> 'UNASSIGNED';
lookup(CP) when 128896 =< CP, CP =< 128984 -> 'DISALLOWED';
lookup(CP) when 128985 =< CP, CP =< 128991 -> 'UNASSIGNED';
lookup(CP) when 128992 =< CP, CP =< 129003 -> 'DISALLOWED';
lookup(CP) when 129004 =< CP, CP =< 129023 -> 'UNASSIGNED';
lookup(CP) when 129024 =< CP, CP =< 129035 -> 'DISALLOWED';
lookup(CP) when 129036 =< CP, CP =< 129039 -> 'UNASSIGNED';
lookup(CP) when 129040 =< CP, CP =< 129095 -> 'DISALLOWED';
lookup(CP) when 129096 =< CP, CP =< 129103 -> 'UNASSIGNED';
lookup(CP) when 129104 =< CP, CP =< 129113 -> 'DISALLOWED';
lookup(CP) when 129114 =< CP, CP =< 129119 -> 'UNASSIGNED';
lookup(CP) when 129120 =< CP, CP =< 129159 -> 'DISALLOWED';
lookup(CP) when 129160 =< CP, CP =< 129167 -> 'UNASSIGNED';
lookup(CP) when 129168 =< CP, CP =< 129197 -> 'DISALLOWED';
lookup(CP) when 129198 =< CP, CP =< 129199 -> 'UNASSIGNED';
lookup(CP) when 129200 =< CP, CP =< 129201 -> 'DISALLOWED';
lookup(CP) when 129202 =< CP, CP =< 129279 -> 'UNASSIGNED';
lookup(CP) when 129280 =< CP, CP =< 129400 -> 'DISALLOWED';
lookup(CP) when 129402 =< CP, CP =< 129483 -> 'DISALLOWED';
lookup(CP) when 129485 =< CP, CP =< 129619 -> 'DISALLOWED';
lookup(CP) when 129620 =< CP, CP =< 129631 -> 'UNASSIGNED';
lookup(CP) when 129632 =< CP, CP =< 129645 -> 'DISALLOWED';
lookup(CP) when 129646 =< CP, CP =< 129647 -> 'UNASSIGNED';
lookup(CP) when 129648 =< CP, CP =< 129652 -> 'DISALLOWED';
lookup(CP) when 129653 =< CP, CP =< 129655 -> 'UNASSIGNED';
lookup(CP) when 129656 =< CP, CP =< 129658 -> 'DISALLOWED';
lookup(CP) when 129659 =< CP, CP =< 129663 -> 'UNASSIGNED';
lookup(CP) when 129664 =< CP, CP =< 129670 -> 'DISALLOWED';
lookup(CP) when 129671 =< CP, CP =< 129679 -> 'UNASSIGNED';
lookup(CP) when 129680 =< CP, CP =< 129704 -> 'DISALLOWED';
lookup(CP) when 129705 =< CP, CP =< 129711 -> 'UNASSIGNED';
lookup(CP) when 129712 =< CP, CP =< 129718 -> 'DISALLOWED';
lookup(CP) when 129719 =< CP, CP =< 129727 -> 'UNASSIGNED';
lookup(CP) when 129728 =< CP, CP =< 129730 -> 'DISALLOWED';
lookup(CP) when 129731 =< CP, CP =< 129743 -> 'UNASSIGNED';
lookup(CP) when 129744 =< CP, CP =< 129750 -> 'DISALLOWED';
lookup(CP) when 129751 =< CP, CP =< 129791 -> 'UNASSIGNED';
lookup(CP) when 129792 =< CP, CP =< 129938 -> 'DISALLOWED';
lookup(CP) when 129940 =< CP, CP =< 129994 -> 'DISALLOWED';
lookup(CP) when 129995 =< CP, CP =< 130031 -> 'UNASSIGNED';
lookup(CP) when 130032 =< CP, CP =< 130041 -> 'DISALLOWED';
lookup(CP) when 130042 =< CP, CP =< 131069 -> 'UNASSIGNED';
lookup(CP) when 131070 =< CP, CP =< 131071 -> 'DISALLOWED';
lookup(CP) when 131072 =< CP, CP =< 173789 -> 'PVALID';
lookup(CP) when 173790 =< CP, CP =< 173823 -> 'UNASSIGNED';
lookup(CP) when 173824 =< CP, CP =< 177972 -> 'PVALID';
lookup(CP) when 177973 =< CP, CP =< 177983 -> 'UNASSIGNED';
lookup(CP) when 177984 =< CP, CP =< 178205 -> 'PVALID';
lookup(CP) when 178206 =< CP, CP =< 178207 -> 'UNASSIGNED';
lookup(CP) when 178208 =< CP, CP =< 183969 -> 'PVALID';
lookup(CP) when 183970 =< CP, CP =< 183983 -> 'UNASSIGNED';
lookup(CP) when 183984 =< CP, CP =< 191456 -> 'PVALID';
lookup(CP) when 191457 =< CP, CP =< 194559 -> 'UNASSIGNED';
lookup(CP) when 194560 =< CP, CP =< 195101 -> 'DISALLOWED';
lookup(CP) when 195102 =< CP, CP =< 196605 -> 'UNASSIGNED';
lookup(CP) when 196606 =< CP, CP =< 196607 -> 'DISALLOWED';
lookup(CP) when 196608 =< CP, CP =< 201546 -> 'PVALID';
lookup(CP) when 201547 =< CP, CP =< 262141 -> 'UNASSIGNED';
lookup(CP) when 262142 =< CP, CP =< 262143 -> 'DISALLOWED';
lookup(CP) when 262144 =< CP, CP =< 327677 -> 'UNASSIGNED';
lookup(CP) when 327678 =< CP, CP =< 327679 -> 'DISALLOWED';
lookup(CP) when 327680 =< CP, CP =< 393213 -> 'UNASSIGNED';
lookup(CP) when 393214 =< CP, CP =< 393215 -> 'DISALLOWED';
lookup(CP) when 393216 =< CP, CP =< 458749 -> 'UNASSIGNED';
lookup(CP) when 458750 =< CP, CP =< 458751 -> 'DISALLOWED';
lookup(CP) when 458752 =< CP, CP =< 524285 -> 'UNASSIGNED';
lookup(CP) when 524286 =< CP, CP =< 524287 -> 'DISALLOWED';
lookup(CP) when 524288 =< CP, CP =< 589821 -> 'UNASSIGNED';
lookup(CP) when 589822 =< CP, CP =< 589823 -> 'DISALLOWED';
lookup(CP) when 589824 =< CP, CP =< 655357 -> 'UNASSIGNED';
lookup(CP) when 655358 =< CP, CP =< 655359 -> 'DISALLOWED';
lookup(CP) when 655360 =< CP, CP =< 720893 -> 'UNASSIGNED';
lookup(CP) when 720894 =< CP, CP =< 720895 -> 'DISALLOWED';
lookup(CP) when 720896 =< CP, CP =< 786429 -> 'UNASSIGNED';
lookup(CP) when 786430 =< CP, CP =< 786431 -> 'DISALLOWED';
lookup(CP) when 786432 =< CP, CP =< 851965 -> 'UNASSIGNED';
lookup(CP) when 851966 =< CP, CP =< 851967 -> 'DISALLOWED';
lookup(CP) when 851968 =< CP, CP =< 917501 -> 'UNASSIGNED';
lookup(CP) when 917502 =< CP, CP =< 917503 -> 'DISALLOWED';
lookup(CP) when 917506 =< CP, CP =< 917535 -> 'UNASSIGNED';
lookup(CP) when 917536 =< CP, CP =< 917631 -> 'DISALLOWED';
lookup(CP) when 917632 =< CP, CP =< 917759 -> 'UNASSIGNED';
lookup(CP) when 917760 =< CP, CP =< 917999 -> 'DISALLOWED';
lookup(CP) when 918000 =< CP, CP =< 983037 -> 'UNASSIGNED';
lookup(CP) when 983038 =< CP, CP =< 1114111 -> 'DISALLOWED';
lookup(_) -> 'UNASSIGNED'.