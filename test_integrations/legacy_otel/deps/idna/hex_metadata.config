{<<"app">>,<<"idna">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"A pure Erlang IDNA implementation">>}.
{<<"files">>,
 [<<"CHANGELOG">>,<<"LICENSE">>,<<"NOTICE">>,<<"README.md">>,
  <<"rebar.config">>,<<"rebar.lock">>,<<"src/idna.app.src">>,
  <<"src/idna.erl">>,<<"src/idna_bidi.erl">>,<<"src/idna_context.erl">>,
  <<"src/idna_data.erl">>,<<"src/idna_logger.hrl">>,
  <<"src/idna_mapping.erl">>,<<"src/idna_table.erl">>,<<"src/idna_ucs.erl">>,
  <<"src/punycode.erl">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/benoitc/erlang-idna">>}]}.
{<<"name">>,<<"idna">>}.
{<<"requirements">>,
 [{<<"unicode_util_compat">>,
   [{<<"app">>,<<"unicode_util_compat">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>0.7.0">>}]}]}.
{<<"version">>,<<"6.1.1">>}.
