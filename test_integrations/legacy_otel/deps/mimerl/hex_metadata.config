{<<"app">>,<<"mimerl">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"Library to handle mimetypes">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"rebar.config">>,<<"rebar.lock">>,<<"src">>,
  <<"src/mimerl.app.src">>,<<"src/mimerl.erl">>,<<"src/mimerl.erl.src">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/benoitc/mimerl">>}]}.
{<<"name">>,<<"mimerl">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"1.4.0">>}.
