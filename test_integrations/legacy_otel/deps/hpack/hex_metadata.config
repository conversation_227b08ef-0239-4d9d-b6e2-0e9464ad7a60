{<<"app">>,<<"hpack">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"HPACK Implementation">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"rebar.config">>,<<"rebar.lock">>,<<"src">>,
  <<"src/hpack.app.src">>,<<"src/hpack.erl">>,<<"src/hpack_index.erl">>,
  <<"src/hpack_integer.erl">>,<<"src/hpack_string.erl">>,
  <<"src/huffman.erl">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/joedevivo/hpack">>}]}.
{<<"name">>,<<"hpack_erl">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"0.3.0">>}.
