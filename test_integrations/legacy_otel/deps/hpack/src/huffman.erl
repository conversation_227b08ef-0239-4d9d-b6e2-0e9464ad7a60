%% @private

-module(huffman).

-export([decode/1, encode/1]).

decode(Bin) -> decode(Bin, []).
decode(<<    16#1ff8:13,Bits/bits>>, Acc) -> decode(Bits, [  0|Acc]);
decode(<<  16#7fffd8:23,Bits/bits>>, Acc) -> decode(Bits, [  1|Acc]);
decode(<< 16#fffffe2:28,Bits/bits>>, Acc) -> decode(Bits, [  2|Acc]);
decode(<< 16#fffffe3:28,Bits/bits>>, Acc) -> decode(Bits, [  3|Acc]);
decode(<< 16#fffffe4:28,Bits/bits>>, Acc) -> decode(Bits, [  4|Acc]);
decode(<< 16#fffffe5:28,Bits/bits>>, Acc) -> decode(Bits, [  5|Acc]);
decode(<< 16#fffffe6:28,Bits/bits>>, Acc) -> decode(Bits, [  6|Acc]);
decode(<< 16#fffffe7:28,Bits/bits>>, Acc) -> decode(Bits, [  7|Acc]);
decode(<< 16#fffffe8:28,Bits/bits>>, Acc) -> decode(Bits, [  8|Acc]);
decode(<<  16#ffffea:24,Bits/bits>>, Acc) -> decode(Bits, [  9|Acc]);
decode(<<16#3ffffffc:30,Bits/bits>>, Acc) -> decode(Bits, [ 10|Acc]);
decode(<< 16#fffffe9:28,Bits/bits>>, Acc) -> decode(Bits, [ 11|Acc]);
decode(<< 16#fffffea:28,Bits/bits>>, Acc) -> decode(Bits, [ 12|Acc]);
decode(<<16#3ffffffd:30,Bits/bits>>, Acc) -> decode(Bits, [ 13|Acc]);
decode(<< 16#fffffeb:28,Bits/bits>>, Acc) -> decode(Bits, [ 14|Acc]);
decode(<< 16#fffffec:28,Bits/bits>>, Acc) -> decode(Bits, [ 15|Acc]);
decode(<< 16#fffffed:28,Bits/bits>>, Acc) -> decode(Bits, [ 16|Acc]);
decode(<< 16#fffffee:28,Bits/bits>>, Acc) -> decode(Bits, [ 17|Acc]);
decode(<< 16#fffffef:28,Bits/bits>>, Acc) -> decode(Bits, [ 18|Acc]);
decode(<< 16#ffffff0:28,Bits/bits>>, Acc) -> decode(Bits, [ 19|Acc]);
decode(<< 16#ffffff1:28,Bits/bits>>, Acc) -> decode(Bits, [ 20|Acc]);
decode(<< 16#ffffff2:28,Bits/bits>>, Acc) -> decode(Bits, [ 21|Acc]);
decode(<<16#3ffffffe:30,Bits/bits>>, Acc) -> decode(Bits, [ 22|Acc]);
decode(<< 16#ffffff3:28,Bits/bits>>, Acc) -> decode(Bits, [ 23|Acc]);
decode(<< 16#ffffff4:28,Bits/bits>>, Acc) -> decode(Bits, [ 24|Acc]);
decode(<< 16#ffffff5:28,Bits/bits>>, Acc) -> decode(Bits, [ 25|Acc]);
decode(<< 16#ffffff6:28,Bits/bits>>, Acc) -> decode(Bits, [ 26|Acc]);
decode(<< 16#ffffff7:28,Bits/bits>>, Acc) -> decode(Bits, [ 27|Acc]);
decode(<< 16#ffffff8:28,Bits/bits>>, Acc) -> decode(Bits, [ 28|Acc]);
decode(<< 16#ffffff9:28,Bits/bits>>, Acc) -> decode(Bits, [ 29|Acc]);
decode(<< 16#ffffffa:28,Bits/bits>>, Acc) -> decode(Bits, [ 30|Acc]);
decode(<< 16#ffffffb:28,Bits/bits>>, Acc) -> decode(Bits, [ 31|Acc]);
decode(<<       16#14:6,Bits/bits>>, Acc) -> decode(Bits, [ 32|Acc]);
decode(<<     16#3f8:10,Bits/bits>>, Acc) -> decode(Bits, [ 33|Acc]);
decode(<<     16#3f9:10,Bits/bits>>, Acc) -> decode(Bits, [ 34|Acc]);
decode(<<     16#ffa:12,Bits/bits>>, Acc) -> decode(Bits, [ 35|Acc]);
decode(<<    16#1ff9:13,Bits/bits>>, Acc) -> decode(Bits, [ 36|Acc]);
decode(<<       16#15:6,Bits/bits>>, Acc) -> decode(Bits, [ 37|Acc]);
decode(<<       16#f8:8,Bits/bits>>, Acc) -> decode(Bits, [ 38|Acc]);
decode(<<     16#7fa:11,Bits/bits>>, Acc) -> decode(Bits, [ 39|Acc]);
decode(<<     16#3fa:10,Bits/bits>>, Acc) -> decode(Bits, [ 40|Acc]);
decode(<<     16#3fb:10,Bits/bits>>, Acc) -> decode(Bits, [ 41|Acc]);
decode(<<       16#f9:8,Bits/bits>>, Acc) -> decode(Bits, [ 42|Acc]);
decode(<<     16#7fb:11,Bits/bits>>, Acc) -> decode(Bits, [ 43|Acc]);
decode(<<       16#fa:8,Bits/bits>>, Acc) -> decode(Bits, [ 44|Acc]);
decode(<<       16#16:6,Bits/bits>>, Acc) -> decode(Bits, [ 45|Acc]);
decode(<<       16#17:6,Bits/bits>>, Acc) -> decode(Bits, [ 46|Acc]);
decode(<<       16#18:6,Bits/bits>>, Acc) -> decode(Bits, [ 47|Acc]);
decode(<<        16#0:5,Bits/bits>>, Acc) -> decode(Bits, [ 48|Acc]);
decode(<<        16#1:5,Bits/bits>>, Acc) -> decode(Bits, [ 49|Acc]);
decode(<<        16#2:5,Bits/bits>>, Acc) -> decode(Bits, [ 50|Acc]);
decode(<<       16#19:6,Bits/bits>>, Acc) -> decode(Bits, [ 51|Acc]);
decode(<<       16#1a:6,Bits/bits>>, Acc) -> decode(Bits, [ 52|Acc]);
decode(<<       16#1b:6,Bits/bits>>, Acc) -> decode(Bits, [ 53|Acc]);
decode(<<       16#1c:6,Bits/bits>>, Acc) -> decode(Bits, [ 54|Acc]);
decode(<<       16#1d:6,Bits/bits>>, Acc) -> decode(Bits, [ 55|Acc]);
decode(<<       16#1e:6,Bits/bits>>, Acc) -> decode(Bits, [ 56|Acc]);
decode(<<       16#1f:6,Bits/bits>>, Acc) -> decode(Bits, [ 57|Acc]);
decode(<<       16#5c:7,Bits/bits>>, Acc) -> decode(Bits, [ 58|Acc]);
decode(<<       16#fb:8,Bits/bits>>, Acc) -> decode(Bits, [ 59|Acc]);
decode(<<    16#7ffc:15,Bits/bits>>, Acc) -> decode(Bits, [ 60|Acc]);
decode(<<       16#20:6,Bits/bits>>, Acc) -> decode(Bits, [ 61|Acc]);
decode(<<     16#ffb:12,Bits/bits>>, Acc) -> decode(Bits, [ 62|Acc]);
decode(<<     16#3fc:10,Bits/bits>>, Acc) -> decode(Bits, [ 63|Acc]);
decode(<<    16#1ffa:13,Bits/bits>>, Acc) -> decode(Bits, [ 64|Acc]);
decode(<<       16#21:6,Bits/bits>>, Acc) -> decode(Bits, [ 65|Acc]);
decode(<<       16#5d:7,Bits/bits>>, Acc) -> decode(Bits, [ 66|Acc]);
decode(<<       16#5e:7,Bits/bits>>, Acc) -> decode(Bits, [ 67|Acc]);
decode(<<       16#5f:7,Bits/bits>>, Acc) -> decode(Bits, [ 68|Acc]);
decode(<<       16#60:7,Bits/bits>>, Acc) -> decode(Bits, [ 69|Acc]);
decode(<<       16#61:7,Bits/bits>>, Acc) -> decode(Bits, [ 70|Acc]);
decode(<<       16#62:7,Bits/bits>>, Acc) -> decode(Bits, [ 71|Acc]);
decode(<<       16#63:7,Bits/bits>>, Acc) -> decode(Bits, [ 72|Acc]);
decode(<<       16#64:7,Bits/bits>>, Acc) -> decode(Bits, [ 73|Acc]);
decode(<<       16#65:7,Bits/bits>>, Acc) -> decode(Bits, [ 74|Acc]);
decode(<<       16#66:7,Bits/bits>>, Acc) -> decode(Bits, [ 75|Acc]);
decode(<<       16#67:7,Bits/bits>>, Acc) -> decode(Bits, [ 76|Acc]);
decode(<<       16#68:7,Bits/bits>>, Acc) -> decode(Bits, [ 77|Acc]);
decode(<<       16#69:7,Bits/bits>>, Acc) -> decode(Bits, [ 78|Acc]);
decode(<<       16#6a:7,Bits/bits>>, Acc) -> decode(Bits, [ 79|Acc]);
decode(<<       16#6b:7,Bits/bits>>, Acc) -> decode(Bits, [ 80|Acc]);
decode(<<       16#6c:7,Bits/bits>>, Acc) -> decode(Bits, [ 81|Acc]);
decode(<<       16#6d:7,Bits/bits>>, Acc) -> decode(Bits, [ 82|Acc]);
decode(<<       16#6e:7,Bits/bits>>, Acc) -> decode(Bits, [ 83|Acc]);
decode(<<       16#6f:7,Bits/bits>>, Acc) -> decode(Bits, [ 84|Acc]);
decode(<<       16#70:7,Bits/bits>>, Acc) -> decode(Bits, [ 85|Acc]);
decode(<<       16#71:7,Bits/bits>>, Acc) -> decode(Bits, [ 86|Acc]);
decode(<<       16#72:7,Bits/bits>>, Acc) -> decode(Bits, [ 87|Acc]);
decode(<<       16#fc:8,Bits/bits>>, Acc) -> decode(Bits, [ 88|Acc]);
decode(<<       16#73:7,Bits/bits>>, Acc) -> decode(Bits, [ 89|Acc]);
decode(<<       16#fd:8,Bits/bits>>, Acc) -> decode(Bits, [ 90|Acc]);
decode(<<    16#1ffb:13,Bits/bits>>, Acc) -> decode(Bits, [ 91|Acc]);
decode(<<   16#7fff0:19,Bits/bits>>, Acc) -> decode(Bits, [ 92|Acc]);
decode(<<    16#1ffc:13,Bits/bits>>, Acc) -> decode(Bits, [ 93|Acc]);
decode(<<    16#3ffc:14,Bits/bits>>, Acc) -> decode(Bits, [ 94|Acc]);
decode(<<       16#22:6,Bits/bits>>, Acc) -> decode(Bits, [ 95|Acc]);
decode(<<    16#7ffd:15,Bits/bits>>, Acc) -> decode(Bits, [ 96|Acc]);
decode(<<        16#3:5,Bits/bits>>, Acc) -> decode(Bits, [ 97|Acc]);
decode(<<       16#23:6,Bits/bits>>, Acc) -> decode(Bits, [ 98|Acc]);
decode(<<        16#4:5,Bits/bits>>, Acc) -> decode(Bits, [ 99|Acc]);
decode(<<       16#24:6,Bits/bits>>, Acc) -> decode(Bits, [100|Acc]);
decode(<<        16#5:5,Bits/bits>>, Acc) -> decode(Bits, [101|Acc]);
decode(<<       16#25:6,Bits/bits>>, Acc) -> decode(Bits, [102|Acc]);
decode(<<       16#26:6,Bits/bits>>, Acc) -> decode(Bits, [103|Acc]);
decode(<<       16#27:6,Bits/bits>>, Acc) -> decode(Bits, [104|Acc]);
decode(<<        16#6:5,Bits/bits>>, Acc) -> decode(Bits, [105|Acc]);
decode(<<       16#74:7,Bits/bits>>, Acc) -> decode(Bits, [106|Acc]);
decode(<<       16#75:7,Bits/bits>>, Acc) -> decode(Bits, [107|Acc]);
decode(<<       16#28:6,Bits/bits>>, Acc) -> decode(Bits, [108|Acc]);
decode(<<       16#29:6,Bits/bits>>, Acc) -> decode(Bits, [109|Acc]);
decode(<<       16#2a:6,Bits/bits>>, Acc) -> decode(Bits, [110|Acc]);
decode(<<        16#7:5,Bits/bits>>, Acc) -> decode(Bits, [111|Acc]);
decode(<<       16#2b:6,Bits/bits>>, Acc) -> decode(Bits, [112|Acc]);
decode(<<       16#76:7,Bits/bits>>, Acc) -> decode(Bits, [113|Acc]);
decode(<<       16#2c:6,Bits/bits>>, Acc) -> decode(Bits, [114|Acc]);
decode(<<        16#8:5,Bits/bits>>, Acc) -> decode(Bits, [115|Acc]);
decode(<<        16#9:5,Bits/bits>>, Acc) -> decode(Bits, [116|Acc]);
decode(<<       16#2d:6,Bits/bits>>, Acc) -> decode(Bits, [117|Acc]);
decode(<<       16#77:7,Bits/bits>>, Acc) -> decode(Bits, [118|Acc]);
decode(<<       16#78:7,Bits/bits>>, Acc) -> decode(Bits, [119|Acc]);
decode(<<       16#79:7,Bits/bits>>, Acc) -> decode(Bits, [120|Acc]);
decode(<<       16#7a:7,Bits/bits>>, Acc) -> decode(Bits, [121|Acc]);
decode(<<       16#7b:7,Bits/bits>>, Acc) -> decode(Bits, [122|Acc]);
decode(<<    16#7ffe:15,Bits/bits>>, Acc) -> decode(Bits, [123|Acc]);
decode(<<     16#7fc:11,Bits/bits>>, Acc) -> decode(Bits, [124|Acc]);
decode(<<    16#3ffd:14,Bits/bits>>, Acc) -> decode(Bits, [125|Acc]);
decode(<<    16#1ffd:13,Bits/bits>>, Acc) -> decode(Bits, [126|Acc]);
decode(<< 16#ffffffc:28,Bits/bits>>, Acc) -> decode(Bits, [127|Acc]);
decode(<<   16#fffe6:20,Bits/bits>>, Acc) -> decode(Bits, [128|Acc]);
decode(<<  16#3fffd2:22,Bits/bits>>, Acc) -> decode(Bits, [129|Acc]);
decode(<<   16#fffe7:20,Bits/bits>>, Acc) -> decode(Bits, [130|Acc]);
decode(<<   16#fffe8:20,Bits/bits>>, Acc) -> decode(Bits, [131|Acc]);
decode(<<  16#3fffd3:22,Bits/bits>>, Acc) -> decode(Bits, [132|Acc]);
decode(<<  16#3fffd4:22,Bits/bits>>, Acc) -> decode(Bits, [133|Acc]);
decode(<<  16#3fffd5:22,Bits/bits>>, Acc) -> decode(Bits, [134|Acc]);
decode(<<  16#7fffd9:23,Bits/bits>>, Acc) -> decode(Bits, [135|Acc]);
decode(<<  16#3fffd6:22,Bits/bits>>, Acc) -> decode(Bits, [136|Acc]);
decode(<<  16#7fffda:23,Bits/bits>>, Acc) -> decode(Bits, [137|Acc]);
decode(<<  16#7fffdb:23,Bits/bits>>, Acc) -> decode(Bits, [138|Acc]);
decode(<<  16#7fffdc:23,Bits/bits>>, Acc) -> decode(Bits, [139|Acc]);
decode(<<  16#7fffdd:23,Bits/bits>>, Acc) -> decode(Bits, [140|Acc]);
decode(<<  16#7fffde:23,Bits/bits>>, Acc) -> decode(Bits, [141|Acc]);
decode(<<  16#ffffeb:24,Bits/bits>>, Acc) -> decode(Bits, [142|Acc]);
decode(<<  16#7fffdf:23,Bits/bits>>, Acc) -> decode(Bits, [143|Acc]);
decode(<<  16#ffffec:24,Bits/bits>>, Acc) -> decode(Bits, [144|Acc]);
decode(<<  16#ffffed:24,Bits/bits>>, Acc) -> decode(Bits, [145|Acc]);
decode(<<  16#3fffd7:22,Bits/bits>>, Acc) -> decode(Bits, [146|Acc]);
decode(<<  16#7fffe0:23,Bits/bits>>, Acc) -> decode(Bits, [147|Acc]);
decode(<<  16#ffffee:24,Bits/bits>>, Acc) -> decode(Bits, [148|Acc]);
decode(<<  16#7fffe1:23,Bits/bits>>, Acc) -> decode(Bits, [149|Acc]);
decode(<<  16#7fffe2:23,Bits/bits>>, Acc) -> decode(Bits, [150|Acc]);
decode(<<  16#7fffe3:23,Bits/bits>>, Acc) -> decode(Bits, [151|Acc]);
decode(<<  16#7fffe4:23,Bits/bits>>, Acc) -> decode(Bits, [152|Acc]);
decode(<<  16#1fffdc:21,Bits/bits>>, Acc) -> decode(Bits, [153|Acc]);
decode(<<  16#3fffd8:22,Bits/bits>>, Acc) -> decode(Bits, [154|Acc]);
decode(<<  16#7fffe5:23,Bits/bits>>, Acc) -> decode(Bits, [155|Acc]);
decode(<<  16#3fffd9:22,Bits/bits>>, Acc) -> decode(Bits, [156|Acc]);
decode(<<  16#7fffe6:23,Bits/bits>>, Acc) -> decode(Bits, [157|Acc]);
decode(<<  16#7fffe7:23,Bits/bits>>, Acc) -> decode(Bits, [158|Acc]);
decode(<<  16#ffffef:24,Bits/bits>>, Acc) -> decode(Bits, [159|Acc]);
decode(<<  16#3fffda:22,Bits/bits>>, Acc) -> decode(Bits, [160|Acc]);
decode(<<  16#1fffdd:21,Bits/bits>>, Acc) -> decode(Bits, [161|Acc]);
decode(<<   16#fffe9:20,Bits/bits>>, Acc) -> decode(Bits, [162|Acc]);
decode(<<  16#3fffdb:22,Bits/bits>>, Acc) -> decode(Bits, [163|Acc]);
decode(<<  16#3fffdc:22,Bits/bits>>, Acc) -> decode(Bits, [164|Acc]);
decode(<<  16#7fffe8:23,Bits/bits>>, Acc) -> decode(Bits, [165|Acc]);
decode(<<  16#7fffe9:23,Bits/bits>>, Acc) -> decode(Bits, [166|Acc]);
decode(<<  16#1fffde:21,Bits/bits>>, Acc) -> decode(Bits, [167|Acc]);
decode(<<  16#7fffea:23,Bits/bits>>, Acc) -> decode(Bits, [168|Acc]);
decode(<<  16#3fffdd:22,Bits/bits>>, Acc) -> decode(Bits, [169|Acc]);
decode(<<  16#3fffde:22,Bits/bits>>, Acc) -> decode(Bits, [170|Acc]);
decode(<<  16#fffff0:24,Bits/bits>>, Acc) -> decode(Bits, [171|Acc]);
decode(<<  16#1fffdf:21,Bits/bits>>, Acc) -> decode(Bits, [172|Acc]);
decode(<<  16#3fffdf:22,Bits/bits>>, Acc) -> decode(Bits, [173|Acc]);
decode(<<  16#7fffeb:23,Bits/bits>>, Acc) -> decode(Bits, [174|Acc]);
decode(<<  16#7fffec:23,Bits/bits>>, Acc) -> decode(Bits, [175|Acc]);
decode(<<  16#1fffe0:21,Bits/bits>>, Acc) -> decode(Bits, [176|Acc]);
decode(<<  16#1fffe1:21,Bits/bits>>, Acc) -> decode(Bits, [177|Acc]);
decode(<<  16#3fffe0:22,Bits/bits>>, Acc) -> decode(Bits, [178|Acc]);
decode(<<  16#1fffe2:21,Bits/bits>>, Acc) -> decode(Bits, [179|Acc]);
decode(<<  16#7fffed:23,Bits/bits>>, Acc) -> decode(Bits, [180|Acc]);
decode(<<  16#3fffe1:22,Bits/bits>>, Acc) -> decode(Bits, [181|Acc]);
decode(<<  16#7fffee:23,Bits/bits>>, Acc) -> decode(Bits, [182|Acc]);
decode(<<  16#7fffef:23,Bits/bits>>, Acc) -> decode(Bits, [183|Acc]);
decode(<<   16#fffea:20,Bits/bits>>, Acc) -> decode(Bits, [184|Acc]);
decode(<<  16#3fffe2:22,Bits/bits>>, Acc) -> decode(Bits, [185|Acc]);
decode(<<  16#3fffe3:22,Bits/bits>>, Acc) -> decode(Bits, [186|Acc]);
decode(<<  16#3fffe4:22,Bits/bits>>, Acc) -> decode(Bits, [187|Acc]);
decode(<<  16#7ffff0:23,Bits/bits>>, Acc) -> decode(Bits, [188|Acc]);
decode(<<  16#3fffe5:22,Bits/bits>>, Acc) -> decode(Bits, [189|Acc]);
decode(<<  16#3fffe6:22,Bits/bits>>, Acc) -> decode(Bits, [190|Acc]);
decode(<<  16#7ffff1:23,Bits/bits>>, Acc) -> decode(Bits, [191|Acc]);
decode(<< 16#3ffffe0:26,Bits/bits>>, Acc) -> decode(Bits, [192|Acc]);
decode(<< 16#3ffffe1:26,Bits/bits>>, Acc) -> decode(Bits, [193|Acc]);
decode(<<   16#fffeb:20,Bits/bits>>, Acc) -> decode(Bits, [194|Acc]);
decode(<<   16#7fff1:19,Bits/bits>>, Acc) -> decode(Bits, [195|Acc]);
decode(<<  16#3fffe7:22,Bits/bits>>, Acc) -> decode(Bits, [196|Acc]);
decode(<<  16#7ffff2:23,Bits/bits>>, Acc) -> decode(Bits, [197|Acc]);
decode(<<  16#3fffe8:22,Bits/bits>>, Acc) -> decode(Bits, [198|Acc]);
decode(<< 16#1ffffec:25,Bits/bits>>, Acc) -> decode(Bits, [199|Acc]);
decode(<< 16#3ffffe2:26,Bits/bits>>, Acc) -> decode(Bits, [200|Acc]);
decode(<< 16#3ffffe3:26,Bits/bits>>, Acc) -> decode(Bits, [201|Acc]);
decode(<< 16#3ffffe4:26,Bits/bits>>, Acc) -> decode(Bits, [202|Acc]);
decode(<< 16#7ffffde:27,Bits/bits>>, Acc) -> decode(Bits, [203|Acc]);
decode(<< 16#7ffffdf:27,Bits/bits>>, Acc) -> decode(Bits, [204|Acc]);
decode(<< 16#3ffffe5:26,Bits/bits>>, Acc) -> decode(Bits, [205|Acc]);
decode(<<  16#fffff1:24,Bits/bits>>, Acc) -> decode(Bits, [206|Acc]);
decode(<< 16#1ffffed:25,Bits/bits>>, Acc) -> decode(Bits, [207|Acc]);
decode(<<   16#7fff2:19,Bits/bits>>, Acc) -> decode(Bits, [208|Acc]);
decode(<<  16#1fffe3:21,Bits/bits>>, Acc) -> decode(Bits, [209|Acc]);
decode(<< 16#3ffffe6:26,Bits/bits>>, Acc) -> decode(Bits, [210|Acc]);
decode(<< 16#7ffffe0:27,Bits/bits>>, Acc) -> decode(Bits, [211|Acc]);
decode(<< 16#7ffffe1:27,Bits/bits>>, Acc) -> decode(Bits, [212|Acc]);
decode(<< 16#3ffffe7:26,Bits/bits>>, Acc) -> decode(Bits, [213|Acc]);
decode(<< 16#7ffffe2:27,Bits/bits>>, Acc) -> decode(Bits, [214|Acc]);
decode(<<  16#fffff2:24,Bits/bits>>, Acc) -> decode(Bits, [215|Acc]);
decode(<<  16#1fffe4:21,Bits/bits>>, Acc) -> decode(Bits, [216|Acc]);
decode(<<  16#1fffe5:21,Bits/bits>>, Acc) -> decode(Bits, [217|Acc]);
decode(<< 16#3ffffe8:26,Bits/bits>>, Acc) -> decode(Bits, [218|Acc]);
decode(<< 16#3ffffe9:26,Bits/bits>>, Acc) -> decode(Bits, [219|Acc]);
decode(<< 16#ffffffd:28,Bits/bits>>, Acc) -> decode(Bits, [220|Acc]);
decode(<< 16#7ffffe3:27,Bits/bits>>, Acc) -> decode(Bits, [221|Acc]);
decode(<< 16#7ffffe4:27,Bits/bits>>, Acc) -> decode(Bits, [222|Acc]);
decode(<< 16#7ffffe5:27,Bits/bits>>, Acc) -> decode(Bits, [223|Acc]);
decode(<<   16#fffec:20,Bits/bits>>, Acc) -> decode(Bits, [224|Acc]);
decode(<<  16#fffff3:24,Bits/bits>>, Acc) -> decode(Bits, [225|Acc]);
decode(<<   16#fffed:20,Bits/bits>>, Acc) -> decode(Bits, [226|Acc]);
decode(<<  16#1fffe6:21,Bits/bits>>, Acc) -> decode(Bits, [227|Acc]);
decode(<<  16#3fffe9:22,Bits/bits>>, Acc) -> decode(Bits, [228|Acc]);
decode(<<  16#1fffe7:21,Bits/bits>>, Acc) -> decode(Bits, [229|Acc]);
decode(<<  16#1fffe8:21,Bits/bits>>, Acc) -> decode(Bits, [230|Acc]);
decode(<<  16#7ffff3:23,Bits/bits>>, Acc) -> decode(Bits, [231|Acc]);
decode(<<  16#3fffea:22,Bits/bits>>, Acc) -> decode(Bits, [232|Acc]);
decode(<<  16#3fffeb:22,Bits/bits>>, Acc) -> decode(Bits, [233|Acc]);
decode(<< 16#1ffffee:25,Bits/bits>>, Acc) -> decode(Bits, [234|Acc]);
decode(<< 16#1ffffef:25,Bits/bits>>, Acc) -> decode(Bits, [235|Acc]);
decode(<<  16#fffff4:24,Bits/bits>>, Acc) -> decode(Bits, [236|Acc]);
decode(<<  16#fffff5:24,Bits/bits>>, Acc) -> decode(Bits, [237|Acc]);
decode(<< 16#3ffffea:26,Bits/bits>>, Acc) -> decode(Bits, [238|Acc]);
decode(<<  16#7ffff4:23,Bits/bits>>, Acc) -> decode(Bits, [239|Acc]);
decode(<< 16#3ffffeb:26,Bits/bits>>, Acc) -> decode(Bits, [240|Acc]);
decode(<< 16#7ffffe6:27,Bits/bits>>, Acc) -> decode(Bits, [241|Acc]);
decode(<< 16#3ffffec:26,Bits/bits>>, Acc) -> decode(Bits, [242|Acc]);
decode(<< 16#3ffffed:26,Bits/bits>>, Acc) -> decode(Bits, [243|Acc]);
decode(<< 16#7ffffe7:27,Bits/bits>>, Acc) -> decode(Bits, [244|Acc]);
decode(<< 16#7ffffe8:27,Bits/bits>>, Acc) -> decode(Bits, [245|Acc]);
decode(<< 16#7ffffe9:27,Bits/bits>>, Acc) -> decode(Bits, [246|Acc]);
decode(<< 16#7ffffea:27,Bits/bits>>, Acc) -> decode(Bits, [247|Acc]);
decode(<< 16#7ffffeb:27,Bits/bits>>, Acc) -> decode(Bits, [248|Acc]);
decode(<< 16#ffffffe:28,Bits/bits>>, Acc) -> decode(Bits, [249|Acc]);
decode(<< 16#7ffffec:27,Bits/bits>>, Acc) -> decode(Bits, [250|Acc]);
decode(<< 16#7ffffed:27,Bits/bits>>, Acc) -> decode(Bits, [251|Acc]);
decode(<< 16#7ffffee:27,Bits/bits>>, Acc) -> decode(Bits, [252|Acc]);
decode(<< 16#7ffffef:27,Bits/bits>>, Acc) -> decode(Bits, [253|Acc]);
decode(<< 16#7fffff0:27,Bits/bits>>, Acc) -> decode(Bits, [254|Acc]);
decode(<< 16#3ffffee:26,Bits/bits>>, Acc) -> decode(Bits, [255|Acc]);
decode(<<16#3fffffff:30,Bits/bits>>, Acc) -> decode(Bits, [256|Acc]);
decode(_, Acc) ->
    list_to_binary(lists:reverse(Acc)).

encode(Bin) -> encode(Bin, <<>>).
encode(<<  0:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#1ff8:13>>);
encode(<<  1:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffd8:23>>);
encode(<<  2:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffe2:28>>);
encode(<<  3:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffe3:28>>);
encode(<<  4:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffe4:28>>);
encode(<<  5:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffe5:28>>);
encode(<<  6:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffe6:28>>);
encode(<<  7:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffe7:28>>);
encode(<<  8:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffe8:28>>);
encode(<<  9:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#ffffea:24>>);
encode(<< 10:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,16#3ffffffc:30>>);
encode(<< 11:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffe9:28>>);
encode(<< 12:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffea:28>>);
encode(<< 13:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,16#3ffffffd:30>>);
encode(<< 14:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffeb:28>>);
encode(<< 15:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffec:28>>);
encode(<< 16:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffed:28>>);
encode(<< 17:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffee:28>>);
encode(<< 18:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#fffffef:28>>);
encode(<< 19:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff0:28>>);
encode(<< 20:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff1:28>>);
encode(<< 21:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff2:28>>);
encode(<< 22:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,16#3ffffffe:30>>);
encode(<< 23:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff3:28>>);
encode(<< 24:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff4:28>>);
encode(<< 25:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff5:28>>);
encode(<< 26:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff6:28>>);
encode(<< 27:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff7:28>>);
encode(<< 28:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff8:28>>);
encode(<< 29:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffff9:28>>);
encode(<< 30:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffffa:28>>);
encode(<< 31:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffffb:28>>);
encode(<< 32:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#14:6>>);
encode(<< 33:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#3f8:10>>);
encode(<< 34:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#3f9:10>>);
encode(<< 35:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#ffa:12>>);
encode(<< 36:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#1ff9:13>>);
encode(<< 37:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#15:6>>);
encode(<< 38:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#f8:8>>);
encode(<< 39:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#7fa:11>>);
encode(<< 40:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#3fa:10>>);
encode(<< 41:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#3fb:10>>);
encode(<< 42:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#f9:8>>);
encode(<< 43:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#7fb:11>>);
encode(<< 44:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#fa:8>>);
encode(<< 45:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#16:6>>);
encode(<< 46:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#17:6>>);
encode(<< 47:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#18:6>>);
encode(<< 48:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#0:5>>);
encode(<< 49:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#1:5>>);
encode(<< 50:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#2:5>>);
encode(<< 51:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#19:6>>);
encode(<< 52:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#1a:6>>);
encode(<< 53:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#1b:6>>);
encode(<< 54:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#1c:6>>);
encode(<< 55:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#1d:6>>);
encode(<< 56:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#1e:6>>);
encode(<< 57:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#1f:6>>);
encode(<< 58:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#5c:7>>);
encode(<< 59:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#fb:8>>);
encode(<< 60:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#7ffc:15>>);
encode(<< 61:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#20:6>>);
encode(<< 62:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#ffb:12>>);
encode(<< 63:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#3fc:10>>);
encode(<< 64:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#1ffa:13>>);
encode(<< 65:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#21:6>>);
encode(<< 66:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#5d:7>>);
encode(<< 67:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#5e:7>>);
encode(<< 68:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#5f:7>>);
encode(<< 69:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#60:7>>);
encode(<< 70:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#61:7>>);
encode(<< 71:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#62:7>>);
encode(<< 72:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#63:7>>);
encode(<< 73:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#64:7>>);
encode(<< 74:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#65:7>>);
encode(<< 75:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#66:7>>);
encode(<< 76:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#67:7>>);
encode(<< 77:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#68:7>>);
encode(<< 78:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#69:7>>);
encode(<< 79:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#6a:7>>);
encode(<< 80:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#6b:7>>);
encode(<< 81:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#6c:7>>);
encode(<< 82:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#6d:7>>);
encode(<< 83:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#6e:7>>);
encode(<< 84:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#6f:7>>);
encode(<< 85:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#70:7>>);
encode(<< 86:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#71:7>>);
encode(<< 87:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#72:7>>);
encode(<< 88:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#fc:8>>);
encode(<< 89:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#73:7>>);
encode(<< 90:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#fd:8>>);
encode(<< 91:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#1ffb:13>>);
encode(<< 92:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#7fff0:19>>);
encode(<< 93:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#1ffc:13>>);
encode(<< 94:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#3ffc:14>>);
encode(<< 95:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#22:6>>);
encode(<< 96:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#7ffd:15>>);
encode(<< 97:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#3:5>>);
encode(<< 98:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#23:6>>);
encode(<< 99:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#4:5>>);
encode(<<100:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#24:6>>);
encode(<<101:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#5:5>>);
encode(<<102:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#25:6>>);
encode(<<103:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#26:6>>);
encode(<<104:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#27:6>>);
encode(<<105:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#6:5>>);
encode(<<106:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#74:7>>);
encode(<<107:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#75:7>>);
encode(<<108:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#28:6>>);
encode(<<109:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#29:6>>);
encode(<<110:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#2a:6>>);
encode(<<111:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#7:5>>);
encode(<<112:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#2b:6>>);
encode(<<113:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#76:7>>);
encode(<<114:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#2c:6>>);
encode(<<115:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#8:5>>);
encode(<<116:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,        16#9:5>>);
encode(<<117:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#2d:6>>);
encode(<<118:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#77:7>>);
encode(<<119:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#78:7>>);
encode(<<120:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#79:7>>);
encode(<<121:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#7a:7>>);
encode(<<122:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,       16#7b:7>>);
encode(<<123:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#7ffe:15>>);
encode(<<124:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,     16#7fc:11>>);
encode(<<125:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#3ffd:14>>);
encode(<<126:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,    16#1ffd:13>>);
encode(<<127:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffffc:28>>);
encode(<<128:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#fffe6:20>>);
encode(<<129:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffd2:22>>);
encode(<<130:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#fffe7:20>>);
encode(<<131:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#fffe8:20>>);
encode(<<132:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffd3:22>>);
encode(<<133:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffd4:22>>);
encode(<<134:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffd5:22>>);
encode(<<135:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffd9:23>>);
encode(<<136:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffd6:22>>);
encode(<<137:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffda:23>>);
encode(<<138:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffdb:23>>);
encode(<<139:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffdc:23>>);
encode(<<140:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffdd:23>>);
encode(<<141:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffde:23>>);
encode(<<142:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#ffffeb:24>>);
encode(<<143:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffdf:23>>);
encode(<<144:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#ffffec:24>>);
encode(<<145:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#ffffed:24>>);
encode(<<146:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffd7:22>>);
encode(<<147:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe0:23>>);
encode(<<148:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#ffffee:24>>);
encode(<<149:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe1:23>>);
encode(<<150:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe2:23>>);
encode(<<151:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe3:23>>);
encode(<<152:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe4:23>>);
encode(<<153:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffdc:21>>);
encode(<<154:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffd8:22>>);
encode(<<155:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe5:23>>);
encode(<<156:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffd9:22>>);
encode(<<157:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe6:23>>);
encode(<<158:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe7:23>>);
encode(<<159:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#ffffef:24>>);
encode(<<160:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffda:22>>);
encode(<<161:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffdd:21>>);
encode(<<162:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#fffe9:20>>);
encode(<<163:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffdb:22>>);
encode(<<164:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffdc:22>>);
encode(<<165:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe8:23>>);
encode(<<166:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffe9:23>>);
encode(<<167:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffde:21>>);
encode(<<168:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffea:23>>);
encode(<<169:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffdd:22>>);
encode(<<170:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffde:22>>);
encode(<<171:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#fffff0:24>>);
encode(<<172:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffdf:21>>);
encode(<<173:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffdf:22>>);
encode(<<174:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffeb:23>>);
encode(<<175:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffec:23>>);
encode(<<176:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe0:21>>);
encode(<<177:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe1:21>>);
encode(<<178:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe0:22>>);
encode(<<179:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe2:21>>);
encode(<<180:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffed:23>>);
encode(<<181:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe1:22>>);
encode(<<182:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffee:23>>);
encode(<<183:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7fffef:23>>);
encode(<<184:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#fffea:20>>);
encode(<<185:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe2:22>>);
encode(<<186:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe3:22>>);
encode(<<187:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe4:22>>);
encode(<<188:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7ffff0:23>>);
encode(<<189:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe5:22>>);
encode(<<190:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe6:22>>);
encode(<<191:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7ffff1:23>>);
encode(<<192:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe0:26>>);
encode(<<193:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe1:26>>);
encode(<<194:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#fffeb:20>>);
encode(<<195:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#7fff1:19>>);
encode(<<196:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe7:22>>);
encode(<<197:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7ffff2:23>>);
encode(<<198:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe8:22>>);
encode(<<199:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#1ffffec:25>>);
encode(<<200:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe2:26>>);
encode(<<201:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe3:26>>);
encode(<<202:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe4:26>>);
encode(<<203:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffde:27>>);
encode(<<204:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffdf:27>>);
encode(<<205:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe5:26>>);
encode(<<206:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#fffff1:24>>);
encode(<<207:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#1ffffed:25>>);
encode(<<208:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#7fff2:19>>);
encode(<<209:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe3:21>>);
encode(<<210:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe6:26>>);
encode(<<211:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe0:27>>);
encode(<<212:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe1:27>>);
encode(<<213:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe7:26>>);
encode(<<214:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe2:27>>);
encode(<<215:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#fffff2:24>>);
encode(<<216:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe4:21>>);
encode(<<217:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe5:21>>);
encode(<<218:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe8:26>>);
encode(<<219:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffe9:26>>);
encode(<<220:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffffd:28>>);
encode(<<221:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe3:27>>);
encode(<<222:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe4:27>>);
encode(<<223:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe5:27>>);
encode(<<224:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#fffec:20>>);
encode(<<225:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#fffff3:24>>);
encode(<<226:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,   16#fffed:20>>);
encode(<<227:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe6:21>>);
encode(<<228:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffe9:22>>);
encode(<<229:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe7:21>>);
encode(<<230:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#1fffe8:21>>);
encode(<<231:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7ffff3:23>>);
encode(<<232:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffea:22>>);
encode(<<233:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#3fffeb:22>>);
encode(<<234:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#1ffffee:25>>);
encode(<<235:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#1ffffef:25>>);
encode(<<236:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#fffff4:24>>);
encode(<<237:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#fffff5:24>>);
encode(<<238:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffea:26>>);
encode(<<239:8,T/binary>>, Acc) -> encode(T, <<Acc/bits,  16#7ffff4:23>>);
encode(<<240:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffeb:26>>);
encode(<<241:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe6:27>>);
encode(<<242:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffec:26>>);
encode(<<243:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffed:26>>);
encode(<<244:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe7:27>>);
encode(<<245:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe8:27>>);
encode(<<246:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffe9:27>>);
encode(<<247:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffea:27>>);
encode(<<248:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffeb:27>>);
encode(<<249:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#ffffffe:28>>);
encode(<<250:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffec:27>>);
encode(<<251:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffed:27>>);
encode(<<252:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffee:27>>);
encode(<<253:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7ffffef:27>>);
encode(<<254:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#7fffff0:27>>);
encode(<<255:8,T/binary>>, Acc) -> encode(T, <<Acc/bits, 16#3ffffee:26>>);
encode(<<256:9,T/binary>>, Acc) -> encode(T, <<Acc/bits,16#3fffffff:30>>);
%%TODO: This "256" EOS Behavior. I really need to dig into it.
encode(<<>>, Acc) when bit_size(Acc) rem 8 > 0 ->
    NumberOfBits = 8 - (bit_size(Acc) rem 8),
    Bits = round(math:pow(2,NumberOfBits) - 1),
    <<Acc/bits,Bits:NumberOfBits>>;
encode(<<>>, Acc) -> Acc.
