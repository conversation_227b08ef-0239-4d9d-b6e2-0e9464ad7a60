{<<"name">>,<<"metrics">>}.
{<<"version">>,<<"1.0.1">>}.
{<<"app">>,<<"metrics">>}.
{<<"maintainers">>,[<<"Benoit Chesneau">>]}.
{<<"precompiled">>,false}.
{<<"description">>,
 <<"A generic interface to different metrics systems in Erlang.">>}.
{<<"files">>,
 [{<<"src/metrics.app.src">>,
   <<"{application,metrics,\n             [{description,\"A generic interface to different metrics systems in Erlang.\"},\n              {vsn,\"1.0.1\"},\n              {registered,[]},\n              {applications,[kernel,stdlib]},\n              {env,[]},\n              {modules,[]},\n              {maintainers,[\"Benoit Chesneau\"]},\n              {licenses,[\"BSD\"]},\n              {links,[{\"Github\",\n                       \"https://github.com/benoitc/erlang-metrics\"}]}]}.\n">>},
  <<"src/metrics.erl">>,<<"src/metrics_dummy.erl">>,
  <<"src/metrics_exometer.erl">>,<<"src/metrics_folsom.erl">>,
  <<"rebar.config">>,<<"rebar.lock">>,<<"README.md">>,<<"LICENSE">>]}.
{<<"licenses">>,[<<"BSD">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/benoitc/erlang-metrics">>}]}.
{<<"build_tools">>,[<<"rebar3">>]}.
