{<<"app">>,<<"gproc">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"Extended process registry for Erlang">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"include">>,<<"include/gproc.hrl">>,
  <<"priv">>,<<"priv/check_edown.script">>,<<"priv/remove_deps.script">>,
  <<"priv/sys.config">>,<<"rebar.config">>,<<"rebar.config.script">>,
  <<"rebar.lock">>,<<"src">>,<<"src/gproc.app.src">>,<<"src/gproc.erl">>,
  <<"src/gproc_app.erl">>,<<"src/gproc_bcast.erl">>,<<"src/gproc_dist.erl">>,
  <<"src/gproc_info.erl">>,<<"src/gproc_init.erl">>,<<"src/gproc_int.hrl">>,
  <<"src/gproc_lib.erl">>,<<"src/gproc_monitor.erl">>,
  <<"src/gproc_pool.erl">>,<<"src/gproc_ps.erl">>,<<"src/gproc_pt.erl">>,
  <<"src/gproc_sup.erl">>,<<"src/gproc_trace.hrl">>]}.
{<<"licenses">>,[<<"APL 2.0">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/uwiger/gproc">>}]}.
{<<"name">>,<<"gproc">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"0.9.1">>}.
