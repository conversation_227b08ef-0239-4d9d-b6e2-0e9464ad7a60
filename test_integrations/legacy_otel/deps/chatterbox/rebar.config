%% -*- mode: erlang -*-
%% -*- tab-width: 4;erlang-indent-level: 4;indent-tabs-mode: nil -*-
%% ex: ts=4 sw=4 ft=erlang et
{erl_opts, [
    warnings_as_errors,
    debug_info
]}.

{deps,
 [{hpack, {pkg, hpack_erl}}]}.

{cover_enabled, true}.

{ct_opts, [{verbose,true}]}.

{profiles,
 [
  {test, [
          {erl_opts,
           [
            {i,["include"]},
            nowarn_export_all
           ]},
          {deps,
           [{ranch, "2.0.0"}]}
         ]
  },
  {check, [
          {deps,
           [{ranch, "2.0.0"}]},
          {dialyzer,
           [{warnings,
             [error_handling, unknown]},
            {plt_extra_apps,
             [inets, ranch]}
           ]},
          {xref_checks,
           [deprecated_function_calls, locals_not_used, undefined_function_calls]}
         ]
  }]
}.

{relx, [
  {release,{chatterbox,"0.0.1"},
    [chatterbox]},
        {sys_config, "./config/sys.config"},
        %%{vm_args, "./config/vm.args"},

        {dev_mode, true},
        {include_erts, false},

        {extended_start_script, true},
        {overlay,[
                  {template,"config/sys.config","sys.config"},
                  {copy,"config/localhost.crt","."},
                  {copy,"config/localhost.key","."}
                 ]}
]}.
