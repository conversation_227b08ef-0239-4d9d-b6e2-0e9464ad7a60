{<<"app">>,<<"chatterbox">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"chatterbox library for http2">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"include">>,<<"include/http2.hrl">>,
  <<"priv">>,<<"priv/index.html">>,<<"rebar.config">>,
  <<"rebar.config.script">>,<<"rebar.lock">>,<<"src">>,
  <<"src/chatterbox.app.src">>,<<"src/chatterbox.erl">>,
  <<"src/chatterbox_ranch_protocol.erl">>,
  <<"src/chatterbox_static_stream.erl">>,<<"src/chatterbox_sup.erl">>,
  <<"src/h2_client.erl">>,<<"src/h2_connection.erl">>,<<"src/h2_frame.erl">>,
  <<"src/h2_frame_continuation.erl">>,<<"src/h2_frame_data.erl">>,
  <<"src/h2_frame_goaway.erl">>,<<"src/h2_frame_headers.erl">>,
  <<"src/h2_frame_ping.erl">>,<<"src/h2_frame_priority.erl">>,
  <<"src/h2_frame_push_promise.erl">>,<<"src/h2_frame_rst_stream.erl">>,
  <<"src/h2_frame_settings.erl">>,<<"src/h2_frame_window_update.erl">>,
  <<"src/h2_padding.erl">>,<<"src/h2_settings.erl">>,<<"src/h2_stream.erl">>,
  <<"src/h2_stream_set.erl">>,<<"src/sock.erl">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/joedevivo/chatterbox">>}]}.
{<<"name">>,<<"ts_chatterbox">>}.
{<<"requirements">>,
 [{<<"hpack_erl">>,
   [{<<"app">>,<<"hpack">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>0.3.0">>}]}]}.
{<<"version">>,<<"0.15.1">>}.
