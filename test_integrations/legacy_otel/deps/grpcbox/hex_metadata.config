{<<"app">>,<<"grpcbox">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"Erlang grpc library based on chatterbox">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"include">>,<<"include/grpcbox.hrl">>,
  <<"rebar.config">>,<<"rebar.lock">>,<<"src">>,<<"src/grpcbox.app.src">>,
  <<"src/grpcbox.erl">>,<<"src/grpcbox_acceptor.erl">>,
  <<"src/grpcbox_app.erl">>,<<"src/grpcbox_chain_interceptor.erl">>,
  <<"src/grpcbox_channel.erl">>,<<"src/grpcbox_channel_sup.erl">>,
  <<"src/grpcbox_client.erl">>,<<"src/grpcbox_client_stream.erl">>,
  <<"src/grpcbox_frame.erl">>,<<"src/grpcbox_health_bhvr.erl">>,
  <<"src/grpcbox_health_client.erl">>,<<"src/grpcbox_health_pb.erl">>,
  <<"src/grpcbox_health_service.erl">>,<<"src/grpcbox_metadata.erl">>,
  <<"src/grpcbox_name_resolver.erl">>,<<"src/grpcbox_oc_stats.erl">>,
  <<"src/grpcbox_oc_stats_handler.erl">>,<<"src/grpcbox_pool.erl">>,
  <<"src/grpcbox_reflection_bhvr.erl">>,
  <<"src/grpcbox_reflection_client.erl">>,<<"src/grpcbox_reflection_pb.erl">>,
  <<"src/grpcbox_reflection_service.erl">>,
  <<"src/grpcbox_services_simple_sup.erl">>,
  <<"src/grpcbox_services_sup.erl">>,<<"src/grpcbox_socket.erl">>,
  <<"src/grpcbox_stream.erl">>,<<"src/grpcbox_subchannel.erl">>,
  <<"src/grpcbox_sup.erl">>,<<"src/grpcbox_trace.erl">>,
  <<"src/grpcbox_utils.erl">>]}.
{<<"licenses">>,[<<"Apache 2.0">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/tsloughter/grpcbox">>}]}.
{<<"name">>,<<"grpcbox">>}.
{<<"requirements">>,
 [{<<"acceptor_pool">>,
   [{<<"app">>,<<"acceptor_pool">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>1.0.0">>}]},
  {<<"ctx">>,
   [{<<"app">>,<<"ctx">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>0.6.0">>}]},
  {<<"gproc">>,
   [{<<"app">>,<<"gproc">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>0.9.1">>}]},
  {<<"ts_chatterbox">>,
   [{<<"app">>,<<"chatterbox">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>0.15.1">>}]}]}.
{<<"version">>,<<"0.17.1">>}.
