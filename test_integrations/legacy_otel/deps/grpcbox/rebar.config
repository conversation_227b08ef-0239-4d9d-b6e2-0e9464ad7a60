{erl_opts, [debug_info]}.

{deps, [{chatterbox, {pkg, ts_chatterbox}},
        ctx,
        acceptor_pool,
        gproc]}.

{grpc, [{protos, ["proto"]},
        {service_modules, [{'grpc.health.v1.Health', "grpcbox_health"},
                           {'grpc.reflection.v1alpha.ServerReflection', "grpcbox_reflection"}]},
        {gpb_opts, [{descriptor, true},
                    {module_name_prefix, "grpcbox_"},
                    {module_name_suffix, "_pb"}]}]}.

{profiles,
 [{test, [{erl_opts, [nowarn_export_all]},

          {shell, [{config, "config/test.config"}]},

          {grpc, [{protos, "test/grpcbox_SUITE_data"},
                  {out_dir, "test"},
                  {gpb_opts, [{o, "test"},
                              {descriptor, true},
                              {module_name_suffix, "_pb"}]}]},
          {overrides, [{override, opencensus, [{erl_opts, []}]}]},
          {deps, [opencensus, jsx]}]},

  {interop, [{deps, [recon]},

             {grpc, [{protos, "interop/proto"},
                     {out_dir, "interop/src"},
                     {gpb_opts, [{o, "interop/src"},
                                 {descriptor, true},
                                 {module_name_suffix, "_pb"}]}]},

             {relx, [{release, {grpc_interop, "0.1.0"}, [grpcbox]},

                     {sys_config, "interop/config/sys.config"}]},

             {erl_opts, [{i, "interop/include"}]},

             %% use src_dirs so the modules are in the release for testing
             {src_dirs, ["src", "interop"]},

             {ct_opts, [{config, "interop/config/sys.config"},
                        {dir, "interop/test"}]},

             {shell, [{apps, [grpcbox]},
                      {config, "interop/config/sys.config"}]}]},

  {benchmark, [{grpc, [{protos, "benchmark/proto"},
                       {out_dir, "benchmark/src"},
                       {gpb_opts, [{o, "benchmark/src"},
                                   {descriptor, true},
                                   {module_name_suffix, "_pb"}]}]},

             {extra_src_dirs, ["benchmark"]},

             {ct_opts, [{config, "benchmark/config/test.config"},
                        {dir, "benchmark/test"}]},

             {shell, [{apps, [grpcbox]},
                      {config, "benchmark/config/sys.config"}]}]}
]}.

{shell, [{apps, [grpcbox]}]}.

{xref_checks, [undefined_function_calls, undefined_functions,
               deprecated_function_calls, deprecated_functions]}.

{project_plugins, [covertool,
                   {grpcbox_plugin, "~> 0.9.0"},
                   rebar3_lint]}.

{cover_enabled, true}.
{cover_opts, [verbose]}.
{cover_export_enabled, true}.
{covertool, [{coverdata_files, ["ct.coverdata"]}]}.

%% create junit xml for circleci
{ct_opts, [{ct_hooks, [cth_surefire]}]}.

{dialyzer, [{warnings, [no_unknown]}]}.
