%% -*- coding: utf-8 -*-
%% @private
%% Automatically generated, do not edit
%% Generated by gpb_compile version 4.7.3
-module(grpcbox_reflection_pb).

-export([encode_msg/2, encode_msg/3]).
-export([decode_msg/2, decode_msg/3]).
-export([merge_msgs/3, merge_msgs/4]).
-export([verify_msg/2, verify_msg/3]).
-export([get_msg_defs/0]).
-export([get_msg_names/0]).
-export([get_group_names/0]).
-export([get_msg_or_group_names/0]).
-export([get_enum_names/0]).
-export([find_msg_def/1, fetch_msg_def/1]).
-export([find_enum_def/1, fetch_enum_def/1]).
-export([enum_symbol_by_value/2, enum_value_by_symbol/2]).
-export([get_service_names/0]).
-export([get_service_def/1]).
-export([get_rpc_names/1]).
-export([find_rpc_def/2, fetch_rpc_def/2]).
-export([fqbin_to_service_name/1]).
-export([service_name_to_fqbin/1]).
-export([fqbins_to_service_and_rpc_name/2]).
-export([service_and_rpc_name_to_fqbins/2]).
-export([fqbin_to_msg_name/1]).
-export([msg_name_to_fqbin/1]).
-export([fqbin_to_enum_name/1]).
-export([enum_name_to_fqbin/1]).
-export([get_package_name/0]).
-export([uses_packages/0]).
-export([source_basename/0]).
-export([get_all_source_basenames/0]).
-export([get_all_proto_names/0]).
-export([get_msg_containment/1]).
-export([get_pkg_containment/1]).
-export([get_service_containment/1]).
-export([get_rpc_containment/1]).
-export([get_enum_containment/1]).
-export([get_proto_by_msg_name_as_fqbin/1]).
-export([get_proto_by_service_name_as_fqbin/1]).
-export([get_proto_by_enum_name_as_fqbin/1]).
-export([get_protos_by_pkg_name_as_fqbin/1]).
-export([descriptor/0, descriptor/1]).
-export([gpb_version_as_string/0, gpb_version_as_list/0]).


%% enumerated types

-export_type([]).

%% message types
-type server_reflection_request() ::
      #{host                    => iodata(),        % = 1
        message_request         => {file_by_filename, iodata()} | {file_containing_symbol, iodata()} | {file_containing_extension, extension_request()} | {all_extension_numbers_of_type, iodata()} | {list_services, iodata()} % oneof
       }.

-type extension_request() ::
      #{containing_type         => iodata(),        % = 1
        extension_number        => integer()        % = 2, 32 bits
       }.

-type server_reflection_response() ::
      #{valid_host              => iodata(),        % = 1
        original_request        => server_reflection_request(), % = 2
        message_response        => {file_descriptor_response, file_descriptor_response()} | {all_extension_numbers_response, extension_number_response()} | {list_services_response, list_service_response()} | {error_response, error_response()} % oneof
       }.

-type file_descriptor_response() ::
      #{file_descriptor_proto   => [iodata()]       % = 1
       }.

-type extension_number_response() ::
      #{base_type_name          => iodata(),        % = 1
        extension_number        => [integer()]      % = 2, 32 bits
       }.

-type list_service_response() ::
      #{service                 => [service_response()] % = 1
       }.

-type service_response() ::
      #{name                    => iodata()         % = 1
       }.

-type error_response() ::
      #{error_code              => integer(),       % = 1, 32 bits
        error_message           => iodata()         % = 2
       }.

-export_type(['server_reflection_request'/0, 'extension_request'/0, 'server_reflection_response'/0, 'file_descriptor_response'/0, 'extension_number_response'/0, 'list_service_response'/0, 'service_response'/0, 'error_response'/0]).

-spec encode_msg(server_reflection_request() | extension_request() | server_reflection_response() | file_descriptor_response() | extension_number_response() | list_service_response() | service_response() | error_response(), atom()) -> binary().
encode_msg(Msg, MsgName) when is_atom(MsgName) ->
    encode_msg(Msg, MsgName, []).

-spec encode_msg(server_reflection_request() | extension_request() | server_reflection_response() | file_descriptor_response() | extension_number_response() | list_service_response() | service_response() | error_response(), atom(), list()) -> binary().
encode_msg(Msg, MsgName, Opts) ->
    case proplists:get_bool(verify, Opts) of
        true -> verify_msg(Msg, MsgName, Opts);
        false -> ok
    end,
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        server_reflection_request ->
            encode_msg_server_reflection_request(id(Msg,
                                                    TrUserData),
                                                 TrUserData);
        extension_request ->
            encode_msg_extension_request(id(Msg, TrUserData),
                                         TrUserData);
        server_reflection_response ->
            encode_msg_server_reflection_response(id(Msg,
                                                     TrUserData),
                                                  TrUserData);
        file_descriptor_response ->
            encode_msg_file_descriptor_response(id(Msg, TrUserData),
                                                TrUserData);
        extension_number_response ->
            encode_msg_extension_number_response(id(Msg,
                                                    TrUserData),
                                                 TrUserData);
        list_service_response ->
            encode_msg_list_service_response(id(Msg, TrUserData),
                                             TrUserData);
        service_response ->
            encode_msg_service_response(id(Msg, TrUserData),
                                        TrUserData);
        error_response ->
            encode_msg_error_response(id(Msg, TrUserData),
                                      TrUserData)
    end.


encode_msg_server_reflection_request(Msg, TrUserData) ->
    encode_msg_server_reflection_request(Msg,
                                         <<>>,
                                         TrUserData).


encode_msg_server_reflection_request(#{} = M, Bin,
                                     TrUserData) ->
    B1 = case M of
             #{host := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false ->
                             e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{message_request := F2} ->
            case id(F2, TrUserData) of
                {file_by_filename, TF2} ->
                    begin
                        TrTF2 = id(TF2, TrUserData),
                        e_type_string(TrTF2, <<B1/binary, 26>>, TrUserData)
                    end;
                {file_containing_symbol, TF2} ->
                    begin
                        TrTF2 = id(TF2, TrUserData),
                        e_type_string(TrTF2, <<B1/binary, 34>>, TrUserData)
                    end;
                {file_containing_extension, TF2} ->
                    begin
                        TrTF2 = id(TF2, TrUserData),
                        e_mfield_server_reflection_request_file_containing_extension(TrTF2,
                                                                                     <<B1/binary,
                                                                                       42>>,
                                                                                     TrUserData)
                    end;
                {all_extension_numbers_of_type, TF2} ->
                    begin
                        TrTF2 = id(TF2, TrUserData),
                        e_type_string(TrTF2, <<B1/binary, 50>>, TrUserData)
                    end;
                {list_services, TF2} ->
                    begin
                        TrTF2 = id(TF2, TrUserData),
                        e_type_string(TrTF2, <<B1/binary, 58>>, TrUserData)
                    end
            end;
        _ -> B1
    end.

encode_msg_extension_request(Msg, TrUserData) ->
    encode_msg_extension_request(Msg, <<>>, TrUserData).


encode_msg_extension_request(#{} = M, Bin,
                             TrUserData) ->
    B1 = case M of
             #{containing_type := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false ->
                             e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{extension_number := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                if TrF2 =:= 0 -> B1;
                   true ->
                       e_type_int32(TrF2, <<B1/binary, 16>>, TrUserData)
                end
            end;
        _ -> B1
    end.

encode_msg_server_reflection_response(Msg,
                                      TrUserData) ->
    encode_msg_server_reflection_response(Msg,
                                          <<>>,
                                          TrUserData).


encode_msg_server_reflection_response(#{} = M, Bin,
                                      TrUserData) ->
    B1 = case M of
             #{valid_host := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false ->
                             e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{original_request := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     if TrF2 =:= undefined -> B1;
                        true ->
                            e_mfield_server_reflection_response_original_request(TrF2,
                                                                                 <<B1/binary,
                                                                                   18>>,
                                                                                 TrUserData)
                     end
                 end;
             _ -> B1
         end,
    case M of
        #{message_response := F3} ->
            case id(F3, TrUserData) of
                {file_descriptor_response, TF3} ->
                    begin
                        TrTF3 = id(TF3, TrUserData),
                        e_mfield_server_reflection_response_file_descriptor_response(TrTF3,
                                                                                     <<B2/binary,
                                                                                       34>>,
                                                                                     TrUserData)
                    end;
                {all_extension_numbers_response, TF3} ->
                    begin
                        TrTF3 = id(TF3, TrUserData),
                        e_mfield_server_reflection_response_all_extension_numbers_response(TrTF3,
                                                                                           <<B2/binary,
                                                                                             42>>,
                                                                                           TrUserData)
                    end;
                {list_services_response, TF3} ->
                    begin
                        TrTF3 = id(TF3, TrUserData),
                        e_mfield_server_reflection_response_list_services_response(TrTF3,
                                                                                   <<B2/binary,
                                                                                     50>>,
                                                                                   TrUserData)
                    end;
                {error_response, TF3} ->
                    begin
                        TrTF3 = id(TF3, TrUserData),
                        e_mfield_server_reflection_response_error_response(TrTF3,
                                                                           <<B2/binary,
                                                                             58>>,
                                                                           TrUserData)
                    end
            end;
        _ -> B2
    end.

encode_msg_file_descriptor_response(Msg, TrUserData) ->
    encode_msg_file_descriptor_response(Msg,
                                        <<>>,
                                        TrUserData).


encode_msg_file_descriptor_response(#{} = M, Bin,
                                    TrUserData) ->
    case M of
        #{file_descriptor_proto := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true ->
                   e_field_file_descriptor_response_file_descriptor_proto(TrF1,
                                                                          Bin,
                                                                          TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_extension_number_response(Msg, TrUserData) ->
    encode_msg_extension_number_response(Msg,
                                         <<>>,
                                         TrUserData).


encode_msg_extension_number_response(#{} = M, Bin,
                                     TrUserData) ->
    B1 = case M of
             #{base_type_name := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false ->
                             e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{extension_number := F2} ->
            TrF2 = id(F2, TrUserData),
            if TrF2 == [] -> B1;
               true ->
                   e_field_extension_number_response_extension_number(TrF2,
                                                                      B1,
                                                                      TrUserData)
            end;
        _ -> B1
    end.

encode_msg_list_service_response(Msg, TrUserData) ->
    encode_msg_list_service_response(Msg, <<>>, TrUserData).


encode_msg_list_service_response(#{} = M, Bin,
                                 TrUserData) ->
    case M of
        #{service := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true ->
                   e_field_list_service_response_service(TrF1,
                                                         Bin,
                                                         TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_service_response(Msg, TrUserData) ->
    encode_msg_service_response(Msg, <<>>, TrUserData).


encode_msg_service_response(#{} = M, Bin, TrUserData) ->
    case M of
        #{name := F1} ->
            begin
                TrF1 = id(F1, TrUserData),
                case is_empty_string(TrF1) of
                    true -> Bin;
                    false ->
                        e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                end
            end;
        _ -> Bin
    end.

encode_msg_error_response(Msg, TrUserData) ->
    encode_msg_error_response(Msg, <<>>, TrUserData).


encode_msg_error_response(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{error_code := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= 0 -> Bin;
                        true ->
                            e_type_int32(TrF1, <<Bin/binary, 8>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{error_message := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                case is_empty_string(TrF2) of
                    true -> B1;
                    false ->
                        e_type_string(TrF2, <<B1/binary, 18>>, TrUserData)
                end
            end;
        _ -> B1
    end.

e_mfield_server_reflection_request_file_containing_extension(Msg,
                                                             Bin, TrUserData) ->
    SubBin = encode_msg_extension_request(Msg,
                                          <<>>,
                                          TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_server_reflection_response_original_request(Msg,
                                                     Bin, TrUserData) ->
    SubBin = encode_msg_server_reflection_request(Msg,
                                                  <<>>,
                                                  TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_server_reflection_response_file_descriptor_response(Msg,
                                                             Bin, TrUserData) ->
    SubBin = encode_msg_file_descriptor_response(Msg,
                                                 <<>>,
                                                 TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_server_reflection_response_all_extension_numbers_response(Msg,
                                                                   Bin,
                                                                   TrUserData) ->
    SubBin = encode_msg_extension_number_response(Msg,
                                                  <<>>,
                                                  TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_server_reflection_response_list_services_response(Msg,
                                                           Bin, TrUserData) ->
    SubBin = encode_msg_list_service_response(Msg,
                                              <<>>,
                                              TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_server_reflection_response_error_response(Msg,
                                                   Bin, TrUserData) ->
    SubBin = encode_msg_error_response(Msg,
                                       <<>>,
                                       TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_file_descriptor_response_file_descriptor_proto([Elem
                                                        | Rest],
                                                       Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_type_bytes(id(Elem, TrUserData),
                        Bin2,
                        TrUserData),
    e_field_file_descriptor_response_file_descriptor_proto(Rest,
                                                           Bin3,
                                                           TrUserData);
e_field_file_descriptor_response_file_descriptor_proto([],
                                                       Bin, _TrUserData) ->
    Bin.

e_field_extension_number_response_extension_number(Elems,
                                                   Bin, TrUserData)
    when Elems =/= [] ->
    SubBin =
        e_pfield_extension_number_response_extension_number(Elems,
                                                            <<>>,
                                                            TrUserData),
    Bin2 = <<Bin/binary, 18>>,
    Bin3 = e_varint(byte_size(SubBin), Bin2),
    <<Bin3/binary, SubBin/binary>>;
e_field_extension_number_response_extension_number([],
                                                   Bin, _TrUserData) ->
    Bin.

e_pfield_extension_number_response_extension_number([Value
                                                     | Rest],
                                                    Bin, TrUserData) ->
    Bin2 = e_type_int32(id(Value, TrUserData),
                        Bin,
                        TrUserData),
    e_pfield_extension_number_response_extension_number(Rest,
                                                        Bin2,
                                                        TrUserData);
e_pfield_extension_number_response_extension_number([],
                                                    Bin, _TrUserData) ->
    Bin.

e_mfield_list_service_response_service(Msg, Bin,
                                       TrUserData) ->
    SubBin = encode_msg_service_response(Msg,
                                         <<>>,
                                         TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_list_service_response_service([Elem | Rest],
                                      Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_list_service_response_service(id(Elem,
                                                     TrUserData),
                                                  Bin2,
                                                  TrUserData),
    e_field_list_service_response_service(Rest,
                                          Bin3,
                                          TrUserData);
e_field_list_service_response_service([], Bin,
                                      _TrUserData) ->
    Bin.

-compile({nowarn_unused_function,e_type_sint/3}).
e_type_sint(Value, Bin, _TrUserData) when Value >= 0 ->
    e_varint(Value * 2, Bin);
e_type_sint(Value, Bin, _TrUserData) ->
    e_varint(Value * -2 - 1, Bin).

-compile({nowarn_unused_function,e_type_int32/3}).
e_type_int32(Value, Bin, _TrUserData)
    when 0 =< Value, Value =< 127 ->
    <<Bin/binary, Value>>;
e_type_int32(Value, Bin, _TrUserData) ->
    <<N:64/unsigned-native>> = <<Value:64/signed-native>>,
    e_varint(N, Bin).

-compile({nowarn_unused_function,e_type_int64/3}).
e_type_int64(Value, Bin, _TrUserData)
    when 0 =< Value, Value =< 127 ->
    <<Bin/binary, Value>>;
e_type_int64(Value, Bin, _TrUserData) ->
    <<N:64/unsigned-native>> = <<Value:64/signed-native>>,
    e_varint(N, Bin).

-compile({nowarn_unused_function,e_type_bool/3}).
e_type_bool(true, Bin, _TrUserData) ->
    <<Bin/binary, 1>>;
e_type_bool(false, Bin, _TrUserData) ->
    <<Bin/binary, 0>>;
e_type_bool(1, Bin, _TrUserData) -> <<Bin/binary, 1>>;
e_type_bool(0, Bin, _TrUserData) -> <<Bin/binary, 0>>.

-compile({nowarn_unused_function,e_type_string/3}).
e_type_string(S, Bin, _TrUserData) ->
    Utf8 = unicode:characters_to_binary(S),
    Bin2 = e_varint(byte_size(Utf8), Bin),
    <<Bin2/binary, Utf8/binary>>.

-compile({nowarn_unused_function,e_type_bytes/3}).
e_type_bytes(Bytes, Bin, _TrUserData)
    when is_binary(Bytes) ->
    Bin2 = e_varint(byte_size(Bytes), Bin),
    <<Bin2/binary, Bytes/binary>>;
e_type_bytes(Bytes, Bin, _TrUserData)
    when is_list(Bytes) ->
    BytesBin = iolist_to_binary(Bytes),
    Bin2 = e_varint(byte_size(BytesBin), Bin),
    <<Bin2/binary, BytesBin/binary>>.

-compile({nowarn_unused_function,e_type_fixed32/3}).
e_type_fixed32(Value, Bin, _TrUserData) ->
    <<Bin/binary, Value:32/little>>.

-compile({nowarn_unused_function,e_type_sfixed32/3}).
e_type_sfixed32(Value, Bin, _TrUserData) ->
    <<Bin/binary, Value:32/little-signed>>.

-compile({nowarn_unused_function,e_type_fixed64/3}).
e_type_fixed64(Value, Bin, _TrUserData) ->
    <<Bin/binary, Value:64/little>>.

-compile({nowarn_unused_function,e_type_sfixed64/3}).
e_type_sfixed64(Value, Bin, _TrUserData) ->
    <<Bin/binary, Value:64/little-signed>>.

-compile({nowarn_unused_function,e_type_float/3}).
e_type_float(V, Bin, _) when is_number(V) ->
    <<Bin/binary, V:32/little-float>>;
e_type_float(infinity, Bin, _) ->
    <<Bin/binary, 0:16, 128, 127>>;
e_type_float('-infinity', Bin, _) ->
    <<Bin/binary, 0:16, 128, 255>>;
e_type_float(nan, Bin, _) ->
    <<Bin/binary, 0:16, 192, 127>>.

-compile({nowarn_unused_function,e_type_double/3}).
e_type_double(V, Bin, _) when is_number(V) ->
    <<Bin/binary, V:64/little-float>>;
e_type_double(infinity, Bin, _) ->
    <<Bin/binary, 0:48, 240, 127>>;
e_type_double('-infinity', Bin, _) ->
    <<Bin/binary, 0:48, 240, 255>>;
e_type_double(nan, Bin, _) ->
    <<Bin/binary, 0:48, 248, 127>>.

-compile({nowarn_unused_function,e_varint/3}).
e_varint(N, Bin, _TrUserData) -> e_varint(N, Bin).

-compile({nowarn_unused_function,e_varint/2}).
e_varint(N, Bin) when N =< 127 -> <<Bin/binary, N>>;
e_varint(N, Bin) ->
    Bin2 = <<Bin/binary, (N band 127 bor 128)>>,
    e_varint(N bsr 7, Bin2).

is_empty_string("") -> true;
is_empty_string(<<>>) -> true;
is_empty_string(L) when is_list(L) ->
    not string_has_chars(L);
is_empty_string(B) when is_binary(B) -> false.

string_has_chars([C | _]) when is_integer(C) -> true;
string_has_chars([H | T]) ->
    case string_has_chars(H) of
        true -> true;
        false -> string_has_chars(T)
    end;
string_has_chars(B)
    when is_binary(B), byte_size(B) =/= 0 ->
    true;
string_has_chars(C) when is_integer(C) -> true;
string_has_chars(<<>>) -> false;
string_has_chars([]) -> false.


decode_msg(Bin, MsgName) when is_binary(Bin) ->
    decode_msg(Bin, MsgName, []).

decode_msg(Bin, MsgName, Opts) when is_binary(Bin) ->
    TrUserData = proplists:get_value(user_data, Opts),
    decode_msg_1_catch(Bin, MsgName, TrUserData).

-ifdef('OTP_RELEASE').
decode_msg_1_catch(Bin, MsgName, TrUserData) ->
    try decode_msg_2_doit(MsgName, Bin, TrUserData)
    catch Class:Reason:StackTrace -> error({gpb_error,{decoding_failure, {Bin, MsgName, {Class, Reason, StackTrace}}}})
    end.
-else.
decode_msg_1_catch(Bin, MsgName, TrUserData) ->
    try decode_msg_2_doit(MsgName, Bin, TrUserData)
    catch Class:Reason ->
        StackTrace = erlang:get_stacktrace(),
        error({gpb_error,{decoding_failure, {Bin, MsgName, {Class, Reason, StackTrace}}}})
    end.
-endif.

decode_msg_2_doit(server_reflection_request, Bin,
                  TrUserData) ->
    id(decode_msg_server_reflection_request(Bin,
                                            TrUserData),
       TrUserData);
decode_msg_2_doit(extension_request, Bin, TrUserData) ->
    id(decode_msg_extension_request(Bin, TrUserData),
       TrUserData);
decode_msg_2_doit(server_reflection_response, Bin,
                  TrUserData) ->
    id(decode_msg_server_reflection_response(Bin,
                                             TrUserData),
       TrUserData);
decode_msg_2_doit(file_descriptor_response, Bin,
                  TrUserData) ->
    id(decode_msg_file_descriptor_response(Bin, TrUserData),
       TrUserData);
decode_msg_2_doit(extension_number_response, Bin,
                  TrUserData) ->
    id(decode_msg_extension_number_response(Bin,
                                            TrUserData),
       TrUserData);
decode_msg_2_doit(list_service_response, Bin,
                  TrUserData) ->
    id(decode_msg_list_service_response(Bin, TrUserData),
       TrUserData);
decode_msg_2_doit(service_response, Bin, TrUserData) ->
    id(decode_msg_service_response(Bin, TrUserData),
       TrUserData);
decode_msg_2_doit(error_response, Bin, TrUserData) ->
    id(decode_msg_error_response(Bin, TrUserData),
       TrUserData).



decode_msg_server_reflection_request(Bin, TrUserData) ->
    dfp_read_field_def_server_reflection_request(Bin,
                                                 0,
                                                 0,
                                                 id(<<>>, TrUserData),
                                                 id('$undef', TrUserData),
                                                 TrUserData).

dfp_read_field_def_server_reflection_request(<<10,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_server_reflection_request_host(Rest,
                                           Z1,
                                           Z2,
                                           F@_1,
                                           F@_2,
                                           TrUserData);
dfp_read_field_def_server_reflection_request(<<26,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_server_reflection_request_file_by_filename(Rest,
                                                       Z1,
                                                       Z2,
                                                       F@_1,
                                                       F@_2,
                                                       TrUserData);
dfp_read_field_def_server_reflection_request(<<34,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_server_reflection_request_file_containing_symbol(Rest,
                                                             Z1,
                                                             Z2,
                                                             F@_1,
                                                             F@_2,
                                                             TrUserData);
dfp_read_field_def_server_reflection_request(<<42,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_server_reflection_request_file_containing_extension(Rest,
                                                                Z1,
                                                                Z2,
                                                                F@_1,
                                                                F@_2,
                                                                TrUserData);
dfp_read_field_def_server_reflection_request(<<50,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_server_reflection_request_all_extension_numbers_of_type(Rest,
                                                                    Z1,
                                                                    Z2,
                                                                    F@_1,
                                                                    F@_2,
                                                                    TrUserData);
dfp_read_field_def_server_reflection_request(<<58,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_server_reflection_request_list_services(Rest,
                                                    Z1,
                                                    Z2,
                                                    F@_1,
                                                    F@_2,
                                                    TrUserData);
dfp_read_field_def_server_reflection_request(<<>>, 0, 0,
                                             F@_1, F@_2, _) ->
    S1 = #{host => F@_1},
    if F@_2 == '$undef' -> S1;
       true -> S1#{message_request => F@_2}
    end;
dfp_read_field_def_server_reflection_request(Other, Z1,
                                             Z2, F@_1, F@_2, TrUserData) ->
    dg_read_field_def_server_reflection_request(Other,
                                                Z1,
                                                Z2,
                                                F@_1,
                                                F@_2,
                                                TrUserData).

dg_read_field_def_server_reflection_request(<<1:1, X:7,
                                              Rest/binary>>,
                                            N, Acc, F@_1, F@_2, TrUserData)
    when N < 32 - 7 ->
    dg_read_field_def_server_reflection_request(Rest,
                                                N + 7,
                                                X bsl N + Acc,
                                                F@_1,
                                                F@_2,
                                                TrUserData);
dg_read_field_def_server_reflection_request(<<0:1, X:7,
                                              Rest/binary>>,
                                            N, Acc, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 ->
            d_field_server_reflection_request_host(Rest,
                                                   0,
                                                   0,
                                                   F@_1,
                                                   F@_2,
                                                   TrUserData);
        26 ->
            d_field_server_reflection_request_file_by_filename(Rest,
                                                               0,
                                                               0,
                                                               F@_1,
                                                               F@_2,
                                                               TrUserData);
        34 ->
            d_field_server_reflection_request_file_containing_symbol(Rest,
                                                                     0,
                                                                     0,
                                                                     F@_1,
                                                                     F@_2,
                                                                     TrUserData);
        42 ->
            d_field_server_reflection_request_file_containing_extension(Rest,
                                                                        0,
                                                                        0,
                                                                        F@_1,
                                                                        F@_2,
                                                                        TrUserData);
        50 ->
            d_field_server_reflection_request_all_extension_numbers_of_type(Rest,
                                                                            0,
                                                                            0,
                                                                            F@_1,
                                                                            F@_2,
                                                                            TrUserData);
        58 ->
            d_field_server_reflection_request_list_services(Rest,
                                                            0,
                                                            0,
                                                            F@_1,
                                                            F@_2,
                                                            TrUserData);
        _ ->
            case Key band 7 of
                0 ->
                    skip_varint_server_reflection_request(Rest,
                                                          0,
                                                          0,
                                                          F@_1,
                                                          F@_2,
                                                          TrUserData);
                1 ->
                    skip_64_server_reflection_request(Rest,
                                                      0,
                                                      0,
                                                      F@_1,
                                                      F@_2,
                                                      TrUserData);
                2 ->
                    skip_length_delimited_server_reflection_request(Rest,
                                                                    0,
                                                                    0,
                                                                    F@_1,
                                                                    F@_2,
                                                                    TrUserData);
                3 ->
                    skip_group_server_reflection_request(Rest,
                                                         Key bsr 3,
                                                         0,
                                                         F@_1,
                                                         F@_2,
                                                         TrUserData);
                5 ->
                    skip_32_server_reflection_request(Rest,
                                                      0,
                                                      0,
                                                      F@_1,
                                                      F@_2,
                                                      TrUserData)
            end
    end;
dg_read_field_def_server_reflection_request(<<>>, 0, 0,
                                            F@_1, F@_2, _) ->
    S1 = #{host => F@_1},
    if F@_2 == '$undef' -> S1;
       true -> S1#{message_request => F@_2}
    end.

d_field_server_reflection_request_host(<<1:1, X:7,
                                         Rest/binary>>,
                                       N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    d_field_server_reflection_request_host(Rest,
                                           N + 7,
                                           X bsl N + Acc,
                                           F@_1,
                                           F@_2,
                                           TrUserData);
d_field_server_reflection_request_host(<<0:1, X:7,
                                         Rest/binary>>,
                                       N, Acc, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_server_reflection_request(RestF,
                                                 0,
                                                 0,
                                                 NewFValue,
                                                 F@_2,
                                                 TrUserData).

d_field_server_reflection_request_file_by_filename(<<1:1,
                                                     X:7, Rest/binary>>,
                                                   N, Acc, F@_1, F@_2,
                                                   TrUserData)
    when N < 57 ->
    d_field_server_reflection_request_file_by_filename(Rest,
                                                       N + 7,
                                                       X bsl N + Acc,
                                                       F@_1,
                                                       F@_2,
                                                       TrUserData);
d_field_server_reflection_request_file_by_filename(<<0:1,
                                                     X:7, Rest/binary>>,
                                                   N, Acc, F@_1, _,
                                                   TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_server_reflection_request(RestF,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 id({file_by_filename,
                                                     NewFValue},
                                                    TrUserData),
                                                 TrUserData).

d_field_server_reflection_request_file_containing_symbol(<<1:1,
                                                           X:7, Rest/binary>>,
                                                         N, Acc, F@_1, F@_2,
                                                         TrUserData)
    when N < 57 ->
    d_field_server_reflection_request_file_containing_symbol(Rest,
                                                             N + 7,
                                                             X bsl N + Acc,
                                                             F@_1,
                                                             F@_2,
                                                             TrUserData);
d_field_server_reflection_request_file_containing_symbol(<<0:1,
                                                           X:7, Rest/binary>>,
                                                         N, Acc, F@_1, _,
                                                         TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_server_reflection_request(RestF,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 id({file_containing_symbol,
                                                     NewFValue},
                                                    TrUserData),
                                                 TrUserData).

d_field_server_reflection_request_file_containing_extension(<<1:1,
                                                              X:7,
                                                              Rest/binary>>,
                                                            N, Acc, F@_1, F@_2,
                                                            TrUserData)
    when N < 57 ->
    d_field_server_reflection_request_file_containing_extension(Rest,
                                                                N + 7,
                                                                X bsl N + Acc,
                                                                F@_1,
                                                                F@_2,
                                                                TrUserData);
d_field_server_reflection_request_file_containing_extension(<<0:1,
                                                              X:7,
                                                              Rest/binary>>,
                                                            N, Acc, F@_1, Prev,
                                                            TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bs:Len/binary, Rest2/binary>> = Rest,
                             {id(decode_msg_extension_request(Bs, TrUserData),
                                 TrUserData),
                              Rest2}
                         end,
    dfp_read_field_def_server_reflection_request(RestF,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 case Prev of
                                                     '$undef' ->
                                                         id({file_containing_extension,
                                                             NewFValue},
                                                            TrUserData);
                                                     {file_containing_extension,
                                                      MVPrev} ->
                                                         id({file_containing_extension,
                                                             merge_msg_extension_request(MVPrev,
                                                                                         NewFValue,
                                                                                         TrUserData)},
                                                            TrUserData);
                                                     _ ->
                                                         id({file_containing_extension,
                                                             NewFValue},
                                                            TrUserData)
                                                 end,
                                                 TrUserData).

d_field_server_reflection_request_all_extension_numbers_of_type(<<1:1,
                                                                  X:7,
                                                                  Rest/binary>>,
                                                                N, Acc, F@_1,
                                                                F@_2,
                                                                TrUserData)
    when N < 57 ->
    d_field_server_reflection_request_all_extension_numbers_of_type(Rest,
                                                                    N + 7,
                                                                    X bsl N +
                                                                        Acc,
                                                                    F@_1,
                                                                    F@_2,
                                                                    TrUserData);
d_field_server_reflection_request_all_extension_numbers_of_type(<<0:1,
                                                                  X:7,
                                                                  Rest/binary>>,
                                                                N, Acc, F@_1, _,
                                                                TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_server_reflection_request(RestF,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 id({all_extension_numbers_of_type,
                                                     NewFValue},
                                                    TrUserData),
                                                 TrUserData).

d_field_server_reflection_request_list_services(<<1:1,
                                                  X:7, Rest/binary>>,
                                                N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    d_field_server_reflection_request_list_services(Rest,
                                                    N + 7,
                                                    X bsl N + Acc,
                                                    F@_1,
                                                    F@_2,
                                                    TrUserData);
d_field_server_reflection_request_list_services(<<0:1,
                                                  X:7, Rest/binary>>,
                                                N, Acc, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_server_reflection_request(RestF,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 id({list_services, NewFValue},
                                                    TrUserData),
                                                 TrUserData).

skip_varint_server_reflection_request(<<1:1, _:7,
                                        Rest/binary>>,
                                      Z1, Z2, F@_1, F@_2, TrUserData) ->
    skip_varint_server_reflection_request(Rest,
                                          Z1,
                                          Z2,
                                          F@_1,
                                          F@_2,
                                          TrUserData);
skip_varint_server_reflection_request(<<0:1, _:7,
                                        Rest/binary>>,
                                      Z1, Z2, F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_server_reflection_request(Rest,
                                                 Z1,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

skip_length_delimited_server_reflection_request(<<1:1,
                                                  X:7, Rest/binary>>,
                                                N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    skip_length_delimited_server_reflection_request(Rest,
                                                    N + 7,
                                                    X bsl N + Acc,
                                                    F@_1,
                                                    F@_2,
                                                    TrUserData);
skip_length_delimited_server_reflection_request(<<0:1,
                                                  X:7, Rest/binary>>,
                                                N, Acc, F@_1, F@_2,
                                                TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_server_reflection_request(Rest2,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

skip_group_server_reflection_request(Bin, FNum, Z2,
                                     F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_server_reflection_request(Rest,
                                                 0,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

skip_32_server_reflection_request(<<_:32, Rest/binary>>,
                                  Z1, Z2, F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_server_reflection_request(Rest,
                                                 Z1,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

skip_64_server_reflection_request(<<_:64, Rest/binary>>,
                                  Z1, Z2, F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_server_reflection_request(Rest,
                                                 Z1,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

decode_msg_extension_request(Bin, TrUserData) ->
    dfp_read_field_def_extension_request(Bin,
                                         0,
                                         0,
                                         id(<<>>, TrUserData),
                                         id(0, TrUserData),
                                         TrUserData).

dfp_read_field_def_extension_request(<<10,
                                       Rest/binary>>,
                                     Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_extension_request_containing_type(Rest,
                                              Z1,
                                              Z2,
                                              F@_1,
                                              F@_2,
                                              TrUserData);
dfp_read_field_def_extension_request(<<16,
                                       Rest/binary>>,
                                     Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_extension_request_extension_number(Rest,
                                               Z1,
                                               Z2,
                                               F@_1,
                                               F@_2,
                                               TrUserData);
dfp_read_field_def_extension_request(<<>>, 0, 0, F@_1,
                                     F@_2, _) ->
    #{containing_type => F@_1, extension_number => F@_2};
dfp_read_field_def_extension_request(Other, Z1, Z2,
                                     F@_1, F@_2, TrUserData) ->
    dg_read_field_def_extension_request(Other,
                                        Z1,
                                        Z2,
                                        F@_1,
                                        F@_2,
                                        TrUserData).

dg_read_field_def_extension_request(<<1:1, X:7,
                                      Rest/binary>>,
                                    N, Acc, F@_1, F@_2, TrUserData)
    when N < 32 - 7 ->
    dg_read_field_def_extension_request(Rest,
                                        N + 7,
                                        X bsl N + Acc,
                                        F@_1,
                                        F@_2,
                                        TrUserData);
dg_read_field_def_extension_request(<<0:1, X:7,
                                      Rest/binary>>,
                                    N, Acc, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 ->
            d_field_extension_request_containing_type(Rest,
                                                      0,
                                                      0,
                                                      F@_1,
                                                      F@_2,
                                                      TrUserData);
        16 ->
            d_field_extension_request_extension_number(Rest,
                                                       0,
                                                       0,
                                                       F@_1,
                                                       F@_2,
                                                       TrUserData);
        _ ->
            case Key band 7 of
                0 ->
                    skip_varint_extension_request(Rest,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  F@_2,
                                                  TrUserData);
                1 ->
                    skip_64_extension_request(Rest,
                                              0,
                                              0,
                                              F@_1,
                                              F@_2,
                                              TrUserData);
                2 ->
                    skip_length_delimited_extension_request(Rest,
                                                            0,
                                                            0,
                                                            F@_1,
                                                            F@_2,
                                                            TrUserData);
                3 ->
                    skip_group_extension_request(Rest,
                                                 Key bsr 3,
                                                 0,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData);
                5 ->
                    skip_32_extension_request(Rest,
                                              0,
                                              0,
                                              F@_1,
                                              F@_2,
                                              TrUserData)
            end
    end;
dg_read_field_def_extension_request(<<>>, 0, 0, F@_1,
                                    F@_2, _) ->
    #{containing_type => F@_1, extension_number => F@_2}.

d_field_extension_request_containing_type(<<1:1, X:7,
                                            Rest/binary>>,
                                          N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    d_field_extension_request_containing_type(Rest,
                                              N + 7,
                                              X bsl N + Acc,
                                              F@_1,
                                              F@_2,
                                              TrUserData);
d_field_extension_request_containing_type(<<0:1, X:7,
                                            Rest/binary>>,
                                          N, Acc, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_extension_request(RestF,
                                         0,
                                         0,
                                         NewFValue,
                                         F@_2,
                                         TrUserData).

d_field_extension_request_extension_number(<<1:1, X:7,
                                             Rest/binary>>,
                                           N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    d_field_extension_request_extension_number(Rest,
                                               N + 7,
                                               X bsl N + Acc,
                                               F@_1,
                                               F@_2,
                                               TrUserData);
d_field_extension_request_extension_number(<<0:1, X:7,
                                             Rest/binary>>,
                                           N, Acc, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = {begin
                              <<Res:32/signed-native>> = <<(X bsl N +
                                                                Acc):32/unsigned-native>>,
                              id(Res, TrUserData)
                          end,
                          Rest},
    dfp_read_field_def_extension_request(RestF,
                                         0,
                                         0,
                                         F@_1,
                                         NewFValue,
                                         TrUserData).

skip_varint_extension_request(<<1:1, _:7, Rest/binary>>,
                              Z1, Z2, F@_1, F@_2, TrUserData) ->
    skip_varint_extension_request(Rest,
                                  Z1,
                                  Z2,
                                  F@_1,
                                  F@_2,
                                  TrUserData);
skip_varint_extension_request(<<0:1, _:7, Rest/binary>>,
                              Z1, Z2, F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_extension_request(Rest,
                                         Z1,
                                         Z2,
                                         F@_1,
                                         F@_2,
                                         TrUserData).

skip_length_delimited_extension_request(<<1:1, X:7,
                                          Rest/binary>>,
                                        N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    skip_length_delimited_extension_request(Rest,
                                            N + 7,
                                            X bsl N + Acc,
                                            F@_1,
                                            F@_2,
                                            TrUserData);
skip_length_delimited_extension_request(<<0:1, X:7,
                                          Rest/binary>>,
                                        N, Acc, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_extension_request(Rest2,
                                         0,
                                         0,
                                         F@_1,
                                         F@_2,
                                         TrUserData).

skip_group_extension_request(Bin, FNum, Z2, F@_1, F@_2,
                             TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_extension_request(Rest,
                                         0,
                                         Z2,
                                         F@_1,
                                         F@_2,
                                         TrUserData).

skip_32_extension_request(<<_:32, Rest/binary>>, Z1, Z2,
                          F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_extension_request(Rest,
                                         Z1,
                                         Z2,
                                         F@_1,
                                         F@_2,
                                         TrUserData).

skip_64_extension_request(<<_:64, Rest/binary>>, Z1, Z2,
                          F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_extension_request(Rest,
                                         Z1,
                                         Z2,
                                         F@_1,
                                         F@_2,
                                         TrUserData).

decode_msg_server_reflection_response(Bin,
                                      TrUserData) ->
    dfp_read_field_def_server_reflection_response(Bin,
                                                  0,
                                                  0,
                                                  id(<<>>, TrUserData),
                                                  id('$undef', TrUserData),
                                                  id('$undef', TrUserData),
                                                  TrUserData).

dfp_read_field_def_server_reflection_response(<<10,
                                                Rest/binary>>,
                                              Z1, Z2, F@_1, F@_2, F@_3,
                                              TrUserData) ->
    d_field_server_reflection_response_valid_host(Rest,
                                                  Z1,
                                                  Z2,
                                                  F@_1,
                                                  F@_2,
                                                  F@_3,
                                                  TrUserData);
dfp_read_field_def_server_reflection_response(<<18,
                                                Rest/binary>>,
                                              Z1, Z2, F@_1, F@_2, F@_3,
                                              TrUserData) ->
    d_field_server_reflection_response_original_request(Rest,
                                                        Z1,
                                                        Z2,
                                                        F@_1,
                                                        F@_2,
                                                        F@_3,
                                                        TrUserData);
dfp_read_field_def_server_reflection_response(<<34,
                                                Rest/binary>>,
                                              Z1, Z2, F@_1, F@_2, F@_3,
                                              TrUserData) ->
    d_field_server_reflection_response_file_descriptor_response(Rest,
                                                                Z1,
                                                                Z2,
                                                                F@_1,
                                                                F@_2,
                                                                F@_3,
                                                                TrUserData);
dfp_read_field_def_server_reflection_response(<<42,
                                                Rest/binary>>,
                                              Z1, Z2, F@_1, F@_2, F@_3,
                                              TrUserData) ->
    d_field_server_reflection_response_all_extension_numbers_response(Rest,
                                                                      Z1,
                                                                      Z2,
                                                                      F@_1,
                                                                      F@_2,
                                                                      F@_3,
                                                                      TrUserData);
dfp_read_field_def_server_reflection_response(<<50,
                                                Rest/binary>>,
                                              Z1, Z2, F@_1, F@_2, F@_3,
                                              TrUserData) ->
    d_field_server_reflection_response_list_services_response(Rest,
                                                              Z1,
                                                              Z2,
                                                              F@_1,
                                                              F@_2,
                                                              F@_3,
                                                              TrUserData);
dfp_read_field_def_server_reflection_response(<<58,
                                                Rest/binary>>,
                                              Z1, Z2, F@_1, F@_2, F@_3,
                                              TrUserData) ->
    d_field_server_reflection_response_error_response(Rest,
                                                      Z1,
                                                      Z2,
                                                      F@_1,
                                                      F@_2,
                                                      F@_3,
                                                      TrUserData);
dfp_read_field_def_server_reflection_response(<<>>, 0,
                                              0, F@_1, F@_2, F@_3, _) ->
    S1 = #{valid_host => F@_1},
    S2 = if F@_2 == '$undef' -> S1;
            true -> S1#{original_request => F@_2}
         end,
    if F@_3 == '$undef' -> S2;
       true -> S2#{message_response => F@_3}
    end;
dfp_read_field_def_server_reflection_response(Other, Z1,
                                              Z2, F@_1, F@_2, F@_3,
                                              TrUserData) ->
    dg_read_field_def_server_reflection_response(Other,
                                                 Z1,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 F@_3,
                                                 TrUserData).

dg_read_field_def_server_reflection_response(<<1:1, X:7,
                                               Rest/binary>>,
                                             N, Acc, F@_1, F@_2, F@_3,
                                             TrUserData)
    when N < 32 - 7 ->
    dg_read_field_def_server_reflection_response(Rest,
                                                 N + 7,
                                                 X bsl N + Acc,
                                                 F@_1,
                                                 F@_2,
                                                 F@_3,
                                                 TrUserData);
dg_read_field_def_server_reflection_response(<<0:1, X:7,
                                               Rest/binary>>,
                                             N, Acc, F@_1, F@_2, F@_3,
                                             TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 ->
            d_field_server_reflection_response_valid_host(Rest,
                                                          0,
                                                          0,
                                                          F@_1,
                                                          F@_2,
                                                          F@_3,
                                                          TrUserData);
        18 ->
            d_field_server_reflection_response_original_request(Rest,
                                                                0,
                                                                0,
                                                                F@_1,
                                                                F@_2,
                                                                F@_3,
                                                                TrUserData);
        34 ->
            d_field_server_reflection_response_file_descriptor_response(Rest,
                                                                        0,
                                                                        0,
                                                                        F@_1,
                                                                        F@_2,
                                                                        F@_3,
                                                                        TrUserData);
        42 ->
            d_field_server_reflection_response_all_extension_numbers_response(Rest,
                                                                              0,
                                                                              0,
                                                                              F@_1,
                                                                              F@_2,
                                                                              F@_3,
                                                                              TrUserData);
        50 ->
            d_field_server_reflection_response_list_services_response(Rest,
                                                                      0,
                                                                      0,
                                                                      F@_1,
                                                                      F@_2,
                                                                      F@_3,
                                                                      TrUserData);
        58 ->
            d_field_server_reflection_response_error_response(Rest,
                                                              0,
                                                              0,
                                                              F@_1,
                                                              F@_2,
                                                              F@_3,
                                                              TrUserData);
        _ ->
            case Key band 7 of
                0 ->
                    skip_varint_server_reflection_response(Rest,
                                                           0,
                                                           0,
                                                           F@_1,
                                                           F@_2,
                                                           F@_3,
                                                           TrUserData);
                1 ->
                    skip_64_server_reflection_response(Rest,
                                                       0,
                                                       0,
                                                       F@_1,
                                                       F@_2,
                                                       F@_3,
                                                       TrUserData);
                2 ->
                    skip_length_delimited_server_reflection_response(Rest,
                                                                     0,
                                                                     0,
                                                                     F@_1,
                                                                     F@_2,
                                                                     F@_3,
                                                                     TrUserData);
                3 ->
                    skip_group_server_reflection_response(Rest,
                                                          Key bsr 3,
                                                          0,
                                                          F@_1,
                                                          F@_2,
                                                          F@_3,
                                                          TrUserData);
                5 ->
                    skip_32_server_reflection_response(Rest,
                                                       0,
                                                       0,
                                                       F@_1,
                                                       F@_2,
                                                       F@_3,
                                                       TrUserData)
            end
    end;
dg_read_field_def_server_reflection_response(<<>>, 0, 0,
                                             F@_1, F@_2, F@_3, _) ->
    S1 = #{valid_host => F@_1},
    S2 = if F@_2 == '$undef' -> S1;
            true -> S1#{original_request => F@_2}
         end,
    if F@_3 == '$undef' -> S2;
       true -> S2#{message_response => F@_3}
    end.

d_field_server_reflection_response_valid_host(<<1:1,
                                                X:7, Rest/binary>>,
                                              N, Acc, F@_1, F@_2, F@_3,
                                              TrUserData)
    when N < 57 ->
    d_field_server_reflection_response_valid_host(Rest,
                                                  N + 7,
                                                  X bsl N + Acc,
                                                  F@_1,
                                                  F@_2,
                                                  F@_3,
                                                  TrUserData);
d_field_server_reflection_response_valid_host(<<0:1,
                                                X:7, Rest/binary>>,
                                              N, Acc, _, F@_2, F@_3,
                                              TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_server_reflection_response(RestF,
                                                  0,
                                                  0,
                                                  NewFValue,
                                                  F@_2,
                                                  F@_3,
                                                  TrUserData).

d_field_server_reflection_response_original_request(<<1:1,
                                                      X:7, Rest/binary>>,
                                                    N, Acc, F@_1, F@_2, F@_3,
                                                    TrUserData)
    when N < 57 ->
    d_field_server_reflection_response_original_request(Rest,
                                                        N + 7,
                                                        X bsl N + Acc,
                                                        F@_1,
                                                        F@_2,
                                                        F@_3,
                                                        TrUserData);
d_field_server_reflection_response_original_request(<<0:1,
                                                      X:7, Rest/binary>>,
                                                    N, Acc, F@_1, Prev, F@_3,
                                                    TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bs:Len/binary, Rest2/binary>> = Rest,
                             {id(decode_msg_server_reflection_request(Bs,
                                                                      TrUserData),
                                 TrUserData),
                              Rest2}
                         end,
    dfp_read_field_def_server_reflection_response(RestF,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  if Prev == '$undef' ->
                                                         NewFValue;
                                                     true ->
                                                         merge_msg_server_reflection_request(Prev,
                                                                                             NewFValue,
                                                                                             TrUserData)
                                                  end,
                                                  F@_3,
                                                  TrUserData).

d_field_server_reflection_response_file_descriptor_response(<<1:1,
                                                              X:7,
                                                              Rest/binary>>,
                                                            N, Acc, F@_1, F@_2,
                                                            F@_3, TrUserData)
    when N < 57 ->
    d_field_server_reflection_response_file_descriptor_response(Rest,
                                                                N + 7,
                                                                X bsl N + Acc,
                                                                F@_1,
                                                                F@_2,
                                                                F@_3,
                                                                TrUserData);
d_field_server_reflection_response_file_descriptor_response(<<0:1,
                                                              X:7,
                                                              Rest/binary>>,
                                                            N, Acc, F@_1, F@_2,
                                                            Prev, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bs:Len/binary, Rest2/binary>> = Rest,
                             {id(decode_msg_file_descriptor_response(Bs,
                                                                     TrUserData),
                                 TrUserData),
                              Rest2}
                         end,
    dfp_read_field_def_server_reflection_response(RestF,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  F@_2,
                                                  case Prev of
                                                      '$undef' ->
                                                          id({file_descriptor_response,
                                                              NewFValue},
                                                             TrUserData);
                                                      {file_descriptor_response,
                                                       MVPrev} ->
                                                          id({file_descriptor_response,
                                                              merge_msg_file_descriptor_response(MVPrev,
                                                                                                 NewFValue,
                                                                                                 TrUserData)},
                                                             TrUserData);
                                                      _ ->
                                                          id({file_descriptor_response,
                                                              NewFValue},
                                                             TrUserData)
                                                  end,
                                                  TrUserData).

d_field_server_reflection_response_all_extension_numbers_response(<<1:1,
                                                                    X:7,
                                                                    Rest/binary>>,
                                                                  N, Acc, F@_1,
                                                                  F@_2, F@_3,
                                                                  TrUserData)
    when N < 57 ->
    d_field_server_reflection_response_all_extension_numbers_response(Rest,
                                                                      N + 7,
                                                                      X bsl N +
                                                                          Acc,
                                                                      F@_1,
                                                                      F@_2,
                                                                      F@_3,
                                                                      TrUserData);
d_field_server_reflection_response_all_extension_numbers_response(<<0:1,
                                                                    X:7,
                                                                    Rest/binary>>,
                                                                  N, Acc, F@_1,
                                                                  F@_2, Prev,
                                                                  TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bs:Len/binary, Rest2/binary>> = Rest,
                             {id(decode_msg_extension_number_response(Bs,
                                                                      TrUserData),
                                 TrUserData),
                              Rest2}
                         end,
    dfp_read_field_def_server_reflection_response(RestF,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  F@_2,
                                                  case Prev of
                                                      '$undef' ->
                                                          id({all_extension_numbers_response,
                                                              NewFValue},
                                                             TrUserData);
                                                      {all_extension_numbers_response,
                                                       MVPrev} ->
                                                          id({all_extension_numbers_response,
                                                              merge_msg_extension_number_response(MVPrev,
                                                                                                  NewFValue,
                                                                                                  TrUserData)},
                                                             TrUserData);
                                                      _ ->
                                                          id({all_extension_numbers_response,
                                                              NewFValue},
                                                             TrUserData)
                                                  end,
                                                  TrUserData).

d_field_server_reflection_response_list_services_response(<<1:1,
                                                            X:7, Rest/binary>>,
                                                          N, Acc, F@_1, F@_2,
                                                          F@_3, TrUserData)
    when N < 57 ->
    d_field_server_reflection_response_list_services_response(Rest,
                                                              N + 7,
                                                              X bsl N + Acc,
                                                              F@_1,
                                                              F@_2,
                                                              F@_3,
                                                              TrUserData);
d_field_server_reflection_response_list_services_response(<<0:1,
                                                            X:7, Rest/binary>>,
                                                          N, Acc, F@_1, F@_2,
                                                          Prev, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bs:Len/binary, Rest2/binary>> = Rest,
                             {id(decode_msg_list_service_response(Bs,
                                                                  TrUserData),
                                 TrUserData),
                              Rest2}
                         end,
    dfp_read_field_def_server_reflection_response(RestF,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  F@_2,
                                                  case Prev of
                                                      '$undef' ->
                                                          id({list_services_response,
                                                              NewFValue},
                                                             TrUserData);
                                                      {list_services_response,
                                                       MVPrev} ->
                                                          id({list_services_response,
                                                              merge_msg_list_service_response(MVPrev,
                                                                                              NewFValue,
                                                                                              TrUserData)},
                                                             TrUserData);
                                                      _ ->
                                                          id({list_services_response,
                                                              NewFValue},
                                                             TrUserData)
                                                  end,
                                                  TrUserData).

d_field_server_reflection_response_error_response(<<1:1,
                                                    X:7, Rest/binary>>,
                                                  N, Acc, F@_1, F@_2, F@_3,
                                                  TrUserData)
    when N < 57 ->
    d_field_server_reflection_response_error_response(Rest,
                                                      N + 7,
                                                      X bsl N + Acc,
                                                      F@_1,
                                                      F@_2,
                                                      F@_3,
                                                      TrUserData);
d_field_server_reflection_response_error_response(<<0:1,
                                                    X:7, Rest/binary>>,
                                                  N, Acc, F@_1, F@_2, Prev,
                                                  TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bs:Len/binary, Rest2/binary>> = Rest,
                             {id(decode_msg_error_response(Bs, TrUserData),
                                 TrUserData),
                              Rest2}
                         end,
    dfp_read_field_def_server_reflection_response(RestF,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  F@_2,
                                                  case Prev of
                                                      '$undef' ->
                                                          id({error_response,
                                                              NewFValue},
                                                             TrUserData);
                                                      {error_response,
                                                       MVPrev} ->
                                                          id({error_response,
                                                              merge_msg_error_response(MVPrev,
                                                                                       NewFValue,
                                                                                       TrUserData)},
                                                             TrUserData);
                                                      _ ->
                                                          id({error_response,
                                                              NewFValue},
                                                             TrUserData)
                                                  end,
                                                  TrUserData).

skip_varint_server_reflection_response(<<1:1, _:7,
                                         Rest/binary>>,
                                       Z1, Z2, F@_1, F@_2, F@_3, TrUserData) ->
    skip_varint_server_reflection_response(Rest,
                                           Z1,
                                           Z2,
                                           F@_1,
                                           F@_2,
                                           F@_3,
                                           TrUserData);
skip_varint_server_reflection_response(<<0:1, _:7,
                                         Rest/binary>>,
                                       Z1, Z2, F@_1, F@_2, F@_3, TrUserData) ->
    dfp_read_field_def_server_reflection_response(Rest,
                                                  Z1,
                                                  Z2,
                                                  F@_1,
                                                  F@_2,
                                                  F@_3,
                                                  TrUserData).

skip_length_delimited_server_reflection_response(<<1:1,
                                                   X:7, Rest/binary>>,
                                                 N, Acc, F@_1, F@_2, F@_3,
                                                 TrUserData)
    when N < 57 ->
    skip_length_delimited_server_reflection_response(Rest,
                                                     N + 7,
                                                     X bsl N + Acc,
                                                     F@_1,
                                                     F@_2,
                                                     F@_3,
                                                     TrUserData);
skip_length_delimited_server_reflection_response(<<0:1,
                                                   X:7, Rest/binary>>,
                                                 N, Acc, F@_1, F@_2, F@_3,
                                                 TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_server_reflection_response(Rest2,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  F@_2,
                                                  F@_3,
                                                  TrUserData).

skip_group_server_reflection_response(Bin, FNum, Z2,
                                      F@_1, F@_2, F@_3, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_server_reflection_response(Rest,
                                                  0,
                                                  Z2,
                                                  F@_1,
                                                  F@_2,
                                                  F@_3,
                                                  TrUserData).

skip_32_server_reflection_response(<<_:32,
                                     Rest/binary>>,
                                   Z1, Z2, F@_1, F@_2, F@_3, TrUserData) ->
    dfp_read_field_def_server_reflection_response(Rest,
                                                  Z1,
                                                  Z2,
                                                  F@_1,
                                                  F@_2,
                                                  F@_3,
                                                  TrUserData).

skip_64_server_reflection_response(<<_:64,
                                     Rest/binary>>,
                                   Z1, Z2, F@_1, F@_2, F@_3, TrUserData) ->
    dfp_read_field_def_server_reflection_response(Rest,
                                                  Z1,
                                                  Z2,
                                                  F@_1,
                                                  F@_2,
                                                  F@_3,
                                                  TrUserData).

decode_msg_file_descriptor_response(Bin, TrUserData) ->
    dfp_read_field_def_file_descriptor_response(Bin,
                                                0,
                                                0,
                                                id([], TrUserData),
                                                TrUserData).

dfp_read_field_def_file_descriptor_response(<<10,
                                              Rest/binary>>,
                                            Z1, Z2, F@_1, TrUserData) ->
    d_field_file_descriptor_response_file_descriptor_proto(Rest,
                                                           Z1,
                                                           Z2,
                                                           F@_1,
                                                           TrUserData);
dfp_read_field_def_file_descriptor_response(<<>>, 0, 0,
                                            R1, TrUserData) ->
    #{file_descriptor_proto =>
          lists_reverse(R1, TrUserData)};
dfp_read_field_def_file_descriptor_response(Other, Z1,
                                            Z2, F@_1, TrUserData) ->
    dg_read_field_def_file_descriptor_response(Other,
                                               Z1,
                                               Z2,
                                               F@_1,
                                               TrUserData).

dg_read_field_def_file_descriptor_response(<<1:1, X:7,
                                             Rest/binary>>,
                                           N, Acc, F@_1, TrUserData)
    when N < 32 - 7 ->
    dg_read_field_def_file_descriptor_response(Rest,
                                               N + 7,
                                               X bsl N + Acc,
                                               F@_1,
                                               TrUserData);
dg_read_field_def_file_descriptor_response(<<0:1, X:7,
                                             Rest/binary>>,
                                           N, Acc, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 ->
            d_field_file_descriptor_response_file_descriptor_proto(Rest,
                                                                   0,
                                                                   0,
                                                                   F@_1,
                                                                   TrUserData);
        _ ->
            case Key band 7 of
                0 ->
                    skip_varint_file_descriptor_response(Rest,
                                                         0,
                                                         0,
                                                         F@_1,
                                                         TrUserData);
                1 ->
                    skip_64_file_descriptor_response(Rest,
                                                     0,
                                                     0,
                                                     F@_1,
                                                     TrUserData);
                2 ->
                    skip_length_delimited_file_descriptor_response(Rest,
                                                                   0,
                                                                   0,
                                                                   F@_1,
                                                                   TrUserData);
                3 ->
                    skip_group_file_descriptor_response(Rest,
                                                        Key bsr 3,
                                                        0,
                                                        F@_1,
                                                        TrUserData);
                5 ->
                    skip_32_file_descriptor_response(Rest,
                                                     0,
                                                     0,
                                                     F@_1,
                                                     TrUserData)
            end
    end;
dg_read_field_def_file_descriptor_response(<<>>, 0, 0,
                                           R1, TrUserData) ->
    #{file_descriptor_proto =>
          lists_reverse(R1, TrUserData)}.

d_field_file_descriptor_response_file_descriptor_proto(<<1:1,
                                                         X:7, Rest/binary>>,
                                                       N, Acc, F@_1, TrUserData)
    when N < 57 ->
    d_field_file_descriptor_response_file_descriptor_proto(Rest,
                                                           N + 7,
                                                           X bsl N + Acc,
                                                           F@_1,
                                                           TrUserData);
d_field_file_descriptor_response_file_descriptor_proto(<<0:1,
                                                         X:7, Rest/binary>>,
                                                       N, Acc, Prev,
                                                       TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_file_descriptor_response(RestF,
                                                0,
                                                0,
                                                cons(NewFValue,
                                                     Prev,
                                                     TrUserData),
                                                TrUserData).

skip_varint_file_descriptor_response(<<1:1, _:7,
                                       Rest/binary>>,
                                     Z1, Z2, F@_1, TrUserData) ->
    skip_varint_file_descriptor_response(Rest,
                                         Z1,
                                         Z2,
                                         F@_1,
                                         TrUserData);
skip_varint_file_descriptor_response(<<0:1, _:7,
                                       Rest/binary>>,
                                     Z1, Z2, F@_1, TrUserData) ->
    dfp_read_field_def_file_descriptor_response(Rest,
                                                Z1,
                                                Z2,
                                                F@_1,
                                                TrUserData).

skip_length_delimited_file_descriptor_response(<<1:1,
                                                 X:7, Rest/binary>>,
                                               N, Acc, F@_1, TrUserData)
    when N < 57 ->
    skip_length_delimited_file_descriptor_response(Rest,
                                                   N + 7,
                                                   X bsl N + Acc,
                                                   F@_1,
                                                   TrUserData);
skip_length_delimited_file_descriptor_response(<<0:1,
                                                 X:7, Rest/binary>>,
                                               N, Acc, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_file_descriptor_response(Rest2,
                                                0,
                                                0,
                                                F@_1,
                                                TrUserData).

skip_group_file_descriptor_response(Bin, FNum, Z2, F@_1,
                                    TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_file_descriptor_response(Rest,
                                                0,
                                                Z2,
                                                F@_1,
                                                TrUserData).

skip_32_file_descriptor_response(<<_:32, Rest/binary>>,
                                 Z1, Z2, F@_1, TrUserData) ->
    dfp_read_field_def_file_descriptor_response(Rest,
                                                Z1,
                                                Z2,
                                                F@_1,
                                                TrUserData).

skip_64_file_descriptor_response(<<_:64, Rest/binary>>,
                                 Z1, Z2, F@_1, TrUserData) ->
    dfp_read_field_def_file_descriptor_response(Rest,
                                                Z1,
                                                Z2,
                                                F@_1,
                                                TrUserData).

decode_msg_extension_number_response(Bin, TrUserData) ->
    dfp_read_field_def_extension_number_response(Bin,
                                                 0,
                                                 0,
                                                 id(<<>>, TrUserData),
                                                 id([], TrUserData),
                                                 TrUserData).

dfp_read_field_def_extension_number_response(<<10,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_extension_number_response_base_type_name(Rest,
                                                     Z1,
                                                     Z2,
                                                     F@_1,
                                                     F@_2,
                                                     TrUserData);
dfp_read_field_def_extension_number_response(<<18,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_pfield_extension_number_response_extension_number(Rest,
                                                        Z1,
                                                        Z2,
                                                        F@_1,
                                                        F@_2,
                                                        TrUserData);
dfp_read_field_def_extension_number_response(<<16,
                                               Rest/binary>>,
                                             Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_extension_number_response_extension_number(Rest,
                                                       Z1,
                                                       Z2,
                                                       F@_1,
                                                       F@_2,
                                                       TrUserData);
dfp_read_field_def_extension_number_response(<<>>, 0, 0,
                                             F@_1, R1, TrUserData) ->
    #{base_type_name => F@_1,
      extension_number => lists_reverse(R1, TrUserData)};
dfp_read_field_def_extension_number_response(Other, Z1,
                                             Z2, F@_1, F@_2, TrUserData) ->
    dg_read_field_def_extension_number_response(Other,
                                                Z1,
                                                Z2,
                                                F@_1,
                                                F@_2,
                                                TrUserData).

dg_read_field_def_extension_number_response(<<1:1, X:7,
                                              Rest/binary>>,
                                            N, Acc, F@_1, F@_2, TrUserData)
    when N < 32 - 7 ->
    dg_read_field_def_extension_number_response(Rest,
                                                N + 7,
                                                X bsl N + Acc,
                                                F@_1,
                                                F@_2,
                                                TrUserData);
dg_read_field_def_extension_number_response(<<0:1, X:7,
                                              Rest/binary>>,
                                            N, Acc, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 ->
            d_field_extension_number_response_base_type_name(Rest,
                                                             0,
                                                             0,
                                                             F@_1,
                                                             F@_2,
                                                             TrUserData);
        18 ->
            d_pfield_extension_number_response_extension_number(Rest,
                                                                0,
                                                                0,
                                                                F@_1,
                                                                F@_2,
                                                                TrUserData);
        16 ->
            d_field_extension_number_response_extension_number(Rest,
                                                               0,
                                                               0,
                                                               F@_1,
                                                               F@_2,
                                                               TrUserData);
        _ ->
            case Key band 7 of
                0 ->
                    skip_varint_extension_number_response(Rest,
                                                          0,
                                                          0,
                                                          F@_1,
                                                          F@_2,
                                                          TrUserData);
                1 ->
                    skip_64_extension_number_response(Rest,
                                                      0,
                                                      0,
                                                      F@_1,
                                                      F@_2,
                                                      TrUserData);
                2 ->
                    skip_length_delimited_extension_number_response(Rest,
                                                                    0,
                                                                    0,
                                                                    F@_1,
                                                                    F@_2,
                                                                    TrUserData);
                3 ->
                    skip_group_extension_number_response(Rest,
                                                         Key bsr 3,
                                                         0,
                                                         F@_1,
                                                         F@_2,
                                                         TrUserData);
                5 ->
                    skip_32_extension_number_response(Rest,
                                                      0,
                                                      0,
                                                      F@_1,
                                                      F@_2,
                                                      TrUserData)
            end
    end;
dg_read_field_def_extension_number_response(<<>>, 0, 0,
                                            F@_1, R1, TrUserData) ->
    #{base_type_name => F@_1,
      extension_number => lists_reverse(R1, TrUserData)}.

d_field_extension_number_response_base_type_name(<<1:1,
                                                   X:7, Rest/binary>>,
                                                 N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    d_field_extension_number_response_base_type_name(Rest,
                                                     N + 7,
                                                     X bsl N + Acc,
                                                     F@_1,
                                                     F@_2,
                                                     TrUserData);
d_field_extension_number_response_base_type_name(<<0:1,
                                                   X:7, Rest/binary>>,
                                                 N, Acc, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_extension_number_response(RestF,
                                                 0,
                                                 0,
                                                 NewFValue,
                                                 F@_2,
                                                 TrUserData).

d_field_extension_number_response_extension_number(<<1:1,
                                                     X:7, Rest/binary>>,
                                                   N, Acc, F@_1, F@_2,
                                                   TrUserData)
    when N < 57 ->
    d_field_extension_number_response_extension_number(Rest,
                                                       N + 7,
                                                       X bsl N + Acc,
                                                       F@_1,
                                                       F@_2,
                                                       TrUserData);
d_field_extension_number_response_extension_number(<<0:1,
                                                     X:7, Rest/binary>>,
                                                   N, Acc, F@_1, Prev,
                                                   TrUserData) ->
    {NewFValue, RestF} = {begin
                              <<Res:32/signed-native>> = <<(X bsl N +
                                                                Acc):32/unsigned-native>>,
                              id(Res, TrUserData)
                          end,
                          Rest},
    dfp_read_field_def_extension_number_response(RestF,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 cons(NewFValue,
                                                      Prev,
                                                      TrUserData),
                                                 TrUserData).

d_pfield_extension_number_response_extension_number(<<1:1,
                                                      X:7, Rest/binary>>,
                                                    N, Acc, F@_1, F@_2,
                                                    TrUserData)
    when N < 57 ->
    d_pfield_extension_number_response_extension_number(Rest,
                                                        N + 7,
                                                        X bsl N + Acc,
                                                        F@_1,
                                                        F@_2,
                                                        TrUserData);
d_pfield_extension_number_response_extension_number(<<0:1,
                                                      X:7, Rest/binary>>,
                                                    N, Acc, F@_1, E,
                                                    TrUserData) ->
    Len = X bsl N + Acc,
    <<PackedBytes:Len/binary, Rest2/binary>> = Rest,
    NewSeq =
        d_packed_field_extension_number_response_extension_number(PackedBytes,
                                                                  0,
                                                                  0,
                                                                  E,
                                                                  TrUserData),
    dfp_read_field_def_extension_number_response(Rest2,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 NewSeq,
                                                 TrUserData).

d_packed_field_extension_number_response_extension_number(<<1:1,
                                                            X:7, Rest/binary>>,
                                                          N, Acc, AccSeq,
                                                          TrUserData)
    when N < 57 ->
    d_packed_field_extension_number_response_extension_number(Rest,
                                                              N + 7,
                                                              X bsl N + Acc,
                                                              AccSeq,
                                                              TrUserData);
d_packed_field_extension_number_response_extension_number(<<0:1,
                                                            X:7, Rest/binary>>,
                                                          N, Acc, AccSeq,
                                                          TrUserData) ->
    {NewFValue, RestF} = {begin
                              <<Res:32/signed-native>> = <<(X bsl N +
                                                                Acc):32/unsigned-native>>,
                              id(Res, TrUserData)
                          end,
                          Rest},
    d_packed_field_extension_number_response_extension_number(RestF,
                                                              0,
                                                              0,
                                                              [NewFValue
                                                               | AccSeq],
                                                              TrUserData);
d_packed_field_extension_number_response_extension_number(<<>>,
                                                          0, 0, AccSeq, _) ->
    AccSeq.

skip_varint_extension_number_response(<<1:1, _:7,
                                        Rest/binary>>,
                                      Z1, Z2, F@_1, F@_2, TrUserData) ->
    skip_varint_extension_number_response(Rest,
                                          Z1,
                                          Z2,
                                          F@_1,
                                          F@_2,
                                          TrUserData);
skip_varint_extension_number_response(<<0:1, _:7,
                                        Rest/binary>>,
                                      Z1, Z2, F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_extension_number_response(Rest,
                                                 Z1,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

skip_length_delimited_extension_number_response(<<1:1,
                                                  X:7, Rest/binary>>,
                                                N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    skip_length_delimited_extension_number_response(Rest,
                                                    N + 7,
                                                    X bsl N + Acc,
                                                    F@_1,
                                                    F@_2,
                                                    TrUserData);
skip_length_delimited_extension_number_response(<<0:1,
                                                  X:7, Rest/binary>>,
                                                N, Acc, F@_1, F@_2,
                                                TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_extension_number_response(Rest2,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

skip_group_extension_number_response(Bin, FNum, Z2,
                                     F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_extension_number_response(Rest,
                                                 0,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

skip_32_extension_number_response(<<_:32, Rest/binary>>,
                                  Z1, Z2, F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_extension_number_response(Rest,
                                                 Z1,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

skip_64_extension_number_response(<<_:64, Rest/binary>>,
                                  Z1, Z2, F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_extension_number_response(Rest,
                                                 Z1,
                                                 Z2,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData).

decode_msg_list_service_response(Bin, TrUserData) ->
    dfp_read_field_def_list_service_response(Bin,
                                             0,
                                             0,
                                             id([], TrUserData),
                                             TrUserData).

dfp_read_field_def_list_service_response(<<10,
                                           Rest/binary>>,
                                         Z1, Z2, F@_1, TrUserData) ->
    d_field_list_service_response_service(Rest,
                                          Z1,
                                          Z2,
                                          F@_1,
                                          TrUserData);
dfp_read_field_def_list_service_response(<<>>, 0, 0, R1,
                                         TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{service => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_list_service_response(Other, Z1, Z2,
                                         F@_1, TrUserData) ->
    dg_read_field_def_list_service_response(Other,
                                            Z1,
                                            Z2,
                                            F@_1,
                                            TrUserData).

dg_read_field_def_list_service_response(<<1:1, X:7,
                                          Rest/binary>>,
                                        N, Acc, F@_1, TrUserData)
    when N < 32 - 7 ->
    dg_read_field_def_list_service_response(Rest,
                                            N + 7,
                                            X bsl N + Acc,
                                            F@_1,
                                            TrUserData);
dg_read_field_def_list_service_response(<<0:1, X:7,
                                          Rest/binary>>,
                                        N, Acc, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 ->
            d_field_list_service_response_service(Rest,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  TrUserData);
        _ ->
            case Key band 7 of
                0 ->
                    skip_varint_list_service_response(Rest,
                                                      0,
                                                      0,
                                                      F@_1,
                                                      TrUserData);
                1 ->
                    skip_64_list_service_response(Rest,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  TrUserData);
                2 ->
                    skip_length_delimited_list_service_response(Rest,
                                                                0,
                                                                0,
                                                                F@_1,
                                                                TrUserData);
                3 ->
                    skip_group_list_service_response(Rest,
                                                     Key bsr 3,
                                                     0,
                                                     F@_1,
                                                     TrUserData);
                5 ->
                    skip_32_list_service_response(Rest,
                                                  0,
                                                  0,
                                                  F@_1,
                                                  TrUserData)
            end
    end;
dg_read_field_def_list_service_response(<<>>, 0, 0, R1,
                                        TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{service => lists_reverse(R1, TrUserData)}
    end.

d_field_list_service_response_service(<<1:1, X:7,
                                        Rest/binary>>,
                                      N, Acc, F@_1, TrUserData)
    when N < 57 ->
    d_field_list_service_response_service(Rest,
                                          N + 7,
                                          X bsl N + Acc,
                                          F@_1,
                                          TrUserData);
d_field_list_service_response_service(<<0:1, X:7,
                                        Rest/binary>>,
                                      N, Acc, Prev, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bs:Len/binary, Rest2/binary>> = Rest,
                             {id(decode_msg_service_response(Bs, TrUserData),
                                 TrUserData),
                              Rest2}
                         end,
    dfp_read_field_def_list_service_response(RestF,
                                             0,
                                             0,
                                             cons(NewFValue, Prev, TrUserData),
                                             TrUserData).

skip_varint_list_service_response(<<1:1, _:7,
                                    Rest/binary>>,
                                  Z1, Z2, F@_1, TrUserData) ->
    skip_varint_list_service_response(Rest,
                                      Z1,
                                      Z2,
                                      F@_1,
                                      TrUserData);
skip_varint_list_service_response(<<0:1, _:7,
                                    Rest/binary>>,
                                  Z1, Z2, F@_1, TrUserData) ->
    dfp_read_field_def_list_service_response(Rest,
                                             Z1,
                                             Z2,
                                             F@_1,
                                             TrUserData).

skip_length_delimited_list_service_response(<<1:1, X:7,
                                              Rest/binary>>,
                                            N, Acc, F@_1, TrUserData)
    when N < 57 ->
    skip_length_delimited_list_service_response(Rest,
                                                N + 7,
                                                X bsl N + Acc,
                                                F@_1,
                                                TrUserData);
skip_length_delimited_list_service_response(<<0:1, X:7,
                                              Rest/binary>>,
                                            N, Acc, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_list_service_response(Rest2,
                                             0,
                                             0,
                                             F@_1,
                                             TrUserData).

skip_group_list_service_response(Bin, FNum, Z2, F@_1,
                                 TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_list_service_response(Rest,
                                             0,
                                             Z2,
                                             F@_1,
                                             TrUserData).

skip_32_list_service_response(<<_:32, Rest/binary>>, Z1,
                              Z2, F@_1, TrUserData) ->
    dfp_read_field_def_list_service_response(Rest,
                                             Z1,
                                             Z2,
                                             F@_1,
                                             TrUserData).

skip_64_list_service_response(<<_:64, Rest/binary>>, Z1,
                              Z2, F@_1, TrUserData) ->
    dfp_read_field_def_list_service_response(Rest,
                                             Z1,
                                             Z2,
                                             F@_1,
                                             TrUserData).

decode_msg_service_response(Bin, TrUserData) ->
    dfp_read_field_def_service_response(Bin,
                                        0,
                                        0,
                                        id(<<>>, TrUserData),
                                        TrUserData).

dfp_read_field_def_service_response(<<10, Rest/binary>>,
                                    Z1, Z2, F@_1, TrUserData) ->
    d_field_service_response_name(Rest,
                                  Z1,
                                  Z2,
                                  F@_1,
                                  TrUserData);
dfp_read_field_def_service_response(<<>>, 0, 0, F@_1,
                                    _) ->
    #{name => F@_1};
dfp_read_field_def_service_response(Other, Z1, Z2, F@_1,
                                    TrUserData) ->
    dg_read_field_def_service_response(Other,
                                       Z1,
                                       Z2,
                                       F@_1,
                                       TrUserData).

dg_read_field_def_service_response(<<1:1, X:7,
                                     Rest/binary>>,
                                   N, Acc, F@_1, TrUserData)
    when N < 32 - 7 ->
    dg_read_field_def_service_response(Rest,
                                       N + 7,
                                       X bsl N + Acc,
                                       F@_1,
                                       TrUserData);
dg_read_field_def_service_response(<<0:1, X:7,
                                     Rest/binary>>,
                                   N, Acc, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 ->
            d_field_service_response_name(Rest,
                                          0,
                                          0,
                                          F@_1,
                                          TrUserData);
        _ ->
            case Key band 7 of
                0 ->
                    skip_varint_service_response(Rest,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 TrUserData);
                1 ->
                    skip_64_service_response(Rest, 0, 0, F@_1, TrUserData);
                2 ->
                    skip_length_delimited_service_response(Rest,
                                                           0,
                                                           0,
                                                           F@_1,
                                                           TrUserData);
                3 ->
                    skip_group_service_response(Rest,
                                                Key bsr 3,
                                                0,
                                                F@_1,
                                                TrUserData);
                5 ->
                    skip_32_service_response(Rest, 0, 0, F@_1, TrUserData)
            end
    end;
dg_read_field_def_service_response(<<>>, 0, 0, F@_1,
                                   _) ->
    #{name => F@_1}.

d_field_service_response_name(<<1:1, X:7, Rest/binary>>,
                              N, Acc, F@_1, TrUserData)
    when N < 57 ->
    d_field_service_response_name(Rest,
                                  N + 7,
                                  X bsl N + Acc,
                                  F@_1,
                                  TrUserData);
d_field_service_response_name(<<0:1, X:7, Rest/binary>>,
                              N, Acc, _, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_service_response(RestF,
                                        0,
                                        0,
                                        NewFValue,
                                        TrUserData).

skip_varint_service_response(<<1:1, _:7, Rest/binary>>,
                             Z1, Z2, F@_1, TrUserData) ->
    skip_varint_service_response(Rest,
                                 Z1,
                                 Z2,
                                 F@_1,
                                 TrUserData);
skip_varint_service_response(<<0:1, _:7, Rest/binary>>,
                             Z1, Z2, F@_1, TrUserData) ->
    dfp_read_field_def_service_response(Rest,
                                        Z1,
                                        Z2,
                                        F@_1,
                                        TrUserData).

skip_length_delimited_service_response(<<1:1, X:7,
                                         Rest/binary>>,
                                       N, Acc, F@_1, TrUserData)
    when N < 57 ->
    skip_length_delimited_service_response(Rest,
                                           N + 7,
                                           X bsl N + Acc,
                                           F@_1,
                                           TrUserData);
skip_length_delimited_service_response(<<0:1, X:7,
                                         Rest/binary>>,
                                       N, Acc, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_service_response(Rest2,
                                        0,
                                        0,
                                        F@_1,
                                        TrUserData).

skip_group_service_response(Bin, FNum, Z2, F@_1,
                            TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_service_response(Rest,
                                        0,
                                        Z2,
                                        F@_1,
                                        TrUserData).

skip_32_service_response(<<_:32, Rest/binary>>, Z1, Z2,
                         F@_1, TrUserData) ->
    dfp_read_field_def_service_response(Rest,
                                        Z1,
                                        Z2,
                                        F@_1,
                                        TrUserData).

skip_64_service_response(<<_:64, Rest/binary>>, Z1, Z2,
                         F@_1, TrUserData) ->
    dfp_read_field_def_service_response(Rest,
                                        Z1,
                                        Z2,
                                        F@_1,
                                        TrUserData).

decode_msg_error_response(Bin, TrUserData) ->
    dfp_read_field_def_error_response(Bin,
                                      0,
                                      0,
                                      id(0, TrUserData),
                                      id(<<>>, TrUserData),
                                      TrUserData).

dfp_read_field_def_error_response(<<8, Rest/binary>>,
                                  Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_error_response_error_code(Rest,
                                      Z1,
                                      Z2,
                                      F@_1,
                                      F@_2,
                                      TrUserData);
dfp_read_field_def_error_response(<<18, Rest/binary>>,
                                  Z1, Z2, F@_1, F@_2, TrUserData) ->
    d_field_error_response_error_message(Rest,
                                         Z1,
                                         Z2,
                                         F@_1,
                                         F@_2,
                                         TrUserData);
dfp_read_field_def_error_response(<<>>, 0, 0, F@_1,
                                  F@_2, _) ->
    #{error_code => F@_1, error_message => F@_2};
dfp_read_field_def_error_response(Other, Z1, Z2, F@_1,
                                  F@_2, TrUserData) ->
    dg_read_field_def_error_response(Other,
                                     Z1,
                                     Z2,
                                     F@_1,
                                     F@_2,
                                     TrUserData).

dg_read_field_def_error_response(<<1:1, X:7,
                                   Rest/binary>>,
                                 N, Acc, F@_1, F@_2, TrUserData)
    when N < 32 - 7 ->
    dg_read_field_def_error_response(Rest,
                                     N + 7,
                                     X bsl N + Acc,
                                     F@_1,
                                     F@_2,
                                     TrUserData);
dg_read_field_def_error_response(<<0:1, X:7,
                                   Rest/binary>>,
                                 N, Acc, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        8 ->
            d_field_error_response_error_code(Rest,
                                              0,
                                              0,
                                              F@_1,
                                              F@_2,
                                              TrUserData);
        18 ->
            d_field_error_response_error_message(Rest,
                                                 0,
                                                 0,
                                                 F@_1,
                                                 F@_2,
                                                 TrUserData);
        _ ->
            case Key band 7 of
                0 ->
                    skip_varint_error_response(Rest,
                                               0,
                                               0,
                                               F@_1,
                                               F@_2,
                                               TrUserData);
                1 ->
                    skip_64_error_response(Rest,
                                           0,
                                           0,
                                           F@_1,
                                           F@_2,
                                           TrUserData);
                2 ->
                    skip_length_delimited_error_response(Rest,
                                                         0,
                                                         0,
                                                         F@_1,
                                                         F@_2,
                                                         TrUserData);
                3 ->
                    skip_group_error_response(Rest,
                                              Key bsr 3,
                                              0,
                                              F@_1,
                                              F@_2,
                                              TrUserData);
                5 ->
                    skip_32_error_response(Rest,
                                           0,
                                           0,
                                           F@_1,
                                           F@_2,
                                           TrUserData)
            end
    end;
dg_read_field_def_error_response(<<>>, 0, 0, F@_1, F@_2,
                                 _) ->
    #{error_code => F@_1, error_message => F@_2}.

d_field_error_response_error_code(<<1:1, X:7,
                                    Rest/binary>>,
                                  N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    d_field_error_response_error_code(Rest,
                                      N + 7,
                                      X bsl N + Acc,
                                      F@_1,
                                      F@_2,
                                      TrUserData);
d_field_error_response_error_code(<<0:1, X:7,
                                    Rest/binary>>,
                                  N, Acc, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = {begin
                              <<Res:32/signed-native>> = <<(X bsl N +
                                                                Acc):32/unsigned-native>>,
                              id(Res, TrUserData)
                          end,
                          Rest},
    dfp_read_field_def_error_response(RestF,
                                      0,
                                      0,
                                      NewFValue,
                                      F@_2,
                                      TrUserData).

d_field_error_response_error_message(<<1:1, X:7,
                                       Rest/binary>>,
                                     N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    d_field_error_response_error_message(Rest,
                                         N + 7,
                                         X bsl N + Acc,
                                         F@_1,
                                         F@_2,
                                         TrUserData);
d_field_error_response_error_message(<<0:1, X:7,
                                       Rest/binary>>,
                                     N, Acc, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = begin
                             Len = X bsl N + Acc,
                             <<Bytes:Len/binary, Rest2/binary>> = Rest,
                             {id(binary:copy(Bytes), TrUserData), Rest2}
                         end,
    dfp_read_field_def_error_response(RestF,
                                      0,
                                      0,
                                      F@_1,
                                      NewFValue,
                                      TrUserData).

skip_varint_error_response(<<1:1, _:7, Rest/binary>>,
                           Z1, Z2, F@_1, F@_2, TrUserData) ->
    skip_varint_error_response(Rest,
                               Z1,
                               Z2,
                               F@_1,
                               F@_2,
                               TrUserData);
skip_varint_error_response(<<0:1, _:7, Rest/binary>>,
                           Z1, Z2, F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_error_response(Rest,
                                      Z1,
                                      Z2,
                                      F@_1,
                                      F@_2,
                                      TrUserData).

skip_length_delimited_error_response(<<1:1, X:7,
                                       Rest/binary>>,
                                     N, Acc, F@_1, F@_2, TrUserData)
    when N < 57 ->
    skip_length_delimited_error_response(Rest,
                                         N + 7,
                                         X bsl N + Acc,
                                         F@_1,
                                         F@_2,
                                         TrUserData);
skip_length_delimited_error_response(<<0:1, X:7,
                                       Rest/binary>>,
                                     N, Acc, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_error_response(Rest2,
                                      0,
                                      0,
                                      F@_1,
                                      F@_2,
                                      TrUserData).

skip_group_error_response(Bin, FNum, Z2, F@_1, F@_2,
                          TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_error_response(Rest,
                                      0,
                                      Z2,
                                      F@_1,
                                      F@_2,
                                      TrUserData).

skip_32_error_response(<<_:32, Rest/binary>>, Z1, Z2,
                       F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_error_response(Rest,
                                      Z1,
                                      Z2,
                                      F@_1,
                                      F@_2,
                                      TrUserData).

skip_64_error_response(<<_:64, Rest/binary>>, Z1, Z2,
                       F@_1, F@_2, TrUserData) ->
    dfp_read_field_def_error_response(Rest,
                                      Z1,
                                      Z2,
                                      F@_1,
                                      F@_2,
                                      TrUserData).

read_group(Bin, FieldNum) ->
    {NumBytes, EndTagLen} = read_gr_b(Bin, 0, 0, 0, 0, FieldNum),
    <<Group:NumBytes/binary, _:EndTagLen/binary, Rest/binary>> = Bin,
    {Group, Rest}.

%% Like skipping over fields, but record the total length,
%% Each field is <(FieldNum bsl 3) bor FieldType> ++ <FieldValue>
%% Record the length because varints may be non-optimally encoded.
%%
%% Groups can be nested, but assume the same FieldNum cannot be nested
%% because group field numbers are shared with the rest of the fields
%% numbers. Thus we can search just for an group-end with the same
%% field number.
%%
%% (The only time the same group field number could occur would
%% be in a nested sub message, but then it would be inside a
%% length-delimited entry, which we skip-read by length.)
read_gr_b(<<1:1, X:7, Tl/binary>>, N, Acc, NumBytes, TagLen, FieldNum)
  when N < (32-7) ->
    read_gr_b(Tl, N+7, X bsl N + Acc, NumBytes, TagLen+1, FieldNum);
read_gr_b(<<0:1, X:7, Tl/binary>>, N, Acc, NumBytes, TagLen,
          FieldNum) ->
    Key = X bsl N + Acc,
    TagLen1 = TagLen + 1,
    case {Key bsr 3, Key band 7} of
        {FieldNum, 4} -> % 4 = group_end
            {NumBytes, TagLen1};
        {_, 0} -> % 0 = varint
            read_gr_vi(Tl, 0, NumBytes + TagLen1, FieldNum);
        {_, 1} -> % 1 = bits64
            <<_:64, Tl2/binary>> = Tl,
            read_gr_b(Tl2, 0, 0, NumBytes + TagLen1 + 8, 0, FieldNum);
        {_, 2} -> % 2 = length_delimited
            read_gr_ld(Tl, 0, 0, NumBytes + TagLen1, FieldNum);
        {_, 3} -> % 3 = group_start
            read_gr_b(Tl, 0, 0, NumBytes + TagLen1, 0, FieldNum);
        {_, 4} -> % 4 = group_end
            read_gr_b(Tl, 0, 0, NumBytes + TagLen1, 0, FieldNum);
        {_, 5} -> % 5 = bits32
            <<_:32, Tl2/binary>> = Tl,
            read_gr_b(Tl2, 0, 0, NumBytes + TagLen1 + 4, 0, FieldNum)
    end.

read_gr_vi(<<1:1, _:7, Tl/binary>>, N, NumBytes, FieldNum)
  when N < (64-7) ->
    read_gr_vi(Tl, N+7, NumBytes+1, FieldNum);
read_gr_vi(<<0:1, _:7, Tl/binary>>, _, NumBytes, FieldNum) ->
    read_gr_b(Tl, 0, 0, NumBytes+1, 0, FieldNum).

read_gr_ld(<<1:1, X:7, Tl/binary>>, N, Acc, NumBytes, FieldNum)
  when N < (64-7) ->
    read_gr_ld(Tl, N+7, X bsl N + Acc, NumBytes+1, FieldNum);
read_gr_ld(<<0:1, X:7, Tl/binary>>, N, Acc, NumBytes, FieldNum) ->
    Len = X bsl N + Acc,
    NumBytes1 = NumBytes + 1,
    <<_:Len/binary, Tl2/binary>> = Tl,
    read_gr_b(Tl2, 0, 0, NumBytes1 + Len, 0, FieldNum).

merge_msgs(Prev, New, MsgName) when is_atom(MsgName) ->
    merge_msgs(Prev, New, MsgName, []).

merge_msgs(Prev, New, MsgName, Opts) ->
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        server_reflection_request ->
            merge_msg_server_reflection_request(Prev,
                                                New,
                                                TrUserData);
        extension_request ->
            merge_msg_extension_request(Prev, New, TrUserData);
        server_reflection_response ->
            merge_msg_server_reflection_response(Prev,
                                                 New,
                                                 TrUserData);
        file_descriptor_response ->
            merge_msg_file_descriptor_response(Prev,
                                               New,
                                               TrUserData);
        extension_number_response ->
            merge_msg_extension_number_response(Prev,
                                                New,
                                                TrUserData);
        list_service_response ->
            merge_msg_list_service_response(Prev, New, TrUserData);
        service_response ->
            merge_msg_service_response(Prev, New, TrUserData);
        error_response ->
            merge_msg_error_response(Prev, New, TrUserData)
    end.

-compile({nowarn_unused_function,merge_msg_server_reflection_request/3}).
merge_msg_server_reflection_request(PMsg, NMsg,
                                    TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{host := NFhost}} -> S1#{host => NFhost};
             {#{host := PFhost}, _} -> S1#{host => PFhost};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {#{message_request :=
               {file_containing_extension, OPFmessage_request}},
         #{message_request :=
               {file_containing_extension, ONFmessage_request}}} ->
            S2#{message_request =>
                    {file_containing_extension,
                     merge_msg_extension_request(OPFmessage_request,
                                                 ONFmessage_request,
                                                 TrUserData)}};
        {_, #{message_request := NFmessage_request}} ->
            S2#{message_request => NFmessage_request};
        {#{message_request := PFmessage_request}, _} ->
            S2#{message_request => PFmessage_request};
        {_, _} -> S2
    end.

-compile({nowarn_unused_function,merge_msg_extension_request/3}).
merge_msg_extension_request(PMsg, NMsg, _) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{containing_type := NFcontaining_type}} ->
                 S1#{containing_type => NFcontaining_type};
             {#{containing_type := PFcontaining_type}, _} ->
                 S1#{containing_type => PFcontaining_type};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{extension_number := NFextension_number}} ->
            S2#{extension_number => NFextension_number};
        {#{extension_number := PFextension_number}, _} ->
            S2#{extension_number => PFextension_number};
        _ -> S2
    end.

-compile({nowarn_unused_function,merge_msg_server_reflection_response/3}).
merge_msg_server_reflection_response(PMsg, NMsg,
                                     TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{valid_host := NFvalid_host}} ->
                 S1#{valid_host => NFvalid_host};
             {#{valid_host := PFvalid_host}, _} ->
                 S1#{valid_host => PFvalid_host};
             _ -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {#{original_request := PForiginal_request},
              #{original_request := NForiginal_request}} ->
                 S2#{original_request =>
                         merge_msg_server_reflection_request(PForiginal_request,
                                                             NForiginal_request,
                                                             TrUserData)};
             {_, #{original_request := NForiginal_request}} ->
                 S2#{original_request => NForiginal_request};
             {#{original_request := PForiginal_request}, _} ->
                 S2#{original_request => PForiginal_request};
             {_, _} -> S2
         end,
    case {PMsg, NMsg} of
        {#{message_response :=
               {file_descriptor_response, OPFmessage_response}},
         #{message_response :=
               {file_descriptor_response, ONFmessage_response}}} ->
            S3#{message_response =>
                    {file_descriptor_response,
                     merge_msg_file_descriptor_response(OPFmessage_response,
                                                        ONFmessage_response,
                                                        TrUserData)}};
        {#{message_response :=
               {all_extension_numbers_response, OPFmessage_response}},
         #{message_response :=
               {all_extension_numbers_response,
                ONFmessage_response}}} ->
            S3#{message_response =>
                    {all_extension_numbers_response,
                     merge_msg_extension_number_response(OPFmessage_response,
                                                         ONFmessage_response,
                                                         TrUserData)}};
        {#{message_response :=
               {list_services_response, OPFmessage_response}},
         #{message_response :=
               {list_services_response, ONFmessage_response}}} ->
            S3#{message_response =>
                    {list_services_response,
                     merge_msg_list_service_response(OPFmessage_response,
                                                     ONFmessage_response,
                                                     TrUserData)}};
        {#{message_response :=
               {error_response, OPFmessage_response}},
         #{message_response :=
               {error_response, ONFmessage_response}}} ->
            S3#{message_response =>
                    {error_response,
                     merge_msg_error_response(OPFmessage_response,
                                              ONFmessage_response,
                                              TrUserData)}};
        {_, #{message_response := NFmessage_response}} ->
            S3#{message_response => NFmessage_response};
        {#{message_response := PFmessage_response}, _} ->
            S3#{message_response => PFmessage_response};
        {_, _} -> S3
    end.

-compile({nowarn_unused_function,merge_msg_file_descriptor_response/3}).
merge_msg_file_descriptor_response(PMsg, NMsg,
                                   TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{file_descriptor_proto := PFfile_descriptor_proto},
         #{file_descriptor_proto := NFfile_descriptor_proto}} ->
            S1#{file_descriptor_proto =>
                    'erlang_++'(PFfile_descriptor_proto,
                                NFfile_descriptor_proto,
                                TrUserData)};
        {_,
         #{file_descriptor_proto := NFfile_descriptor_proto}} ->
            S1#{file_descriptor_proto => NFfile_descriptor_proto};
        {#{file_descriptor_proto := PFfile_descriptor_proto},
         _} ->
            S1#{file_descriptor_proto => PFfile_descriptor_proto};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_extension_number_response/3}).
merge_msg_extension_number_response(PMsg, NMsg,
                                    TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{base_type_name := NFbase_type_name}} ->
                 S1#{base_type_name => NFbase_type_name};
             {#{base_type_name := PFbase_type_name}, _} ->
                 S1#{base_type_name => PFbase_type_name};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {#{extension_number := PFextension_number},
         #{extension_number := NFextension_number}} ->
            S2#{extension_number =>
                    'erlang_++'(PFextension_number,
                                NFextension_number,
                                TrUserData)};
        {_, #{extension_number := NFextension_number}} ->
            S2#{extension_number => NFextension_number};
        {#{extension_number := PFextension_number}, _} ->
            S2#{extension_number => PFextension_number};
        {_, _} -> S2
    end.

-compile({nowarn_unused_function,merge_msg_list_service_response/3}).
merge_msg_list_service_response(PMsg, NMsg,
                                TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{service := PFservice}, #{service := NFservice}} ->
            S1#{service =>
                    'erlang_++'(PFservice, NFservice, TrUserData)};
        {_, #{service := NFservice}} ->
            S1#{service => NFservice};
        {#{service := PFservice}, _} ->
            S1#{service => PFservice};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_service_response/3}).
merge_msg_service_response(PMsg, NMsg, _) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {_, #{name := NFname}} -> S1#{name => NFname};
        {#{name := PFname}, _} -> S1#{name => PFname};
        _ -> S1
    end.

-compile({nowarn_unused_function,merge_msg_error_response/3}).
merge_msg_error_response(PMsg, NMsg, _) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{error_code := NFerror_code}} ->
                 S1#{error_code => NFerror_code};
             {#{error_code := PFerror_code}, _} ->
                 S1#{error_code => PFerror_code};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{error_message := NFerror_message}} ->
            S2#{error_message => NFerror_message};
        {#{error_message := PFerror_message}, _} ->
            S2#{error_message => PFerror_message};
        _ -> S2
    end.


verify_msg(Msg, MsgName) when is_atom(MsgName) ->
    verify_msg(Msg, MsgName, []).

verify_msg(Msg, MsgName, Opts) ->
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        server_reflection_request ->
            v_msg_server_reflection_request(Msg,
                                            [MsgName],
                                            TrUserData);
        extension_request ->
            v_msg_extension_request(Msg, [MsgName], TrUserData);
        server_reflection_response ->
            v_msg_server_reflection_response(Msg,
                                             [MsgName],
                                             TrUserData);
        file_descriptor_response ->
            v_msg_file_descriptor_response(Msg,
                                           [MsgName],
                                           TrUserData);
        extension_number_response ->
            v_msg_extension_number_response(Msg,
                                            [MsgName],
                                            TrUserData);
        list_service_response ->
            v_msg_list_service_response(Msg, [MsgName], TrUserData);
        service_response ->
            v_msg_service_response(Msg, [MsgName], TrUserData);
        error_response ->
            v_msg_error_response(Msg, [MsgName], TrUserData);
        _ -> mk_type_error(not_a_known_message, Msg, [])
    end.


-compile({nowarn_unused_function,v_msg_server_reflection_request/3}).
-dialyzer({nowarn_function,v_msg_server_reflection_request/3}).
v_msg_server_reflection_request(#{} = M, Path,
                                TrUserData) ->
    case M of
        #{host := F1} ->
            v_type_string(F1, [host | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{message_request := {file_by_filename, OF2}} ->
            v_type_string(OF2,
                          [file_by_filename, message_request | Path],
                          TrUserData);
        #{message_request := {file_containing_symbol, OF2}} ->
            v_type_string(OF2,
                          [file_containing_symbol, message_request | Path],
                          TrUserData);
        #{message_request :=
              {file_containing_extension, OF2}} ->
            v_msg_extension_request(OF2,
                                    [file_containing_extension, message_request
                                     | Path],
                                    TrUserData);
        #{message_request :=
              {all_extension_numbers_of_type, OF2}} ->
            v_type_string(OF2,
                          [all_extension_numbers_of_type, message_request
                           | Path],
                          TrUserData);
        #{message_request := {list_services, OF2}} ->
            v_type_string(OF2,
                          [list_services, message_request | Path],
                          TrUserData);
        #{message_request := F2} ->
            mk_type_error(invalid_oneof,
                          F2,
                          [message_request | Path]);
        _ -> ok
    end,
    lists:foreach(fun (host) -> ok;
                      (message_request) -> ok;
                      (OtherKey) ->
                          mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_server_reflection_request(M, Path, _TrUserData)
    when is_map(M) ->
    mk_type_error({missing_fields,
                   [] -- maps:keys(M),
                   server_reflection_request},
                  M,
                  Path);
v_msg_server_reflection_request(X, Path, _TrUserData) ->
    mk_type_error({expected_msg, server_reflection_request},
                  X,
                  Path).

-compile({nowarn_unused_function,v_msg_extension_request/3}).
-dialyzer({nowarn_function,v_msg_extension_request/3}).
v_msg_extension_request(#{} = M, Path, TrUserData) ->
    case M of
        #{containing_type := F1} ->
            v_type_string(F1, [containing_type | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{extension_number := F2} ->
            v_type_int32(F2, [extension_number | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (containing_type) -> ok;
                      (extension_number) -> ok;
                      (OtherKey) ->
                          mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_extension_request(M, Path, _TrUserData)
    when is_map(M) ->
    mk_type_error({missing_fields,
                   [] -- maps:keys(M),
                   extension_request},
                  M,
                  Path);
v_msg_extension_request(X, Path, _TrUserData) ->
    mk_type_error({expected_msg, extension_request},
                  X,
                  Path).

-compile({nowarn_unused_function,v_msg_server_reflection_response/3}).
-dialyzer({nowarn_function,v_msg_server_reflection_response/3}).
v_msg_server_reflection_response(#{} = M, Path,
                                 TrUserData) ->
    case M of
        #{valid_host := F1} ->
            v_type_string(F1, [valid_host | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{original_request := F2} ->
            v_msg_server_reflection_request(F2,
                                            [original_request | Path],
                                            TrUserData);
        _ -> ok
    end,
    case M of
        #{message_response :=
              {file_descriptor_response, OF3}} ->
            v_msg_file_descriptor_response(OF3,
                                           [file_descriptor_response,
                                            message_response
                                            | Path],
                                           TrUserData);
        #{message_response :=
              {all_extension_numbers_response, OF3}} ->
            v_msg_extension_number_response(OF3,
                                            [all_extension_numbers_response,
                                             message_response
                                             | Path],
                                            TrUserData);
        #{message_response := {list_services_response, OF3}} ->
            v_msg_list_service_response(OF3,
                                        [list_services_response,
                                         message_response
                                         | Path],
                                        TrUserData);
        #{message_response := {error_response, OF3}} ->
            v_msg_error_response(OF3,
                                 [error_response, message_response | Path],
                                 TrUserData);
        #{message_response := F3} ->
            mk_type_error(invalid_oneof,
                          F3,
                          [message_response | Path]);
        _ -> ok
    end,
    lists:foreach(fun (valid_host) -> ok;
                      (original_request) -> ok;
                      (message_response) -> ok;
                      (OtherKey) ->
                          mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_server_reflection_response(M, Path, _TrUserData)
    when is_map(M) ->
    mk_type_error({missing_fields,
                   [] -- maps:keys(M),
                   server_reflection_response},
                  M,
                  Path);
v_msg_server_reflection_response(X, Path,
                                 _TrUserData) ->
    mk_type_error({expected_msg,
                   server_reflection_response},
                  X,
                  Path).

-compile({nowarn_unused_function,v_msg_file_descriptor_response/3}).
-dialyzer({nowarn_function,v_msg_file_descriptor_response/3}).
v_msg_file_descriptor_response(#{} = M, Path,
                               TrUserData) ->
    case M of
        #{file_descriptor_proto := F1} ->
            if is_list(F1) ->
                   _ = [v_type_bytes(Elem,
                                     [file_descriptor_proto | Path],
                                     TrUserData)
                        || Elem <- F1],
                   ok;
               true ->
                   mk_type_error({invalid_list_of, bytes},
                                 F1,
                                 [file_descriptor_proto | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (file_descriptor_proto) -> ok;
                      (OtherKey) ->
                          mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_file_descriptor_response(M, Path, _TrUserData)
    when is_map(M) ->
    mk_type_error({missing_fields,
                   [] -- maps:keys(M),
                   file_descriptor_response},
                  M,
                  Path);
v_msg_file_descriptor_response(X, Path, _TrUserData) ->
    mk_type_error({expected_msg, file_descriptor_response},
                  X,
                  Path).

-compile({nowarn_unused_function,v_msg_extension_number_response/3}).
-dialyzer({nowarn_function,v_msg_extension_number_response/3}).
v_msg_extension_number_response(#{} = M, Path,
                                TrUserData) ->
    case M of
        #{base_type_name := F1} ->
            v_type_string(F1, [base_type_name | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{extension_number := F2} ->
            if is_list(F2) ->
                   _ = [v_type_int32(Elem,
                                     [extension_number | Path],
                                     TrUserData)
                        || Elem <- F2],
                   ok;
               true ->
                   mk_type_error({invalid_list_of, int32},
                                 F2,
                                 [extension_number | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (base_type_name) -> ok;
                      (extension_number) -> ok;
                      (OtherKey) ->
                          mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_extension_number_response(M, Path, _TrUserData)
    when is_map(M) ->
    mk_type_error({missing_fields,
                   [] -- maps:keys(M),
                   extension_number_response},
                  M,
                  Path);
v_msg_extension_number_response(X, Path, _TrUserData) ->
    mk_type_error({expected_msg, extension_number_response},
                  X,
                  Path).

-compile({nowarn_unused_function,v_msg_list_service_response/3}).
-dialyzer({nowarn_function,v_msg_list_service_response/3}).
v_msg_list_service_response(#{} = M, Path,
                            TrUserData) ->
    case M of
        #{service := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_service_response(Elem,
                                               [service | Path],
                                               TrUserData)
                        || Elem <- F1],
                   ok;
               true ->
                   mk_type_error({invalid_list_of,
                                  {msg, service_response}},
                                 F1,
                                 [service | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (service) -> ok;
                      (OtherKey) ->
                          mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_list_service_response(M, Path, _TrUserData)
    when is_map(M) ->
    mk_type_error({missing_fields,
                   [] -- maps:keys(M),
                   list_service_response},
                  M,
                  Path);
v_msg_list_service_response(X, Path, _TrUserData) ->
    mk_type_error({expected_msg, list_service_response},
                  X,
                  Path).

-compile({nowarn_unused_function,v_msg_service_response/3}).
-dialyzer({nowarn_function,v_msg_service_response/3}).
v_msg_service_response(#{} = M, Path, TrUserData) ->
    case M of
        #{name := F1} ->
            v_type_string(F1, [name | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (name) -> ok;
                      (OtherKey) ->
                          mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_service_response(M, Path, _TrUserData)
    when is_map(M) ->
    mk_type_error({missing_fields,
                   [] -- maps:keys(M),
                   service_response},
                  M,
                  Path);
v_msg_service_response(X, Path, _TrUserData) ->
    mk_type_error({expected_msg, service_response},
                  X,
                  Path).

-compile({nowarn_unused_function,v_msg_error_response/3}).
-dialyzer({nowarn_function,v_msg_error_response/3}).
v_msg_error_response(#{} = M, Path, TrUserData) ->
    case M of
        #{error_code := F1} ->
            v_type_int32(F1, [error_code | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{error_message := F2} ->
            v_type_string(F2, [error_message | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (error_code) -> ok;
                      (error_message) -> ok;
                      (OtherKey) ->
                          mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_error_response(M, Path, _TrUserData)
    when is_map(M) ->
    mk_type_error({missing_fields,
                   [] -- maps:keys(M),
                   error_response},
                  M,
                  Path);
v_msg_error_response(X, Path, _TrUserData) ->
    mk_type_error({expected_msg, error_response}, X, Path).

-compile({nowarn_unused_function,v_type_int32/3}).
-dialyzer({nowarn_function,v_type_int32/3}).
v_type_int32(N, _Path, _TrUserData)
    when -2147483648 =< N, N =< 2147483647 ->
    ok;
v_type_int32(N, Path, _TrUserData) when is_integer(N) ->
    mk_type_error({value_out_of_range, int32, signed, 32},
                  N,
                  Path);
v_type_int32(X, Path, _TrUserData) ->
    mk_type_error({bad_integer, int32, signed, 32},
                  X,
                  Path).

-compile({nowarn_unused_function,v_type_string/3}).
-dialyzer({nowarn_function,v_type_string/3}).
v_type_string(S, Path, _TrUserData)
    when is_list(S); is_binary(S) ->
    try unicode:characters_to_binary(S) of
        B when is_binary(B) -> ok;
        {error, _, _} ->
            mk_type_error(bad_unicode_string, S, Path)
    catch
        error:badarg ->
            mk_type_error(bad_unicode_string, S, Path)
    end;
v_type_string(X, Path, _TrUserData) ->
    mk_type_error(bad_unicode_string, X, Path).

-compile({nowarn_unused_function,v_type_bytes/3}).
-dialyzer({nowarn_function,v_type_bytes/3}).
v_type_bytes(B, _Path, _TrUserData) when is_binary(B) ->
    ok;
v_type_bytes(B, _Path, _TrUserData) when is_list(B) ->
    ok;
v_type_bytes(X, Path, _TrUserData) ->
    mk_type_error(bad_binary_value, X, Path).

-compile({nowarn_unused_function,mk_type_error/3}).
-spec mk_type_error(_, _, list()) -> no_return().
mk_type_error(Error, ValueSeen, Path) ->
    Path2 = prettify_path(Path),
    erlang:error({gpb_type_error,
                  {Error, [{value, ValueSeen}, {path, Path2}]}}).


-compile({nowarn_unused_function,prettify_path/1}).
-dialyzer({nowarn_function,prettify_path/1}).
prettify_path([]) -> top_level;
prettify_path(PathR) ->
    list_to_atom(lists:append(lists:join(".",
                                         lists:map(fun atom_to_list/1,
                                                   lists:reverse(PathR))))).


-compile({nowarn_unused_function,id/2}).
-compile({inline,id/2}).
id(X, _TrUserData) -> X.

-compile({nowarn_unused_function,v_ok/3}).
-compile({inline,v_ok/3}).
v_ok(_Value, _Path, _TrUserData) -> ok.

-compile({nowarn_unused_function,m_overwrite/3}).
-compile({inline,m_overwrite/3}).
m_overwrite(_Prev, New, _TrUserData) -> New.

-compile({nowarn_unused_function,cons/3}).
-compile({inline,cons/3}).
cons(Elem, Acc, _TrUserData) -> [Elem | Acc].

-compile({nowarn_unused_function,lists_reverse/2}).
-compile({inline,lists_reverse/2}).
'lists_reverse'(L, _TrUserData) -> lists:reverse(L).
-compile({nowarn_unused_function,'erlang_++'/3}).
-compile({inline,'erlang_++'/3}).
'erlang_++'(A, B, _TrUserData) -> A ++ B.

get_msg_defs() ->
    [{{msg, server_reflection_request},
      [#{name => host, fnum => 1, rnum => 2, type => string,
         occurrence => optional, opts => []},
       #{name => message_request, rnum => 3,
         fields =>
             [#{name => file_by_filename, fnum => 3, rnum => 3,
                type => string, occurrence => optional, opts => []},
              #{name => file_containing_symbol, fnum => 4, rnum => 3,
                type => string, occurrence => optional, opts => []},
              #{name => file_containing_extension, fnum => 5,
                rnum => 3, type => {msg, extension_request},
                occurrence => optional, opts => []},
              #{name => all_extension_numbers_of_type, fnum => 6,
                rnum => 3, type => string, occurrence => optional,
                opts => []},
              #{name => list_services, fnum => 7, rnum => 3,
                type => string, occurrence => optional, opts => []}]}]},
     {{msg, extension_request},
      [#{name => containing_type, fnum => 1, rnum => 2,
         type => string, occurrence => optional, opts => []},
       #{name => extension_number, fnum => 2, rnum => 3,
         type => int32, occurrence => optional, opts => []}]},
     {{msg, server_reflection_response},
      [#{name => valid_host, fnum => 1, rnum => 2,
         type => string, occurrence => optional, opts => []},
       #{name => original_request, fnum => 2, rnum => 3,
         type => {msg, server_reflection_request},
         occurrence => optional, opts => []},
       #{name => message_response, rnum => 4,
         fields =>
             [#{name => file_descriptor_response, fnum => 4,
                rnum => 4, type => {msg, file_descriptor_response},
                occurrence => optional, opts => []},
              #{name => all_extension_numbers_response, fnum => 5,
                rnum => 4, type => {msg, extension_number_response},
                occurrence => optional, opts => []},
              #{name => list_services_response, fnum => 6, rnum => 4,
                type => {msg, list_service_response},
                occurrence => optional, opts => []},
              #{name => error_response, fnum => 7, rnum => 4,
                type => {msg, error_response}, occurrence => optional,
                opts => []}]}]},
     {{msg, file_descriptor_response},
      [#{name => file_descriptor_proto, fnum => 1, rnum => 2,
         type => bytes, occurrence => repeated, opts => []}]},
     {{msg, extension_number_response},
      [#{name => base_type_name, fnum => 1, rnum => 2,
         type => string, occurrence => optional, opts => []},
       #{name => extension_number, fnum => 2, rnum => 3,
         type => int32, occurrence => repeated,
         opts => [packed]}]},
     {{msg, list_service_response},
      [#{name => service, fnum => 1, rnum => 2,
         type => {msg, service_response}, occurrence => repeated,
         opts => []}]},
     {{msg, service_response},
      [#{name => name, fnum => 1, rnum => 2, type => string,
         occurrence => optional, opts => []}]},
     {{msg, error_response},
      [#{name => error_code, fnum => 1, rnum => 2,
         type => int32, occurrence => optional, opts => []},
       #{name => error_message, fnum => 2, rnum => 3,
         type => string, occurrence => optional, opts => []}]}].


get_msg_names() ->
    [server_reflection_request,
     extension_request,
     server_reflection_response,
     file_descriptor_response,
     extension_number_response,
     list_service_response,
     service_response,
     error_response].


get_group_names() -> [].


get_msg_or_group_names() ->
    [server_reflection_request,
     extension_request,
     server_reflection_response,
     file_descriptor_response,
     extension_number_response,
     list_service_response,
     service_response,
     error_response].


get_enum_names() -> [].


fetch_msg_def(MsgName) ->
    case find_msg_def(MsgName) of
        Fs when is_list(Fs) -> Fs;
        error -> erlang:error({no_such_msg, MsgName})
    end.


-spec fetch_enum_def(_) -> no_return().
fetch_enum_def(EnumName) ->
    erlang:error({no_such_enum, EnumName}).


find_msg_def(server_reflection_request) ->
    [#{name => host, fnum => 1, rnum => 2, type => string,
       occurrence => optional, opts => []},
     #{name => message_request, rnum => 3,
       fields =>
           [#{name => file_by_filename, fnum => 3, rnum => 3,
              type => string, occurrence => optional, opts => []},
            #{name => file_containing_symbol, fnum => 4, rnum => 3,
              type => string, occurrence => optional, opts => []},
            #{name => file_containing_extension, fnum => 5,
              rnum => 3, type => {msg, extension_request},
              occurrence => optional, opts => []},
            #{name => all_extension_numbers_of_type, fnum => 6,
              rnum => 3, type => string, occurrence => optional,
              opts => []},
            #{name => list_services, fnum => 7, rnum => 3,
              type => string, occurrence => optional, opts => []}]}];
find_msg_def(extension_request) ->
    [#{name => containing_type, fnum => 1, rnum => 2,
       type => string, occurrence => optional, opts => []},
     #{name => extension_number, fnum => 2, rnum => 3,
       type => int32, occurrence => optional, opts => []}];
find_msg_def(server_reflection_response) ->
    [#{name => valid_host, fnum => 1, rnum => 2,
       type => string, occurrence => optional, opts => []},
     #{name => original_request, fnum => 2, rnum => 3,
       type => {msg, server_reflection_request},
       occurrence => optional, opts => []},
     #{name => message_response, rnum => 4,
       fields =>
           [#{name => file_descriptor_response, fnum => 4,
              rnum => 4, type => {msg, file_descriptor_response},
              occurrence => optional, opts => []},
            #{name => all_extension_numbers_response, fnum => 5,
              rnum => 4, type => {msg, extension_number_response},
              occurrence => optional, opts => []},
            #{name => list_services_response, fnum => 6, rnum => 4,
              type => {msg, list_service_response},
              occurrence => optional, opts => []},
            #{name => error_response, fnum => 7, rnum => 4,
              type => {msg, error_response}, occurrence => optional,
              opts => []}]}];
find_msg_def(file_descriptor_response) ->
    [#{name => file_descriptor_proto, fnum => 1, rnum => 2,
       type => bytes, occurrence => repeated, opts => []}];
find_msg_def(extension_number_response) ->
    [#{name => base_type_name, fnum => 1, rnum => 2,
       type => string, occurrence => optional, opts => []},
     #{name => extension_number, fnum => 2, rnum => 3,
       type => int32, occurrence => repeated,
       opts => [packed]}];
find_msg_def(list_service_response) ->
    [#{name => service, fnum => 1, rnum => 2,
       type => {msg, service_response}, occurrence => repeated,
       opts => []}];
find_msg_def(service_response) ->
    [#{name => name, fnum => 1, rnum => 2, type => string,
       occurrence => optional, opts => []}];
find_msg_def(error_response) ->
    [#{name => error_code, fnum => 1, rnum => 2,
       type => int32, occurrence => optional, opts => []},
     #{name => error_message, fnum => 2, rnum => 3,
       type => string, occurrence => optional, opts => []}];
find_msg_def(_) -> error.


find_enum_def(_) -> error.


-spec enum_symbol_by_value(_, _) -> no_return().
enum_symbol_by_value(E, V) ->
    erlang:error({no_enum_defs, E, V}).


-spec enum_value_by_symbol(_, _) -> no_return().
enum_value_by_symbol(E, V) ->
    erlang:error({no_enum_defs, E, V}).



get_service_names() ->
    ['grpc.reflection.v1alpha.ServerReflection'].


get_service_def('grpc.reflection.v1alpha.ServerReflection') ->
    {{service, 'grpc.reflection.v1alpha.ServerReflection'},
     [#{name => 'ServerReflectionInfo',
        input => server_reflection_request,
        output => server_reflection_response,
        input_stream => true, output_stream => true,
        opts => []}]};
get_service_def(_) -> error.


get_rpc_names('grpc.reflection.v1alpha.ServerReflection') ->
    ['ServerReflectionInfo'];
get_rpc_names(_) -> error.


find_rpc_def('grpc.reflection.v1alpha.ServerReflection',
             RpcName) ->
    'find_rpc_def_grpc.reflection.v1alpha.ServerReflection'(RpcName);
find_rpc_def(_, _) -> error.


'find_rpc_def_grpc.reflection.v1alpha.ServerReflection'('ServerReflectionInfo') ->
    #{name => 'ServerReflectionInfo',
      input => server_reflection_request,
      output => server_reflection_response,
      input_stream => true, output_stream => true,
      opts => []};
'find_rpc_def_grpc.reflection.v1alpha.ServerReflection'(_) ->
    error.


fetch_rpc_def(ServiceName, RpcName) ->
    case find_rpc_def(ServiceName, RpcName) of
        Def when is_map(Def) -> Def;
        error ->
            erlang:error({no_such_rpc, ServiceName, RpcName})
    end.


%% Convert a a fully qualified (ie with package name) service name
%% as a binary to a service name as an atom.
fqbin_to_service_name(<<"grpc.reflection.v1alpha.ServerReflection">>) ->
    'grpc.reflection.v1alpha.ServerReflection';
fqbin_to_service_name(X) ->
    error({gpb_error, {badservice, X}}).


%% Convert a service name as an atom to a fully qualified
%% (ie with package name) name as a binary.
service_name_to_fqbin('grpc.reflection.v1alpha.ServerReflection') ->
    <<"grpc.reflection.v1alpha.ServerReflection">>;
service_name_to_fqbin(X) ->
    error({gpb_error, {badservice, X}}).


%% Convert a a fully qualified (ie with package name) service name
%% and an rpc name, both as binaries to a service name and an rpc
%% name, as atoms.
fqbins_to_service_and_rpc_name(<<"grpc.reflection.v1alpha.ServerReflection">>, <<"ServerReflectionInfo">>) ->
    {'grpc.reflection.v1alpha.ServerReflection',
     'ServerReflectionInfo'};
fqbins_to_service_and_rpc_name(S, R) ->
    error({gpb_error, {badservice_or_rpc, {S, R}}}).


%% Convert a service name and an rpc name, both as atoms,
%% to a fully qualified (ie with package name) service name and
%% an rpc name as binaries.
service_and_rpc_name_to_fqbins('grpc.reflection.v1alpha.ServerReflection',
                               'ServerReflectionInfo') ->
    {<<"grpc.reflection.v1alpha.ServerReflection">>, <<"ServerReflectionInfo">>};
service_and_rpc_name_to_fqbins(S, R) ->
    error({gpb_error, {badservice_or_rpc, {S, R}}}).


fqbin_to_msg_name(<<"grpc.reflection.v1alpha.ServerReflectionRequest">>) -> server_reflection_request;
fqbin_to_msg_name(<<"grpc.reflection.v1alpha.ExtensionRequest">>) -> extension_request;
fqbin_to_msg_name(<<"grpc.reflection.v1alpha.ServerReflectionResponse">>) ->
    server_reflection_response;
fqbin_to_msg_name(<<"grpc.reflection.v1alpha.FileDescriptorResponse">>) -> file_descriptor_response;
fqbin_to_msg_name(<<"grpc.reflection.v1alpha.ExtensionNumberResponse">>) -> extension_number_response;
fqbin_to_msg_name(<<"grpc.reflection.v1alpha.ListServiceResponse">>) -> list_service_response;
fqbin_to_msg_name(<<"grpc.reflection.v1alpha.ServiceResponse">>) -> service_response;
fqbin_to_msg_name(<<"grpc.reflection.v1alpha.ErrorResponse">>) -> error_response;
fqbin_to_msg_name(E) -> error({gpb_error, {badmsg, E}}).


msg_name_to_fqbin(server_reflection_request) -> <<"grpc.reflection.v1alpha.ServerReflectionRequest">>;
msg_name_to_fqbin(extension_request) -> <<"grpc.reflection.v1alpha.ExtensionRequest">>;
msg_name_to_fqbin(server_reflection_response) ->
    <<"grpc.reflection.v1alpha.ServerReflectionResponse">>;
msg_name_to_fqbin(file_descriptor_response) -> <<"grpc.reflection.v1alpha.FileDescriptorResponse">>;
msg_name_to_fqbin(extension_number_response) -> <<"grpc.reflection.v1alpha.ExtensionNumberResponse">>;
msg_name_to_fqbin(list_service_response) -> <<"grpc.reflection.v1alpha.ListServiceResponse">>;
msg_name_to_fqbin(service_response) -> <<"grpc.reflection.v1alpha.ServiceResponse">>;
msg_name_to_fqbin(error_response) -> <<"grpc.reflection.v1alpha.ErrorResponse">>;
msg_name_to_fqbin(E) -> error({gpb_error, {badmsg, E}}).


-spec fqbin_to_enum_name(_) -> no_return().
fqbin_to_enum_name(E) ->
    error({gpb_error, {badenum, E}}).


-spec enum_name_to_fqbin(_) -> no_return().
enum_name_to_fqbin(E) ->
    error({gpb_error, {badenum, E}}).


get_package_name() -> 'grpc.reflection.v1alpha'.


%% Whether or not the message names
%% are prepended with package name or not.
uses_packages() -> true.


source_basename() -> "reflection.proto".


%% Retrieve all proto file names, also imported ones.
%% The order is top-down. The first element is always the main
%% source file. The files are returned with extension,
%% see get_all_proto_names/0 for a version that returns
%% the basenames sans extension
get_all_source_basenames() -> ["reflection.proto"].


%% Retrieve all proto file names, also imported ones.
%% The order is top-down. The first element is always the main
%% source file. The files are returned sans .proto extension,
%% to make it easier to use them with the various get_xyz_containment
%% functions.
get_all_proto_names() -> ["reflection"].


get_msg_containment("reflection") ->
    [error_response,
     extension_number_response,
     extension_request,
     file_descriptor_response,
     list_service_response,
     server_reflection_request,
     server_reflection_response,
     service_response];
get_msg_containment(P) ->
    error({gpb_error, {badproto, P}}).


get_pkg_containment("reflection") ->
    'grpc.reflection.v1alpha';
get_pkg_containment(P) ->
    error({gpb_error, {badproto, P}}).


get_service_containment("reflection") ->
    ['grpc.reflection.v1alpha.ServerReflection'];
get_service_containment(P) ->
    error({gpb_error, {badproto, P}}).


get_rpc_containment("reflection") ->
    [{'grpc.reflection.v1alpha.ServerReflection',
      'ServerReflectionInfo'}];
get_rpc_containment(P) ->
    error({gpb_error, {badproto, P}}).


get_enum_containment("reflection") -> [];
get_enum_containment(P) ->
    error({gpb_error, {badproto, P}}).


get_proto_by_msg_name_as_fqbin(<<"grpc.reflection.v1alpha.ServerReflectionRequest">>) -> "reflection";
get_proto_by_msg_name_as_fqbin(<<"grpc.reflection.v1alpha.ExtensionRequest">>) -> "reflection";
get_proto_by_msg_name_as_fqbin(<<"grpc.reflection.v1alpha.ServiceResponse">>) -> "reflection";
get_proto_by_msg_name_as_fqbin(<<"grpc.reflection.v1alpha.ServerReflectionResponse">>) -> "reflection";
get_proto_by_msg_name_as_fqbin(<<"grpc.reflection.v1alpha.ListServiceResponse">>) -> "reflection";
get_proto_by_msg_name_as_fqbin(<<"grpc.reflection.v1alpha.FileDescriptorResponse">>) -> "reflection";
get_proto_by_msg_name_as_fqbin(<<"grpc.reflection.v1alpha.ExtensionNumberResponse">>) -> "reflection";
get_proto_by_msg_name_as_fqbin(<<"grpc.reflection.v1alpha.ErrorResponse">>) -> "reflection";
get_proto_by_msg_name_as_fqbin(E) ->
    error({gpb_error, {badmsg, E}}).


get_proto_by_service_name_as_fqbin(<<"grpc.reflection.v1alpha.ServerReflection">>) ->
    "reflection";
get_proto_by_service_name_as_fqbin(E) ->
    error({gpb_error, {badservice, E}}).


-spec get_proto_by_enum_name_as_fqbin(_) -> no_return().
get_proto_by_enum_name_as_fqbin(E) ->
    error({gpb_error, {badenum, E}}).


get_protos_by_pkg_name_as_fqbin(<<"grpc.reflection.v1alpha">>) ->
    ["reflection"];
get_protos_by_pkg_name_as_fqbin(E) ->
    error({gpb_error, {badpkg, E}}).


descriptor() ->
    <<10, 202, 10, 10, 40, 103, 114, 112, 99, 47, 114, 101,
      102, 108, 101, 99, 116, 105, 111, 110, 47, 118, 49, 97,
      108, 112, 104, 97, 47, 114, 101, 102, 108, 101, 99, 116,
      105, 111, 110, 46, 112, 114, 111, 116, 111, 18, 23, 103,
      114, 112, 99, 46, 114, 101, 102, 108, 101, 99, 116, 105,
      111, 110, 46, 118, 49, 97, 108, 112, 104, 97, 34, 58,
      10, 13, 69, 114, 114, 111, 114, 82, 101, 115, 112, 111,
      110, 115, 101, 18, 18, 10, 10, 101, 114, 114, 111, 114,
      95, 99, 111, 100, 101, 24, 1, 32, 1, 40, 5, 18, 21, 10,
      13, 101, 114, 114, 111, 114, 95, 109, 101, 115, 115, 97,
      103, 101, 24, 2, 32, 1, 40, 9, 34, 87, 10, 23, 69, 120,
      116, 101, 110, 115, 105, 111, 110, 78, 117, 109, 98,
      101, 114, 82, 101, 115, 112, 111, 110, 115, 101, 18, 22,
      10, 14, 98, 97, 115, 101, 95, 116, 121, 112, 101, 95,
      110, 97, 109, 101, 24, 1, 32, 1, 40, 9, 18, 36, 10, 16,
      101, 120, 116, 101, 110, 115, 105, 111, 110, 95, 110,
      117, 109, 98, 101, 114, 24, 2, 32, 3, 40, 5, 66, 10, 8,
      0, 16, 1, 48, 0, 40, 0, 80, 0, 34, 69, 10, 16, 69, 120,
      116, 101, 110, 115, 105, 111, 110, 82, 101, 113, 117,
      101, 115, 116, 18, 23, 10, 15, 99, 111, 110, 116, 97,
      105, 110, 105, 110, 103, 95, 116, 121, 112, 101, 24, 1,
      32, 1, 40, 9, 18, 24, 10, 16, 101, 120, 116, 101, 110,
      115, 105, 111, 110, 95, 110, 117, 109, 98, 101, 114, 24,
      2, 32, 1, 40, 5, 34, 55, 10, 22, 70, 105, 108, 101, 68,
      101, 115, 99, 114, 105, 112, 116, 111, 114, 82, 101,
      115, 112, 111, 110, 115, 101, 18, 29, 10, 21, 102, 105,
      108, 101, 95, 100, 101, 115, 99, 114, 105, 112, 116,
      111, 114, 95, 112, 114, 111, 116, 111, 24, 1, 32, 3, 40,
      12, 34, 80, 10, 19, 76, 105, 115, 116, 83, 101, 114,
      118, 105, 99, 101, 82, 101, 115, 112, 111, 110, 115,
      101, 18, 57, 10, 7, 115, 101, 114, 118, 105, 99, 101,
      24, 1, 32, 3, 40, 11, 50, 40, 46, 103, 114, 112, 99, 46,
      114, 101, 102, 108, 101, 99, 116, 105, 111, 110, 46,
      118, 49, 97, 108, 112, 104, 97, 46, 83, 101, 114, 118,
      105, 99, 101, 82, 101, 115, 112, 111, 110, 115, 101, 34,
      138, 2, 10, 23, 83, 101, 114, 118, 101, 114, 82, 101,
      102, 108, 101, 99, 116, 105, 111, 110, 82, 101, 113,
      117, 101, 115, 116, 18, 12, 10, 4, 104, 111, 115, 116,
      24, 1, 32, 1, 40, 9, 18, 26, 10, 16, 102, 105, 108, 101,
      95, 98, 121, 95, 102, 105, 108, 101, 110, 97, 109, 101,
      24, 3, 32, 1, 40, 9, 72, 0, 18, 32, 10, 22, 102, 105,
      108, 101, 95, 99, 111, 110, 116, 97, 105, 110, 105, 110,
      103, 95, 115, 121, 109, 98, 111, 108, 24, 4, 32, 1, 40,
      9, 72, 0, 18, 78, 10, 25, 102, 105, 108, 101, 95, 99,
      111, 110, 116, 97, 105, 110, 105, 110, 103, 95, 101,
      120, 116, 101, 110, 115, 105, 111, 110, 24, 5, 32, 1,
      40, 11, 50, 41, 46, 103, 114, 112, 99, 46, 114, 101,
      102, 108, 101, 99, 116, 105, 111, 110, 46, 118, 49, 97,
      108, 112, 104, 97, 46, 69, 120, 116, 101, 110, 115, 105,
      111, 110, 82, 101, 113, 117, 101, 115, 116, 72, 0, 18,
      39, 10, 29, 97, 108, 108, 95, 101, 120, 116, 101, 110,
      115, 105, 111, 110, 95, 110, 117, 109, 98, 101, 114,
      115, 95, 111, 102, 95, 116, 121, 112, 101, 24, 6, 32, 1,
      40, 9, 72, 0, 18, 23, 10, 13, 108, 105, 115, 116, 95,
      115, 101, 114, 118, 105, 99, 101, 115, 24, 7, 32, 1, 40,
      9, 72, 0, 66, 17, 10, 15, 109, 101, 115, 115, 97, 103,
      101, 95, 114, 101, 113, 117, 101, 115, 116, 34, 209, 3,
      10, 24, 83, 101, 114, 118, 101, 114, 82, 101, 102, 108,
      101, 99, 116, 105, 111, 110, 82, 101, 115, 112, 111,
      110, 115, 101, 18, 18, 10, 10, 118, 97, 108, 105, 100,
      95, 104, 111, 115, 116, 24, 1, 32, 1, 40, 9, 18, 74, 10,
      16, 111, 114, 105, 103, 105, 110, 97, 108, 95, 114, 101,
      113, 117, 101, 115, 116, 24, 2, 32, 1, 40, 11, 50, 48,
      46, 103, 114, 112, 99, 46, 114, 101, 102, 108, 101, 99,
      116, 105, 111, 110, 46, 118, 49, 97, 108, 112, 104, 97,
      46, 83, 101, 114, 118, 101, 114, 82, 101, 102, 108, 101,
      99, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115,
      116, 18, 83, 10, 24, 102, 105, 108, 101, 95, 100, 101,
      115, 99, 114, 105, 112, 116, 111, 114, 95, 114, 101,
      115, 112, 111, 110, 115, 101, 24, 4, 32, 1, 40, 11, 50,
      47, 46, 103, 114, 112, 99, 46, 114, 101, 102, 108, 101,
      99, 116, 105, 111, 110, 46, 118, 49, 97, 108, 112, 104,
      97, 46, 70, 105, 108, 101, 68, 101, 115, 99, 114, 105,
      112, 116, 111, 114, 82, 101, 115, 112, 111, 110, 115,
      101, 72, 0, 18, 90, 10, 30, 97, 108, 108, 95, 101, 120,
      116, 101, 110, 115, 105, 111, 110, 95, 110, 117, 109,
      98, 101, 114, 115, 95, 114, 101, 115, 112, 111, 110,
      115, 101, 24, 5, 32, 1, 40, 11, 50, 48, 46, 103, 114,
      112, 99, 46, 114, 101, 102, 108, 101, 99, 116, 105, 111,
      110, 46, 118, 49, 97, 108, 112, 104, 97, 46, 69, 120,
      116, 101, 110, 115, 105, 111, 110, 78, 117, 109, 98,
      101, 114, 82, 101, 115, 112, 111, 110, 115, 101, 72, 0,
      18, 78, 10, 22, 108, 105, 115, 116, 95, 115, 101, 114,
      118, 105, 99, 101, 115, 95, 114, 101, 115, 112, 111,
      110, 115, 101, 24, 6, 32, 1, 40, 11, 50, 44, 46, 103,
      114, 112, 99, 46, 114, 101, 102, 108, 101, 99, 116, 105,
      111, 110, 46, 118, 49, 97, 108, 112, 104, 97, 46, 76,
      105, 115, 116, 83, 101, 114, 118, 105, 99, 101, 82, 101,
      115, 112, 111, 110, 115, 101, 72, 0, 18, 64, 10, 14,
      101, 114, 114, 111, 114, 95, 114, 101, 115, 112, 111,
      110, 115, 101, 24, 7, 32, 1, 40, 11, 50, 38, 46, 103,
      114, 112, 99, 46, 114, 101, 102, 108, 101, 99, 116, 105,
      111, 110, 46, 118, 49, 97, 108, 112, 104, 97, 46, 69,
      114, 114, 111, 114, 82, 101, 115, 112, 111, 110, 115,
      101, 72, 0, 66, 18, 10, 16, 109, 101, 115, 115, 97, 103,
      101, 95, 114, 101, 115, 112, 111, 110, 115, 101, 34, 31,
      10, 15, 83, 101, 114, 118, 105, 99, 101, 82, 101, 115,
      112, 111, 110, 115, 101, 18, 12, 10, 4, 110, 97, 109,
      101, 24, 1, 32, 1, 40, 9, 50, 147, 1, 10, 16, 83, 101,
      114, 118, 101, 114, 82, 101, 102, 108, 101, 99, 116,
      105, 111, 110, 18, 127, 10, 20, 83, 101, 114, 118, 101,
      114, 82, 101, 102, 108, 101, 99, 116, 105, 111, 110, 73,
      110, 102, 111, 18, 48, 46, 103, 114, 112, 99, 46, 114,
      101, 102, 108, 101, 99, 116, 105, 111, 110, 46, 118, 49,
      97, 108, 112, 104, 97, 46, 83, 101, 114, 118, 101, 114,
      82, 101, 102, 108, 101, 99, 116, 105, 111, 110, 82, 101,
      113, 117, 101, 115, 116, 26, 49, 46, 103, 114, 112, 99,
      46, 114, 101, 102, 108, 101, 99, 116, 105, 111, 110, 46,
      118, 49, 97, 108, 112, 104, 97, 46, 83, 101, 114, 118,
      101, 114, 82, 101, 102, 108, 101, 99, 116, 105, 111,
      110, 82, 101, 115, 112, 111, 110, 115, 101, 40, 0, 48,
      0, 98, 6, 112, 114, 111, 116, 111, 51>>.

descriptor("reflection") ->
    <<10, 40, 103, 114, 112, 99, 47, 114, 101, 102, 108,
      101, 99, 116, 105, 111, 110, 47, 118, 49, 97, 108, 112,
      104, 97, 47, 114, 101, 102, 108, 101, 99, 116, 105, 111,
      110, 46, 112, 114, 111, 116, 111, 18, 23, 103, 114, 112,
      99, 46, 114, 101, 102, 108, 101, 99, 116, 105, 111, 110,
      46, 118, 49, 97, 108, 112, 104, 97, 34, 58, 10, 13, 69,
      114, 114, 111, 114, 82, 101, 115, 112, 111, 110, 115,
      101, 18, 18, 10, 10, 101, 114, 114, 111, 114, 95, 99,
      111, 100, 101, 24, 1, 32, 1, 40, 5, 18, 21, 10, 13, 101,
      114, 114, 111, 114, 95, 109, 101, 115, 115, 97, 103,
      101, 24, 2, 32, 1, 40, 9, 34, 87, 10, 23, 69, 120, 116,
      101, 110, 115, 105, 111, 110, 78, 117, 109, 98, 101,
      114, 82, 101, 115, 112, 111, 110, 115, 101, 18, 22, 10,
      14, 98, 97, 115, 101, 95, 116, 121, 112, 101, 95, 110,
      97, 109, 101, 24, 1, 32, 1, 40, 9, 18, 36, 10, 16, 101,
      120, 116, 101, 110, 115, 105, 111, 110, 95, 110, 117,
      109, 98, 101, 114, 24, 2, 32, 3, 40, 5, 66, 10, 8, 0,
      16, 1, 48, 0, 40, 0, 80, 0, 34, 69, 10, 16, 69, 120,
      116, 101, 110, 115, 105, 111, 110, 82, 101, 113, 117,
      101, 115, 116, 18, 23, 10, 15, 99, 111, 110, 116, 97,
      105, 110, 105, 110, 103, 95, 116, 121, 112, 101, 24, 1,
      32, 1, 40, 9, 18, 24, 10, 16, 101, 120, 116, 101, 110,
      115, 105, 111, 110, 95, 110, 117, 109, 98, 101, 114, 24,
      2, 32, 1, 40, 5, 34, 55, 10, 22, 70, 105, 108, 101, 68,
      101, 115, 99, 114, 105, 112, 116, 111, 114, 82, 101,
      115, 112, 111, 110, 115, 101, 18, 29, 10, 21, 102, 105,
      108, 101, 95, 100, 101, 115, 99, 114, 105, 112, 116,
      111, 114, 95, 112, 114, 111, 116, 111, 24, 1, 32, 3, 40,
      12, 34, 80, 10, 19, 76, 105, 115, 116, 83, 101, 114,
      118, 105, 99, 101, 82, 101, 115, 112, 111, 110, 115,
      101, 18, 57, 10, 7, 115, 101, 114, 118, 105, 99, 101,
      24, 1, 32, 3, 40, 11, 50, 40, 46, 103, 114, 112, 99, 46,
      114, 101, 102, 108, 101, 99, 116, 105, 111, 110, 46,
      118, 49, 97, 108, 112, 104, 97, 46, 83, 101, 114, 118,
      105, 99, 101, 82, 101, 115, 112, 111, 110, 115, 101, 34,
      138, 2, 10, 23, 83, 101, 114, 118, 101, 114, 82, 101,
      102, 108, 101, 99, 116, 105, 111, 110, 82, 101, 113,
      117, 101, 115, 116, 18, 12, 10, 4, 104, 111, 115, 116,
      24, 1, 32, 1, 40, 9, 18, 26, 10, 16, 102, 105, 108, 101,
      95, 98, 121, 95, 102, 105, 108, 101, 110, 97, 109, 101,
      24, 3, 32, 1, 40, 9, 72, 0, 18, 32, 10, 22, 102, 105,
      108, 101, 95, 99, 111, 110, 116, 97, 105, 110, 105, 110,
      103, 95, 115, 121, 109, 98, 111, 108, 24, 4, 32, 1, 40,
      9, 72, 0, 18, 78, 10, 25, 102, 105, 108, 101, 95, 99,
      111, 110, 116, 97, 105, 110, 105, 110, 103, 95, 101,
      120, 116, 101, 110, 115, 105, 111, 110, 24, 5, 32, 1,
      40, 11, 50, 41, 46, 103, 114, 112, 99, 46, 114, 101,
      102, 108, 101, 99, 116, 105, 111, 110, 46, 118, 49, 97,
      108, 112, 104, 97, 46, 69, 120, 116, 101, 110, 115, 105,
      111, 110, 82, 101, 113, 117, 101, 115, 116, 72, 0, 18,
      39, 10, 29, 97, 108, 108, 95, 101, 120, 116, 101, 110,
      115, 105, 111, 110, 95, 110, 117, 109, 98, 101, 114,
      115, 95, 111, 102, 95, 116, 121, 112, 101, 24, 6, 32, 1,
      40, 9, 72, 0, 18, 23, 10, 13, 108, 105, 115, 116, 95,
      115, 101, 114, 118, 105, 99, 101, 115, 24, 7, 32, 1, 40,
      9, 72, 0, 66, 17, 10, 15, 109, 101, 115, 115, 97, 103,
      101, 95, 114, 101, 113, 117, 101, 115, 116, 34, 209, 3,
      10, 24, 83, 101, 114, 118, 101, 114, 82, 101, 102, 108,
      101, 99, 116, 105, 111, 110, 82, 101, 115, 112, 111,
      110, 115, 101, 18, 18, 10, 10, 118, 97, 108, 105, 100,
      95, 104, 111, 115, 116, 24, 1, 32, 1, 40, 9, 18, 74, 10,
      16, 111, 114, 105, 103, 105, 110, 97, 108, 95, 114, 101,
      113, 117, 101, 115, 116, 24, 2, 32, 1, 40, 11, 50, 48,
      46, 103, 114, 112, 99, 46, 114, 101, 102, 108, 101, 99,
      116, 105, 111, 110, 46, 118, 49, 97, 108, 112, 104, 97,
      46, 83, 101, 114, 118, 101, 114, 82, 101, 102, 108, 101,
      99, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115,
      116, 18, 83, 10, 24, 102, 105, 108, 101, 95, 100, 101,
      115, 99, 114, 105, 112, 116, 111, 114, 95, 114, 101,
      115, 112, 111, 110, 115, 101, 24, 4, 32, 1, 40, 11, 50,
      47, 46, 103, 114, 112, 99, 46, 114, 101, 102, 108, 101,
      99, 116, 105, 111, 110, 46, 118, 49, 97, 108, 112, 104,
      97, 46, 70, 105, 108, 101, 68, 101, 115, 99, 114, 105,
      112, 116, 111, 114, 82, 101, 115, 112, 111, 110, 115,
      101, 72, 0, 18, 90, 10, 30, 97, 108, 108, 95, 101, 120,
      116, 101, 110, 115, 105, 111, 110, 95, 110, 117, 109,
      98, 101, 114, 115, 95, 114, 101, 115, 112, 111, 110,
      115, 101, 24, 5, 32, 1, 40, 11, 50, 48, 46, 103, 114,
      112, 99, 46, 114, 101, 102, 108, 101, 99, 116, 105, 111,
      110, 46, 118, 49, 97, 108, 112, 104, 97, 46, 69, 120,
      116, 101, 110, 115, 105, 111, 110, 78, 117, 109, 98,
      101, 114, 82, 101, 115, 112, 111, 110, 115, 101, 72, 0,
      18, 78, 10, 22, 108, 105, 115, 116, 95, 115, 101, 114,
      118, 105, 99, 101, 115, 95, 114, 101, 115, 112, 111,
      110, 115, 101, 24, 6, 32, 1, 40, 11, 50, 44, 46, 103,
      114, 112, 99, 46, 114, 101, 102, 108, 101, 99, 116, 105,
      111, 110, 46, 118, 49, 97, 108, 112, 104, 97, 46, 76,
      105, 115, 116, 83, 101, 114, 118, 105, 99, 101, 82, 101,
      115, 112, 111, 110, 115, 101, 72, 0, 18, 64, 10, 14,
      101, 114, 114, 111, 114, 95, 114, 101, 115, 112, 111,
      110, 115, 101, 24, 7, 32, 1, 40, 11, 50, 38, 46, 103,
      114, 112, 99, 46, 114, 101, 102, 108, 101, 99, 116, 105,
      111, 110, 46, 118, 49, 97, 108, 112, 104, 97, 46, 69,
      114, 114, 111, 114, 82, 101, 115, 112, 111, 110, 115,
      101, 72, 0, 66, 18, 10, 16, 109, 101, 115, 115, 97, 103,
      101, 95, 114, 101, 115, 112, 111, 110, 115, 101, 34, 31,
      10, 15, 83, 101, 114, 118, 105, 99, 101, 82, 101, 115,
      112, 111, 110, 115, 101, 18, 12, 10, 4, 110, 97, 109,
      101, 24, 1, 32, 1, 40, 9, 50, 147, 1, 10, 16, 83, 101,
      114, 118, 101, 114, 82, 101, 102, 108, 101, 99, 116,
      105, 111, 110, 18, 127, 10, 20, 83, 101, 114, 118, 101,
      114, 82, 101, 102, 108, 101, 99, 116, 105, 111, 110, 73,
      110, 102, 111, 18, 48, 46, 103, 114, 112, 99, 46, 114,
      101, 102, 108, 101, 99, 116, 105, 111, 110, 46, 118, 49,
      97, 108, 112, 104, 97, 46, 83, 101, 114, 118, 101, 114,
      82, 101, 102, 108, 101, 99, 116, 105, 111, 110, 82, 101,
      113, 117, 101, 115, 116, 26, 49, 46, 103, 114, 112, 99,
      46, 114, 101, 102, 108, 101, 99, 116, 105, 111, 110, 46,
      118, 49, 97, 108, 112, 104, 97, 46, 83, 101, 114, 118,
      101, 114, 82, 101, 102, 108, 101, 99, 116, 105, 111,
      110, 82, 101, 115, 112, 111, 110, 115, 101, 40, 0, 48,
      0, 98, 6, 112, 114, 111, 116, 111, 51>>;
descriptor(X) -> error({gpb_error, {badname, X}}).


gpb_version_as_string() ->
    "4.7.3".

gpb_version_as_list() ->
    [4,7,3].
