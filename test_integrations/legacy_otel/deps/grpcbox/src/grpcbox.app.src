{application,grpcbox,
             [{description,"Erlang grpc library based on chatterbox"},
              {vsn,"0.17.1"},
              {registered,[]},
              {mod,{grpcbox_app,[]}},
              {applications,[kernel,stdlib,chatterbox,acceptor_pool,gproc,
                             ctx]},
              {env,[{client,#{channels => []}},
                    {grpc_opts,#{service_protos => [],client_cert_dir => []}},
                    {transport_opts,#{ssl => false,cacertfile => [],
                                      certfile => [],keyfile => []}},
                    {listen_opts,#{port => 8080,ip => {0,0,0,0}}},
                    {pool_opts,#{size => 10}},
                    {server_opts,#{header_table_size => 4096,enable_push => 1,
                                   max_concurrent_streams => unlimited,
                                   initial_window_size => 65535,
                                   max_frame_size => 16384,
                                   max_header_list_size => unlimited}}]},
              {modules,[]},
              {licenses,["Apache 2.0"]},
              {links,[{"Github","https://github.com/tsloughter/grpcbox"}]}]}.
