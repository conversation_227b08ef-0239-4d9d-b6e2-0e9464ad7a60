{<<"name">>,<<"acceptor_pool">>}.
{<<"version">>,<<"1.0.0">>}.
{<<"requirements">>,#{}}.
{<<"app">>,<<"acceptor_pool">>}.
{<<"maintainers">>,[<<"James Fish">>]}.
{<<"precompiled">>,false}.
{<<"description">>,<<"A tcp acceptor pool library">>}.
{<<"files">>,
 [<<"src/acceptor_pool.app.src">>,<<"LICENSE">>,<<"README.md">>,
  <<"rebar.config">>,<<"rebar.lock">>,<<"src/acceptor.erl">>,
  <<"src/acceptor_loop.erl">>,<<"src/acceptor_pool.erl">>]}.
{<<"licenses">>,[<<"Apache v2.0">>]}.
{<<"links">>,
 [{<<"Github">>,<<"https://github.com/fishcakez/acceptor_pool">>}]}.
{<<"build_tools">>,[<<"rebar3">>]}.
