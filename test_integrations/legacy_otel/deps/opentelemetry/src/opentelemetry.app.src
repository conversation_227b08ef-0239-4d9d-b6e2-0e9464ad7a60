{application,opentelemetry,
             [{description,"Implementation of stable OpenTelemetry signals"},
              {vsn,"1.3.1"},
              {registered,[otel_tracer_provider_sup]},
              {mod,{opentelemetry_app,[]}},
              {applications,[kernel,stdlib,opentelemetry_api]},
              {env,[]},
              {modules,[]},
              {doc,"doc"},
              {licenses,["Apache-2.0"]},
              {links,[{"GitHub",
                       "https://github.com/open-telemetry/opentelemetry-erlang"}]}]}.
