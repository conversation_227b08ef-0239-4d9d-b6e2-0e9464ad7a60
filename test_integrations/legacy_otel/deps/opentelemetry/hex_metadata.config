{<<"app">>,<<"opentelemetry">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"Implementation of stable OpenTelemetry signals">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"include">>,<<"include/otel_sampler.hrl">>,
  <<"include/otel_span.hrl">>,<<"rebar.config">>,<<"rebar.lock">>,<<"src">>,
  <<"src/opentelemetry.app.src">>,<<"src/opentelemetry_app.erl">>,
  <<"src/opentelemetry_sup.erl">>,<<"src/otel_attributes.erl">>,
  <<"src/otel_batch_processor.erl">>,<<"src/otel_configuration.erl">>,
  <<"src/otel_events.erl">>,<<"src/otel_exporter.erl">>,
  <<"src/otel_exporter_pid.erl">>,<<"src/otel_exporter_stdout.erl">>,
  <<"src/otel_exporter_tab.erl">>,<<"src/otel_id_generator.erl">>,
  <<"src/otel_links.erl">>,<<"src/otel_resource.erl">>,
  <<"src/otel_resource_app_env.erl">>,<<"src/otel_resource_detector.erl">>,
  <<"src/otel_resource_env_var.erl">>,<<"src/otel_sampler.erl">>,
  <<"src/otel_sampler_always_off.erl">>,<<"src/otel_sampler_always_on.erl">>,
  <<"src/otel_sampler_parent_based.erl">>,
  <<"src/otel_sampler_trace_id_ratio_based.erl">>,
  <<"src/otel_simple_processor.erl">>,<<"src/otel_span_ets.erl">>,
  <<"src/otel_span_ets.hrl">>,<<"src/otel_span_limits.erl">>,
  <<"src/otel_span_processor.erl">>,<<"src/otel_span_processor_sup.erl">>,
  <<"src/otel_span_sup.erl">>,<<"src/otel_span_sweeper.erl">>,
  <<"src/otel_span_utils.erl">>,<<"src/otel_tracer.hrl">>,
  <<"src/otel_tracer_default.erl">>,<<"src/otel_tracer_provider_sup.erl">>,
  <<"src/otel_tracer_server.erl">>,<<"src/otel_tracer_server_sup.erl">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"links">>,
 [{<<"GitHub">>,
   <<"https://github.com/open-telemetry/opentelemetry-erlang">>}]}.
{<<"name">>,<<"opentelemetry">>}.
{<<"requirements">>,
 [{<<"opentelemetry_api">>,
   [{<<"app">>,<<"opentelemetry_api">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~> 1.2.2">>}]},
  {<<"opentelemetry_semantic_conventions">>,
   [{<<"app">>,<<"opentelemetry_semantic_conventions">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~> 0.2">>}]}]}.
{<<"version">>,<<"1.3.1">>}.
