{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/open-telemetry/opentelemetry-erlang">>},
  {<<"OpenTelemetry.io">>,<<"https://opentelemetry.io">>}]}.
{<<"name">>,<<"opentelemetry_api">>}.
{<<"version">>,<<"1.2.2">>}.
{<<"description">>,<<"OpenTelemetry API">>}.
{<<"elixir">>,<<"~> 1.8">>}.
{<<"app">>,<<"opentelemetry_api">>}.
{<<"build_tools">>,[<<"rebar3">>,<<"mix">>]}.
{<<"files">>,
 [<<"lib">>,<<"lib/open_telemetry">>,<<"lib/open_telemetry/baggage.ex">>,
  <<"lib/open_telemetry/ctx.ex">>,<<"lib/open_telemetry/tracer.ex">>,
  <<"lib/open_telemetry/span.ex">>,<<"lib/open_telemetry.ex">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE">>,<<"rebar.config">>,<<"include">>,
  <<"include/gradualizer.hrl">>,<<"include/opentelemetry.hrl">>,
  <<"include/otel_tracer.hrl">>,<<"src">>,<<"src/otel_propagator_b3.erl">>,
  <<"src/otel_propagator_baggage.erl">>,
  <<"src/otel_propagator_text_map_composite.erl">>,
  <<"src/otel_propagator_text_map_noop.erl">>,<<"src/otel_tracer.erl">>,
  <<"src/otel_propagator_text_map.erl">>,<<"src/otel_baggage.erl">>,
  <<"src/otel_ctx.erl">>,<<"src/otel_propagator.erl">>,
  <<"src/otel_propagator_b3multi.erl">>,
  <<"src/otel_propagator_b3single.erl">>,<<"src/otel_tracer_noop.erl">>,
  <<"src/otel_utils.erl">>,<<"src/otel_propagator_trace_context.erl">>,
  <<"src/otel_span.erl">>,<<"src/opentelemetry.erl">>,
  <<"src/opentelemetry_api.app.src">>,<<"src/otel_tracer_provider.erl">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"opentelemetry_semantic_conventions">>},
   {<<"app">>,<<"opentelemetry_semantic_conventions">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.2">>},
   {<<"repository">>,<<"hexpm">>}]]}.
