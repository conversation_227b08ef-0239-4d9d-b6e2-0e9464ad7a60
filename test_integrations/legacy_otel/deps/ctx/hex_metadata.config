{<<"app">>,<<"ctx">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"Context carries request scoped values and deadlines">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"rebar.config">>,<<"rebar.lock">>,
  <<"src/ctx.app.src">>,<<"src/ctx.erl">>]}.
{<<"licenses">>,[<<"Apache 2.0">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/tsloughter/ctx">>}]}.
{<<"name">>,<<"ctx">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"0.6.0">>}.
