%% -*- coding: utf-8 -*-
%% @private
%% Automatically generated, do not edit
%% Generated by gpb_compile version 4.19.5
%% Version source: file
-module(opentelemetry_exporter_metrics_service_pb).

-export([encode_msg/2, encode_msg/3]).
-export([decode_msg/2, decode_msg/3]).
-export([merge_msgs/3, merge_msgs/4]).
-export([verify_msg/2, verify_msg/3]).
-export([get_msg_defs/0]).
-export([get_msg_names/0]).
-export([get_group_names/0]).
-export([get_msg_or_group_names/0]).
-export([get_enum_names/0]).
-export([find_msg_def/1, fetch_msg_def/1]).
-export([find_enum_def/1, fetch_enum_def/1]).
-export([enum_symbol_by_value/2, enum_value_by_symbol/2]).
-export(['enum_symbol_by_value_opentelemetry.proto.metrics.v1.AggregationTemporality'/1, 'enum_value_by_symbol_opentelemetry.proto.metrics.v1.AggregationTemporality'/1]).
-export(['enum_symbol_by_value_opentelemetry.proto.metrics.v1.DataPointFlags'/1, 'enum_value_by_symbol_opentelemetry.proto.metrics.v1.DataPointFlags'/1]).
-export([get_service_names/0]).
-export([get_service_def/1]).
-export([get_rpc_names/1]).
-export([find_rpc_def/2, fetch_rpc_def/2]).
-export([fqbin_to_service_name/1]).
-export([service_name_to_fqbin/1]).
-export([fqbins_to_service_and_rpc_name/2]).
-export([service_and_rpc_name_to_fqbins/2]).
-export([fqbin_to_msg_name/1]).
-export([msg_name_to_fqbin/1]).
-export([fqbin_to_enum_name/1]).
-export([enum_name_to_fqbin/1]).
-export([get_package_name/0]).
-export([uses_packages/0]).
-export([source_basename/0]).
-export([get_all_source_basenames/0]).
-export([get_all_proto_names/0]).
-export([get_msg_containment/1]).
-export([get_pkg_containment/1]).
-export([get_service_containment/1]).
-export([get_rpc_containment/1]).
-export([get_enum_containment/1]).
-export([get_proto_by_msg_name_as_fqbin/1]).
-export([get_proto_by_service_name_as_fqbin/1]).
-export([get_proto_by_enum_name_as_fqbin/1]).
-export([get_protos_by_pkg_name_as_fqbin/1]).
-export([gpb_version_as_string/0, gpb_version_as_list/0]).
-export([gpb_version_source/0]).


%% enumerated types
-type 'opentelemetry.proto.metrics.v1.AggregationTemporality'() :: 'AGGREGATION_TEMPORALITY_UNSPECIFIED' | 'AGGREGATION_TEMPORALITY_DELTA' | 'AGGREGATION_TEMPORALITY_CUMULATIVE'.
-type 'opentelemetry.proto.metrics.v1.DataPointFlags'() :: 'FLAG_NONE' | 'FLAG_NO_RECORDED_VALUE'.
-export_type(['opentelemetry.proto.metrics.v1.AggregationTemporality'/0, 'opentelemetry.proto.metrics.v1.DataPointFlags'/0]).

%% message types
-type export_metrics_service_request() ::
      #{resource_metrics        => [resource_metrics()] % = 1, repeated
       }.

-type export_metrics_service_response() ::
      #{partial_success         => export_metrics_partial_success() % = 1, optional
       }.

-type export_metrics_partial_success() ::
      #{rejected_data_points    => integer(),       % = 1, optional, 64 bits
        error_message           => unicode:chardata() % = 2, optional
       }.

-type metrics_data() ::
      #{resource_metrics        => [resource_metrics()] % = 1, repeated
       }.

-type resource_metrics() ::
      #{resource                => resource(),      % = 1, optional
        scope_metrics           => [scope_metrics()], % = 2, repeated
        schema_url              => unicode:chardata() % = 3, optional
       }.

-type scope_metrics() ::
      #{scope                   => instrumentation_scope(), % = 1, optional
        metrics                 => [metric()],      % = 2, repeated
        schema_url              => unicode:chardata() % = 3, optional
       }.

-type metric() ::
      #{name                    => unicode:chardata(), % = 1, optional
        description             => unicode:chardata(), % = 2, optional
        unit                    => unicode:chardata(), % = 3, optional
        data                    => {gauge, gauge()} | {sum, sum()} | {histogram, histogram()} | {exponential_histogram, exponential_histogram()} | {summary, summary()} % oneof
       }.

-type gauge() ::
      #{data_points             => [number_data_point()] % = 1, repeated
       }.

-type sum() ::
      #{data_points             => [number_data_point()], % = 1, repeated
        aggregation_temporality => 'AGGREGATION_TEMPORALITY_UNSPECIFIED' | 'AGGREGATION_TEMPORALITY_DELTA' | 'AGGREGATION_TEMPORALITY_CUMULATIVE' | integer(), % = 2, optional, enum opentelemetry.proto.metrics.v1.AggregationTemporality
        is_monotonic            => boolean() | 0 | 1 % = 3, optional
       }.

-type histogram() ::
      #{data_points             => [histogram_data_point()], % = 1, repeated
        aggregation_temporality => 'AGGREGATION_TEMPORALITY_UNSPECIFIED' | 'AGGREGATION_TEMPORALITY_DELTA' | 'AGGREGATION_TEMPORALITY_CUMULATIVE' | integer() % = 2, optional, enum opentelemetry.proto.metrics.v1.AggregationTemporality
       }.

-type exponential_histogram() ::
      #{data_points             => [exponential_histogram_data_point()], % = 1, repeated
        aggregation_temporality => 'AGGREGATION_TEMPORALITY_UNSPECIFIED' | 'AGGREGATION_TEMPORALITY_DELTA' | 'AGGREGATION_TEMPORALITY_CUMULATIVE' | integer() % = 2, optional, enum opentelemetry.proto.metrics.v1.AggregationTemporality
       }.

-type summary() ::
      #{data_points             => [summary_data_point()] % = 1, repeated
       }.

-type number_data_point() ::
      #{attributes              => [key_value()],   % = 7, repeated
        start_time_unix_nano    => non_neg_integer(), % = 2, optional, 64 bits
        time_unix_nano          => non_neg_integer(), % = 3, optional, 64 bits
        value                   => {as_double, float() | integer() | infinity | '-infinity' | nan} | {as_int, integer()}, % oneof
        exemplars               => [exemplar()],    % = 5, repeated
        flags                   => non_neg_integer() % = 8, optional, 32 bits
       }.

-type histogram_data_point() ::
      #{attributes              => [key_value()],   % = 9, repeated
        start_time_unix_nano    => non_neg_integer(), % = 2, optional, 64 bits
        time_unix_nano          => non_neg_integer(), % = 3, optional, 64 bits
        count                   => non_neg_integer(), % = 4, optional, 64 bits
        sum                     => float() | integer() | infinity | '-infinity' | nan, % = 5, optional
        bucket_counts           => [non_neg_integer()], % = 6, repeated, 64 bits
        explicit_bounds         => [float() | integer() | infinity | '-infinity' | nan], % = 7, repeated
        exemplars               => [exemplar()],    % = 8, repeated
        flags                   => non_neg_integer(), % = 10, optional, 32 bits
        min                     => float() | integer() | infinity | '-infinity' | nan, % = 11, optional
        max                     => float() | integer() | infinity | '-infinity' | nan % = 12, optional
       }.

-type buckets() ::
      #{offset                  => integer(),       % = 1, optional, 32 bits
        bucket_counts           => [non_neg_integer()] % = 2, repeated, 64 bits
       }.

-type exponential_histogram_data_point() ::
      #{attributes              => [key_value()],   % = 1, repeated
        start_time_unix_nano    => non_neg_integer(), % = 2, optional, 64 bits
        time_unix_nano          => non_neg_integer(), % = 3, optional, 64 bits
        count                   => non_neg_integer(), % = 4, optional, 64 bits
        sum                     => float() | integer() | infinity | '-infinity' | nan, % = 5, optional
        scale                   => integer(),       % = 6, optional, 32 bits
        zero_count              => non_neg_integer(), % = 7, optional, 64 bits
        positive                => buckets(),       % = 8, optional
        negative                => buckets(),       % = 9, optional
        flags                   => non_neg_integer(), % = 10, optional, 32 bits
        exemplars               => [exemplar()],    % = 11, repeated
        min                     => float() | integer() | infinity | '-infinity' | nan, % = 12, optional
        max                     => float() | integer() | infinity | '-infinity' | nan % = 13, optional
       }.

-type value_at_quantile() ::
      #{quantile                => float() | integer() | infinity | '-infinity' | nan, % = 1, optional
        value                   => float() | integer() | infinity | '-infinity' | nan % = 2, optional
       }.

-type summary_data_point() ::
      #{attributes              => [key_value()],   % = 7, repeated
        start_time_unix_nano    => non_neg_integer(), % = 2, optional, 64 bits
        time_unix_nano          => non_neg_integer(), % = 3, optional, 64 bits
        count                   => non_neg_integer(), % = 4, optional, 64 bits
        sum                     => float() | integer() | infinity | '-infinity' | nan, % = 5, optional
        quantile_values         => [value_at_quantile()], % = 6, repeated
        flags                   => non_neg_integer() % = 8, optional, 32 bits
       }.

-type exemplar() ::
      #{filtered_attributes     => [key_value()],   % = 7, repeated
        time_unix_nano          => non_neg_integer(), % = 2, optional, 64 bits
        value                   => {as_double, float() | integer() | infinity | '-infinity' | nan} | {as_int, integer()}, % oneof
        span_id                 => iodata(),        % = 4, optional
        trace_id                => iodata()         % = 5, optional
       }.

-type any_value() ::
      #{value                   => {string_value, unicode:chardata()} | {bool_value, boolean() | 0 | 1} | {int_value, integer()} | {double_value, float() | integer() | infinity | '-infinity' | nan} | {array_value, array_value()} | {kvlist_value, key_value_list()} | {bytes_value, iodata()} % oneof
       }.

-type array_value() ::
      #{values                  => [any_value()]    % = 1, repeated
       }.

-type key_value_list() ::
      #{values                  => [key_value()]    % = 1, repeated
       }.

-type key_value() ::
      #{key                     => unicode:chardata(), % = 1, optional
        value                   => any_value()      % = 2, optional
       }.

-type instrumentation_scope() ::
      #{name                    => unicode:chardata(), % = 1, optional
        version                 => unicode:chardata(), % = 2, optional
        attributes              => [key_value()],   % = 3, repeated
        dropped_attributes_count => non_neg_integer() % = 4, optional, 32 bits
       }.

-type resource() ::
      #{attributes              => [key_value()],   % = 1, repeated
        dropped_attributes_count => non_neg_integer() % = 2, optional, 32 bits
       }.

-export_type(['export_metrics_service_request'/0, 'export_metrics_service_response'/0, 'export_metrics_partial_success'/0, 'metrics_data'/0, 'resource_metrics'/0, 'scope_metrics'/0, 'metric'/0, 'gauge'/0, 'sum'/0, 'histogram'/0, 'exponential_histogram'/0, 'summary'/0, 'number_data_point'/0, 'histogram_data_point'/0, 'buckets'/0, 'exponential_histogram_data_point'/0, 'value_at_quantile'/0, 'summary_data_point'/0, 'exemplar'/0, 'any_value'/0, 'array_value'/0, 'key_value_list'/0, 'key_value'/0, 'instrumentation_scope'/0, 'resource'/0]).
-type '$msg_name'() :: export_metrics_service_request | export_metrics_service_response | export_metrics_partial_success | metrics_data | resource_metrics | scope_metrics | metric | gauge | sum | histogram | exponential_histogram | summary | number_data_point | histogram_data_point | buckets | exponential_histogram_data_point | value_at_quantile | summary_data_point | exemplar | any_value | array_value | key_value_list | key_value | instrumentation_scope | resource.
-type '$msg'() :: export_metrics_service_request() | export_metrics_service_response() | export_metrics_partial_success() | metrics_data() | resource_metrics() | scope_metrics() | metric() | gauge() | sum() | histogram() | exponential_histogram() | summary() | number_data_point() | histogram_data_point() | buckets() | exponential_histogram_data_point() | value_at_quantile() | summary_data_point() | exemplar() | any_value() | array_value() | key_value_list() | key_value() | instrumentation_scope() | resource().
-export_type(['$msg_name'/0, '$msg'/0]).

-if(?OTP_RELEASE >= 24).
-dialyzer({no_underspecs, encode_msg/2}).
-endif.
-spec encode_msg('$msg'(), '$msg_name'()) -> binary().
encode_msg(Msg, MsgName) when is_atom(MsgName) -> encode_msg(Msg, MsgName, []).

-if(?OTP_RELEASE >= 24).
-dialyzer({no_underspecs, encode_msg/3}).
-endif.
-spec encode_msg('$msg'(), '$msg_name'(), list()) -> binary().
encode_msg(Msg, MsgName, Opts) ->
    case proplists:get_bool(verify, Opts) of
        true -> verify_msg(Msg, MsgName, Opts);
        false -> ok
    end,
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        export_metrics_service_request -> encode_msg_export_metrics_service_request(id(Msg, TrUserData), TrUserData);
        export_metrics_service_response -> encode_msg_export_metrics_service_response(id(Msg, TrUserData), TrUserData);
        export_metrics_partial_success -> encode_msg_export_metrics_partial_success(id(Msg, TrUserData), TrUserData);
        metrics_data -> encode_msg_metrics_data(id(Msg, TrUserData), TrUserData);
        resource_metrics -> encode_msg_resource_metrics(id(Msg, TrUserData), TrUserData);
        scope_metrics -> encode_msg_scope_metrics(id(Msg, TrUserData), TrUserData);
        metric -> encode_msg_metric(id(Msg, TrUserData), TrUserData);
        gauge -> encode_msg_gauge(id(Msg, TrUserData), TrUserData);
        sum -> encode_msg_sum(id(Msg, TrUserData), TrUserData);
        histogram -> encode_msg_histogram(id(Msg, TrUserData), TrUserData);
        exponential_histogram -> encode_msg_exponential_histogram(id(Msg, TrUserData), TrUserData);
        summary -> encode_msg_summary(id(Msg, TrUserData), TrUserData);
        number_data_point -> encode_msg_number_data_point(id(Msg, TrUserData), TrUserData);
        histogram_data_point -> encode_msg_histogram_data_point(id(Msg, TrUserData), TrUserData);
        buckets -> encode_msg_buckets(id(Msg, TrUserData), TrUserData);
        exponential_histogram_data_point -> encode_msg_exponential_histogram_data_point(id(Msg, TrUserData), TrUserData);
        value_at_quantile -> encode_msg_value_at_quantile(id(Msg, TrUserData), TrUserData);
        summary_data_point -> encode_msg_summary_data_point(id(Msg, TrUserData), TrUserData);
        exemplar -> encode_msg_exemplar(id(Msg, TrUserData), TrUserData);
        any_value -> encode_msg_any_value(id(Msg, TrUserData), TrUserData);
        array_value -> encode_msg_array_value(id(Msg, TrUserData), TrUserData);
        key_value_list -> encode_msg_key_value_list(id(Msg, TrUserData), TrUserData);
        key_value -> encode_msg_key_value(id(Msg, TrUserData), TrUserData);
        instrumentation_scope -> encode_msg_instrumentation_scope(id(Msg, TrUserData), TrUserData);
        resource -> encode_msg_resource(id(Msg, TrUserData), TrUserData)
    end.


encode_msg_export_metrics_service_request(Msg, TrUserData) -> encode_msg_export_metrics_service_request(Msg, <<>>, TrUserData).


encode_msg_export_metrics_service_request(#{} = M, Bin, TrUserData) ->
    case M of
        #{resource_metrics := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_export_metrics_service_request_resource_metrics(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_export_metrics_service_response(Msg, TrUserData) -> encode_msg_export_metrics_service_response(Msg, <<>>, TrUserData).


encode_msg_export_metrics_service_response(#{} = M, Bin, TrUserData) ->
    case M of
        #{partial_success := F1} ->
            begin
                TrF1 = id(F1, TrUserData),
                if TrF1 =:= undefined -> Bin;
                   true -> e_mfield_export_metrics_service_response_partial_success(TrF1, <<Bin/binary, 10>>, TrUserData)
                end
            end;
        _ -> Bin
    end.

encode_msg_export_metrics_partial_success(Msg, TrUserData) -> encode_msg_export_metrics_partial_success(Msg, <<>>, TrUserData).


encode_msg_export_metrics_partial_success(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{rejected_data_points := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= 0 -> Bin;
                        true -> e_type_int64(TrF1, <<Bin/binary, 8>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{error_message := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                case is_empty_string(TrF2) of
                    true -> B1;
                    false -> e_type_string(TrF2, <<B1/binary, 18>>, TrUserData)
                end
            end;
        _ -> B1
    end.

encode_msg_metrics_data(Msg, TrUserData) -> encode_msg_metrics_data(Msg, <<>>, TrUserData).


encode_msg_metrics_data(#{} = M, Bin, TrUserData) ->
    case M of
        #{resource_metrics := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_metrics_data_resource_metrics(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_resource_metrics(Msg, TrUserData) -> encode_msg_resource_metrics(Msg, <<>>, TrUserData).


encode_msg_resource_metrics(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{resource := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= undefined -> Bin;
                        true -> e_mfield_resource_metrics_resource(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{scope_metrics := F2} ->
                 TrF2 = id(F2, TrUserData),
                 if TrF2 == [] -> B1;
                    true -> e_field_resource_metrics_scope_metrics(TrF2, B1, TrUserData)
                 end;
             _ -> B1
         end,
    case M of
        #{schema_url := F3} ->
            begin
                TrF3 = id(F3, TrUserData),
                case is_empty_string(TrF3) of
                    true -> B2;
                    false -> e_type_string(TrF3, <<B2/binary, 26>>, TrUserData)
                end
            end;
        _ -> B2
    end.

encode_msg_scope_metrics(Msg, TrUserData) -> encode_msg_scope_metrics(Msg, <<>>, TrUserData).


encode_msg_scope_metrics(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{scope := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= undefined -> Bin;
                        true -> e_mfield_scope_metrics_scope(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{metrics := F2} ->
                 TrF2 = id(F2, TrUserData),
                 if TrF2 == [] -> B1;
                    true -> e_field_scope_metrics_metrics(TrF2, B1, TrUserData)
                 end;
             _ -> B1
         end,
    case M of
        #{schema_url := F3} ->
            begin
                TrF3 = id(F3, TrUserData),
                case is_empty_string(TrF3) of
                    true -> B2;
                    false -> e_type_string(TrF3, <<B2/binary, 26>>, TrUserData)
                end
            end;
        _ -> B2
    end.

encode_msg_metric(Msg, TrUserData) -> encode_msg_metric(Msg, <<>>, TrUserData).


encode_msg_metric(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{name := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false -> e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{description := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     case is_empty_string(TrF2) of
                         true -> B1;
                         false -> e_type_string(TrF2, <<B1/binary, 18>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{unit := F3} ->
                 begin
                     TrF3 = id(F3, TrUserData),
                     case is_empty_string(TrF3) of
                         true -> B2;
                         false -> e_type_string(TrF3, <<B2/binary, 26>>, TrUserData)
                     end
                 end;
             _ -> B2
         end,
    case M of
        #{data := F4} ->
            case id(F4, TrUserData) of
                {gauge, TF4} -> begin TrTF4 = id(TF4, TrUserData), e_mfield_metric_gauge(TrTF4, <<B3/binary, 42>>, TrUserData) end;
                {sum, TF4} -> begin TrTF4 = id(TF4, TrUserData), e_mfield_metric_sum(TrTF4, <<B3/binary, 58>>, TrUserData) end;
                {histogram, TF4} -> begin TrTF4 = id(TF4, TrUserData), e_mfield_metric_histogram(TrTF4, <<B3/binary, 74>>, TrUserData) end;
                {exponential_histogram, TF4} -> begin TrTF4 = id(TF4, TrUserData), e_mfield_metric_exponential_histogram(TrTF4, <<B3/binary, 82>>, TrUserData) end;
                {summary, TF4} -> begin TrTF4 = id(TF4, TrUserData), e_mfield_metric_summary(TrTF4, <<B3/binary, 90>>, TrUserData) end
            end;
        _ -> B3
    end.

encode_msg_gauge(Msg, TrUserData) -> encode_msg_gauge(Msg, <<>>, TrUserData).


encode_msg_gauge(#{} = M, Bin, TrUserData) ->
    case M of
        #{data_points := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_gauge_data_points(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_sum(Msg, TrUserData) -> encode_msg_sum(Msg, <<>>, TrUserData).


encode_msg_sum(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{data_points := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_sum_data_points(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{aggregation_temporality := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     if TrF2 =:= 'AGGREGATION_TEMPORALITY_UNSPECIFIED'; TrF2 =:= 0 -> B1;
                        true -> 'e_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(TrF2, <<B1/binary, 16>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    case M of
        #{is_monotonic := F3} ->
            begin
                TrF3 = id(F3, TrUserData),
                if TrF3 =:= false -> B2;
                   true -> e_type_bool(TrF3, <<B2/binary, 24>>, TrUserData)
                end
            end;
        _ -> B2
    end.

encode_msg_histogram(Msg, TrUserData) -> encode_msg_histogram(Msg, <<>>, TrUserData).


encode_msg_histogram(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{data_points := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_histogram_data_points(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    case M of
        #{aggregation_temporality := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                if TrF2 =:= 'AGGREGATION_TEMPORALITY_UNSPECIFIED'; TrF2 =:= 0 -> B1;
                   true -> 'e_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(TrF2, <<B1/binary, 16>>, TrUserData)
                end
            end;
        _ -> B1
    end.

encode_msg_exponential_histogram(Msg, TrUserData) -> encode_msg_exponential_histogram(Msg, <<>>, TrUserData).


encode_msg_exponential_histogram(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{data_points := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_exponential_histogram_data_points(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    case M of
        #{aggregation_temporality := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                if TrF2 =:= 'AGGREGATION_TEMPORALITY_UNSPECIFIED'; TrF2 =:= 0 -> B1;
                   true -> 'e_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(TrF2, <<B1/binary, 16>>, TrUserData)
                end
            end;
        _ -> B1
    end.

encode_msg_summary(Msg, TrUserData) -> encode_msg_summary(Msg, <<>>, TrUserData).


encode_msg_summary(#{} = M, Bin, TrUserData) ->
    case M of
        #{data_points := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_summary_data_points(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_number_data_point(Msg, TrUserData) -> encode_msg_number_data_point(Msg, <<>>, TrUserData).


encode_msg_number_data_point(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{attributes := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_number_data_point_attributes(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{start_time_unix_nano := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     if TrF2 =:= 0 -> B1;
                        true -> e_type_fixed64(TrF2, <<B1/binary, 17>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{time_unix_nano := F3} ->
                 begin
                     TrF3 = id(F3, TrUserData),
                     if TrF3 =:= 0 -> B2;
                        true -> e_type_fixed64(TrF3, <<B2/binary, 25>>, TrUserData)
                     end
                 end;
             _ -> B2
         end,
    B4 = case M of
             #{value := F4} ->
                 case id(F4, TrUserData) of
                     {as_double, TF4} -> begin TrTF4 = id(TF4, TrUserData), e_type_double(TrTF4, <<B3/binary, 33>>, TrUserData) end;
                     {as_int, TF4} -> begin TrTF4 = id(TF4, TrUserData), e_type_sfixed64(TrTF4, <<B3/binary, 49>>, TrUserData) end
                 end;
             _ -> B3
         end,
    B5 = case M of
             #{exemplars := F5} ->
                 TrF5 = id(F5, TrUserData),
                 if TrF5 == [] -> B4;
                    true -> e_field_number_data_point_exemplars(TrF5, B4, TrUserData)
                 end;
             _ -> B4
         end,
    case M of
        #{flags := F6} ->
            begin
                TrF6 = id(F6, TrUserData),
                if TrF6 =:= 0 -> B5;
                   true -> e_varint(TrF6, <<B5/binary, 64>>, TrUserData)
                end
            end;
        _ -> B5
    end.

encode_msg_histogram_data_point(Msg, TrUserData) -> encode_msg_histogram_data_point(Msg, <<>>, TrUserData).


encode_msg_histogram_data_point(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{attributes := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_histogram_data_point_attributes(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{start_time_unix_nano := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     if TrF2 =:= 0 -> B1;
                        true -> e_type_fixed64(TrF2, <<B1/binary, 17>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{time_unix_nano := F3} ->
                 begin
                     TrF3 = id(F3, TrUserData),
                     if TrF3 =:= 0 -> B2;
                        true -> e_type_fixed64(TrF3, <<B2/binary, 25>>, TrUserData)
                     end
                 end;
             _ -> B2
         end,
    B4 = case M of
             #{count := F4} ->
                 begin
                     TrF4 = id(F4, TrUserData),
                     if TrF4 =:= 0 -> B3;
                        true -> e_type_fixed64(TrF4, <<B3/binary, 33>>, TrUserData)
                     end
                 end;
             _ -> B3
         end,
    B5 = case M of
             #{sum := F5} -> begin TrF5 = id(F5, TrUserData), e_type_double(TrF5, <<B4/binary, 41>>, TrUserData) end;
             _ -> B4
         end,
    B6 = case M of
             #{bucket_counts := F6} ->
                 TrF6 = id(F6, TrUserData),
                 if TrF6 == [] -> B5;
                    true -> e_field_histogram_data_point_bucket_counts(TrF6, B5, TrUserData)
                 end;
             _ -> B5
         end,
    B7 = case M of
             #{explicit_bounds := F7} ->
                 TrF7 = id(F7, TrUserData),
                 if TrF7 == [] -> B6;
                    true -> e_field_histogram_data_point_explicit_bounds(TrF7, B6, TrUserData)
                 end;
             _ -> B6
         end,
    B8 = case M of
             #{exemplars := F8} ->
                 TrF8 = id(F8, TrUserData),
                 if TrF8 == [] -> B7;
                    true -> e_field_histogram_data_point_exemplars(TrF8, B7, TrUserData)
                 end;
             _ -> B7
         end,
    B9 = case M of
             #{flags := F9} ->
                 begin
                     TrF9 = id(F9, TrUserData),
                     if TrF9 =:= 0 -> B8;
                        true -> e_varint(TrF9, <<B8/binary, 80>>, TrUserData)
                     end
                 end;
             _ -> B8
         end,
    B10 = case M of
              #{min := F10} -> begin TrF10 = id(F10, TrUserData), e_type_double(TrF10, <<B9/binary, 89>>, TrUserData) end;
              _ -> B9
          end,
    case M of
        #{max := F11} -> begin TrF11 = id(F11, TrUserData), e_type_double(TrF11, <<B10/binary, 97>>, TrUserData) end;
        _ -> B10
    end.

encode_msg_buckets(Msg, TrUserData) -> encode_msg_buckets(Msg, <<>>, TrUserData).


encode_msg_buckets(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{offset := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= 0 -> Bin;
                        true -> e_type_sint(TrF1, <<Bin/binary, 8>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{bucket_counts := F2} ->
            TrF2 = id(F2, TrUserData),
            if TrF2 == [] -> B1;
               true -> e_field_buckets_bucket_counts(TrF2, B1, TrUserData)
            end;
        _ -> B1
    end.

encode_msg_exponential_histogram_data_point(Msg, TrUserData) -> encode_msg_exponential_histogram_data_point(Msg, <<>>, TrUserData).


encode_msg_exponential_histogram_data_point(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{attributes := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_exponential_histogram_data_point_attributes(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{start_time_unix_nano := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     if TrF2 =:= 0 -> B1;
                        true -> e_type_fixed64(TrF2, <<B1/binary, 17>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{time_unix_nano := F3} ->
                 begin
                     TrF3 = id(F3, TrUserData),
                     if TrF3 =:= 0 -> B2;
                        true -> e_type_fixed64(TrF3, <<B2/binary, 25>>, TrUserData)
                     end
                 end;
             _ -> B2
         end,
    B4 = case M of
             #{count := F4} ->
                 begin
                     TrF4 = id(F4, TrUserData),
                     if TrF4 =:= 0 -> B3;
                        true -> e_type_fixed64(TrF4, <<B3/binary, 33>>, TrUserData)
                     end
                 end;
             _ -> B3
         end,
    B5 = case M of
             #{sum := F5} -> begin TrF5 = id(F5, TrUserData), e_type_double(TrF5, <<B4/binary, 41>>, TrUserData) end;
             _ -> B4
         end,
    B6 = case M of
             #{scale := F6} ->
                 begin
                     TrF6 = id(F6, TrUserData),
                     if TrF6 =:= 0 -> B5;
                        true -> e_type_sint(TrF6, <<B5/binary, 48>>, TrUserData)
                     end
                 end;
             _ -> B5
         end,
    B7 = case M of
             #{zero_count := F7} ->
                 begin
                     TrF7 = id(F7, TrUserData),
                     if TrF7 =:= 0 -> B6;
                        true -> e_type_fixed64(TrF7, <<B6/binary, 57>>, TrUserData)
                     end
                 end;
             _ -> B6
         end,
    B8 = case M of
             #{positive := F8} ->
                 begin
                     TrF8 = id(F8, TrUserData),
                     if TrF8 =:= undefined -> B7;
                        true -> e_mfield_exponential_histogram_data_point_positive(TrF8, <<B7/binary, 66>>, TrUserData)
                     end
                 end;
             _ -> B7
         end,
    B9 = case M of
             #{negative := F9} ->
                 begin
                     TrF9 = id(F9, TrUserData),
                     if TrF9 =:= undefined -> B8;
                        true -> e_mfield_exponential_histogram_data_point_negative(TrF9, <<B8/binary, 74>>, TrUserData)
                     end
                 end;
             _ -> B8
         end,
    B10 = case M of
              #{flags := F10} ->
                  begin
                      TrF10 = id(F10, TrUserData),
                      if TrF10 =:= 0 -> B9;
                         true -> e_varint(TrF10, <<B9/binary, 80>>, TrUserData)
                      end
                  end;
              _ -> B9
          end,
    B11 = case M of
              #{exemplars := F11} ->
                  TrF11 = id(F11, TrUserData),
                  if TrF11 == [] -> B10;
                     true -> e_field_exponential_histogram_data_point_exemplars(TrF11, B10, TrUserData)
                  end;
              _ -> B10
          end,
    B12 = case M of
              #{min := F12} -> begin TrF12 = id(F12, TrUserData), e_type_double(TrF12, <<B11/binary, 97>>, TrUserData) end;
              _ -> B11
          end,
    case M of
        #{max := F13} -> begin TrF13 = id(F13, TrUserData), e_type_double(TrF13, <<B12/binary, 105>>, TrUserData) end;
        _ -> B12
    end.

encode_msg_value_at_quantile(Msg, TrUserData) -> encode_msg_value_at_quantile(Msg, <<>>, TrUserData).


encode_msg_value_at_quantile(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{quantile := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= 0.0 -> Bin;
                        true -> e_type_double(TrF1, <<Bin/binary, 9>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{value := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                if TrF2 =:= 0.0 -> B1;
                   true -> e_type_double(TrF2, <<B1/binary, 17>>, TrUserData)
                end
            end;
        _ -> B1
    end.

encode_msg_summary_data_point(Msg, TrUserData) -> encode_msg_summary_data_point(Msg, <<>>, TrUserData).


encode_msg_summary_data_point(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{attributes := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_summary_data_point_attributes(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{start_time_unix_nano := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     if TrF2 =:= 0 -> B1;
                        true -> e_type_fixed64(TrF2, <<B1/binary, 17>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{time_unix_nano := F3} ->
                 begin
                     TrF3 = id(F3, TrUserData),
                     if TrF3 =:= 0 -> B2;
                        true -> e_type_fixed64(TrF3, <<B2/binary, 25>>, TrUserData)
                     end
                 end;
             _ -> B2
         end,
    B4 = case M of
             #{count := F4} ->
                 begin
                     TrF4 = id(F4, TrUserData),
                     if TrF4 =:= 0 -> B3;
                        true -> e_type_fixed64(TrF4, <<B3/binary, 33>>, TrUserData)
                     end
                 end;
             _ -> B3
         end,
    B5 = case M of
             #{sum := F5} ->
                 begin
                     TrF5 = id(F5, TrUserData),
                     if TrF5 =:= 0.0 -> B4;
                        true -> e_type_double(TrF5, <<B4/binary, 41>>, TrUserData)
                     end
                 end;
             _ -> B4
         end,
    B6 = case M of
             #{quantile_values := F6} ->
                 TrF6 = id(F6, TrUserData),
                 if TrF6 == [] -> B5;
                    true -> e_field_summary_data_point_quantile_values(TrF6, B5, TrUserData)
                 end;
             _ -> B5
         end,
    case M of
        #{flags := F7} ->
            begin
                TrF7 = id(F7, TrUserData),
                if TrF7 =:= 0 -> B6;
                   true -> e_varint(TrF7, <<B6/binary, 64>>, TrUserData)
                end
            end;
        _ -> B6
    end.

encode_msg_exemplar(Msg, TrUserData) -> encode_msg_exemplar(Msg, <<>>, TrUserData).


encode_msg_exemplar(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{filtered_attributes := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_exemplar_filtered_attributes(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{time_unix_nano := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     if TrF2 =:= 0 -> B1;
                        true -> e_type_fixed64(TrF2, <<B1/binary, 17>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{value := F3} ->
                 case id(F3, TrUserData) of
                     {as_double, TF3} -> begin TrTF3 = id(TF3, TrUserData), e_type_double(TrTF3, <<B2/binary, 25>>, TrUserData) end;
                     {as_int, TF3} -> begin TrTF3 = id(TF3, TrUserData), e_type_sfixed64(TrTF3, <<B2/binary, 49>>, TrUserData) end
                 end;
             _ -> B2
         end,
    B4 = case M of
             #{span_id := F4} ->
                 begin
                     TrF4 = id(F4, TrUserData),
                     case iolist_size(TrF4) of
                         0 -> B3;
                         _ -> e_type_bytes(TrF4, <<B3/binary, 34>>, TrUserData)
                     end
                 end;
             _ -> B3
         end,
    case M of
        #{trace_id := F5} ->
            begin
                TrF5 = id(F5, TrUserData),
                case iolist_size(TrF5) of
                    0 -> B4;
                    _ -> e_type_bytes(TrF5, <<B4/binary, 42>>, TrUserData)
                end
            end;
        _ -> B4
    end.

encode_msg_any_value(Msg, TrUserData) -> encode_msg_any_value(Msg, <<>>, TrUserData).


encode_msg_any_value(#{} = M, Bin, TrUserData) ->
    case M of
        #{value := F1} ->
            case id(F1, TrUserData) of
                {string_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_string(TrTF1, <<Bin/binary, 10>>, TrUserData) end;
                {bool_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_bool(TrTF1, <<Bin/binary, 16>>, TrUserData) end;
                {int_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_int64(TrTF1, <<Bin/binary, 24>>, TrUserData) end;
                {double_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_double(TrTF1, <<Bin/binary, 33>>, TrUserData) end;
                {array_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_mfield_any_value_array_value(TrTF1, <<Bin/binary, 42>>, TrUserData) end;
                {kvlist_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_mfield_any_value_kvlist_value(TrTF1, <<Bin/binary, 50>>, TrUserData) end;
                {bytes_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_bytes(TrTF1, <<Bin/binary, 58>>, TrUserData) end
            end;
        _ -> Bin
    end.

encode_msg_array_value(Msg, TrUserData) -> encode_msg_array_value(Msg, <<>>, TrUserData).


encode_msg_array_value(#{} = M, Bin, TrUserData) ->
    case M of
        #{values := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_array_value_values(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_key_value_list(Msg, TrUserData) -> encode_msg_key_value_list(Msg, <<>>, TrUserData).


encode_msg_key_value_list(#{} = M, Bin, TrUserData) ->
    case M of
        #{values := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_key_value_list_values(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_key_value(Msg, TrUserData) -> encode_msg_key_value(Msg, <<>>, TrUserData).


encode_msg_key_value(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{key := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false -> e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{value := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                if TrF2 =:= undefined -> B1;
                   true -> e_mfield_key_value_value(TrF2, <<B1/binary, 18>>, TrUserData)
                end
            end;
        _ -> B1
    end.

encode_msg_instrumentation_scope(Msg, TrUserData) -> encode_msg_instrumentation_scope(Msg, <<>>, TrUserData).


encode_msg_instrumentation_scope(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{name := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false -> e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{version := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     case is_empty_string(TrF2) of
                         true -> B1;
                         false -> e_type_string(TrF2, <<B1/binary, 18>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{attributes := F3} ->
                 TrF3 = id(F3, TrUserData),
                 if TrF3 == [] -> B2;
                    true -> e_field_instrumentation_scope_attributes(TrF3, B2, TrUserData)
                 end;
             _ -> B2
         end,
    case M of
        #{dropped_attributes_count := F4} ->
            begin
                TrF4 = id(F4, TrUserData),
                if TrF4 =:= 0 -> B3;
                   true -> e_varint(TrF4, <<B3/binary, 32>>, TrUserData)
                end
            end;
        _ -> B3
    end.

encode_msg_resource(Msg, TrUserData) -> encode_msg_resource(Msg, <<>>, TrUserData).


encode_msg_resource(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{attributes := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_resource_attributes(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    case M of
        #{dropped_attributes_count := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                if TrF2 =:= 0 -> B1;
                   true -> e_varint(TrF2, <<B1/binary, 16>>, TrUserData)
                end
            end;
        _ -> B1
    end.

e_mfield_export_metrics_service_request_resource_metrics(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_resource_metrics(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_export_metrics_service_request_resource_metrics([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_export_metrics_service_request_resource_metrics(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_export_metrics_service_request_resource_metrics(Rest, Bin3, TrUserData);
e_field_export_metrics_service_request_resource_metrics([], Bin, _TrUserData) -> Bin.

e_mfield_export_metrics_service_response_partial_success(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_export_metrics_partial_success(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_metrics_data_resource_metrics(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_resource_metrics(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_metrics_data_resource_metrics([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_metrics_data_resource_metrics(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_metrics_data_resource_metrics(Rest, Bin3, TrUserData);
e_field_metrics_data_resource_metrics([], Bin, _TrUserData) -> Bin.

e_mfield_resource_metrics_resource(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_resource(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_resource_metrics_scope_metrics(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_scope_metrics(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_resource_metrics_scope_metrics([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 18>>,
    Bin3 = e_mfield_resource_metrics_scope_metrics(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_resource_metrics_scope_metrics(Rest, Bin3, TrUserData);
e_field_resource_metrics_scope_metrics([], Bin, _TrUserData) -> Bin.

e_mfield_scope_metrics_scope(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_instrumentation_scope(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_scope_metrics_metrics(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_metric(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_scope_metrics_metrics([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 18>>,
    Bin3 = e_mfield_scope_metrics_metrics(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_scope_metrics_metrics(Rest, Bin3, TrUserData);
e_field_scope_metrics_metrics([], Bin, _TrUserData) -> Bin.

e_mfield_metric_gauge(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_gauge(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_metric_sum(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_sum(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_metric_histogram(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_histogram(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_metric_exponential_histogram(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_exponential_histogram(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_metric_summary(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_summary(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_gauge_data_points(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_number_data_point(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_gauge_data_points([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_gauge_data_points(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_gauge_data_points(Rest, Bin3, TrUserData);
e_field_gauge_data_points([], Bin, _TrUserData) -> Bin.

e_mfield_sum_data_points(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_number_data_point(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_sum_data_points([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_sum_data_points(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_sum_data_points(Rest, Bin3, TrUserData);
e_field_sum_data_points([], Bin, _TrUserData) -> Bin.

e_mfield_histogram_data_points(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_histogram_data_point(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_histogram_data_points([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_histogram_data_points(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_histogram_data_points(Rest, Bin3, TrUserData);
e_field_histogram_data_points([], Bin, _TrUserData) -> Bin.

e_mfield_exponential_histogram_data_points(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_exponential_histogram_data_point(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_exponential_histogram_data_points([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_exponential_histogram_data_points(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_exponential_histogram_data_points(Rest, Bin3, TrUserData);
e_field_exponential_histogram_data_points([], Bin, _TrUserData) -> Bin.

e_mfield_summary_data_points(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_summary_data_point(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_summary_data_points([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_summary_data_points(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_summary_data_points(Rest, Bin3, TrUserData);
e_field_summary_data_points([], Bin, _TrUserData) -> Bin.

e_mfield_number_data_point_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_number_data_point_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 58>>,
    Bin3 = e_mfield_number_data_point_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_number_data_point_attributes(Rest, Bin3, TrUserData);
e_field_number_data_point_attributes([], Bin, _TrUserData) -> Bin.

e_mfield_number_data_point_exemplars(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_exemplar(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_number_data_point_exemplars([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 42>>,
    Bin3 = e_mfield_number_data_point_exemplars(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_number_data_point_exemplars(Rest, Bin3, TrUserData);
e_field_number_data_point_exemplars([], Bin, _TrUserData) -> Bin.

e_mfield_histogram_data_point_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_histogram_data_point_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 74>>,
    Bin3 = e_mfield_histogram_data_point_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_histogram_data_point_attributes(Rest, Bin3, TrUserData);
e_field_histogram_data_point_attributes([], Bin, _TrUserData) -> Bin.

e_field_histogram_data_point_bucket_counts(Elems, Bin, TrUserData) when Elems =/= [] ->
    Bin2 = <<Bin/binary, 50>>,
    Bin3 = e_varint(length(Elems) * 8, Bin2),
    e_pfield_histogram_data_point_bucket_counts(Elems, Bin3, TrUserData);
e_field_histogram_data_point_bucket_counts([], Bin, _TrUserData) -> Bin.

e_pfield_histogram_data_point_bucket_counts([Value | Rest], Bin, TrUserData) ->
    TrValue = id(Value, TrUserData),
    Bin2 = <<Bin/binary, TrValue:64/little>>,
    e_pfield_histogram_data_point_bucket_counts(Rest, Bin2, TrUserData);
e_pfield_histogram_data_point_bucket_counts([], Bin, _TrUserData) -> Bin.

e_field_histogram_data_point_explicit_bounds(Elems, Bin, TrUserData) when Elems =/= [] ->
    Bin2 = <<Bin/binary, 58>>,
    Bin3 = e_varint(length(Elems) * 8, Bin2),
    e_pfield_histogram_data_point_explicit_bounds(Elems, Bin3, TrUserData);
e_field_histogram_data_point_explicit_bounds([], Bin, _TrUserData) -> Bin.

e_pfield_histogram_data_point_explicit_bounds([V | Rest], Bin, TrUserData) ->
    TrV = id(V, TrUserData),
    Bin2 = if is_number(TrV) -> <<Bin/binary, TrV:64/float-little>>;
              TrV =:= infinity -> <<Bin/binary, 0:48, 240, 127>>;
              TrV =:= '-infinity' -> <<Bin/binary, 0:48, 240, 255>>;
              TrV =:= nan -> <<Bin/binary, 0:48, 248, 127>>
           end,
    e_pfield_histogram_data_point_explicit_bounds(Rest, Bin2, TrUserData);
e_pfield_histogram_data_point_explicit_bounds([], Bin, _TrUserData) -> Bin.

e_mfield_histogram_data_point_exemplars(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_exemplar(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_histogram_data_point_exemplars([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 66>>,
    Bin3 = e_mfield_histogram_data_point_exemplars(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_histogram_data_point_exemplars(Rest, Bin3, TrUserData);
e_field_histogram_data_point_exemplars([], Bin, _TrUserData) -> Bin.

e_field_buckets_bucket_counts(Elems, Bin, TrUserData) when Elems =/= [] ->
    SubBin = e_pfield_buckets_bucket_counts(Elems, <<>>, TrUserData),
    Bin2 = <<Bin/binary, 18>>,
    Bin3 = e_varint(byte_size(SubBin), Bin2),
    <<Bin3/binary, SubBin/binary>>;
e_field_buckets_bucket_counts([], Bin, _TrUserData) -> Bin.

e_pfield_buckets_bucket_counts([Value | Rest], Bin, TrUserData) ->
    Bin2 = e_varint(id(Value, TrUserData), Bin, TrUserData),
    e_pfield_buckets_bucket_counts(Rest, Bin2, TrUserData);
e_pfield_buckets_bucket_counts([], Bin, _TrUserData) -> Bin.

e_mfield_exponential_histogram_data_point_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_exponential_histogram_data_point_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_exponential_histogram_data_point_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_exponential_histogram_data_point_attributes(Rest, Bin3, TrUserData);
e_field_exponential_histogram_data_point_attributes([], Bin, _TrUserData) -> Bin.

e_mfield_exponential_histogram_data_point_positive(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_buckets(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_exponential_histogram_data_point_negative(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_buckets(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_exponential_histogram_data_point_exemplars(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_exemplar(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_exponential_histogram_data_point_exemplars([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 90>>,
    Bin3 = e_mfield_exponential_histogram_data_point_exemplars(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_exponential_histogram_data_point_exemplars(Rest, Bin3, TrUserData);
e_field_exponential_histogram_data_point_exemplars([], Bin, _TrUserData) -> Bin.

e_mfield_summary_data_point_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_summary_data_point_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 58>>,
    Bin3 = e_mfield_summary_data_point_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_summary_data_point_attributes(Rest, Bin3, TrUserData);
e_field_summary_data_point_attributes([], Bin, _TrUserData) -> Bin.

e_mfield_summary_data_point_quantile_values(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_value_at_quantile(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_summary_data_point_quantile_values([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 50>>,
    Bin3 = e_mfield_summary_data_point_quantile_values(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_summary_data_point_quantile_values(Rest, Bin3, TrUserData);
e_field_summary_data_point_quantile_values([], Bin, _TrUserData) -> Bin.

e_mfield_exemplar_filtered_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_exemplar_filtered_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 58>>,
    Bin3 = e_mfield_exemplar_filtered_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_exemplar_filtered_attributes(Rest, Bin3, TrUserData);
e_field_exemplar_filtered_attributes([], Bin, _TrUserData) -> Bin.

e_mfield_any_value_array_value(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_array_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_any_value_kvlist_value(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value_list(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_array_value_values(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_any_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_array_value_values([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_array_value_values(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_array_value_values(Rest, Bin3, TrUserData);
e_field_array_value_values([], Bin, _TrUserData) -> Bin.

e_mfield_key_value_list_values(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_key_value_list_values([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_key_value_list_values(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_key_value_list_values(Rest, Bin3, TrUserData);
e_field_key_value_list_values([], Bin, _TrUserData) -> Bin.

e_mfield_key_value_value(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_any_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_instrumentation_scope_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_instrumentation_scope_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 26>>,
    Bin3 = e_mfield_instrumentation_scope_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_instrumentation_scope_attributes(Rest, Bin3, TrUserData);
e_field_instrumentation_scope_attributes([], Bin, _TrUserData) -> Bin.

e_mfield_resource_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_resource_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_resource_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_resource_attributes(Rest, Bin3, TrUserData);
e_field_resource_attributes([], Bin, _TrUserData) -> Bin.

'e_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_UNSPECIFIED', Bin, _TrUserData) -> <<Bin/binary, 0>>;
'e_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_DELTA', Bin, _TrUserData) -> <<Bin/binary, 1>>;
'e_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_CUMULATIVE', Bin, _TrUserData) -> <<Bin/binary, 2>>;
'e_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(V, Bin, _TrUserData) -> e_varint(V, Bin).

-compile({nowarn_unused_function,e_type_sint/3}).
e_type_sint(Value, Bin, _TrUserData) when Value >= 0 -> e_varint(Value * 2, Bin);
e_type_sint(Value, Bin, _TrUserData) -> e_varint(Value * -2 - 1, Bin).

-compile({nowarn_unused_function,e_type_int32/3}).
e_type_int32(Value, Bin, _TrUserData) when 0 =< Value, Value =< 127 -> <<Bin/binary, Value>>;
e_type_int32(Value, Bin, _TrUserData) ->
    <<N:64/unsigned-native>> = <<Value:64/signed-native>>,
    e_varint(N, Bin).

-compile({nowarn_unused_function,e_type_int64/3}).
e_type_int64(Value, Bin, _TrUserData) when 0 =< Value, Value =< 127 -> <<Bin/binary, Value>>;
e_type_int64(Value, Bin, _TrUserData) ->
    <<N:64/unsigned-native>> = <<Value:64/signed-native>>,
    e_varint(N, Bin).

-compile({nowarn_unused_function,e_type_bool/3}).
e_type_bool(true, Bin, _TrUserData) -> <<Bin/binary, 1>>;
e_type_bool(false, Bin, _TrUserData) -> <<Bin/binary, 0>>;
e_type_bool(1, Bin, _TrUserData) -> <<Bin/binary, 1>>;
e_type_bool(0, Bin, _TrUserData) -> <<Bin/binary, 0>>.

-compile({nowarn_unused_function,e_type_string/3}).
e_type_string(S, Bin, _TrUserData) ->
    Utf8 = unicode:characters_to_binary(S),
    Bin2 = e_varint(byte_size(Utf8), Bin),
    <<Bin2/binary, Utf8/binary>>.

-compile({nowarn_unused_function,e_type_bytes/3}).
e_type_bytes(Bytes, Bin, _TrUserData) when is_binary(Bytes) ->
    Bin2 = e_varint(byte_size(Bytes), Bin),
    <<Bin2/binary, Bytes/binary>>;
e_type_bytes(Bytes, Bin, _TrUserData) when is_list(Bytes) ->
    BytesBin = iolist_to_binary(Bytes),
    Bin2 = e_varint(byte_size(BytesBin), Bin),
    <<Bin2/binary, BytesBin/binary>>.

-compile({nowarn_unused_function,e_type_fixed32/3}).
e_type_fixed32(Value, Bin, _TrUserData) -> <<Bin/binary, Value:32/little>>.

-compile({nowarn_unused_function,e_type_sfixed32/3}).
e_type_sfixed32(Value, Bin, _TrUserData) -> <<Bin/binary, Value:32/little-signed>>.

-compile({nowarn_unused_function,e_type_fixed64/3}).
e_type_fixed64(Value, Bin, _TrUserData) -> <<Bin/binary, Value:64/little>>.

-compile({nowarn_unused_function,e_type_sfixed64/3}).
e_type_sfixed64(Value, Bin, _TrUserData) -> <<Bin/binary, Value:64/little-signed>>.

-compile({nowarn_unused_function,e_type_float/3}).
e_type_float(V, Bin, _) when is_number(V) -> <<Bin/binary, V:32/little-float>>;
e_type_float(infinity, Bin, _) -> <<Bin/binary, 0:16, 128, 127>>;
e_type_float('-infinity', Bin, _) -> <<Bin/binary, 0:16, 128, 255>>;
e_type_float(nan, Bin, _) -> <<Bin/binary, 0:16, 192, 127>>.

-compile({nowarn_unused_function,e_type_double/3}).
e_type_double(V, Bin, _) when is_number(V) -> <<Bin/binary, V:64/little-float>>;
e_type_double(infinity, Bin, _) -> <<Bin/binary, 0:48, 240, 127>>;
e_type_double('-infinity', Bin, _) -> <<Bin/binary, 0:48, 240, 255>>;
e_type_double(nan, Bin, _) -> <<Bin/binary, 0:48, 248, 127>>.

-compile({nowarn_unused_function,e_unknown_elems/2}).
e_unknown_elems([Elem | Rest], Bin) ->
    BinR = case Elem of
               {varint, FNum, N} ->
                   BinF = e_varint(FNum bsl 3, Bin),
                   e_varint(N, BinF);
               {length_delimited, FNum, Data} ->
                   BinF = e_varint(FNum bsl 3 bor 2, Bin),
                   BinL = e_varint(byte_size(Data), BinF),
                   <<BinL/binary, Data/binary>>;
               {group, FNum, GroupFields} ->
                   Bin1 = e_varint(FNum bsl 3 bor 3, Bin),
                   Bin2 = e_unknown_elems(GroupFields, Bin1),
                   e_varint(FNum bsl 3 bor 4, Bin2);
               {fixed32, FNum, V} ->
                   BinF = e_varint(FNum bsl 3 bor 5, Bin),
                   <<BinF/binary, V:32/little>>;
               {fixed64, FNum, V} ->
                   BinF = e_varint(FNum bsl 3 bor 1, Bin),
                   <<BinF/binary, V:64/little>>
           end,
    e_unknown_elems(Rest, BinR);
e_unknown_elems([], Bin) -> Bin.

-compile({nowarn_unused_function,e_varint/3}).
e_varint(N, Bin, _TrUserData) -> e_varint(N, Bin).

-compile({nowarn_unused_function,e_varint/2}).
e_varint(N, Bin) when N =< 127 -> <<Bin/binary, N>>;
e_varint(N, Bin) ->
    Bin2 = <<Bin/binary, (N band 127 bor 128)>>,
    e_varint(N bsr 7, Bin2).

is_empty_string("") -> true;
is_empty_string(<<>>) -> true;
is_empty_string(L) when is_list(L) -> not string_has_chars(L);
is_empty_string(B) when is_binary(B) -> false.

string_has_chars([C | _]) when is_integer(C) -> true;
string_has_chars([H | T]) ->
    case string_has_chars(H) of
        true -> true;
        false -> string_has_chars(T)
    end;
string_has_chars(B) when is_binary(B), byte_size(B) =/= 0 -> true;
string_has_chars(C) when is_integer(C) -> true;
string_has_chars(<<>>) -> false;
string_has_chars([]) -> false.


decode_msg(Bin, MsgName) when is_binary(Bin) -> decode_msg(Bin, MsgName, []).

decode_msg(Bin, MsgName, Opts) when is_binary(Bin) ->
    TrUserData = proplists:get_value(user_data, Opts),
    decode_msg_1_catch(Bin, MsgName, TrUserData).

-ifdef('OTP_RELEASE').
decode_msg_1_catch(Bin, MsgName, TrUserData) ->
    try decode_msg_2_doit(MsgName, Bin, TrUserData)
    catch
        error:{gpb_error,_}=Reason:StackTrace ->
            erlang:raise(error, Reason, StackTrace);
        Class:Reason:StackTrace -> error({gpb_error,{decoding_failure, {Bin, MsgName, {Class, Reason, StackTrace}}}})
    end.
-else.
decode_msg_1_catch(Bin, MsgName, TrUserData) ->
    try decode_msg_2_doit(MsgName, Bin, TrUserData)
    catch
        error:{gpb_error,_}=Reason ->
            erlang:raise(error, Reason,
                         erlang:get_stacktrace());
        Class:Reason ->
            StackTrace = erlang:get_stacktrace(),
            error({gpb_error,{decoding_failure, {Bin, MsgName, {Class, Reason, StackTrace}}}})
    end.
-endif.

decode_msg_2_doit(export_metrics_service_request, Bin, TrUserData) -> id(decode_msg_export_metrics_service_request(Bin, TrUserData), TrUserData);
decode_msg_2_doit(export_metrics_service_response, Bin, TrUserData) -> id(decode_msg_export_metrics_service_response(Bin, TrUserData), TrUserData);
decode_msg_2_doit(export_metrics_partial_success, Bin, TrUserData) -> id(decode_msg_export_metrics_partial_success(Bin, TrUserData), TrUserData);
decode_msg_2_doit(metrics_data, Bin, TrUserData) -> id(decode_msg_metrics_data(Bin, TrUserData), TrUserData);
decode_msg_2_doit(resource_metrics, Bin, TrUserData) -> id(decode_msg_resource_metrics(Bin, TrUserData), TrUserData);
decode_msg_2_doit(scope_metrics, Bin, TrUserData) -> id(decode_msg_scope_metrics(Bin, TrUserData), TrUserData);
decode_msg_2_doit(metric, Bin, TrUserData) -> id(decode_msg_metric(Bin, TrUserData), TrUserData);
decode_msg_2_doit(gauge, Bin, TrUserData) -> id(decode_msg_gauge(Bin, TrUserData), TrUserData);
decode_msg_2_doit(sum, Bin, TrUserData) -> id(decode_msg_sum(Bin, TrUserData), TrUserData);
decode_msg_2_doit(histogram, Bin, TrUserData) -> id(decode_msg_histogram(Bin, TrUserData), TrUserData);
decode_msg_2_doit(exponential_histogram, Bin, TrUserData) -> id(decode_msg_exponential_histogram(Bin, TrUserData), TrUserData);
decode_msg_2_doit(summary, Bin, TrUserData) -> id(decode_msg_summary(Bin, TrUserData), TrUserData);
decode_msg_2_doit(number_data_point, Bin, TrUserData) -> id(decode_msg_number_data_point(Bin, TrUserData), TrUserData);
decode_msg_2_doit(histogram_data_point, Bin, TrUserData) -> id(decode_msg_histogram_data_point(Bin, TrUserData), TrUserData);
decode_msg_2_doit(buckets, Bin, TrUserData) -> id(decode_msg_buckets(Bin, TrUserData), TrUserData);
decode_msg_2_doit(exponential_histogram_data_point, Bin, TrUserData) -> id(decode_msg_exponential_histogram_data_point(Bin, TrUserData), TrUserData);
decode_msg_2_doit(value_at_quantile, Bin, TrUserData) -> id(decode_msg_value_at_quantile(Bin, TrUserData), TrUserData);
decode_msg_2_doit(summary_data_point, Bin, TrUserData) -> id(decode_msg_summary_data_point(Bin, TrUserData), TrUserData);
decode_msg_2_doit(exemplar, Bin, TrUserData) -> id(decode_msg_exemplar(Bin, TrUserData), TrUserData);
decode_msg_2_doit(any_value, Bin, TrUserData) -> id(decode_msg_any_value(Bin, TrUserData), TrUserData);
decode_msg_2_doit(array_value, Bin, TrUserData) -> id(decode_msg_array_value(Bin, TrUserData), TrUserData);
decode_msg_2_doit(key_value_list, Bin, TrUserData) -> id(decode_msg_key_value_list(Bin, TrUserData), TrUserData);
decode_msg_2_doit(key_value, Bin, TrUserData) -> id(decode_msg_key_value(Bin, TrUserData), TrUserData);
decode_msg_2_doit(instrumentation_scope, Bin, TrUserData) -> id(decode_msg_instrumentation_scope(Bin, TrUserData), TrUserData);
decode_msg_2_doit(resource, Bin, TrUserData) -> id(decode_msg_resource(Bin, TrUserData), TrUserData).



decode_msg_export_metrics_service_request(Bin, TrUserData) -> dfp_read_field_def_export_metrics_service_request(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_export_metrics_service_request(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_export_metrics_service_request_resource_metrics(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_export_metrics_service_request(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{resource_metrics => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_export_metrics_service_request(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_export_metrics_service_request(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_export_metrics_service_request(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_export_metrics_service_request(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_export_metrics_service_request(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_export_metrics_service_request_resource_metrics(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_export_metrics_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_export_metrics_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_export_metrics_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_export_metrics_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_export_metrics_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_export_metrics_service_request(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{resource_metrics => lists_reverse(R1, TrUserData)}
    end.

d_field_export_metrics_service_request_resource_metrics(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_export_metrics_service_request_resource_metrics(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_export_metrics_service_request_resource_metrics(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_resource_metrics(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_export_metrics_service_request(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_export_metrics_service_request(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_export_metrics_service_request(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_export_metrics_service_request(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_metrics_service_request(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_export_metrics_service_request(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_export_metrics_service_request(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_export_metrics_service_request(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_export_metrics_service_request(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_export_metrics_service_request(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_export_metrics_service_request(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_export_metrics_service_request(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_metrics_service_request(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_export_metrics_service_request(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_metrics_service_request(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_export_metrics_service_response(Bin, TrUserData) -> dfp_read_field_def_export_metrics_service_response(Bin, 0, 0, 0, id('$undef', TrUserData), TrUserData).

dfp_read_field_def_export_metrics_service_response(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_export_metrics_service_response_partial_success(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_export_metrics_service_response(<<>>, 0, 0, _, F@_1, _) ->
    S1 = #{},
    if F@_1 == '$undef' -> S1;
       true -> S1#{partial_success => F@_1}
    end;
dfp_read_field_def_export_metrics_service_response(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_export_metrics_service_response(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_export_metrics_service_response(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_export_metrics_service_response(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_export_metrics_service_response(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_export_metrics_service_response_partial_success(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_export_metrics_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_export_metrics_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_export_metrics_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_export_metrics_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_export_metrics_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_export_metrics_service_response(<<>>, 0, 0, _, F@_1, _) ->
    S1 = #{},
    if F@_1 == '$undef' -> S1;
       true -> S1#{partial_success => F@_1}
    end.

d_field_export_metrics_service_response_partial_success(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_export_metrics_service_response_partial_success(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_export_metrics_service_response_partial_success(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_export_metrics_partial_success(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_export_metrics_service_response(RestF,
                                                       0,
                                                       0,
                                                       F,
                                                       if Prev == '$undef' -> NewFValue;
                                                          true -> merge_msg_export_metrics_partial_success(Prev, NewFValue, TrUserData)
                                                       end,
                                                       TrUserData).

skip_varint_export_metrics_service_response(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_export_metrics_service_response(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_export_metrics_service_response(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_metrics_service_response(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_export_metrics_service_response(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_export_metrics_service_response(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_export_metrics_service_response(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_export_metrics_service_response(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_export_metrics_service_response(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_export_metrics_service_response(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_export_metrics_service_response(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_metrics_service_response(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_export_metrics_service_response(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_metrics_service_response(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_export_metrics_partial_success(Bin, TrUserData) -> dfp_read_field_def_export_metrics_partial_success(Bin, 0, 0, 0, id(0, TrUserData), id(<<>>, TrUserData), TrUserData).

dfp_read_field_def_export_metrics_partial_success(<<8, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_export_metrics_partial_success_rejected_data_points(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_export_metrics_partial_success(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_export_metrics_partial_success_error_message(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_export_metrics_partial_success(<<>>, 0, 0, _, F@_1, F@_2, _) -> #{rejected_data_points => F@_1, error_message => F@_2};
dfp_read_field_def_export_metrics_partial_success(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_export_metrics_partial_success(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_export_metrics_partial_success(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_export_metrics_partial_success(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_export_metrics_partial_success(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        8 -> d_field_export_metrics_partial_success_rejected_data_points(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        18 -> d_field_export_metrics_partial_success_error_message(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_export_metrics_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_export_metrics_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_export_metrics_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_export_metrics_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_export_metrics_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_export_metrics_partial_success(<<>>, 0, 0, _, F@_1, F@_2, _) -> #{rejected_data_points => F@_1, error_message => F@_2}.

d_field_export_metrics_partial_success_rejected_data_points(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 ->
    d_field_export_metrics_partial_success_rejected_data_points(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_export_metrics_partial_success_rejected_data_points(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = {begin <<Res:64/signed-native>> = <<(X bsl N + Acc):64/unsigned-native>>, id(Res, TrUserData) end, Rest},
    dfp_read_field_def_export_metrics_partial_success(RestF, 0, 0, F, NewFValue, F@_2, TrUserData).

d_field_export_metrics_partial_success_error_message(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_export_metrics_partial_success_error_message(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_export_metrics_partial_success_error_message(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_export_metrics_partial_success(RestF, 0, 0, F, F@_1, NewFValue, TrUserData).

skip_varint_export_metrics_partial_success(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_export_metrics_partial_success(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_export_metrics_partial_success(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_export_metrics_partial_success(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_export_metrics_partial_success(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_export_metrics_partial_success(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_export_metrics_partial_success(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_export_metrics_partial_success(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_export_metrics_partial_success(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_export_metrics_partial_success(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_export_metrics_partial_success(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_export_metrics_partial_success(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_export_metrics_partial_success(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_export_metrics_partial_success(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

decode_msg_metrics_data(Bin, TrUserData) -> dfp_read_field_def_metrics_data(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_metrics_data(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_metrics_data_resource_metrics(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_metrics_data(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{resource_metrics => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_metrics_data(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_metrics_data(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_metrics_data(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_metrics_data(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_metrics_data(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_metrics_data_resource_metrics(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_metrics_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_metrics_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_metrics_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_metrics_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_metrics_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_metrics_data(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{resource_metrics => lists_reverse(R1, TrUserData)}
    end.

d_field_metrics_data_resource_metrics(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_metrics_data_resource_metrics(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_metrics_data_resource_metrics(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_resource_metrics(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_metrics_data(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_metrics_data(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_metrics_data(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_metrics_data(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_metrics_data(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_metrics_data(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_metrics_data(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_metrics_data(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_metrics_data(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_metrics_data(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_metrics_data(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_metrics_data(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_metrics_data(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_metrics_data(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_metrics_data(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_resource_metrics(Bin, TrUserData) -> dfp_read_field_def_resource_metrics(Bin, 0, 0, 0, id('$undef', TrUserData), id([], TrUserData), id(<<>>, TrUserData), TrUserData).

dfp_read_field_def_resource_metrics(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_resource_metrics_resource(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_resource_metrics(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_resource_metrics_scope_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_resource_metrics(<<26, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_resource_metrics_schema_url(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_resource_metrics(<<>>, 0, 0, _, F@_1, R1, F@_3, TrUserData) ->
    S1 = #{schema_url => F@_3},
    S2 = if F@_1 == '$undef' -> S1;
            true -> S1#{resource => F@_1}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{scope_metrics => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_resource_metrics(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dg_read_field_def_resource_metrics(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

dg_read_field_def_resource_metrics(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 32 - 7 -> dg_read_field_def_resource_metrics(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
dg_read_field_def_resource_metrics(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_resource_metrics_resource(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        18 -> d_field_resource_metrics_scope_metrics(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        26 -> d_field_resource_metrics_schema_url(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_resource_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                1 -> skip_64_resource_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                2 -> skip_length_delimited_resource_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                3 -> skip_group_resource_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                5 -> skip_32_resource_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData)
            end
    end;
dg_read_field_def_resource_metrics(<<>>, 0, 0, _, F@_1, R1, F@_3, TrUserData) ->
    S1 = #{schema_url => F@_3},
    S2 = if F@_1 == '$undef' -> S1;
            true -> S1#{resource => F@_1}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{scope_metrics => lists_reverse(R1, TrUserData)}
    end.

d_field_resource_metrics_resource(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_resource_metrics_resource(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_resource_metrics_resource(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_resource(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_resource_metrics(RestF,
                                        0,
                                        0,
                                        F,
                                        if Prev == '$undef' -> NewFValue;
                                           true -> merge_msg_resource(Prev, NewFValue, TrUserData)
                                        end,
                                        F@_2,
                                        F@_3,
                                        TrUserData).

d_field_resource_metrics_scope_metrics(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_resource_metrics_scope_metrics(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_resource_metrics_scope_metrics(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, Prev, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_scope_metrics(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_resource_metrics(RestF, 0, 0, F, F@_1, cons(NewFValue, Prev, TrUserData), F@_3, TrUserData).

d_field_resource_metrics_schema_url(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_resource_metrics_schema_url(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_resource_metrics_schema_url(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_resource_metrics(RestF, 0, 0, F, F@_1, F@_2, NewFValue, TrUserData).

skip_varint_resource_metrics(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> skip_varint_resource_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
skip_varint_resource_metrics(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_resource_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_length_delimited_resource_metrics(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> skip_length_delimited_resource_metrics(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
skip_length_delimited_resource_metrics(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_resource_metrics(Rest2, 0, 0, F, F@_1, F@_2, F@_3, TrUserData).

skip_group_resource_metrics(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_resource_metrics(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, TrUserData).

skip_32_resource_metrics(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_resource_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_64_resource_metrics(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_resource_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

decode_msg_scope_metrics(Bin, TrUserData) -> dfp_read_field_def_scope_metrics(Bin, 0, 0, 0, id('$undef', TrUserData), id([], TrUserData), id(<<>>, TrUserData), TrUserData).

dfp_read_field_def_scope_metrics(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_scope_metrics_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_scope_metrics(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_scope_metrics_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_scope_metrics(<<26, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_scope_metrics_schema_url(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_scope_metrics(<<>>, 0, 0, _, F@_1, R1, F@_3, TrUserData) ->
    S1 = #{schema_url => F@_3},
    S2 = if F@_1 == '$undef' -> S1;
            true -> S1#{scope => F@_1}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{metrics => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_scope_metrics(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dg_read_field_def_scope_metrics(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

dg_read_field_def_scope_metrics(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 32 - 7 -> dg_read_field_def_scope_metrics(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
dg_read_field_def_scope_metrics(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_scope_metrics_scope(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        18 -> d_field_scope_metrics_metrics(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        26 -> d_field_scope_metrics_schema_url(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_scope_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                1 -> skip_64_scope_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                2 -> skip_length_delimited_scope_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                3 -> skip_group_scope_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                5 -> skip_32_scope_metrics(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData)
            end
    end;
dg_read_field_def_scope_metrics(<<>>, 0, 0, _, F@_1, R1, F@_3, TrUserData) ->
    S1 = #{schema_url => F@_3},
    S2 = if F@_1 == '$undef' -> S1;
            true -> S1#{scope => F@_1}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{metrics => lists_reverse(R1, TrUserData)}
    end.

d_field_scope_metrics_scope(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_scope_metrics_scope(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_scope_metrics_scope(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_instrumentation_scope(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_scope_metrics(RestF,
                                     0,
                                     0,
                                     F,
                                     if Prev == '$undef' -> NewFValue;
                                        true -> merge_msg_instrumentation_scope(Prev, NewFValue, TrUserData)
                                     end,
                                     F@_2,
                                     F@_3,
                                     TrUserData).

d_field_scope_metrics_metrics(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_scope_metrics_metrics(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_scope_metrics_metrics(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, Prev, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_metric(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_scope_metrics(RestF, 0, 0, F, F@_1, cons(NewFValue, Prev, TrUserData), F@_3, TrUserData).

d_field_scope_metrics_schema_url(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_scope_metrics_schema_url(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_scope_metrics_schema_url(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_scope_metrics(RestF, 0, 0, F, F@_1, F@_2, NewFValue, TrUserData).

skip_varint_scope_metrics(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> skip_varint_scope_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
skip_varint_scope_metrics(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_scope_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_length_delimited_scope_metrics(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> skip_length_delimited_scope_metrics(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
skip_length_delimited_scope_metrics(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_scope_metrics(Rest2, 0, 0, F, F@_1, F@_2, F@_3, TrUserData).

skip_group_scope_metrics(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_scope_metrics(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, TrUserData).

skip_32_scope_metrics(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_scope_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_64_scope_metrics(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_scope_metrics(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

decode_msg_metric(Bin, TrUserData) -> dfp_read_field_def_metric(Bin, 0, 0, 0, id(<<>>, TrUserData), id(<<>>, TrUserData), id(<<>>, TrUserData), id('$undef', TrUserData), TrUserData).

dfp_read_field_def_metric(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_metric_name(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_metric(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_metric_description(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_metric(<<26, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_metric_unit(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_metric(<<42, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_metric_gauge(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_metric(<<58, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_metric_sum(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_metric(<<74, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_metric_histogram(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_metric(<<82, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_metric_exponential_histogram(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_metric(<<90, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_metric_summary(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_metric(<<>>, 0, 0, _, F@_1, F@_2, F@_3, F@_4, _) ->
    S1 = #{name => F@_1, description => F@_2, unit => F@_3},
    if F@_4 == '$undef' -> S1;
       true -> S1#{data => F@_4}
    end;
dfp_read_field_def_metric(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dg_read_field_def_metric(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

dg_read_field_def_metric(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 32 - 7 -> dg_read_field_def_metric(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dg_read_field_def_metric(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_metric_name(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        18 -> d_field_metric_description(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        26 -> d_field_metric_unit(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        42 -> d_field_metric_gauge(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        58 -> d_field_metric_sum(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        74 -> d_field_metric_histogram(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        82 -> d_field_metric_exponential_histogram(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        90 -> d_field_metric_summary(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_metric(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                1 -> skip_64_metric(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                2 -> skip_length_delimited_metric(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                3 -> skip_group_metric(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                5 -> skip_32_metric(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData)
            end
    end;
dg_read_field_def_metric(<<>>, 0, 0, _, F@_1, F@_2, F@_3, F@_4, _) ->
    S1 = #{name => F@_1, description => F@_2, unit => F@_3},
    if F@_4 == '$undef' -> S1;
       true -> S1#{data => F@_4}
    end.

d_field_metric_name(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_metric_name(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_metric_name(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, F@_2, F@_3, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_metric(RestF, 0, 0, F, NewFValue, F@_2, F@_3, F@_4, TrUserData).

d_field_metric_description(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_metric_description(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_metric_description(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, F@_3, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_metric(RestF, 0, 0, F, F@_1, NewFValue, F@_3, F@_4, TrUserData).

d_field_metric_unit(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_metric_unit(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_metric_unit(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, _, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_metric(RestF, 0, 0, F, F@_1, F@_2, NewFValue, F@_4, TrUserData).

d_field_metric_gauge(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_metric_gauge(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_metric_gauge(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_gauge(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_metric(RestF,
                              0,
                              0,
                              F,
                              F@_1,
                              F@_2,
                              F@_3,
                              case Prev of
                                  '$undef' -> id({gauge, NewFValue}, TrUserData);
                                  {gauge, MVPrev} -> id({gauge, merge_msg_gauge(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                  _ -> id({gauge, NewFValue}, TrUserData)
                              end,
                              TrUserData).

d_field_metric_sum(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_metric_sum(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_metric_sum(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_sum(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_metric(RestF,
                              0,
                              0,
                              F,
                              F@_1,
                              F@_2,
                              F@_3,
                              case Prev of
                                  '$undef' -> id({sum, NewFValue}, TrUserData);
                                  {sum, MVPrev} -> id({sum, merge_msg_sum(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                  _ -> id({sum, NewFValue}, TrUserData)
                              end,
                              TrUserData).

d_field_metric_histogram(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_metric_histogram(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_metric_histogram(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_histogram(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_metric(RestF,
                              0,
                              0,
                              F,
                              F@_1,
                              F@_2,
                              F@_3,
                              case Prev of
                                  '$undef' -> id({histogram, NewFValue}, TrUserData);
                                  {histogram, MVPrev} -> id({histogram, merge_msg_histogram(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                  _ -> id({histogram, NewFValue}, TrUserData)
                              end,
                              TrUserData).

d_field_metric_exponential_histogram(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_metric_exponential_histogram(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_metric_exponential_histogram(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_exponential_histogram(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_metric(RestF,
                              0,
                              0,
                              F,
                              F@_1,
                              F@_2,
                              F@_3,
                              case Prev of
                                  '$undef' -> id({exponential_histogram, NewFValue}, TrUserData);
                                  {exponential_histogram, MVPrev} -> id({exponential_histogram, merge_msg_exponential_histogram(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                  _ -> id({exponential_histogram, NewFValue}, TrUserData)
                              end,
                              TrUserData).

d_field_metric_summary(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_metric_summary(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_metric_summary(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_summary(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_metric(RestF,
                              0,
                              0,
                              F,
                              F@_1,
                              F@_2,
                              F@_3,
                              case Prev of
                                  '$undef' -> id({summary, NewFValue}, TrUserData);
                                  {summary, MVPrev} -> id({summary, merge_msg_summary(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                  _ -> id({summary, NewFValue}, TrUserData)
                              end,
                              TrUserData).

skip_varint_metric(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> skip_varint_metric(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
skip_varint_metric(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_metric(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_length_delimited_metric(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> skip_length_delimited_metric(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
skip_length_delimited_metric(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_metric(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_group_metric(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_metric(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_32_metric(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_metric(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_64_metric(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_metric(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

decode_msg_gauge(Bin, TrUserData) -> dfp_read_field_def_gauge(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_gauge(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_gauge_data_points(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_gauge(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_gauge(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_gauge(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_gauge(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_gauge(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_gauge(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_gauge_data_points(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_gauge(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_gauge(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_gauge(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_gauge(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_gauge(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_gauge(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end.

d_field_gauge_data_points(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_gauge_data_points(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_gauge_data_points(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_number_data_point(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_gauge(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_gauge(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_gauge(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_gauge(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_gauge(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_gauge(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_gauge(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_gauge(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_gauge(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_gauge(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_gauge(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_gauge(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_gauge(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_gauge(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_gauge(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_sum(Bin, TrUserData) -> dfp_read_field_def_sum(Bin, 0, 0, 0, id([], TrUserData), id('AGGREGATION_TEMPORALITY_UNSPECIFIED', TrUserData), id(false, TrUserData), TrUserData).

dfp_read_field_def_sum(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_sum_data_points(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_sum(<<16, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_sum_aggregation_temporality(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_sum(<<24, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_sum_is_monotonic(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_sum(<<>>, 0, 0, _, R1, F@_2, F@_3, TrUserData) ->
    S1 = #{aggregation_temporality => F@_2, is_monotonic => F@_3},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_sum(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dg_read_field_def_sum(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

dg_read_field_def_sum(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 32 - 7 -> dg_read_field_def_sum(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
dg_read_field_def_sum(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_sum_data_points(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        16 -> d_field_sum_aggregation_temporality(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        24 -> d_field_sum_is_monotonic(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_sum(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                1 -> skip_64_sum(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                2 -> skip_length_delimited_sum(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                3 -> skip_group_sum(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                5 -> skip_32_sum(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData)
            end
    end;
dg_read_field_def_sum(<<>>, 0, 0, _, R1, F@_2, F@_3, TrUserData) ->
    S1 = #{aggregation_temporality => F@_2, is_monotonic => F@_3},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end.

d_field_sum_data_points(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_sum_data_points(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_sum_data_points(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_number_data_point(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_sum(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, F@_3, TrUserData).

d_field_sum_aggregation_temporality(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_sum_aggregation_temporality(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_sum_aggregation_temporality(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, F@_3, TrUserData) ->
    {NewFValue, RestF} = {id('d_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(begin <<Res:32/signed-native>> = <<(X bsl N + Acc):32/unsigned-native>>, id(Res, TrUserData) end), TrUserData), Rest},
    dfp_read_field_def_sum(RestF, 0, 0, F, F@_1, NewFValue, F@_3, TrUserData).

d_field_sum_is_monotonic(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_sum_is_monotonic(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_sum_is_monotonic(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, _, TrUserData) ->
    {NewFValue, RestF} = {id(X bsl N + Acc =/= 0, TrUserData), Rest},
    dfp_read_field_def_sum(RestF, 0, 0, F, F@_1, F@_2, NewFValue, TrUserData).

skip_varint_sum(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> skip_varint_sum(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
skip_varint_sum(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_sum(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_length_delimited_sum(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> skip_length_delimited_sum(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
skip_length_delimited_sum(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_sum(Rest2, 0, 0, F, F@_1, F@_2, F@_3, TrUserData).

skip_group_sum(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_sum(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, TrUserData).

skip_32_sum(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_sum(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_64_sum(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_sum(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

decode_msg_histogram(Bin, TrUserData) -> dfp_read_field_def_histogram(Bin, 0, 0, 0, id([], TrUserData), id('AGGREGATION_TEMPORALITY_UNSPECIFIED', TrUserData), TrUserData).

dfp_read_field_def_histogram(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_histogram_data_points(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_histogram(<<16, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_histogram_aggregation_temporality(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_histogram(<<>>, 0, 0, _, R1, F@_2, TrUserData) ->
    S1 = #{aggregation_temporality => F@_2},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_histogram(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_histogram(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_histogram(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_histogram(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_histogram(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_histogram_data_points(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        16 -> d_field_histogram_aggregation_temporality(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_histogram(<<>>, 0, 0, _, R1, F@_2, TrUserData) ->
    S1 = #{aggregation_temporality => F@_2},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end.

d_field_histogram_data_points(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_histogram_data_points(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_histogram_data_points(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_histogram_data_point(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_histogram(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, TrUserData).

d_field_histogram_aggregation_temporality(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_histogram_aggregation_temporality(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_histogram_aggregation_temporality(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = {id('d_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(begin <<Res:32/signed-native>> = <<(X bsl N + Acc):32/unsigned-native>>, id(Res, TrUserData) end), TrUserData), Rest},
    dfp_read_field_def_histogram(RestF, 0, 0, F, F@_1, NewFValue, TrUserData).

skip_varint_histogram(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_histogram(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_histogram(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_histogram(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_histogram(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_histogram(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_histogram(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_histogram(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_histogram(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_histogram(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_histogram(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_histogram(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_histogram(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_histogram(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

decode_msg_exponential_histogram(Bin, TrUserData) -> dfp_read_field_def_exponential_histogram(Bin, 0, 0, 0, id([], TrUserData), id('AGGREGATION_TEMPORALITY_UNSPECIFIED', TrUserData), TrUserData).

dfp_read_field_def_exponential_histogram(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_exponential_histogram_data_points(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_exponential_histogram(<<16, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_exponential_histogram_aggregation_temporality(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_exponential_histogram(<<>>, 0, 0, _, R1, F@_2, TrUserData) ->
    S1 = #{aggregation_temporality => F@_2},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_exponential_histogram(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_exponential_histogram(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_exponential_histogram(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_exponential_histogram(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_exponential_histogram(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_exponential_histogram_data_points(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        16 -> d_field_exponential_histogram_aggregation_temporality(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_exponential_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_exponential_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_exponential_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_exponential_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_exponential_histogram(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_exponential_histogram(<<>>, 0, 0, _, R1, F@_2, TrUserData) ->
    S1 = #{aggregation_temporality => F@_2},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end.

d_field_exponential_histogram_data_points(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_exponential_histogram_data_points(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_exponential_histogram_data_points(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_exponential_histogram_data_point(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_exponential_histogram(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, TrUserData).

d_field_exponential_histogram_aggregation_temporality(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_exponential_histogram_aggregation_temporality(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_exponential_histogram_aggregation_temporality(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = {id('d_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(begin <<Res:32/signed-native>> = <<(X bsl N + Acc):32/unsigned-native>>, id(Res, TrUserData) end), TrUserData), Rest},
    dfp_read_field_def_exponential_histogram(RestF, 0, 0, F, F@_1, NewFValue, TrUserData).

skip_varint_exponential_histogram(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_exponential_histogram(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_exponential_histogram(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_exponential_histogram(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_exponential_histogram(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_exponential_histogram(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_exponential_histogram(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_exponential_histogram(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_exponential_histogram(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_exponential_histogram(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_exponential_histogram(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_exponential_histogram(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_exponential_histogram(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_exponential_histogram(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

decode_msg_summary(Bin, TrUserData) -> dfp_read_field_def_summary(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_summary(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_summary_data_points(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_summary(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_summary(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_summary(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_summary(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_summary(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_summary(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_summary_data_points(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_summary(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_summary(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_summary(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_summary(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_summary(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_summary(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{data_points => lists_reverse(R1, TrUserData)}
    end.

d_field_summary_data_points(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_summary_data_points(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_summary_data_points(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_summary_data_point(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_summary(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_summary(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_summary(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_summary(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_summary(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_summary(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_summary(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_summary(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_summary(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_summary(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_summary(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_summary(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_summary(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_summary(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_summary(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_number_data_point(Bin, TrUserData) -> dfp_read_field_def_number_data_point(Bin, 0, 0, 0, id([], TrUserData), id(0, TrUserData), id(0, TrUserData), id('$undef', TrUserData), id([], TrUserData), id(0, TrUserData), TrUserData).

dfp_read_field_def_number_data_point(<<58, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> d_field_number_data_point_attributes(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
dfp_read_field_def_number_data_point(<<17, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> d_field_number_data_point_start_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
dfp_read_field_def_number_data_point(<<25, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> d_field_number_data_point_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
dfp_read_field_def_number_data_point(<<33, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> d_field_number_data_point_as_double(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
dfp_read_field_def_number_data_point(<<49, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> d_field_number_data_point_as_int(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
dfp_read_field_def_number_data_point(<<42, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> d_field_number_data_point_exemplars(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
dfp_read_field_def_number_data_point(<<64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> d_field_number_data_point_flags(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
dfp_read_field_def_number_data_point(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, R2, F@_6, TrUserData) ->
    S1 = #{start_time_unix_nano => F@_2, time_unix_nano => F@_3, flags => F@_6},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{attributes => lists_reverse(R1, TrUserData)}
         end,
    S3 = if F@_4 == '$undef' -> S2;
            true -> S2#{value => F@_4}
         end,
    if R2 == '$undef' -> S3;
       true -> S3#{exemplars => lists_reverse(R2, TrUserData)}
    end;
dfp_read_field_def_number_data_point(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> dg_read_field_def_number_data_point(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData).

dg_read_field_def_number_data_point(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) when N < 32 - 7 ->
    dg_read_field_def_number_data_point(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
dg_read_field_def_number_data_point(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        58 -> d_field_number_data_point_attributes(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
        17 -> d_field_number_data_point_start_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
        25 -> d_field_number_data_point_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
        33 -> d_field_number_data_point_as_double(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
        49 -> d_field_number_data_point_as_int(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
        42 -> d_field_number_data_point_exemplars(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
        64 -> d_field_number_data_point_flags(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_number_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
                1 -> skip_64_number_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
                2 -> skip_length_delimited_number_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
                3 -> skip_group_number_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
                5 -> skip_32_number_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData)
            end
    end;
dg_read_field_def_number_data_point(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, R2, F@_6, TrUserData) ->
    S1 = #{start_time_unix_nano => F@_2, time_unix_nano => F@_3, flags => F@_6},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{attributes => lists_reverse(R1, TrUserData)}
         end,
    S3 = if F@_4 == '$undef' -> S2;
            true -> S2#{value => F@_4}
         end,
    if R2 == '$undef' -> S3;
       true -> S3#{exemplars => lists_reverse(R2, TrUserData)}
    end.

d_field_number_data_point_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) when N < 57 ->
    d_field_number_data_point_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
d_field_number_data_point_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_number_data_point(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData).

d_field_number_data_point_start_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, _, F@_3, F@_4, F@_5, F@_6, TrUserData) ->
    dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, id(Value, TrUserData), F@_3, F@_4, F@_5, F@_6, TrUserData).

d_field_number_data_point_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, F@_6, TrUserData) ->
    dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, id(Value, TrUserData), F@_4, F@_5, F@_6, TrUserData).

d_field_number_data_point_as_double(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, TrUserData) ->
    dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, id({as_double, id(infinity, TrUserData)}, TrUserData), F@_5, F@_6, TrUserData);
d_field_number_data_point_as_double(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, TrUserData) ->
    dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, id({as_double, id('-infinity', TrUserData)}, TrUserData), F@_5, F@_6, TrUserData);
d_field_number_data_point_as_double(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, TrUserData) ->
    dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, id({as_double, id(nan, TrUserData)}, TrUserData), F@_5, F@_6, TrUserData);
d_field_number_data_point_as_double(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, TrUserData) ->
    dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, id({as_double, id(Value, TrUserData)}, TrUserData), F@_5, F@_6, TrUserData).

d_field_number_data_point_as_int(<<Value:64/little-signed, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, TrUserData) ->
    dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, id({as_int, id(Value, TrUserData)}, TrUserData), F@_5, F@_6, TrUserData).

d_field_number_data_point_exemplars(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) when N < 57 ->
    d_field_number_data_point_exemplars(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
d_field_number_data_point_exemplars(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, Prev, F@_6, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_exemplar(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_number_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, cons(NewFValue, Prev, TrUserData), F@_6, TrUserData).

d_field_number_data_point_flags(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) when N < 57 -> d_field_number_data_point_flags(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
d_field_number_data_point_flags(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, _, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_number_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, NewFValue, TrUserData).

skip_varint_number_data_point(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> skip_varint_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
skip_varint_number_data_point(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData).

skip_length_delimited_number_data_point(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) when N < 57 ->
    skip_length_delimited_number_data_point(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData);
skip_length_delimited_number_data_point(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_number_data_point(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData).

skip_group_number_data_point(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_number_data_point(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData).

skip_32_number_data_point(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData).

skip_64_number_data_point(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData) -> dfp_read_field_def_number_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, TrUserData).

decode_msg_histogram_data_point(Bin, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Bin,
                                            0,
                                            0,
                                            0,
                                            id([], TrUserData),
                                            id(0, TrUserData),
                                            id(0, TrUserData),
                                            id(0, TrUserData),
                                            id('$undef', TrUserData),
                                            id([], TrUserData),
                                            id([], TrUserData),
                                            id([], TrUserData),
                                            id(0, TrUserData),
                                            id('$undef', TrUserData),
                                            id('$undef', TrUserData),
                                            TrUserData).

dfp_read_field_def_histogram_data_point(<<74, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_attributes(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<17, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_start_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<25, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<33, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_count(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<41, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_sum(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<50, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_pfield_histogram_data_point_bucket_counts(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<49, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_bucket_counts(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<58, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_pfield_histogram_data_point_explicit_bounds(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<57, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_explicit_bounds(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<66, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_exemplars(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<80, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_flags(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<89, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_min(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<97, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    d_field_histogram_data_point_max(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dfp_read_field_def_histogram_data_point(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, F@_5, R2, R3, R4, F@_9, F@_10, F@_11, TrUserData) ->
    S1 = #{start_time_unix_nano => F@_2, time_unix_nano => F@_3, count => F@_4, bucket_counts => lists_reverse(R2, TrUserData), explicit_bounds => lists_reverse(R3, TrUserData), flags => F@_9},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{attributes => lists_reverse(R1, TrUserData)}
         end,
    S3 = if F@_5 == '$undef' -> S2;
            true -> S2#{sum => F@_5}
         end,
    S4 = if R4 == '$undef' -> S3;
            true -> S3#{exemplars => lists_reverse(R4, TrUserData)}
         end,
    S5 = if F@_10 == '$undef' -> S4;
            true -> S4#{min => F@_10}
         end,
    if F@_11 == '$undef' -> S5;
       true -> S5#{max => F@_11}
    end;
dfp_read_field_def_histogram_data_point(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dg_read_field_def_histogram_data_point(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

dg_read_field_def_histogram_data_point(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) when N < 32 - 7 ->
    dg_read_field_def_histogram_data_point(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
dg_read_field_def_histogram_data_point(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        74 -> d_field_histogram_data_point_attributes(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        17 -> d_field_histogram_data_point_start_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        25 -> d_field_histogram_data_point_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        33 -> d_field_histogram_data_point_count(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        41 -> d_field_histogram_data_point_sum(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        50 -> d_pfield_histogram_data_point_bucket_counts(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        49 -> d_field_histogram_data_point_bucket_counts(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        58 -> d_pfield_histogram_data_point_explicit_bounds(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        57 -> d_field_histogram_data_point_explicit_bounds(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        66 -> d_field_histogram_data_point_exemplars(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        80 -> d_field_histogram_data_point_flags(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        89 -> d_field_histogram_data_point_min(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        97 -> d_field_histogram_data_point_max(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
                1 -> skip_64_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
                2 -> skip_length_delimited_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
                3 -> skip_group_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
                5 -> skip_32_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData)
            end
    end;
dg_read_field_def_histogram_data_point(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, F@_5, R2, R3, R4, F@_9, F@_10, F@_11, TrUserData) ->
    S1 = #{start_time_unix_nano => F@_2, time_unix_nano => F@_3, count => F@_4, bucket_counts => lists_reverse(R2, TrUserData), explicit_bounds => lists_reverse(R3, TrUserData), flags => F@_9},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{attributes => lists_reverse(R1, TrUserData)}
         end,
    S3 = if F@_5 == '$undef' -> S2;
            true -> S2#{sum => F@_5}
         end,
    S4 = if R4 == '$undef' -> S3;
            true -> S3#{exemplars => lists_reverse(R4, TrUserData)}
         end,
    S5 = if F@_10 == '$undef' -> S4;
            true -> S4#{min => F@_10}
         end,
    if F@_11 == '$undef' -> S5;
       true -> S5#{max => F@_11}
    end.

d_field_histogram_data_point_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) when N < 57 ->
    d_field_histogram_data_point_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_histogram_data_point(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

d_field_histogram_data_point_start_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, _, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, id(Value, TrUserData), F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

d_field_histogram_data_point_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, id(Value, TrUserData), F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

d_field_histogram_data_point_count(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, id(Value, TrUserData), F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

d_field_histogram_data_point_sum(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(infinity, TrUserData), F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_sum(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id('-infinity', TrUserData), F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_sum(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(nan, TrUserData), F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_sum(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(Value, TrUserData), F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

d_field_histogram_data_point_bucket_counts(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, Prev, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, cons(id(Value, TrUserData), Prev, TrUserData), F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

d_pfield_histogram_data_point_bucket_counts(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) when N < 57 ->
    d_pfield_histogram_data_point_bucket_counts(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
d_pfield_histogram_data_point_bucket_counts(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, E, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    Len = X bsl N + Acc,
    <<PackedBytes:Len/binary, Rest2/binary>> = Rest,
    NewSeq = d_packed_field_histogram_data_point_bucket_counts(PackedBytes, 0, 0, F, E, TrUserData),
    dfp_read_field_def_histogram_data_point(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, NewSeq, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

d_packed_field_histogram_data_point_bucket_counts(<<Value:64/little, Rest/binary>>, Z1, Z2, F, AccSeq, TrUserData) -> d_packed_field_histogram_data_point_bucket_counts(Rest, Z1, Z2, F, cons(id(Value, TrUserData), AccSeq, TrUserData), TrUserData);
d_packed_field_histogram_data_point_bucket_counts(<<>>, _, _, _, AccSeq, _) -> AccSeq.

d_field_histogram_data_point_explicit_bounds(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, Prev, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, cons(id(infinity, TrUserData), Prev, TrUserData), F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_explicit_bounds(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, Prev, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, cons(id('-infinity', TrUserData), Prev, TrUserData), F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_explicit_bounds(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, Prev, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, cons(id(nan, TrUserData), Prev, TrUserData), F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_explicit_bounds(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, Prev, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, cons(id(Value, TrUserData), Prev, TrUserData), F@_8, F@_9, F@_10, F@_11, TrUserData).

d_pfield_histogram_data_point_explicit_bounds(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) when N < 57 ->
    d_pfield_histogram_data_point_explicit_bounds(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
d_pfield_histogram_data_point_explicit_bounds(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, E, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    Len = X bsl N + Acc,
    <<PackedBytes:Len/binary, Rest2/binary>> = Rest,
    NewSeq = d_packed_field_histogram_data_point_explicit_bounds(PackedBytes, 0, 0, F, E, TrUserData),
    dfp_read_field_def_histogram_data_point(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, NewSeq, F@_8, F@_9, F@_10, F@_11, TrUserData).

d_packed_field_histogram_data_point_explicit_bounds(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, AccSeq, TrUserData) ->
    d_packed_field_histogram_data_point_explicit_bounds(Rest, Z1, Z2, F, cons(id(infinity, TrUserData), AccSeq, TrUserData), TrUserData);
d_packed_field_histogram_data_point_explicit_bounds(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, AccSeq, TrUserData) ->
    d_packed_field_histogram_data_point_explicit_bounds(Rest, Z1, Z2, F, cons(id('-infinity', TrUserData), AccSeq, TrUserData), TrUserData);
d_packed_field_histogram_data_point_explicit_bounds(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, AccSeq, TrUserData) ->
    d_packed_field_histogram_data_point_explicit_bounds(Rest, Z1, Z2, F, cons(id(nan, TrUserData), AccSeq, TrUserData), TrUserData);
d_packed_field_histogram_data_point_explicit_bounds(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, AccSeq, TrUserData) ->
    d_packed_field_histogram_data_point_explicit_bounds(Rest, Z1, Z2, F, cons(id(Value, TrUserData), AccSeq, TrUserData), TrUserData);
d_packed_field_histogram_data_point_explicit_bounds(<<>>, _, _, _, AccSeq, _) -> AccSeq.

d_field_histogram_data_point_exemplars(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) when N < 57 ->
    d_field_histogram_data_point_exemplars(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_exemplars(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, Prev, F@_9, F@_10, F@_11, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_exemplar(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_histogram_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, cons(NewFValue, Prev, TrUserData), F@_9, F@_10, F@_11, TrUserData).

d_field_histogram_data_point_flags(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) when N < 57 ->
    d_field_histogram_data_point_flags(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
d_field_histogram_data_point_flags(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, _, F@_10, F@_11, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_histogram_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, NewFValue, F@_10, F@_11, TrUserData).

d_field_histogram_data_point_min(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, _, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, id(infinity, TrUserData), F@_11, TrUserData);
d_field_histogram_data_point_min(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, _, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, id('-infinity', TrUserData), F@_11, TrUserData);
d_field_histogram_data_point_min(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, _, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, id(nan, TrUserData), F@_11, TrUserData);
d_field_histogram_data_point_min(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, _, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, id(Value, TrUserData), F@_11, TrUserData).

d_field_histogram_data_point_max(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, _, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, id(infinity, TrUserData), TrUserData);
d_field_histogram_data_point_max(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, _, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, id('-infinity', TrUserData), TrUserData);
d_field_histogram_data_point_max(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, _, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, id(nan, TrUserData), TrUserData);
d_field_histogram_data_point_max(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, _, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, id(Value, TrUserData), TrUserData).

skip_varint_histogram_data_point(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    skip_varint_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
skip_varint_histogram_data_point(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

skip_length_delimited_histogram_data_point(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) when N < 57 ->
    skip_length_delimited_histogram_data_point(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData);
skip_length_delimited_histogram_data_point(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_histogram_data_point(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

skip_group_histogram_data_point(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_histogram_data_point(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

skip_32_histogram_data_point(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

skip_64_histogram_data_point(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData) ->
    dfp_read_field_def_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, TrUserData).

decode_msg_buckets(Bin, TrUserData) -> dfp_read_field_def_buckets(Bin, 0, 0, 0, id(0, TrUserData), id([], TrUserData), TrUserData).

dfp_read_field_def_buckets(<<8, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_buckets_offset(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_buckets(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_pfield_buckets_bucket_counts(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_buckets(<<16, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_buckets_bucket_counts(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_buckets(<<>>, 0, 0, _, F@_1, R1, TrUserData) -> #{offset => F@_1, bucket_counts => lists_reverse(R1, TrUserData)};
dfp_read_field_def_buckets(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_buckets(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_buckets(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_buckets(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_buckets(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        8 -> d_field_buckets_offset(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        18 -> d_pfield_buckets_bucket_counts(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        16 -> d_field_buckets_bucket_counts(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_buckets(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_buckets(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_buckets(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_buckets(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_buckets(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_buckets(<<>>, 0, 0, _, F@_1, R1, TrUserData) -> #{offset => F@_1, bucket_counts => lists_reverse(R1, TrUserData)}.

d_field_buckets_offset(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_buckets_offset(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_buckets_offset(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = {begin
                              ZValue = (X bsl N + Acc) band 4294967295,
                              if ZValue band 1 =:= 0 -> id(ZValue bsr 1, TrUserData);
                                 true -> id(-(ZValue + 1 bsr 1), TrUserData)
                              end
                          end,
                          Rest},
    dfp_read_field_def_buckets(RestF, 0, 0, F, NewFValue, F@_2, TrUserData).

d_field_buckets_bucket_counts(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_buckets_bucket_counts(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_buckets_bucket_counts(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, Prev, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 18446744073709551615, TrUserData), Rest},
    dfp_read_field_def_buckets(RestF, 0, 0, F, F@_1, cons(NewFValue, Prev, TrUserData), TrUserData).

d_pfield_buckets_bucket_counts(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_pfield_buckets_bucket_counts(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_pfield_buckets_bucket_counts(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, E, TrUserData) ->
    Len = X bsl N + Acc,
    <<PackedBytes:Len/binary, Rest2/binary>> = Rest,
    NewSeq = d_packed_field_buckets_bucket_counts(PackedBytes, 0, 0, F, E, TrUserData),
    dfp_read_field_def_buckets(Rest2, 0, 0, F, F@_1, NewSeq, TrUserData).

d_packed_field_buckets_bucket_counts(<<1:1, X:7, Rest/binary>>, N, Acc, F, AccSeq, TrUserData) when N < 57 -> d_packed_field_buckets_bucket_counts(Rest, N + 7, X bsl N + Acc, F, AccSeq, TrUserData);
d_packed_field_buckets_bucket_counts(<<0:1, X:7, Rest/binary>>, N, Acc, F, AccSeq, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 18446744073709551615, TrUserData), Rest},
    d_packed_field_buckets_bucket_counts(RestF, 0, 0, F, [NewFValue | AccSeq], TrUserData);
d_packed_field_buckets_bucket_counts(<<>>, 0, 0, _, AccSeq, _) -> AccSeq.

skip_varint_buckets(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_buckets(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_buckets(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_buckets(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_buckets(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_buckets(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_buckets(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_buckets(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_buckets(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_buckets(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_buckets(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_buckets(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_buckets(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_buckets(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

decode_msg_exponential_histogram_data_point(Bin, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Bin,
                                                        0,
                                                        0,
                                                        0,
                                                        id([], TrUserData),
                                                        id(0, TrUserData),
                                                        id(0, TrUserData),
                                                        id(0, TrUserData),
                                                        id('$undef', TrUserData),
                                                        id(0, TrUserData),
                                                        id(0, TrUserData),
                                                        id('$undef', TrUserData),
                                                        id('$undef', TrUserData),
                                                        id(0, TrUserData),
                                                        id([], TrUserData),
                                                        id('$undef', TrUserData),
                                                        id('$undef', TrUserData),
                                                        TrUserData).

dfp_read_field_def_exponential_histogram_data_point(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_attributes(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<17, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_start_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<25, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<33, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_count(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<41, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_sum(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<48, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_scale(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<57, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_zero_count(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<66, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_positive(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<74, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_negative(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<80, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_flags(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<90, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_exemplars(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<97, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_min(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<105, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    d_field_exponential_histogram_data_point_max(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dfp_read_field_def_exponential_histogram_data_point(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, R2, F@_12, F@_13, TrUserData) ->
    S1 = #{start_time_unix_nano => F@_2, time_unix_nano => F@_3, count => F@_4, scale => F@_6, zero_count => F@_7, flags => F@_10},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{attributes => lists_reverse(R1, TrUserData)}
         end,
    S3 = if F@_5 == '$undef' -> S2;
            true -> S2#{sum => F@_5}
         end,
    S4 = if F@_8 == '$undef' -> S3;
            true -> S3#{positive => F@_8}
         end,
    S5 = if F@_9 == '$undef' -> S4;
            true -> S4#{negative => F@_9}
         end,
    S6 = if R2 == '$undef' -> S5;
            true -> S5#{exemplars => lists_reverse(R2, TrUserData)}
         end,
    S7 = if F@_12 == '$undef' -> S6;
            true -> S6#{min => F@_12}
         end,
    if F@_13 == '$undef' -> S7;
       true -> S7#{max => F@_13}
    end;
dfp_read_field_def_exponential_histogram_data_point(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dg_read_field_def_exponential_histogram_data_point(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

dg_read_field_def_exponential_histogram_data_point(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) when N < 32 - 7 ->
    dg_read_field_def_exponential_histogram_data_point(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
dg_read_field_def_exponential_histogram_data_point(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_exponential_histogram_data_point_attributes(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        17 -> d_field_exponential_histogram_data_point_start_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        25 -> d_field_exponential_histogram_data_point_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        33 -> d_field_exponential_histogram_data_point_count(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        41 -> d_field_exponential_histogram_data_point_sum(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        48 -> d_field_exponential_histogram_data_point_scale(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        57 -> d_field_exponential_histogram_data_point_zero_count(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        66 -> d_field_exponential_histogram_data_point_positive(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        74 -> d_field_exponential_histogram_data_point_negative(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        80 -> d_field_exponential_histogram_data_point_flags(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        90 -> d_field_exponential_histogram_data_point_exemplars(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        97 -> d_field_exponential_histogram_data_point_min(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        105 -> d_field_exponential_histogram_data_point_max(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_exponential_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
                1 -> skip_64_exponential_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
                2 -> skip_length_delimited_exponential_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
                3 -> skip_group_exponential_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
                5 -> skip_32_exponential_histogram_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData)
            end
    end;
dg_read_field_def_exponential_histogram_data_point(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, R2, F@_12, F@_13, TrUserData) ->
    S1 = #{start_time_unix_nano => F@_2, time_unix_nano => F@_3, count => F@_4, scale => F@_6, zero_count => F@_7, flags => F@_10},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{attributes => lists_reverse(R1, TrUserData)}
         end,
    S3 = if F@_5 == '$undef' -> S2;
            true -> S2#{sum => F@_5}
         end,
    S4 = if F@_8 == '$undef' -> S3;
            true -> S3#{positive => F@_8}
         end,
    S5 = if F@_9 == '$undef' -> S4;
            true -> S4#{negative => F@_9}
         end,
    S6 = if R2 == '$undef' -> S5;
            true -> S5#{exemplars => lists_reverse(R2, TrUserData)}
         end,
    S7 = if F@_12 == '$undef' -> S6;
            true -> S6#{min => F@_12}
         end,
    if F@_13 == '$undef' -> S7;
       true -> S7#{max => F@_13}
    end.

d_field_exponential_histogram_data_point_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) when N < 57 ->
    d_field_exponential_histogram_data_point_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_exponential_histogram_data_point(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_start_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, _, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, id(Value, TrUserData), F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, id(Value, TrUserData), F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_count(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, id(Value, TrUserData), F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_sum(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(infinity, TrUserData), F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_sum(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id('-infinity', TrUserData), F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_sum(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(nan, TrUserData), F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_sum(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(Value, TrUserData), F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_scale(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) when N < 57 ->
    d_field_exponential_histogram_data_point_scale(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_scale(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, _, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    {NewFValue, RestF} = {begin
                              ZValue = (X bsl N + Acc) band 4294967295,
                              if ZValue band 1 =:= 0 -> id(ZValue bsr 1, TrUserData);
                                 true -> id(-(ZValue + 1 bsr 1), TrUserData)
                              end
                          end,
                          Rest},
    dfp_read_field_def_exponential_histogram_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, NewFValue, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_zero_count(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, _, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, id(Value, TrUserData), F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_positive(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) when N < 57 ->
    d_field_exponential_histogram_data_point_positive(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_positive(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, Prev, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_buckets(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_exponential_histogram_data_point(RestF,
                                                        0,
                                                        0,
                                                        F,
                                                        F@_1,
                                                        F@_2,
                                                        F@_3,
                                                        F@_4,
                                                        F@_5,
                                                        F@_6,
                                                        F@_7,
                                                        if Prev == '$undef' -> NewFValue;
                                                           true -> merge_msg_buckets(Prev, NewFValue, TrUserData)
                                                        end,
                                                        F@_9,
                                                        F@_10,
                                                        F@_11,
                                                        F@_12,
                                                        F@_13,
                                                        TrUserData).

d_field_exponential_histogram_data_point_negative(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) when N < 57 ->
    d_field_exponential_histogram_data_point_negative(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_negative(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, Prev, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_buckets(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_exponential_histogram_data_point(RestF,
                                                        0,
                                                        0,
                                                        F,
                                                        F@_1,
                                                        F@_2,
                                                        F@_3,
                                                        F@_4,
                                                        F@_5,
                                                        F@_6,
                                                        F@_7,
                                                        F@_8,
                                                        if Prev == '$undef' -> NewFValue;
                                                           true -> merge_msg_buckets(Prev, NewFValue, TrUserData)
                                                        end,
                                                        F@_10,
                                                        F@_11,
                                                        F@_12,
                                                        F@_13,
                                                        TrUserData).

d_field_exponential_histogram_data_point_flags(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) when N < 57 ->
    d_field_exponential_histogram_data_point_flags(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_flags(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, _, F@_11, F@_12, F@_13, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_exponential_histogram_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, NewFValue, F@_11, F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_exemplars(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) when N < 57 ->
    d_field_exponential_histogram_data_point_exemplars(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
d_field_exponential_histogram_data_point_exemplars(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, Prev, F@_12, F@_13, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_exemplar(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_exponential_histogram_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, cons(NewFValue, Prev, TrUserData), F@_12, F@_13, TrUserData).

d_field_exponential_histogram_data_point_min(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, _, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, id(infinity, TrUserData), F@_13, TrUserData);
d_field_exponential_histogram_data_point_min(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, _, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, id('-infinity', TrUserData), F@_13, TrUserData);
d_field_exponential_histogram_data_point_min(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, _, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, id(nan, TrUserData), F@_13, TrUserData);
d_field_exponential_histogram_data_point_min(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, _, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, id(Value, TrUserData), F@_13, TrUserData).

d_field_exponential_histogram_data_point_max(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, _, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, id(infinity, TrUserData), TrUserData);
d_field_exponential_histogram_data_point_max(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, _, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, id('-infinity', TrUserData), TrUserData);
d_field_exponential_histogram_data_point_max(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, _, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, id(nan, TrUserData), TrUserData);
d_field_exponential_histogram_data_point_max(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, _, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, id(Value, TrUserData), TrUserData).

skip_varint_exponential_histogram_data_point(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    skip_varint_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
skip_varint_exponential_histogram_data_point(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

skip_length_delimited_exponential_histogram_data_point(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) when N < 57 ->
    skip_length_delimited_exponential_histogram_data_point(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData);
skip_length_delimited_exponential_histogram_data_point(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_exponential_histogram_data_point(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

skip_group_exponential_histogram_data_point(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_exponential_histogram_data_point(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

skip_32_exponential_histogram_data_point(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

skip_64_exponential_histogram_data_point(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData) ->
    dfp_read_field_def_exponential_histogram_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, F@_11, F@_12, F@_13, TrUserData).

decode_msg_value_at_quantile(Bin, TrUserData) -> dfp_read_field_def_value_at_quantile(Bin, 0, 0, 0, id(0.0, TrUserData), id(0.0, TrUserData), TrUserData).

dfp_read_field_def_value_at_quantile(<<9, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_value_at_quantile_quantile(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_value_at_quantile(<<17, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_value_at_quantile_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_value_at_quantile(<<>>, 0, 0, _, F@_1, F@_2, _) -> #{quantile => F@_1, value => F@_2};
dfp_read_field_def_value_at_quantile(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_value_at_quantile(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_value_at_quantile(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_value_at_quantile(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_value_at_quantile(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        9 -> d_field_value_at_quantile_quantile(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        17 -> d_field_value_at_quantile_value(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_value_at_quantile(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_value_at_quantile(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_value_at_quantile(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_value_at_quantile(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_value_at_quantile(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_value_at_quantile(<<>>, 0, 0, _, F@_1, F@_2, _) -> #{quantile => F@_1, value => F@_2}.

d_field_value_at_quantile_quantile(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, _, F@_2, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, id(infinity, TrUserData), F@_2, TrUserData);
d_field_value_at_quantile_quantile(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, _, F@_2, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, id('-infinity', TrUserData), F@_2, TrUserData);
d_field_value_at_quantile_quantile(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, _, F@_2, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, id(nan, TrUserData), F@_2, TrUserData);
d_field_value_at_quantile_quantile(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, _, F@_2, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, id(Value, TrUserData), F@_2, TrUserData).

d_field_value_at_quantile_value(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, _, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, F@_1, id(infinity, TrUserData), TrUserData);
d_field_value_at_quantile_value(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, _, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, F@_1, id('-infinity', TrUserData), TrUserData);
d_field_value_at_quantile_value(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, _, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, F@_1, id(nan, TrUserData), TrUserData);
d_field_value_at_quantile_value(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, _, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, F@_1, id(Value, TrUserData), TrUserData).

skip_varint_value_at_quantile(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_value_at_quantile(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_value_at_quantile(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_value_at_quantile(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_value_at_quantile(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_value_at_quantile(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_value_at_quantile(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_value_at_quantile(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_value_at_quantile(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_value_at_quantile(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_value_at_quantile(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_value_at_quantile(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

decode_msg_summary_data_point(Bin, TrUserData) ->
    dfp_read_field_def_summary_data_point(Bin, 0, 0, 0, id([], TrUserData), id(0, TrUserData), id(0, TrUserData), id(0, TrUserData), id(0.0, TrUserData), id([], TrUserData), id(0, TrUserData), TrUserData).

dfp_read_field_def_summary_data_point(<<58, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> d_field_summary_data_point_attributes(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
dfp_read_field_def_summary_data_point(<<17, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> d_field_summary_data_point_start_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
dfp_read_field_def_summary_data_point(<<25, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> d_field_summary_data_point_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
dfp_read_field_def_summary_data_point(<<33, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> d_field_summary_data_point_count(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
dfp_read_field_def_summary_data_point(<<41, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> d_field_summary_data_point_sum(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
dfp_read_field_def_summary_data_point(<<50, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> d_field_summary_data_point_quantile_values(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
dfp_read_field_def_summary_data_point(<<64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> d_field_summary_data_point_flags(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
dfp_read_field_def_summary_data_point(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, F@_5, R2, F@_7, TrUserData) ->
    S1 = #{start_time_unix_nano => F@_2, time_unix_nano => F@_3, count => F@_4, sum => F@_5, flags => F@_7},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{attributes => lists_reverse(R1, TrUserData)}
         end,
    if R2 == '$undef' -> S2;
       true -> S2#{quantile_values => lists_reverse(R2, TrUserData)}
    end;
dfp_read_field_def_summary_data_point(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> dg_read_field_def_summary_data_point(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData).

dg_read_field_def_summary_data_point(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) when N < 32 - 7 ->
    dg_read_field_def_summary_data_point(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
dg_read_field_def_summary_data_point(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        58 -> d_field_summary_data_point_attributes(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
        17 -> d_field_summary_data_point_start_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
        25 -> d_field_summary_data_point_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
        33 -> d_field_summary_data_point_count(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
        41 -> d_field_summary_data_point_sum(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
        50 -> d_field_summary_data_point_quantile_values(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
        64 -> d_field_summary_data_point_flags(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_summary_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
                1 -> skip_64_summary_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
                2 -> skip_length_delimited_summary_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
                3 -> skip_group_summary_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
                5 -> skip_32_summary_data_point(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData)
            end
    end;
dg_read_field_def_summary_data_point(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, F@_5, R2, F@_7, TrUserData) ->
    S1 = #{start_time_unix_nano => F@_2, time_unix_nano => F@_3, count => F@_4, sum => F@_5, flags => F@_7},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{attributes => lists_reverse(R1, TrUserData)}
         end,
    if R2 == '$undef' -> S2;
       true -> S2#{quantile_values => lists_reverse(R2, TrUserData)}
    end.

d_field_summary_data_point_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) when N < 57 ->
    d_field_summary_data_point_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
d_field_summary_data_point_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_summary_data_point(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData).

d_field_summary_data_point_start_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, _, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) ->
    dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, id(Value, TrUserData), F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData).

d_field_summary_data_point_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, F@_6, F@_7, TrUserData) ->
    dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, id(Value, TrUserData), F@_4, F@_5, F@_6, F@_7, TrUserData).

d_field_summary_data_point_count(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, F@_7, TrUserData) ->
    dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, id(Value, TrUserData), F@_5, F@_6, F@_7, TrUserData).

d_field_summary_data_point_sum(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, TrUserData) ->
    dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(infinity, TrUserData), F@_6, F@_7, TrUserData);
d_field_summary_data_point_sum(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, TrUserData) ->
    dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id('-infinity', TrUserData), F@_6, F@_7, TrUserData);
d_field_summary_data_point_sum(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, TrUserData) ->
    dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(nan, TrUserData), F@_6, F@_7, TrUserData);
d_field_summary_data_point_sum(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, _, F@_6, F@_7, TrUserData) ->
    dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, id(Value, TrUserData), F@_6, F@_7, TrUserData).

d_field_summary_data_point_quantile_values(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) when N < 57 ->
    d_field_summary_data_point_quantile_values(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
d_field_summary_data_point_quantile_values(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, Prev, F@_7, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_value_at_quantile(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_summary_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, cons(NewFValue, Prev, TrUserData), F@_7, TrUserData).

d_field_summary_data_point_flags(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) when N < 57 ->
    d_field_summary_data_point_flags(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
d_field_summary_data_point_flags(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, _, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_summary_data_point(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, NewFValue, TrUserData).

skip_varint_summary_data_point(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> skip_varint_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
skip_varint_summary_data_point(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData).

skip_length_delimited_summary_data_point(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) when N < 57 ->
    skip_length_delimited_summary_data_point(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData);
skip_length_delimited_summary_data_point(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_summary_data_point(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData).

skip_group_summary_data_point(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_summary_data_point(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData).

skip_32_summary_data_point(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData).

skip_64_summary_data_point(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData) -> dfp_read_field_def_summary_data_point(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, TrUserData).

decode_msg_exemplar(Bin, TrUserData) -> dfp_read_field_def_exemplar(Bin, 0, 0, 0, id([], TrUserData), id(0, TrUserData), id('$undef', TrUserData), id(<<>>, TrUserData), id(<<>>, TrUserData), TrUserData).

dfp_read_field_def_exemplar(<<58, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> d_field_exemplar_filtered_attributes(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
dfp_read_field_def_exemplar(<<17, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> d_field_exemplar_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
dfp_read_field_def_exemplar(<<25, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> d_field_exemplar_as_double(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
dfp_read_field_def_exemplar(<<49, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> d_field_exemplar_as_int(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
dfp_read_field_def_exemplar(<<34, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> d_field_exemplar_span_id(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
dfp_read_field_def_exemplar(<<42, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> d_field_exemplar_trace_id(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
dfp_read_field_def_exemplar(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, F@_5, TrUserData) ->
    S1 = #{time_unix_nano => F@_2, span_id => F@_4, trace_id => F@_5},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{filtered_attributes => lists_reverse(R1, TrUserData)}
         end,
    if F@_3 == '$undef' -> S2;
       true -> S2#{value => F@_3}
    end;
dfp_read_field_def_exemplar(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> dg_read_field_def_exemplar(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData).

dg_read_field_def_exemplar(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) when N < 32 - 7 -> dg_read_field_def_exemplar(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
dg_read_field_def_exemplar(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        58 -> d_field_exemplar_filtered_attributes(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
        17 -> d_field_exemplar_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
        25 -> d_field_exemplar_as_double(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
        49 -> d_field_exemplar_as_int(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
        34 -> d_field_exemplar_span_id(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
        42 -> d_field_exemplar_trace_id(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_exemplar(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
                1 -> skip_64_exemplar(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
                2 -> skip_length_delimited_exemplar(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
                3 -> skip_group_exemplar(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
                5 -> skip_32_exemplar(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData)
            end
    end;
dg_read_field_def_exemplar(<<>>, 0, 0, _, R1, F@_2, F@_3, F@_4, F@_5, TrUserData) ->
    S1 = #{time_unix_nano => F@_2, span_id => F@_4, trace_id => F@_5},
    S2 = if R1 == '$undef' -> S1;
            true -> S1#{filtered_attributes => lists_reverse(R1, TrUserData)}
         end,
    if F@_3 == '$undef' -> S2;
       true -> S2#{value => F@_3}
    end.

d_field_exemplar_filtered_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) when N < 57 -> d_field_exemplar_filtered_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
d_field_exemplar_filtered_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, F@_4, F@_5, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_exemplar(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, F@_3, F@_4, F@_5, TrUserData).

d_field_exemplar_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, _, F@_3, F@_4, F@_5, TrUserData) -> dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, id(Value, TrUserData), F@_3, F@_4, F@_5, TrUserData).

d_field_exemplar_as_double(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, TrUserData) -> dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, id({as_double, id(infinity, TrUserData)}, TrUserData), F@_4, F@_5, TrUserData);
d_field_exemplar_as_double(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, TrUserData) ->
    dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, id({as_double, id('-infinity', TrUserData)}, TrUserData), F@_4, F@_5, TrUserData);
d_field_exemplar_as_double(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, TrUserData) ->
    dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, id({as_double, id(nan, TrUserData)}, TrUserData), F@_4, F@_5, TrUserData);
d_field_exemplar_as_double(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, TrUserData) ->
    dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, id({as_double, id(Value, TrUserData)}, TrUserData), F@_4, F@_5, TrUserData).

d_field_exemplar_as_int(<<Value:64/little-signed, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, _, F@_4, F@_5, TrUserData) -> dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, id({as_int, id(Value, TrUserData)}, TrUserData), F@_4, F@_5, TrUserData).

d_field_exemplar_span_id(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) when N < 57 -> d_field_exemplar_span_id(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
d_field_exemplar_span_id(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, _, F@_5, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_exemplar(RestF, 0, 0, F, F@_1, F@_2, F@_3, NewFValue, F@_5, TrUserData).

d_field_exemplar_trace_id(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) when N < 57 -> d_field_exemplar_trace_id(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
d_field_exemplar_trace_id(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_exemplar(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, NewFValue, TrUserData).

skip_varint_exemplar(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> skip_varint_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
skip_varint_exemplar(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData).

skip_length_delimited_exemplar(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) when N < 57 -> skip_length_delimited_exemplar(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData);
skip_length_delimited_exemplar(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_exemplar(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData).

skip_group_exemplar(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_exemplar(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData).

skip_32_exemplar(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData).

skip_64_exemplar(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData) -> dfp_read_field_def_exemplar(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, TrUserData).

decode_msg_any_value(Bin, TrUserData) -> dfp_read_field_def_any_value(Bin, 0, 0, 0, id('$undef', TrUserData), TrUserData).

dfp_read_field_def_any_value(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_string_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<16, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_bool_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<24, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_int_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<33, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_double_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<42, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_array_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<50, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_kvlist_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<58, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_bytes_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<>>, 0, 0, _, F@_1, _) ->
    S1 = #{},
    if F@_1 == '$undef' -> S1;
       true -> S1#{value => F@_1}
    end;
dfp_read_field_def_any_value(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_any_value(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_any_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_any_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_any_value(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_any_value_string_value(Rest, 0, 0, 0, F@_1, TrUserData);
        16 -> d_field_any_value_bool_value(Rest, 0, 0, 0, F@_1, TrUserData);
        24 -> d_field_any_value_int_value(Rest, 0, 0, 0, F@_1, TrUserData);
        33 -> d_field_any_value_double_value(Rest, 0, 0, 0, F@_1, TrUserData);
        42 -> d_field_any_value_array_value(Rest, 0, 0, 0, F@_1, TrUserData);
        50 -> d_field_any_value_kvlist_value(Rest, 0, 0, 0, F@_1, TrUserData);
        58 -> d_field_any_value_bytes_value(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_any_value(<<>>, 0, 0, _, F@_1, _) ->
    S1 = #{},
    if F@_1 == '$undef' -> S1;
       true -> S1#{value => F@_1}
    end.

d_field_any_value_string_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_string_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_string_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_any_value(RestF, 0, 0, F, id({string_value, NewFValue}, TrUserData), TrUserData).

d_field_any_value_bool_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_bool_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_bool_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, TrUserData) ->
    {NewFValue, RestF} = {id(X bsl N + Acc =/= 0, TrUserData), Rest},
    dfp_read_field_def_any_value(RestF, 0, 0, F, id({bool_value, NewFValue}, TrUserData), TrUserData).

d_field_any_value_int_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_int_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_int_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, TrUserData) ->
    {NewFValue, RestF} = {begin <<Res:64/signed-native>> = <<(X bsl N + Acc):64/unsigned-native>>, id(Res, TrUserData) end, Rest},
    dfp_read_field_def_any_value(RestF, 0, 0, F, id({int_value, NewFValue}, TrUserData), TrUserData).

d_field_any_value_double_value(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, _, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, id({double_value, id(infinity, TrUserData)}, TrUserData), TrUserData);
d_field_any_value_double_value(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, _, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, id({double_value, id('-infinity', TrUserData)}, TrUserData), TrUserData);
d_field_any_value_double_value(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, _, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, id({double_value, id(nan, TrUserData)}, TrUserData), TrUserData);
d_field_any_value_double_value(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, _, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, id({double_value, id(Value, TrUserData)}, TrUserData), TrUserData).

d_field_any_value_array_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_array_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_array_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_array_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_any_value(RestF,
                                 0,
                                 0,
                                 F,
                                 case Prev of
                                     '$undef' -> id({array_value, NewFValue}, TrUserData);
                                     {array_value, MVPrev} -> id({array_value, merge_msg_array_value(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                     _ -> id({array_value, NewFValue}, TrUserData)
                                 end,
                                 TrUserData).

d_field_any_value_kvlist_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_kvlist_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_kvlist_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value_list(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_any_value(RestF,
                                 0,
                                 0,
                                 F,
                                 case Prev of
                                     '$undef' -> id({kvlist_value, NewFValue}, TrUserData);
                                     {kvlist_value, MVPrev} -> id({kvlist_value, merge_msg_key_value_list(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                     _ -> id({kvlist_value, NewFValue}, TrUserData)
                                 end,
                                 TrUserData).

d_field_any_value_bytes_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_bytes_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_bytes_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_any_value(RestF, 0, 0, F, id({bytes_value, NewFValue}, TrUserData), TrUserData).

skip_varint_any_value(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_any_value(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_any_value(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_any_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_any_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_any_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_any_value(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_any_value(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_any_value(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_any_value(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_any_value(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_array_value(Bin, TrUserData) -> dfp_read_field_def_array_value(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_array_value(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_array_value_values(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_array_value(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{values => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_array_value(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_array_value(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_array_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_array_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_array_value(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_array_value_values(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_array_value(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{values => lists_reverse(R1, TrUserData)}
    end.

d_field_array_value_values(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_array_value_values(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_array_value_values(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_any_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_array_value(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_array_value(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_array_value(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_array_value(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_array_value(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_array_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_array_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_array_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_array_value(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_array_value(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_array_value(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_array_value(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_array_value(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_array_value(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_array_value(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_key_value_list(Bin, TrUserData) -> dfp_read_field_def_key_value_list(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_key_value_list(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_key_value_list_values(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_key_value_list(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{values => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_key_value_list(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_key_value_list(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_key_value_list(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_key_value_list(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_key_value_list(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_key_value_list_values(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_key_value_list(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{values => lists_reverse(R1, TrUserData)}
    end.

d_field_key_value_list_values(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_key_value_list_values(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_key_value_list_values(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_key_value_list(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_key_value_list(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_key_value_list(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_key_value_list(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_key_value_list(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_key_value_list(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_key_value_list(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_key_value_list(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_key_value_list(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_key_value_list(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_key_value_list(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_key_value_list(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_key_value_list(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_key_value_list(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_key_value_list(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_key_value(Bin, TrUserData) -> dfp_read_field_def_key_value(Bin, 0, 0, 0, id(<<>>, TrUserData), id('$undef', TrUserData), TrUserData).

dfp_read_field_def_key_value(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_key_value_key(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_key_value(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_key_value_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_key_value(<<>>, 0, 0, _, F@_1, F@_2, _) ->
    S1 = #{key => F@_1},
    if F@_2 == '$undef' -> S1;
       true -> S1#{value => F@_2}
    end;
dfp_read_field_def_key_value(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_key_value(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_key_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_key_value(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_key_value(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_key_value_key(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        18 -> d_field_key_value_value(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_key_value(<<>>, 0, 0, _, F@_1, F@_2, _) ->
    S1 = #{key => F@_1},
    if F@_2 == '$undef' -> S1;
       true -> S1#{value => F@_2}
    end.

d_field_key_value_key(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_key_value_key(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_key_value_key(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_key_value(RestF, 0, 0, F, NewFValue, F@_2, TrUserData).

d_field_key_value_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_key_value_value(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_key_value_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_any_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_key_value(RestF,
                                 0,
                                 0,
                                 F,
                                 F@_1,
                                 if Prev == '$undef' -> NewFValue;
                                    true -> merge_msg_any_value(Prev, NewFValue, TrUserData)
                                 end,
                                 TrUserData).

skip_varint_key_value(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_key_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_key_value(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_key_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_key_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_key_value(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_key_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_key_value(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_key_value(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_key_value(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_key_value(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_key_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_key_value(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_key_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

decode_msg_instrumentation_scope(Bin, TrUserData) -> dfp_read_field_def_instrumentation_scope(Bin, 0, 0, 0, id(<<>>, TrUserData), id(<<>>, TrUserData), id([], TrUserData), id(0, TrUserData), TrUserData).

dfp_read_field_def_instrumentation_scope(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_instrumentation_scope_name(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_instrumentation_scope(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_instrumentation_scope_version(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_instrumentation_scope(<<26, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_instrumentation_scope_attributes(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_instrumentation_scope(<<32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_instrumentation_scope_dropped_attributes_count(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_instrumentation_scope(<<>>, 0, 0, _, F@_1, F@_2, R1, F@_4, TrUserData) ->
    S1 = #{name => F@_1, version => F@_2, dropped_attributes_count => F@_4},
    if R1 == '$undef' -> S1;
       true -> S1#{attributes => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_instrumentation_scope(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dg_read_field_def_instrumentation_scope(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

dg_read_field_def_instrumentation_scope(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 32 - 7 -> dg_read_field_def_instrumentation_scope(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dg_read_field_def_instrumentation_scope(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_instrumentation_scope_name(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        18 -> d_field_instrumentation_scope_version(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        26 -> d_field_instrumentation_scope_attributes(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        32 -> d_field_instrumentation_scope_dropped_attributes_count(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                1 -> skip_64_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                2 -> skip_length_delimited_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                3 -> skip_group_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                5 -> skip_32_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData)
            end
    end;
dg_read_field_def_instrumentation_scope(<<>>, 0, 0, _, F@_1, F@_2, R1, F@_4, TrUserData) ->
    S1 = #{name => F@_1, version => F@_2, dropped_attributes_count => F@_4},
    if R1 == '$undef' -> S1;
       true -> S1#{attributes => lists_reverse(R1, TrUserData)}
    end.

d_field_instrumentation_scope_name(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_instrumentation_scope_name(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_instrumentation_scope_name(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, F@_2, F@_3, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_instrumentation_scope(RestF, 0, 0, F, NewFValue, F@_2, F@_3, F@_4, TrUserData).

d_field_instrumentation_scope_version(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_instrumentation_scope_version(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_instrumentation_scope_version(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, F@_3, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_instrumentation_scope(RestF, 0, 0, F, F@_1, NewFValue, F@_3, F@_4, TrUserData).

d_field_instrumentation_scope_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_instrumentation_scope_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_instrumentation_scope_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, Prev, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_instrumentation_scope(RestF, 0, 0, F, F@_1, F@_2, cons(NewFValue, Prev, TrUserData), F@_4, TrUserData).

d_field_instrumentation_scope_dropped_attributes_count(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 ->
    d_field_instrumentation_scope_dropped_attributes_count(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_instrumentation_scope_dropped_attributes_count(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, _, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_instrumentation_scope(RestF, 0, 0, F, F@_1, F@_2, F@_3, NewFValue, TrUserData).

skip_varint_instrumentation_scope(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> skip_varint_instrumentation_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
skip_varint_instrumentation_scope(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_instrumentation_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_length_delimited_instrumentation_scope(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> skip_length_delimited_instrumentation_scope(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
skip_length_delimited_instrumentation_scope(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_instrumentation_scope(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_group_instrumentation_scope(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_instrumentation_scope(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_32_instrumentation_scope(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_instrumentation_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_64_instrumentation_scope(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_instrumentation_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

decode_msg_resource(Bin, TrUserData) -> dfp_read_field_def_resource(Bin, 0, 0, 0, id([], TrUserData), id(0, TrUserData), TrUserData).

dfp_read_field_def_resource(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_resource_attributes(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_resource(<<16, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_resource_dropped_attributes_count(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_resource(<<>>, 0, 0, _, R1, F@_2, TrUserData) ->
    S1 = #{dropped_attributes_count => F@_2},
    if R1 == '$undef' -> S1;
       true -> S1#{attributes => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_resource(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_resource(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_resource(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_resource(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_resource(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_resource_attributes(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        16 -> d_field_resource_dropped_attributes_count(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_resource(<<>>, 0, 0, _, R1, F@_2, TrUserData) ->
    S1 = #{dropped_attributes_count => F@_2},
    if R1 == '$undef' -> S1;
       true -> S1#{attributes => lists_reverse(R1, TrUserData)}
    end.

d_field_resource_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_resource_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_resource_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_resource(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, TrUserData).

d_field_resource_dropped_attributes_count(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_resource_dropped_attributes_count(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_resource_dropped_attributes_count(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_resource(RestF, 0, 0, F, F@_1, NewFValue, TrUserData).

skip_varint_resource(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_resource(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_resource(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_resource(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_resource(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_resource(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_resource(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_resource(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_resource(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_resource(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_resource(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_resource(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_resource(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_resource(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

'd_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(0) -> 'AGGREGATION_TEMPORALITY_UNSPECIFIED';
'd_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(1) -> 'AGGREGATION_TEMPORALITY_DELTA';
'd_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(2) -> 'AGGREGATION_TEMPORALITY_CUMULATIVE';
'd_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(V) -> V.

read_group(Bin, FieldNum) ->
    {NumBytes, EndTagLen} = read_gr_b(Bin, 0, 0, 0, 0, FieldNum),
    <<Group:NumBytes/binary, _:EndTagLen/binary, Rest/binary>> = Bin,
    {Group, Rest}.

%% Like skipping over fields, but record the total length,
%% Each field is <(FieldNum bsl 3) bor FieldType> ++ <FieldValue>
%% Record the length because varints may be non-optimally encoded.
%%
%% Groups can be nested, but assume the same FieldNum cannot be nested
%% because group field numbers are shared with the rest of the fields
%% numbers. Thus we can search just for an group-end with the same
%% field number.
%%
%% (The only time the same group field number could occur would
%% be in a nested sub message, but then it would be inside a
%% length-delimited entry, which we skip-read by length.)
read_gr_b(<<1:1, X:7, Tl/binary>>, N, Acc, NumBytes, TagLen, FieldNum)
  when N < (32-7) ->
    read_gr_b(Tl, N+7, X bsl N + Acc, NumBytes, TagLen+1, FieldNum);
read_gr_b(<<0:1, X:7, Tl/binary>>, N, Acc, NumBytes, TagLen,
          FieldNum) ->
    Key = X bsl N + Acc,
    TagLen1 = TagLen + 1,
    case {Key bsr 3, Key band 7} of
        {FieldNum, 4} -> % 4 = group_end
            {NumBytes, TagLen1};
        {_, 0} -> % 0 = varint
            read_gr_vi(Tl, 0, NumBytes + TagLen1, FieldNum);
        {_, 1} -> % 1 = bits64
            <<_:64, Tl2/binary>> = Tl,
            read_gr_b(Tl2, 0, 0, NumBytes + TagLen1 + 8, 0, FieldNum);
        {_, 2} -> % 2 = length_delimited
            read_gr_ld(Tl, 0, 0, NumBytes + TagLen1, FieldNum);
        {_, 3} -> % 3 = group_start
            read_gr_b(Tl, 0, 0, NumBytes + TagLen1, 0, FieldNum);
        {_, 4} -> % 4 = group_end
            read_gr_b(Tl, 0, 0, NumBytes + TagLen1, 0, FieldNum);
        {_, 5} -> % 5 = bits32
            <<_:32, Tl2/binary>> = Tl,
            read_gr_b(Tl2, 0, 0, NumBytes + TagLen1 + 4, 0, FieldNum)
    end.

read_gr_vi(<<1:1, _:7, Tl/binary>>, N, NumBytes, FieldNum)
  when N < (64-7) ->
    read_gr_vi(Tl, N+7, NumBytes+1, FieldNum);
read_gr_vi(<<0:1, _:7, Tl/binary>>, _, NumBytes, FieldNum) ->
    read_gr_b(Tl, 0, 0, NumBytes+1, 0, FieldNum).

read_gr_ld(<<1:1, X:7, Tl/binary>>, N, Acc, NumBytes, FieldNum)
  when N < (64-7) ->
    read_gr_ld(Tl, N+7, X bsl N + Acc, NumBytes+1, FieldNum);
read_gr_ld(<<0:1, X:7, Tl/binary>>, N, Acc, NumBytes, FieldNum) ->
    Len = X bsl N + Acc,
    NumBytes1 = NumBytes + 1,
    <<_:Len/binary, Tl2/binary>> = Tl,
    read_gr_b(Tl2, 0, 0, NumBytes1 + Len, 0, FieldNum).

merge_msgs(Prev, New, MsgName) when is_atom(MsgName) -> merge_msgs(Prev, New, MsgName, []).

merge_msgs(Prev, New, MsgName, Opts) ->
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        export_metrics_service_request -> merge_msg_export_metrics_service_request(Prev, New, TrUserData);
        export_metrics_service_response -> merge_msg_export_metrics_service_response(Prev, New, TrUserData);
        export_metrics_partial_success -> merge_msg_export_metrics_partial_success(Prev, New, TrUserData);
        metrics_data -> merge_msg_metrics_data(Prev, New, TrUserData);
        resource_metrics -> merge_msg_resource_metrics(Prev, New, TrUserData);
        scope_metrics -> merge_msg_scope_metrics(Prev, New, TrUserData);
        metric -> merge_msg_metric(Prev, New, TrUserData);
        gauge -> merge_msg_gauge(Prev, New, TrUserData);
        sum -> merge_msg_sum(Prev, New, TrUserData);
        histogram -> merge_msg_histogram(Prev, New, TrUserData);
        exponential_histogram -> merge_msg_exponential_histogram(Prev, New, TrUserData);
        summary -> merge_msg_summary(Prev, New, TrUserData);
        number_data_point -> merge_msg_number_data_point(Prev, New, TrUserData);
        histogram_data_point -> merge_msg_histogram_data_point(Prev, New, TrUserData);
        buckets -> merge_msg_buckets(Prev, New, TrUserData);
        exponential_histogram_data_point -> merge_msg_exponential_histogram_data_point(Prev, New, TrUserData);
        value_at_quantile -> merge_msg_value_at_quantile(Prev, New, TrUserData);
        summary_data_point -> merge_msg_summary_data_point(Prev, New, TrUserData);
        exemplar -> merge_msg_exemplar(Prev, New, TrUserData);
        any_value -> merge_msg_any_value(Prev, New, TrUserData);
        array_value -> merge_msg_array_value(Prev, New, TrUserData);
        key_value_list -> merge_msg_key_value_list(Prev, New, TrUserData);
        key_value -> merge_msg_key_value(Prev, New, TrUserData);
        instrumentation_scope -> merge_msg_instrumentation_scope(Prev, New, TrUserData);
        resource -> merge_msg_resource(Prev, New, TrUserData)
    end.

-compile({nowarn_unused_function,merge_msg_export_metrics_service_request/3}).
merge_msg_export_metrics_service_request(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{resource_metrics := PFresource_metrics}, #{resource_metrics := NFresource_metrics}} -> S1#{resource_metrics => 'erlang_++'(PFresource_metrics, NFresource_metrics, TrUserData)};
        {_, #{resource_metrics := NFresource_metrics}} -> S1#{resource_metrics => NFresource_metrics};
        {#{resource_metrics := PFresource_metrics}, _} -> S1#{resource_metrics => PFresource_metrics};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_export_metrics_service_response/3}).
merge_msg_export_metrics_service_response(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{partial_success := PFpartial_success}, #{partial_success := NFpartial_success}} -> S1#{partial_success => merge_msg_export_metrics_partial_success(PFpartial_success, NFpartial_success, TrUserData)};
        {_, #{partial_success := NFpartial_success}} -> S1#{partial_success => NFpartial_success};
        {#{partial_success := PFpartial_success}, _} -> S1#{partial_success => PFpartial_success};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_export_metrics_partial_success/3}).
merge_msg_export_metrics_partial_success(PMsg, NMsg, _) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{rejected_data_points := NFrejected_data_points}} -> S1#{rejected_data_points => NFrejected_data_points};
             {#{rejected_data_points := PFrejected_data_points}, _} -> S1#{rejected_data_points => PFrejected_data_points};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{error_message := NFerror_message}} -> S2#{error_message => NFerror_message};
        {#{error_message := PFerror_message}, _} -> S2#{error_message => PFerror_message};
        _ -> S2
    end.

-compile({nowarn_unused_function,merge_msg_metrics_data/3}).
merge_msg_metrics_data(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{resource_metrics := PFresource_metrics}, #{resource_metrics := NFresource_metrics}} -> S1#{resource_metrics => 'erlang_++'(PFresource_metrics, NFresource_metrics, TrUserData)};
        {_, #{resource_metrics := NFresource_metrics}} -> S1#{resource_metrics => NFresource_metrics};
        {#{resource_metrics := PFresource_metrics}, _} -> S1#{resource_metrics => PFresource_metrics};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_resource_metrics/3}).
merge_msg_resource_metrics(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{resource := PFresource}, #{resource := NFresource}} -> S1#{resource => merge_msg_resource(PFresource, NFresource, TrUserData)};
             {_, #{resource := NFresource}} -> S1#{resource => NFresource};
             {#{resource := PFresource}, _} -> S1#{resource => PFresource};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {#{scope_metrics := PFscope_metrics}, #{scope_metrics := NFscope_metrics}} -> S2#{scope_metrics => 'erlang_++'(PFscope_metrics, NFscope_metrics, TrUserData)};
             {_, #{scope_metrics := NFscope_metrics}} -> S2#{scope_metrics => NFscope_metrics};
             {#{scope_metrics := PFscope_metrics}, _} -> S2#{scope_metrics => PFscope_metrics};
             {_, _} -> S2
         end,
    case {PMsg, NMsg} of
        {_, #{schema_url := NFschema_url}} -> S3#{schema_url => NFschema_url};
        {#{schema_url := PFschema_url}, _} -> S3#{schema_url => PFschema_url};
        _ -> S3
    end.

-compile({nowarn_unused_function,merge_msg_scope_metrics/3}).
merge_msg_scope_metrics(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{scope := PFscope}, #{scope := NFscope}} -> S1#{scope => merge_msg_instrumentation_scope(PFscope, NFscope, TrUserData)};
             {_, #{scope := NFscope}} -> S1#{scope => NFscope};
             {#{scope := PFscope}, _} -> S1#{scope => PFscope};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {#{metrics := PFmetrics}, #{metrics := NFmetrics}} -> S2#{metrics => 'erlang_++'(PFmetrics, NFmetrics, TrUserData)};
             {_, #{metrics := NFmetrics}} -> S2#{metrics => NFmetrics};
             {#{metrics := PFmetrics}, _} -> S2#{metrics => PFmetrics};
             {_, _} -> S2
         end,
    case {PMsg, NMsg} of
        {_, #{schema_url := NFschema_url}} -> S3#{schema_url => NFschema_url};
        {#{schema_url := PFschema_url}, _} -> S3#{schema_url => PFschema_url};
        _ -> S3
    end.

-compile({nowarn_unused_function,merge_msg_metric/3}).
merge_msg_metric(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{name := NFname}} -> S1#{name => NFname};
             {#{name := PFname}, _} -> S1#{name => PFname};
             _ -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{description := NFdescription}} -> S2#{description => NFdescription};
             {#{description := PFdescription}, _} -> S2#{description => PFdescription};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {_, #{unit := NFunit}} -> S3#{unit => NFunit};
             {#{unit := PFunit}, _} -> S3#{unit => PFunit};
             _ -> S3
         end,
    case {PMsg, NMsg} of
        {#{data := {gauge, OPFdata}}, #{data := {gauge, ONFdata}}} -> S4#{data => {gauge, merge_msg_gauge(OPFdata, ONFdata, TrUserData)}};
        {#{data := {sum, OPFdata}}, #{data := {sum, ONFdata}}} -> S4#{data => {sum, merge_msg_sum(OPFdata, ONFdata, TrUserData)}};
        {#{data := {histogram, OPFdata}}, #{data := {histogram, ONFdata}}} -> S4#{data => {histogram, merge_msg_histogram(OPFdata, ONFdata, TrUserData)}};
        {#{data := {exponential_histogram, OPFdata}}, #{data := {exponential_histogram, ONFdata}}} -> S4#{data => {exponential_histogram, merge_msg_exponential_histogram(OPFdata, ONFdata, TrUserData)}};
        {#{data := {summary, OPFdata}}, #{data := {summary, ONFdata}}} -> S4#{data => {summary, merge_msg_summary(OPFdata, ONFdata, TrUserData)}};
        {_, #{data := NFdata}} -> S4#{data => NFdata};
        {#{data := PFdata}, _} -> S4#{data => PFdata};
        {_, _} -> S4
    end.

-compile({nowarn_unused_function,merge_msg_gauge/3}).
merge_msg_gauge(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{data_points := PFdata_points}, #{data_points := NFdata_points}} -> S1#{data_points => 'erlang_++'(PFdata_points, NFdata_points, TrUserData)};
        {_, #{data_points := NFdata_points}} -> S1#{data_points => NFdata_points};
        {#{data_points := PFdata_points}, _} -> S1#{data_points => PFdata_points};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_sum/3}).
merge_msg_sum(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{data_points := PFdata_points}, #{data_points := NFdata_points}} -> S1#{data_points => 'erlang_++'(PFdata_points, NFdata_points, TrUserData)};
             {_, #{data_points := NFdata_points}} -> S1#{data_points => NFdata_points};
             {#{data_points := PFdata_points}, _} -> S1#{data_points => PFdata_points};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{aggregation_temporality := NFaggregation_temporality}} -> S2#{aggregation_temporality => NFaggregation_temporality};
             {#{aggregation_temporality := PFaggregation_temporality}, _} -> S2#{aggregation_temporality => PFaggregation_temporality};
             _ -> S2
         end,
    case {PMsg, NMsg} of
        {_, #{is_monotonic := NFis_monotonic}} -> S3#{is_monotonic => NFis_monotonic};
        {#{is_monotonic := PFis_monotonic}, _} -> S3#{is_monotonic => PFis_monotonic};
        _ -> S3
    end.

-compile({nowarn_unused_function,merge_msg_histogram/3}).
merge_msg_histogram(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{data_points := PFdata_points}, #{data_points := NFdata_points}} -> S1#{data_points => 'erlang_++'(PFdata_points, NFdata_points, TrUserData)};
             {_, #{data_points := NFdata_points}} -> S1#{data_points => NFdata_points};
             {#{data_points := PFdata_points}, _} -> S1#{data_points => PFdata_points};
             {_, _} -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{aggregation_temporality := NFaggregation_temporality}} -> S2#{aggregation_temporality => NFaggregation_temporality};
        {#{aggregation_temporality := PFaggregation_temporality}, _} -> S2#{aggregation_temporality => PFaggregation_temporality};
        _ -> S2
    end.

-compile({nowarn_unused_function,merge_msg_exponential_histogram/3}).
merge_msg_exponential_histogram(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{data_points := PFdata_points}, #{data_points := NFdata_points}} -> S1#{data_points => 'erlang_++'(PFdata_points, NFdata_points, TrUserData)};
             {_, #{data_points := NFdata_points}} -> S1#{data_points => NFdata_points};
             {#{data_points := PFdata_points}, _} -> S1#{data_points => PFdata_points};
             {_, _} -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{aggregation_temporality := NFaggregation_temporality}} -> S2#{aggregation_temporality => NFaggregation_temporality};
        {#{aggregation_temporality := PFaggregation_temporality}, _} -> S2#{aggregation_temporality => PFaggregation_temporality};
        _ -> S2
    end.

-compile({nowarn_unused_function,merge_msg_summary/3}).
merge_msg_summary(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{data_points := PFdata_points}, #{data_points := NFdata_points}} -> S1#{data_points => 'erlang_++'(PFdata_points, NFdata_points, TrUserData)};
        {_, #{data_points := NFdata_points}} -> S1#{data_points => NFdata_points};
        {#{data_points := PFdata_points}, _} -> S1#{data_points => PFdata_points};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_number_data_point/3}).
merge_msg_number_data_point(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S1#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S1#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S1#{attributes => PFattributes};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{start_time_unix_nano := NFstart_time_unix_nano}} -> S2#{start_time_unix_nano => NFstart_time_unix_nano};
             {#{start_time_unix_nano := PFstart_time_unix_nano}, _} -> S2#{start_time_unix_nano => PFstart_time_unix_nano};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {_, #{time_unix_nano := NFtime_unix_nano}} -> S3#{time_unix_nano => NFtime_unix_nano};
             {#{time_unix_nano := PFtime_unix_nano}, _} -> S3#{time_unix_nano => PFtime_unix_nano};
             _ -> S3
         end,
    S5 = case {PMsg, NMsg} of
             {_, #{value := NFvalue}} -> S4#{value => NFvalue};
             {#{value := PFvalue}, _} -> S4#{value => PFvalue};
             _ -> S4
         end,
    S6 = case {PMsg, NMsg} of
             {#{exemplars := PFexemplars}, #{exemplars := NFexemplars}} -> S5#{exemplars => 'erlang_++'(PFexemplars, NFexemplars, TrUserData)};
             {_, #{exemplars := NFexemplars}} -> S5#{exemplars => NFexemplars};
             {#{exemplars := PFexemplars}, _} -> S5#{exemplars => PFexemplars};
             {_, _} -> S5
         end,
    case {PMsg, NMsg} of
        {_, #{flags := NFflags}} -> S6#{flags => NFflags};
        {#{flags := PFflags}, _} -> S6#{flags => PFflags};
        _ -> S6
    end.

-compile({nowarn_unused_function,merge_msg_histogram_data_point/3}).
merge_msg_histogram_data_point(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S1#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S1#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S1#{attributes => PFattributes};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{start_time_unix_nano := NFstart_time_unix_nano}} -> S2#{start_time_unix_nano => NFstart_time_unix_nano};
             {#{start_time_unix_nano := PFstart_time_unix_nano}, _} -> S2#{start_time_unix_nano => PFstart_time_unix_nano};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {_, #{time_unix_nano := NFtime_unix_nano}} -> S3#{time_unix_nano => NFtime_unix_nano};
             {#{time_unix_nano := PFtime_unix_nano}, _} -> S3#{time_unix_nano => PFtime_unix_nano};
             _ -> S3
         end,
    S5 = case {PMsg, NMsg} of
             {_, #{count := NFcount}} -> S4#{count => NFcount};
             {#{count := PFcount}, _} -> S4#{count => PFcount};
             _ -> S4
         end,
    S6 = case {PMsg, NMsg} of
             {_, #{sum := NFsum}} -> S5#{sum => NFsum};
             {#{sum := PFsum}, _} -> S5#{sum => PFsum};
             _ -> S5
         end,
    S7 = case {PMsg, NMsg} of
             {#{bucket_counts := PFbucket_counts}, #{bucket_counts := NFbucket_counts}} -> S6#{bucket_counts => 'erlang_++'(PFbucket_counts, NFbucket_counts, TrUserData)};
             {_, #{bucket_counts := NFbucket_counts}} -> S6#{bucket_counts => NFbucket_counts};
             {#{bucket_counts := PFbucket_counts}, _} -> S6#{bucket_counts => PFbucket_counts};
             {_, _} -> S6
         end,
    S8 = case {PMsg, NMsg} of
             {#{explicit_bounds := PFexplicit_bounds}, #{explicit_bounds := NFexplicit_bounds}} -> S7#{explicit_bounds => 'erlang_++'(PFexplicit_bounds, NFexplicit_bounds, TrUserData)};
             {_, #{explicit_bounds := NFexplicit_bounds}} -> S7#{explicit_bounds => NFexplicit_bounds};
             {#{explicit_bounds := PFexplicit_bounds}, _} -> S7#{explicit_bounds => PFexplicit_bounds};
             {_, _} -> S7
         end,
    S9 = case {PMsg, NMsg} of
             {#{exemplars := PFexemplars}, #{exemplars := NFexemplars}} -> S8#{exemplars => 'erlang_++'(PFexemplars, NFexemplars, TrUserData)};
             {_, #{exemplars := NFexemplars}} -> S8#{exemplars => NFexemplars};
             {#{exemplars := PFexemplars}, _} -> S8#{exemplars => PFexemplars};
             {_, _} -> S8
         end,
    S10 = case {PMsg, NMsg} of
              {_, #{flags := NFflags}} -> S9#{flags => NFflags};
              {#{flags := PFflags}, _} -> S9#{flags => PFflags};
              _ -> S9
          end,
    S11 = case {PMsg, NMsg} of
              {_, #{min := NFmin}} -> S10#{min => NFmin};
              {#{min := PFmin}, _} -> S10#{min => PFmin};
              _ -> S10
          end,
    case {PMsg, NMsg} of
        {_, #{max := NFmax}} -> S11#{max => NFmax};
        {#{max := PFmax}, _} -> S11#{max => PFmax};
        _ -> S11
    end.

-compile({nowarn_unused_function,merge_msg_buckets/3}).
merge_msg_buckets(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{offset := NFoffset}} -> S1#{offset => NFoffset};
             {#{offset := PFoffset}, _} -> S1#{offset => PFoffset};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {#{bucket_counts := PFbucket_counts}, #{bucket_counts := NFbucket_counts}} -> S2#{bucket_counts => 'erlang_++'(PFbucket_counts, NFbucket_counts, TrUserData)};
        {_, #{bucket_counts := NFbucket_counts}} -> S2#{bucket_counts => NFbucket_counts};
        {#{bucket_counts := PFbucket_counts}, _} -> S2#{bucket_counts => PFbucket_counts};
        {_, _} -> S2
    end.

-compile({nowarn_unused_function,merge_msg_exponential_histogram_data_point/3}).
merge_msg_exponential_histogram_data_point(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S1#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S1#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S1#{attributes => PFattributes};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{start_time_unix_nano := NFstart_time_unix_nano}} -> S2#{start_time_unix_nano => NFstart_time_unix_nano};
             {#{start_time_unix_nano := PFstart_time_unix_nano}, _} -> S2#{start_time_unix_nano => PFstart_time_unix_nano};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {_, #{time_unix_nano := NFtime_unix_nano}} -> S3#{time_unix_nano => NFtime_unix_nano};
             {#{time_unix_nano := PFtime_unix_nano}, _} -> S3#{time_unix_nano => PFtime_unix_nano};
             _ -> S3
         end,
    S5 = case {PMsg, NMsg} of
             {_, #{count := NFcount}} -> S4#{count => NFcount};
             {#{count := PFcount}, _} -> S4#{count => PFcount};
             _ -> S4
         end,
    S6 = case {PMsg, NMsg} of
             {_, #{sum := NFsum}} -> S5#{sum => NFsum};
             {#{sum := PFsum}, _} -> S5#{sum => PFsum};
             _ -> S5
         end,
    S7 = case {PMsg, NMsg} of
             {_, #{scale := NFscale}} -> S6#{scale => NFscale};
             {#{scale := PFscale}, _} -> S6#{scale => PFscale};
             _ -> S6
         end,
    S8 = case {PMsg, NMsg} of
             {_, #{zero_count := NFzero_count}} -> S7#{zero_count => NFzero_count};
             {#{zero_count := PFzero_count}, _} -> S7#{zero_count => PFzero_count};
             _ -> S7
         end,
    S9 = case {PMsg, NMsg} of
             {#{positive := PFpositive}, #{positive := NFpositive}} -> S8#{positive => merge_msg_buckets(PFpositive, NFpositive, TrUserData)};
             {_, #{positive := NFpositive}} -> S8#{positive => NFpositive};
             {#{positive := PFpositive}, _} -> S8#{positive => PFpositive};
             {_, _} -> S8
         end,
    S10 = case {PMsg, NMsg} of
              {#{negative := PFnegative}, #{negative := NFnegative}} -> S9#{negative => merge_msg_buckets(PFnegative, NFnegative, TrUserData)};
              {_, #{negative := NFnegative}} -> S9#{negative => NFnegative};
              {#{negative := PFnegative}, _} -> S9#{negative => PFnegative};
              {_, _} -> S9
          end,
    S11 = case {PMsg, NMsg} of
              {_, #{flags := NFflags}} -> S10#{flags => NFflags};
              {#{flags := PFflags}, _} -> S10#{flags => PFflags};
              _ -> S10
          end,
    S12 = case {PMsg, NMsg} of
              {#{exemplars := PFexemplars}, #{exemplars := NFexemplars}} -> S11#{exemplars => 'erlang_++'(PFexemplars, NFexemplars, TrUserData)};
              {_, #{exemplars := NFexemplars}} -> S11#{exemplars => NFexemplars};
              {#{exemplars := PFexemplars}, _} -> S11#{exemplars => PFexemplars};
              {_, _} -> S11
          end,
    S13 = case {PMsg, NMsg} of
              {_, #{min := NFmin}} -> S12#{min => NFmin};
              {#{min := PFmin}, _} -> S12#{min => PFmin};
              _ -> S12
          end,
    case {PMsg, NMsg} of
        {_, #{max := NFmax}} -> S13#{max => NFmax};
        {#{max := PFmax}, _} -> S13#{max => PFmax};
        _ -> S13
    end.

-compile({nowarn_unused_function,merge_msg_value_at_quantile/3}).
merge_msg_value_at_quantile(PMsg, NMsg, _) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{quantile := NFquantile}} -> S1#{quantile => NFquantile};
             {#{quantile := PFquantile}, _} -> S1#{quantile => PFquantile};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{value := NFvalue}} -> S2#{value => NFvalue};
        {#{value := PFvalue}, _} -> S2#{value => PFvalue};
        _ -> S2
    end.

-compile({nowarn_unused_function,merge_msg_summary_data_point/3}).
merge_msg_summary_data_point(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S1#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S1#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S1#{attributes => PFattributes};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{start_time_unix_nano := NFstart_time_unix_nano}} -> S2#{start_time_unix_nano => NFstart_time_unix_nano};
             {#{start_time_unix_nano := PFstart_time_unix_nano}, _} -> S2#{start_time_unix_nano => PFstart_time_unix_nano};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {_, #{time_unix_nano := NFtime_unix_nano}} -> S3#{time_unix_nano => NFtime_unix_nano};
             {#{time_unix_nano := PFtime_unix_nano}, _} -> S3#{time_unix_nano => PFtime_unix_nano};
             _ -> S3
         end,
    S5 = case {PMsg, NMsg} of
             {_, #{count := NFcount}} -> S4#{count => NFcount};
             {#{count := PFcount}, _} -> S4#{count => PFcount};
             _ -> S4
         end,
    S6 = case {PMsg, NMsg} of
             {_, #{sum := NFsum}} -> S5#{sum => NFsum};
             {#{sum := PFsum}, _} -> S5#{sum => PFsum};
             _ -> S5
         end,
    S7 = case {PMsg, NMsg} of
             {#{quantile_values := PFquantile_values}, #{quantile_values := NFquantile_values}} -> S6#{quantile_values => 'erlang_++'(PFquantile_values, NFquantile_values, TrUserData)};
             {_, #{quantile_values := NFquantile_values}} -> S6#{quantile_values => NFquantile_values};
             {#{quantile_values := PFquantile_values}, _} -> S6#{quantile_values => PFquantile_values};
             {_, _} -> S6
         end,
    case {PMsg, NMsg} of
        {_, #{flags := NFflags}} -> S7#{flags => NFflags};
        {#{flags := PFflags}, _} -> S7#{flags => PFflags};
        _ -> S7
    end.

-compile({nowarn_unused_function,merge_msg_exemplar/3}).
merge_msg_exemplar(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{filtered_attributes := PFfiltered_attributes}, #{filtered_attributes := NFfiltered_attributes}} -> S1#{filtered_attributes => 'erlang_++'(PFfiltered_attributes, NFfiltered_attributes, TrUserData)};
             {_, #{filtered_attributes := NFfiltered_attributes}} -> S1#{filtered_attributes => NFfiltered_attributes};
             {#{filtered_attributes := PFfiltered_attributes}, _} -> S1#{filtered_attributes => PFfiltered_attributes};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{time_unix_nano := NFtime_unix_nano}} -> S2#{time_unix_nano => NFtime_unix_nano};
             {#{time_unix_nano := PFtime_unix_nano}, _} -> S2#{time_unix_nano => PFtime_unix_nano};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {_, #{value := NFvalue}} -> S3#{value => NFvalue};
             {#{value := PFvalue}, _} -> S3#{value => PFvalue};
             _ -> S3
         end,
    S5 = case {PMsg, NMsg} of
             {_, #{span_id := NFspan_id}} -> S4#{span_id => NFspan_id};
             {#{span_id := PFspan_id}, _} -> S4#{span_id => PFspan_id};
             _ -> S4
         end,
    case {PMsg, NMsg} of
        {_, #{trace_id := NFtrace_id}} -> S5#{trace_id => NFtrace_id};
        {#{trace_id := PFtrace_id}, _} -> S5#{trace_id => PFtrace_id};
        _ -> S5
    end.

-compile({nowarn_unused_function,merge_msg_any_value/3}).
merge_msg_any_value(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{value := {array_value, OPFvalue}}, #{value := {array_value, ONFvalue}}} -> S1#{value => {array_value, merge_msg_array_value(OPFvalue, ONFvalue, TrUserData)}};
        {#{value := {kvlist_value, OPFvalue}}, #{value := {kvlist_value, ONFvalue}}} -> S1#{value => {kvlist_value, merge_msg_key_value_list(OPFvalue, ONFvalue, TrUserData)}};
        {_, #{value := NFvalue}} -> S1#{value => NFvalue};
        {#{value := PFvalue}, _} -> S1#{value => PFvalue};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_array_value/3}).
merge_msg_array_value(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{values := PFvalues}, #{values := NFvalues}} -> S1#{values => 'erlang_++'(PFvalues, NFvalues, TrUserData)};
        {_, #{values := NFvalues}} -> S1#{values => NFvalues};
        {#{values := PFvalues}, _} -> S1#{values => PFvalues};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_key_value_list/3}).
merge_msg_key_value_list(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{values := PFvalues}, #{values := NFvalues}} -> S1#{values => 'erlang_++'(PFvalues, NFvalues, TrUserData)};
        {_, #{values := NFvalues}} -> S1#{values => NFvalues};
        {#{values := PFvalues}, _} -> S1#{values => PFvalues};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_key_value/3}).
merge_msg_key_value(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{key := NFkey}} -> S1#{key => NFkey};
             {#{key := PFkey}, _} -> S1#{key => PFkey};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {#{value := PFvalue}, #{value := NFvalue}} -> S2#{value => merge_msg_any_value(PFvalue, NFvalue, TrUserData)};
        {_, #{value := NFvalue}} -> S2#{value => NFvalue};
        {#{value := PFvalue}, _} -> S2#{value => PFvalue};
        {_, _} -> S2
    end.

-compile({nowarn_unused_function,merge_msg_instrumentation_scope/3}).
merge_msg_instrumentation_scope(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{name := NFname}} -> S1#{name => NFname};
             {#{name := PFname}, _} -> S1#{name => PFname};
             _ -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{version := NFversion}} -> S2#{version => NFversion};
             {#{version := PFversion}, _} -> S2#{version => PFversion};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S3#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S3#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S3#{attributes => PFattributes};
             {_, _} -> S3
         end,
    case {PMsg, NMsg} of
        {_, #{dropped_attributes_count := NFdropped_attributes_count}} -> S4#{dropped_attributes_count => NFdropped_attributes_count};
        {#{dropped_attributes_count := PFdropped_attributes_count}, _} -> S4#{dropped_attributes_count => PFdropped_attributes_count};
        _ -> S4
    end.

-compile({nowarn_unused_function,merge_msg_resource/3}).
merge_msg_resource(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S1#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S1#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S1#{attributes => PFattributes};
             {_, _} -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{dropped_attributes_count := NFdropped_attributes_count}} -> S2#{dropped_attributes_count => NFdropped_attributes_count};
        {#{dropped_attributes_count := PFdropped_attributes_count}, _} -> S2#{dropped_attributes_count => PFdropped_attributes_count};
        _ -> S2
    end.


verify_msg(Msg, MsgName) when is_atom(MsgName) -> verify_msg(Msg, MsgName, []).

verify_msg(Msg, MsgName, Opts) ->
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        export_metrics_service_request -> v_msg_export_metrics_service_request(Msg, [MsgName], TrUserData);
        export_metrics_service_response -> v_msg_export_metrics_service_response(Msg, [MsgName], TrUserData);
        export_metrics_partial_success -> v_msg_export_metrics_partial_success(Msg, [MsgName], TrUserData);
        metrics_data -> v_msg_metrics_data(Msg, [MsgName], TrUserData);
        resource_metrics -> v_msg_resource_metrics(Msg, [MsgName], TrUserData);
        scope_metrics -> v_msg_scope_metrics(Msg, [MsgName], TrUserData);
        metric -> v_msg_metric(Msg, [MsgName], TrUserData);
        gauge -> v_msg_gauge(Msg, [MsgName], TrUserData);
        sum -> v_msg_sum(Msg, [MsgName], TrUserData);
        histogram -> v_msg_histogram(Msg, [MsgName], TrUserData);
        exponential_histogram -> v_msg_exponential_histogram(Msg, [MsgName], TrUserData);
        summary -> v_msg_summary(Msg, [MsgName], TrUserData);
        number_data_point -> v_msg_number_data_point(Msg, [MsgName], TrUserData);
        histogram_data_point -> v_msg_histogram_data_point(Msg, [MsgName], TrUserData);
        buckets -> v_msg_buckets(Msg, [MsgName], TrUserData);
        exponential_histogram_data_point -> v_msg_exponential_histogram_data_point(Msg, [MsgName], TrUserData);
        value_at_quantile -> v_msg_value_at_quantile(Msg, [MsgName], TrUserData);
        summary_data_point -> v_msg_summary_data_point(Msg, [MsgName], TrUserData);
        exemplar -> v_msg_exemplar(Msg, [MsgName], TrUserData);
        any_value -> v_msg_any_value(Msg, [MsgName], TrUserData);
        array_value -> v_msg_array_value(Msg, [MsgName], TrUserData);
        key_value_list -> v_msg_key_value_list(Msg, [MsgName], TrUserData);
        key_value -> v_msg_key_value(Msg, [MsgName], TrUserData);
        instrumentation_scope -> v_msg_instrumentation_scope(Msg, [MsgName], TrUserData);
        resource -> v_msg_resource(Msg, [MsgName], TrUserData);
        _ -> mk_type_error(not_a_known_message, Msg, [])
    end.


-compile({nowarn_unused_function,v_msg_export_metrics_service_request/3}).
-dialyzer({nowarn_function,v_msg_export_metrics_service_request/3}).
v_msg_export_metrics_service_request(#{} = M, Path, TrUserData) ->
    case M of
        #{resource_metrics := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_resource_metrics(Elem, [resource_metrics | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, resource_metrics}}, F1, [resource_metrics | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (resource_metrics) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_export_metrics_service_request(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), export_metrics_service_request}, M, Path);
v_msg_export_metrics_service_request(X, Path, _TrUserData) -> mk_type_error({expected_msg, export_metrics_service_request}, X, Path).

-compile({nowarn_unused_function,v_msg_export_metrics_service_response/3}).
-dialyzer({nowarn_function,v_msg_export_metrics_service_response/3}).
v_msg_export_metrics_service_response(#{} = M, Path, TrUserData) ->
    case M of
        #{partial_success := F1} -> v_msg_export_metrics_partial_success(F1, [partial_success | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (partial_success) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_export_metrics_service_response(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), export_metrics_service_response}, M, Path);
v_msg_export_metrics_service_response(X, Path, _TrUserData) -> mk_type_error({expected_msg, export_metrics_service_response}, X, Path).

-compile({nowarn_unused_function,v_msg_export_metrics_partial_success/3}).
-dialyzer({nowarn_function,v_msg_export_metrics_partial_success/3}).
v_msg_export_metrics_partial_success(#{} = M, Path, TrUserData) ->
    case M of
        #{rejected_data_points := F1} -> v_type_int64(F1, [rejected_data_points | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{error_message := F2} -> v_type_string(F2, [error_message | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (rejected_data_points) -> ok;
                      (error_message) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_export_metrics_partial_success(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), export_metrics_partial_success}, M, Path);
v_msg_export_metrics_partial_success(X, Path, _TrUserData) -> mk_type_error({expected_msg, export_metrics_partial_success}, X, Path).

-compile({nowarn_unused_function,v_msg_metrics_data/3}).
-dialyzer({nowarn_function,v_msg_metrics_data/3}).
v_msg_metrics_data(#{} = M, Path, TrUserData) ->
    case M of
        #{resource_metrics := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_resource_metrics(Elem, [resource_metrics | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, resource_metrics}}, F1, [resource_metrics | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (resource_metrics) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_metrics_data(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), metrics_data}, M, Path);
v_msg_metrics_data(X, Path, _TrUserData) -> mk_type_error({expected_msg, metrics_data}, X, Path).

-compile({nowarn_unused_function,v_msg_resource_metrics/3}).
-dialyzer({nowarn_function,v_msg_resource_metrics/3}).
v_msg_resource_metrics(#{} = M, Path, TrUserData) ->
    case M of
        #{resource := F1} -> v_msg_resource(F1, [resource | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{scope_metrics := F2} ->
            if is_list(F2) ->
                   _ = [v_msg_scope_metrics(Elem, [scope_metrics | Path], TrUserData) || Elem <- F2],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, scope_metrics}}, F2, [scope_metrics | Path])
            end;
        _ -> ok
    end,
    case M of
        #{schema_url := F3} -> v_type_string(F3, [schema_url | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (resource) -> ok;
                      (scope_metrics) -> ok;
                      (schema_url) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_resource_metrics(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), resource_metrics}, M, Path);
v_msg_resource_metrics(X, Path, _TrUserData) -> mk_type_error({expected_msg, resource_metrics}, X, Path).

-compile({nowarn_unused_function,v_msg_scope_metrics/3}).
-dialyzer({nowarn_function,v_msg_scope_metrics/3}).
v_msg_scope_metrics(#{} = M, Path, TrUserData) ->
    case M of
        #{scope := F1} -> v_msg_instrumentation_scope(F1, [scope | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{metrics := F2} ->
            if is_list(F2) ->
                   _ = [v_msg_metric(Elem, [metrics | Path], TrUserData) || Elem <- F2],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, metric}}, F2, [metrics | Path])
            end;
        _ -> ok
    end,
    case M of
        #{schema_url := F3} -> v_type_string(F3, [schema_url | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (scope) -> ok;
                      (metrics) -> ok;
                      (schema_url) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_scope_metrics(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), scope_metrics}, M, Path);
v_msg_scope_metrics(X, Path, _TrUserData) -> mk_type_error({expected_msg, scope_metrics}, X, Path).

-compile({nowarn_unused_function,v_msg_metric/3}).
-dialyzer({nowarn_function,v_msg_metric/3}).
v_msg_metric(#{} = M, Path, TrUserData) ->
    case M of
        #{name := F1} -> v_type_string(F1, [name | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{description := F2} -> v_type_string(F2, [description | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{unit := F3} -> v_type_string(F3, [unit | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{data := {gauge, OF4}} -> v_msg_gauge(OF4, [gauge, data | Path], TrUserData);
        #{data := {sum, OF4}} -> v_msg_sum(OF4, [sum, data | Path], TrUserData);
        #{data := {histogram, OF4}} -> v_msg_histogram(OF4, [histogram, data | Path], TrUserData);
        #{data := {exponential_histogram, OF4}} -> v_msg_exponential_histogram(OF4, [exponential_histogram, data | Path], TrUserData);
        #{data := {summary, OF4}} -> v_msg_summary(OF4, [summary, data | Path], TrUserData);
        #{data := F4} -> mk_type_error(invalid_oneof, F4, [data | Path]);
        _ -> ok
    end,
    lists:foreach(fun (name) -> ok;
                      (description) -> ok;
                      (unit) -> ok;
                      (data) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_metric(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), metric}, M, Path);
v_msg_metric(X, Path, _TrUserData) -> mk_type_error({expected_msg, metric}, X, Path).

-compile({nowarn_unused_function,v_msg_gauge/3}).
-dialyzer({nowarn_function,v_msg_gauge/3}).
v_msg_gauge(#{} = M, Path, TrUserData) ->
    case M of
        #{data_points := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_number_data_point(Elem, [data_points | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, number_data_point}}, F1, [data_points | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (data_points) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_gauge(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), gauge}, M, Path);
v_msg_gauge(X, Path, _TrUserData) -> mk_type_error({expected_msg, gauge}, X, Path).

-compile({nowarn_unused_function,v_msg_sum/3}).
-dialyzer({nowarn_function,v_msg_sum/3}).
v_msg_sum(#{} = M, Path, TrUserData) ->
    case M of
        #{data_points := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_number_data_point(Elem, [data_points | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, number_data_point}}, F1, [data_points | Path])
            end;
        _ -> ok
    end,
    case M of
        #{aggregation_temporality := F2} -> 'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(F2, [aggregation_temporality | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{is_monotonic := F3} -> v_type_bool(F3, [is_monotonic | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (data_points) -> ok;
                      (aggregation_temporality) -> ok;
                      (is_monotonic) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_sum(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), sum}, M, Path);
v_msg_sum(X, Path, _TrUserData) -> mk_type_error({expected_msg, sum}, X, Path).

-compile({nowarn_unused_function,v_msg_histogram/3}).
-dialyzer({nowarn_function,v_msg_histogram/3}).
v_msg_histogram(#{} = M, Path, TrUserData) ->
    case M of
        #{data_points := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_histogram_data_point(Elem, [data_points | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, histogram_data_point}}, F1, [data_points | Path])
            end;
        _ -> ok
    end,
    case M of
        #{aggregation_temporality := F2} -> 'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(F2, [aggregation_temporality | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (data_points) -> ok;
                      (aggregation_temporality) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_histogram(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), histogram}, M, Path);
v_msg_histogram(X, Path, _TrUserData) -> mk_type_error({expected_msg, histogram}, X, Path).

-compile({nowarn_unused_function,v_msg_exponential_histogram/3}).
-dialyzer({nowarn_function,v_msg_exponential_histogram/3}).
v_msg_exponential_histogram(#{} = M, Path, TrUserData) ->
    case M of
        #{data_points := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_exponential_histogram_data_point(Elem, [data_points | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, exponential_histogram_data_point}}, F1, [data_points | Path])
            end;
        _ -> ok
    end,
    case M of
        #{aggregation_temporality := F2} -> 'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(F2, [aggregation_temporality | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (data_points) -> ok;
                      (aggregation_temporality) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_exponential_histogram(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), exponential_histogram}, M, Path);
v_msg_exponential_histogram(X, Path, _TrUserData) -> mk_type_error({expected_msg, exponential_histogram}, X, Path).

-compile({nowarn_unused_function,v_msg_summary/3}).
-dialyzer({nowarn_function,v_msg_summary/3}).
v_msg_summary(#{} = M, Path, TrUserData) ->
    case M of
        #{data_points := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_summary_data_point(Elem, [data_points | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, summary_data_point}}, F1, [data_points | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (data_points) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_summary(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), summary}, M, Path);
v_msg_summary(X, Path, _TrUserData) -> mk_type_error({expected_msg, summary}, X, Path).

-compile({nowarn_unused_function,v_msg_number_data_point/3}).
-dialyzer({nowarn_function,v_msg_number_data_point/3}).
v_msg_number_data_point(#{} = M, Path, TrUserData) ->
    case M of
        #{attributes := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{start_time_unix_nano := F2} -> v_type_fixed64(F2, [start_time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{time_unix_nano := F3} -> v_type_fixed64(F3, [time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{value := {as_double, OF4}} -> v_type_double(OF4, [as_double, value | Path], TrUserData);
        #{value := {as_int, OF4}} -> v_type_sfixed64(OF4, [as_int, value | Path], TrUserData);
        #{value := F4} -> mk_type_error(invalid_oneof, F4, [value | Path]);
        _ -> ok
    end,
    case M of
        #{exemplars := F5} ->
            if is_list(F5) ->
                   _ = [v_msg_exemplar(Elem, [exemplars | Path], TrUserData) || Elem <- F5],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, exemplar}}, F5, [exemplars | Path])
            end;
        _ -> ok
    end,
    case M of
        #{flags := F6} -> v_type_uint32(F6, [flags | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (attributes) -> ok;
                      (start_time_unix_nano) -> ok;
                      (time_unix_nano) -> ok;
                      (value) -> ok;
                      (exemplars) -> ok;
                      (flags) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_number_data_point(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), number_data_point}, M, Path);
v_msg_number_data_point(X, Path, _TrUserData) -> mk_type_error({expected_msg, number_data_point}, X, Path).

-compile({nowarn_unused_function,v_msg_histogram_data_point/3}).
-dialyzer({nowarn_function,v_msg_histogram_data_point/3}).
v_msg_histogram_data_point(#{} = M, Path, TrUserData) ->
    case M of
        #{attributes := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{start_time_unix_nano := F2} -> v_type_fixed64(F2, [start_time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{time_unix_nano := F3} -> v_type_fixed64(F3, [time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{count := F4} -> v_type_fixed64(F4, [count | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{sum := F5} -> v_type_double(F5, [sum | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{bucket_counts := F6} ->
            if is_list(F6) ->
                   _ = [v_type_fixed64(Elem, [bucket_counts | Path], TrUserData) || Elem <- F6],
                   ok;
               true -> mk_type_error({invalid_list_of, fixed64}, F6, [bucket_counts | Path])
            end;
        _ -> ok
    end,
    case M of
        #{explicit_bounds := F7} ->
            if is_list(F7) ->
                   _ = [v_type_double(Elem, [explicit_bounds | Path], TrUserData) || Elem <- F7],
                   ok;
               true -> mk_type_error({invalid_list_of, double}, F7, [explicit_bounds | Path])
            end;
        _ -> ok
    end,
    case M of
        #{exemplars := F8} ->
            if is_list(F8) ->
                   _ = [v_msg_exemplar(Elem, [exemplars | Path], TrUserData) || Elem <- F8],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, exemplar}}, F8, [exemplars | Path])
            end;
        _ -> ok
    end,
    case M of
        #{flags := F9} -> v_type_uint32(F9, [flags | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{min := F10} -> v_type_double(F10, [min | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{max := F11} -> v_type_double(F11, [max | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (attributes) -> ok;
                      (start_time_unix_nano) -> ok;
                      (time_unix_nano) -> ok;
                      (count) -> ok;
                      (sum) -> ok;
                      (bucket_counts) -> ok;
                      (explicit_bounds) -> ok;
                      (exemplars) -> ok;
                      (flags) -> ok;
                      (min) -> ok;
                      (max) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_histogram_data_point(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), histogram_data_point}, M, Path);
v_msg_histogram_data_point(X, Path, _TrUserData) -> mk_type_error({expected_msg, histogram_data_point}, X, Path).

-compile({nowarn_unused_function,v_msg_buckets/3}).
-dialyzer({nowarn_function,v_msg_buckets/3}).
v_msg_buckets(#{} = M, Path, TrUserData) ->
    case M of
        #{offset := F1} -> v_type_sint32(F1, [offset | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{bucket_counts := F2} ->
            if is_list(F2) ->
                   _ = [v_type_uint64(Elem, [bucket_counts | Path], TrUserData) || Elem <- F2],
                   ok;
               true -> mk_type_error({invalid_list_of, uint64}, F2, [bucket_counts | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (offset) -> ok;
                      (bucket_counts) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_buckets(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), buckets}, M, Path);
v_msg_buckets(X, Path, _TrUserData) -> mk_type_error({expected_msg, buckets}, X, Path).

-compile({nowarn_unused_function,v_msg_exponential_histogram_data_point/3}).
-dialyzer({nowarn_function,v_msg_exponential_histogram_data_point/3}).
v_msg_exponential_histogram_data_point(#{} = M, Path, TrUserData) ->
    case M of
        #{attributes := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{start_time_unix_nano := F2} -> v_type_fixed64(F2, [start_time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{time_unix_nano := F3} -> v_type_fixed64(F3, [time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{count := F4} -> v_type_fixed64(F4, [count | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{sum := F5} -> v_type_double(F5, [sum | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{scale := F6} -> v_type_sint32(F6, [scale | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{zero_count := F7} -> v_type_fixed64(F7, [zero_count | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{positive := F8} -> v_msg_buckets(F8, [positive | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{negative := F9} -> v_msg_buckets(F9, [negative | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{flags := F10} -> v_type_uint32(F10, [flags | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{exemplars := F11} ->
            if is_list(F11) ->
                   _ = [v_msg_exemplar(Elem, [exemplars | Path], TrUserData) || Elem <- F11],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, exemplar}}, F11, [exemplars | Path])
            end;
        _ -> ok
    end,
    case M of
        #{min := F12} -> v_type_double(F12, [min | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{max := F13} -> v_type_double(F13, [max | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (attributes) -> ok;
                      (start_time_unix_nano) -> ok;
                      (time_unix_nano) -> ok;
                      (count) -> ok;
                      (sum) -> ok;
                      (scale) -> ok;
                      (zero_count) -> ok;
                      (positive) -> ok;
                      (negative) -> ok;
                      (flags) -> ok;
                      (exemplars) -> ok;
                      (min) -> ok;
                      (max) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_exponential_histogram_data_point(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), exponential_histogram_data_point}, M, Path);
v_msg_exponential_histogram_data_point(X, Path, _TrUserData) -> mk_type_error({expected_msg, exponential_histogram_data_point}, X, Path).

-compile({nowarn_unused_function,v_msg_value_at_quantile/3}).
-dialyzer({nowarn_function,v_msg_value_at_quantile/3}).
v_msg_value_at_quantile(#{} = M, Path, TrUserData) ->
    case M of
        #{quantile := F1} -> v_type_double(F1, [quantile | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{value := F2} -> v_type_double(F2, [value | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (quantile) -> ok;
                      (value) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_value_at_quantile(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), value_at_quantile}, M, Path);
v_msg_value_at_quantile(X, Path, _TrUserData) -> mk_type_error({expected_msg, value_at_quantile}, X, Path).

-compile({nowarn_unused_function,v_msg_summary_data_point/3}).
-dialyzer({nowarn_function,v_msg_summary_data_point/3}).
v_msg_summary_data_point(#{} = M, Path, TrUserData) ->
    case M of
        #{attributes := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{start_time_unix_nano := F2} -> v_type_fixed64(F2, [start_time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{time_unix_nano := F3} -> v_type_fixed64(F3, [time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{count := F4} -> v_type_fixed64(F4, [count | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{sum := F5} -> v_type_double(F5, [sum | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{quantile_values := F6} ->
            if is_list(F6) ->
                   _ = [v_msg_value_at_quantile(Elem, [quantile_values | Path], TrUserData) || Elem <- F6],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, value_at_quantile}}, F6, [quantile_values | Path])
            end;
        _ -> ok
    end,
    case M of
        #{flags := F7} -> v_type_uint32(F7, [flags | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (attributes) -> ok;
                      (start_time_unix_nano) -> ok;
                      (time_unix_nano) -> ok;
                      (count) -> ok;
                      (sum) -> ok;
                      (quantile_values) -> ok;
                      (flags) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_summary_data_point(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), summary_data_point}, M, Path);
v_msg_summary_data_point(X, Path, _TrUserData) -> mk_type_error({expected_msg, summary_data_point}, X, Path).

-compile({nowarn_unused_function,v_msg_exemplar/3}).
-dialyzer({nowarn_function,v_msg_exemplar/3}).
v_msg_exemplar(#{} = M, Path, TrUserData) ->
    case M of
        #{filtered_attributes := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [filtered_attributes | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [filtered_attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{time_unix_nano := F2} -> v_type_fixed64(F2, [time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{value := {as_double, OF3}} -> v_type_double(OF3, [as_double, value | Path], TrUserData);
        #{value := {as_int, OF3}} -> v_type_sfixed64(OF3, [as_int, value | Path], TrUserData);
        #{value := F3} -> mk_type_error(invalid_oneof, F3, [value | Path]);
        _ -> ok
    end,
    case M of
        #{span_id := F4} -> v_type_bytes(F4, [span_id | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{trace_id := F5} -> v_type_bytes(F5, [trace_id | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (filtered_attributes) -> ok;
                      (time_unix_nano) -> ok;
                      (value) -> ok;
                      (span_id) -> ok;
                      (trace_id) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_exemplar(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), exemplar}, M, Path);
v_msg_exemplar(X, Path, _TrUserData) -> mk_type_error({expected_msg, exemplar}, X, Path).

-compile({nowarn_unused_function,v_msg_any_value/3}).
-dialyzer({nowarn_function,v_msg_any_value/3}).
v_msg_any_value(#{} = M, Path, TrUserData) ->
    case M of
        #{value := {string_value, OF1}} -> v_type_string(OF1, [string_value, value | Path], TrUserData);
        #{value := {bool_value, OF1}} -> v_type_bool(OF1, [bool_value, value | Path], TrUserData);
        #{value := {int_value, OF1}} -> v_type_int64(OF1, [int_value, value | Path], TrUserData);
        #{value := {double_value, OF1}} -> v_type_double(OF1, [double_value, value | Path], TrUserData);
        #{value := {array_value, OF1}} -> v_msg_array_value(OF1, [array_value, value | Path], TrUserData);
        #{value := {kvlist_value, OF1}} -> v_msg_key_value_list(OF1, [kvlist_value, value | Path], TrUserData);
        #{value := {bytes_value, OF1}} -> v_type_bytes(OF1, [bytes_value, value | Path], TrUserData);
        #{value := F1} -> mk_type_error(invalid_oneof, F1, [value | Path]);
        _ -> ok
    end,
    lists:foreach(fun (value) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_any_value(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), any_value}, M, Path);
v_msg_any_value(X, Path, _TrUserData) -> mk_type_error({expected_msg, any_value}, X, Path).

-compile({nowarn_unused_function,v_msg_array_value/3}).
-dialyzer({nowarn_function,v_msg_array_value/3}).
v_msg_array_value(#{} = M, Path, TrUserData) ->
    case M of
        #{values := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_any_value(Elem, [values | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, any_value}}, F1, [values | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (values) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_array_value(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), array_value}, M, Path);
v_msg_array_value(X, Path, _TrUserData) -> mk_type_error({expected_msg, array_value}, X, Path).

-compile({nowarn_unused_function,v_msg_key_value_list/3}).
-dialyzer({nowarn_function,v_msg_key_value_list/3}).
v_msg_key_value_list(#{} = M, Path, TrUserData) ->
    case M of
        #{values := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [values | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [values | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (values) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_key_value_list(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), key_value_list}, M, Path);
v_msg_key_value_list(X, Path, _TrUserData) -> mk_type_error({expected_msg, key_value_list}, X, Path).

-compile({nowarn_unused_function,v_msg_key_value/3}).
-dialyzer({nowarn_function,v_msg_key_value/3}).
v_msg_key_value(#{} = M, Path, TrUserData) ->
    case M of
        #{key := F1} -> v_type_string(F1, [key | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{value := F2} -> v_msg_any_value(F2, [value | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (key) -> ok;
                      (value) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_key_value(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), key_value}, M, Path);
v_msg_key_value(X, Path, _TrUserData) -> mk_type_error({expected_msg, key_value}, X, Path).

-compile({nowarn_unused_function,v_msg_instrumentation_scope/3}).
-dialyzer({nowarn_function,v_msg_instrumentation_scope/3}).
v_msg_instrumentation_scope(#{} = M, Path, TrUserData) ->
    case M of
        #{name := F1} -> v_type_string(F1, [name | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{version := F2} -> v_type_string(F2, [version | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{attributes := F3} ->
            if is_list(F3) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F3],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F3, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{dropped_attributes_count := F4} -> v_type_uint32(F4, [dropped_attributes_count | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (name) -> ok;
                      (version) -> ok;
                      (attributes) -> ok;
                      (dropped_attributes_count) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_instrumentation_scope(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), instrumentation_scope}, M, Path);
v_msg_instrumentation_scope(X, Path, _TrUserData) -> mk_type_error({expected_msg, instrumentation_scope}, X, Path).

-compile({nowarn_unused_function,v_msg_resource/3}).
-dialyzer({nowarn_function,v_msg_resource/3}).
v_msg_resource(#{} = M, Path, TrUserData) ->
    case M of
        #{attributes := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{dropped_attributes_count := F2} -> v_type_uint32(F2, [dropped_attributes_count | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (attributes) -> ok;
                      (dropped_attributes_count) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_resource(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), resource}, M, Path);
v_msg_resource(X, Path, _TrUserData) -> mk_type_error({expected_msg, resource}, X, Path).

-compile({nowarn_unused_function,'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'/3}).
-dialyzer({nowarn_function,'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'/3}).
'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_UNSPECIFIED', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_DELTA', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_CUMULATIVE', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(V, _Path, _TrUserData) when -2147483648 =< V, V =< 2147483647, is_integer(V) -> ok;
'v_enum_opentelemetry.proto.metrics.v1.AggregationTemporality'(X, Path, _TrUserData) -> mk_type_error({invalid_enum, 'opentelemetry.proto.metrics.v1.AggregationTemporality'}, X, Path).

-compile({nowarn_unused_function,v_type_sint32/3}).
-dialyzer({nowarn_function,v_type_sint32/3}).
v_type_sint32(N, _Path, _TrUserData) when -2147483648 =< N, N =< 2147483647 -> ok;
v_type_sint32(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, sint32, signed, 32}, N, Path);
v_type_sint32(X, Path, _TrUserData) -> mk_type_error({bad_integer, sint32, signed, 32}, X, Path).

-compile({nowarn_unused_function,v_type_int64/3}).
-dialyzer({nowarn_function,v_type_int64/3}).
v_type_int64(N, _Path, _TrUserData) when -9223372036854775808 =< N, N =< 9223372036854775807 -> ok;
v_type_int64(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, int64, signed, 64}, N, Path);
v_type_int64(X, Path, _TrUserData) -> mk_type_error({bad_integer, int64, signed, 64}, X, Path).

-compile({nowarn_unused_function,v_type_uint32/3}).
-dialyzer({nowarn_function,v_type_uint32/3}).
v_type_uint32(N, _Path, _TrUserData) when 0 =< N, N =< 4294967295 -> ok;
v_type_uint32(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, uint32, unsigned, 32}, N, Path);
v_type_uint32(X, Path, _TrUserData) -> mk_type_error({bad_integer, uint32, unsigned, 32}, X, Path).

-compile({nowarn_unused_function,v_type_uint64/3}).
-dialyzer({nowarn_function,v_type_uint64/3}).
v_type_uint64(N, _Path, _TrUserData) when 0 =< N, N =< 18446744073709551615 -> ok;
v_type_uint64(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, uint64, unsigned, 64}, N, Path);
v_type_uint64(X, Path, _TrUserData) -> mk_type_error({bad_integer, uint64, unsigned, 64}, X, Path).

-compile({nowarn_unused_function,v_type_fixed64/3}).
-dialyzer({nowarn_function,v_type_fixed64/3}).
v_type_fixed64(N, _Path, _TrUserData) when 0 =< N, N =< 18446744073709551615 -> ok;
v_type_fixed64(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, fixed64, unsigned, 64}, N, Path);
v_type_fixed64(X, Path, _TrUserData) -> mk_type_error({bad_integer, fixed64, unsigned, 64}, X, Path).

-compile({nowarn_unused_function,v_type_sfixed64/3}).
-dialyzer({nowarn_function,v_type_sfixed64/3}).
v_type_sfixed64(N, _Path, _TrUserData) when -9223372036854775808 =< N, N =< 9223372036854775807 -> ok;
v_type_sfixed64(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, sfixed64, signed, 64}, N, Path);
v_type_sfixed64(X, Path, _TrUserData) -> mk_type_error({bad_integer, sfixed64, signed, 64}, X, Path).

-compile({nowarn_unused_function,v_type_bool/3}).
-dialyzer({nowarn_function,v_type_bool/3}).
v_type_bool(false, _Path, _TrUserData) -> ok;
v_type_bool(true, _Path, _TrUserData) -> ok;
v_type_bool(0, _Path, _TrUserData) -> ok;
v_type_bool(1, _Path, _TrUserData) -> ok;
v_type_bool(X, Path, _TrUserData) -> mk_type_error(bad_boolean_value, X, Path).

-compile({nowarn_unused_function,v_type_double/3}).
-dialyzer({nowarn_function,v_type_double/3}).
v_type_double(N, _Path, _TrUserData) when is_float(N) -> ok;
v_type_double(N, _Path, _TrUserData) when is_integer(N) -> ok;
v_type_double(infinity, _Path, _TrUserData) -> ok;
v_type_double('-infinity', _Path, _TrUserData) -> ok;
v_type_double(nan, _Path, _TrUserData) -> ok;
v_type_double(X, Path, _TrUserData) -> mk_type_error(bad_double_value, X, Path).

-compile({nowarn_unused_function,v_type_string/3}).
-dialyzer({nowarn_function,v_type_string/3}).
v_type_string(S, Path, _TrUserData) when is_list(S); is_binary(S) ->
    try unicode:characters_to_binary(S) of
        B when is_binary(B) -> ok;
        {error, _, _} -> mk_type_error(bad_unicode_string, S, Path)
    catch
        error:badarg -> mk_type_error(bad_unicode_string, S, Path)
    end;
v_type_string(X, Path, _TrUserData) -> mk_type_error(bad_unicode_string, X, Path).

-compile({nowarn_unused_function,v_type_bytes/3}).
-dialyzer({nowarn_function,v_type_bytes/3}).
v_type_bytes(B, _Path, _TrUserData) when is_binary(B) -> ok;
v_type_bytes(B, _Path, _TrUserData) when is_list(B) -> ok;
v_type_bytes(X, Path, _TrUserData) -> mk_type_error(bad_binary_value, X, Path).

-compile({nowarn_unused_function,mk_type_error/3}).
-spec mk_type_error(_, _, list()) -> no_return().
mk_type_error(Error, ValueSeen, Path) ->
    Path2 = prettify_path(Path),
    erlang:error({gpb_type_error, {Error, [{value, ValueSeen}, {path, Path2}]}}).


-compile({nowarn_unused_function,prettify_path/1}).
-dialyzer({nowarn_function,prettify_path/1}).
prettify_path([]) -> top_level;
prettify_path(PathR) -> lists:append(lists:join(".", lists:map(fun atom_to_list/1, lists:reverse(PathR)))).


-compile({nowarn_unused_function,id/2}).
-compile({inline,id/2}).
id(X, _TrUserData) -> X.

-compile({nowarn_unused_function,v_ok/3}).
-compile({inline,v_ok/3}).
v_ok(_Value, _Path, _TrUserData) -> ok.

-compile({nowarn_unused_function,m_overwrite/3}).
-compile({inline,m_overwrite/3}).
m_overwrite(_Prev, New, _TrUserData) -> New.

-compile({nowarn_unused_function,cons/3}).
-compile({inline,cons/3}).
cons(Elem, Acc, _TrUserData) -> [Elem | Acc].

-compile({nowarn_unused_function,lists_reverse/2}).
-compile({inline,lists_reverse/2}).
'lists_reverse'(L, _TrUserData) -> lists:reverse(L).
-compile({nowarn_unused_function,'erlang_++'/3}).
-compile({inline,'erlang_++'/3}).
'erlang_++'(A, B, _TrUserData) -> A ++ B.


get_msg_defs() ->
    [{{enum, 'opentelemetry.proto.metrics.v1.AggregationTemporality'}, [{'AGGREGATION_TEMPORALITY_UNSPECIFIED', 0, []}, {'AGGREGATION_TEMPORALITY_DELTA', 1, []}, {'AGGREGATION_TEMPORALITY_CUMULATIVE', 2, []}]},
     {{enum, 'opentelemetry.proto.metrics.v1.DataPointFlags'}, [{'FLAG_NONE', 0, []}, {'FLAG_NO_RECORDED_VALUE', 1, []}]},
     {{msg, export_metrics_service_request}, [#{name => resource_metrics, fnum => 1, rnum => 2, type => {msg, resource_metrics}, occurrence => repeated, opts => []}]},
     {{msg, export_metrics_service_response}, [#{name => partial_success, fnum => 1, rnum => 2, type => {msg, export_metrics_partial_success}, occurrence => defaulty, opts => []}]},
     {{msg, export_metrics_partial_success}, [#{name => rejected_data_points, fnum => 1, rnum => 2, type => int64, occurrence => defaulty, opts => []}, #{name => error_message, fnum => 2, rnum => 3, type => string, occurrence => defaulty, opts => []}]},
     {{msg, metrics_data}, [#{name => resource_metrics, fnum => 1, rnum => 2, type => {msg, resource_metrics}, occurrence => repeated, opts => []}]},
     {{msg, resource_metrics},
      [#{name => resource, fnum => 1, rnum => 2, type => {msg, resource}, occurrence => defaulty, opts => []},
       #{name => scope_metrics, fnum => 2, rnum => 3, type => {msg, scope_metrics}, occurrence => repeated, opts => []},
       #{name => schema_url, fnum => 3, rnum => 4, type => string, occurrence => defaulty, opts => []}]},
     {{msg, scope_metrics},
      [#{name => scope, fnum => 1, rnum => 2, type => {msg, instrumentation_scope}, occurrence => defaulty, opts => []},
       #{name => metrics, fnum => 2, rnum => 3, type => {msg, metric}, occurrence => repeated, opts => []},
       #{name => schema_url, fnum => 3, rnum => 4, type => string, occurrence => defaulty, opts => []}]},
     {{msg, metric},
      [#{name => name, fnum => 1, rnum => 2, type => string, occurrence => defaulty, opts => []},
       #{name => description, fnum => 2, rnum => 3, type => string, occurrence => defaulty, opts => []},
       #{name => unit, fnum => 3, rnum => 4, type => string, occurrence => defaulty, opts => []},
       #{name => data, rnum => 5,
         fields =>
             [#{name => gauge, fnum => 5, rnum => 5, type => {msg, gauge}, occurrence => optional, opts => []},
              #{name => sum, fnum => 7, rnum => 5, type => {msg, sum}, occurrence => optional, opts => []},
              #{name => histogram, fnum => 9, rnum => 5, type => {msg, histogram}, occurrence => optional, opts => []},
              #{name => exponential_histogram, fnum => 10, rnum => 5, type => {msg, exponential_histogram}, occurrence => optional, opts => []},
              #{name => summary, fnum => 11, rnum => 5, type => {msg, summary}, occurrence => optional, opts => []}],
         opts => []}]},
     {{msg, gauge}, [#{name => data_points, fnum => 1, rnum => 2, type => {msg, number_data_point}, occurrence => repeated, opts => []}]},
     {{msg, sum},
      [#{name => data_points, fnum => 1, rnum => 2, type => {msg, number_data_point}, occurrence => repeated, opts => []},
       #{name => aggregation_temporality, fnum => 2, rnum => 3, type => {enum, 'opentelemetry.proto.metrics.v1.AggregationTemporality'}, occurrence => defaulty, opts => []},
       #{name => is_monotonic, fnum => 3, rnum => 4, type => bool, occurrence => defaulty, opts => []}]},
     {{msg, histogram},
      [#{name => data_points, fnum => 1, rnum => 2, type => {msg, histogram_data_point}, occurrence => repeated, opts => []},
       #{name => aggregation_temporality, fnum => 2, rnum => 3, type => {enum, 'opentelemetry.proto.metrics.v1.AggregationTemporality'}, occurrence => defaulty, opts => []}]},
     {{msg, exponential_histogram},
      [#{name => data_points, fnum => 1, rnum => 2, type => {msg, exponential_histogram_data_point}, occurrence => repeated, opts => []},
       #{name => aggregation_temporality, fnum => 2, rnum => 3, type => {enum, 'opentelemetry.proto.metrics.v1.AggregationTemporality'}, occurrence => defaulty, opts => []}]},
     {{msg, summary}, [#{name => data_points, fnum => 1, rnum => 2, type => {msg, summary_data_point}, occurrence => repeated, opts => []}]},
     {{msg, number_data_point},
      [#{name => attributes, fnum => 7, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
       #{name => start_time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
       #{name => time_unix_nano, fnum => 3, rnum => 4, type => fixed64, occurrence => defaulty, opts => []},
       #{name => value, rnum => 5, fields => [#{name => as_double, fnum => 4, rnum => 5, type => double, occurrence => optional, opts => []}, #{name => as_int, fnum => 6, rnum => 5, type => sfixed64, occurrence => optional, opts => []}], opts => []},
       #{name => exemplars, fnum => 5, rnum => 6, type => {msg, exemplar}, occurrence => repeated, opts => []},
       #{name => flags, fnum => 8, rnum => 7, type => uint32, occurrence => defaulty, opts => []}]},
     {{msg, histogram_data_point},
      [#{name => attributes, fnum => 9, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
       #{name => start_time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
       #{name => time_unix_nano, fnum => 3, rnum => 4, type => fixed64, occurrence => defaulty, opts => []},
       #{name => count, fnum => 4, rnum => 5, type => fixed64, occurrence => defaulty, opts => []},
       #{name => sum, fnum => 5, rnum => 6, type => double, occurrence => optional, opts => []},
       #{name => bucket_counts, fnum => 6, rnum => 7, type => fixed64, occurrence => repeated, opts => [packed]},
       #{name => explicit_bounds, fnum => 7, rnum => 8, type => double, occurrence => repeated, opts => [packed]},
       #{name => exemplars, fnum => 8, rnum => 9, type => {msg, exemplar}, occurrence => repeated, opts => []},
       #{name => flags, fnum => 10, rnum => 10, type => uint32, occurrence => defaulty, opts => []},
       #{name => min, fnum => 11, rnum => 11, type => double, occurrence => optional, opts => []},
       #{name => max, fnum => 12, rnum => 12, type => double, occurrence => optional, opts => []}]},
     {{msg, buckets}, [#{name => offset, fnum => 1, rnum => 2, type => sint32, occurrence => defaulty, opts => []}, #{name => bucket_counts, fnum => 2, rnum => 3, type => uint64, occurrence => repeated, opts => [packed]}]},
     {{msg, exponential_histogram_data_point},
      [#{name => attributes, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
       #{name => start_time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
       #{name => time_unix_nano, fnum => 3, rnum => 4, type => fixed64, occurrence => defaulty, opts => []},
       #{name => count, fnum => 4, rnum => 5, type => fixed64, occurrence => defaulty, opts => []},
       #{name => sum, fnum => 5, rnum => 6, type => double, occurrence => optional, opts => []},
       #{name => scale, fnum => 6, rnum => 7, type => sint32, occurrence => defaulty, opts => []},
       #{name => zero_count, fnum => 7, rnum => 8, type => fixed64, occurrence => defaulty, opts => []},
       #{name => positive, fnum => 8, rnum => 9, type => {msg, buckets}, occurrence => defaulty, opts => []},
       #{name => negative, fnum => 9, rnum => 10, type => {msg, buckets}, occurrence => defaulty, opts => []},
       #{name => flags, fnum => 10, rnum => 11, type => uint32, occurrence => defaulty, opts => []},
       #{name => exemplars, fnum => 11, rnum => 12, type => {msg, exemplar}, occurrence => repeated, opts => []},
       #{name => min, fnum => 12, rnum => 13, type => double, occurrence => optional, opts => []},
       #{name => max, fnum => 13, rnum => 14, type => double, occurrence => optional, opts => []}]},
     {{msg, value_at_quantile}, [#{name => quantile, fnum => 1, rnum => 2, type => double, occurrence => defaulty, opts => []}, #{name => value, fnum => 2, rnum => 3, type => double, occurrence => defaulty, opts => []}]},
     {{msg, summary_data_point},
      [#{name => attributes, fnum => 7, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
       #{name => start_time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
       #{name => time_unix_nano, fnum => 3, rnum => 4, type => fixed64, occurrence => defaulty, opts => []},
       #{name => count, fnum => 4, rnum => 5, type => fixed64, occurrence => defaulty, opts => []},
       #{name => sum, fnum => 5, rnum => 6, type => double, occurrence => defaulty, opts => []},
       #{name => quantile_values, fnum => 6, rnum => 7, type => {msg, value_at_quantile}, occurrence => repeated, opts => []},
       #{name => flags, fnum => 8, rnum => 8, type => uint32, occurrence => defaulty, opts => []}]},
     {{msg, exemplar},
      [#{name => filtered_attributes, fnum => 7, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
       #{name => time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
       #{name => value, rnum => 4, fields => [#{name => as_double, fnum => 3, rnum => 4, type => double, occurrence => optional, opts => []}, #{name => as_int, fnum => 6, rnum => 4, type => sfixed64, occurrence => optional, opts => []}], opts => []},
       #{name => span_id, fnum => 4, rnum => 5, type => bytes, occurrence => defaulty, opts => []},
       #{name => trace_id, fnum => 5, rnum => 6, type => bytes, occurrence => defaulty, opts => []}]},
     {{msg, any_value},
      [#{name => value, rnum => 2,
         fields =>
             [#{name => string_value, fnum => 1, rnum => 2, type => string, occurrence => optional, opts => []},
              #{name => bool_value, fnum => 2, rnum => 2, type => bool, occurrence => optional, opts => []},
              #{name => int_value, fnum => 3, rnum => 2, type => int64, occurrence => optional, opts => []},
              #{name => double_value, fnum => 4, rnum => 2, type => double, occurrence => optional, opts => []},
              #{name => array_value, fnum => 5, rnum => 2, type => {msg, array_value}, occurrence => optional, opts => []},
              #{name => kvlist_value, fnum => 6, rnum => 2, type => {msg, key_value_list}, occurrence => optional, opts => []},
              #{name => bytes_value, fnum => 7, rnum => 2, type => bytes, occurrence => optional, opts => []}],
         opts => []}]},
     {{msg, array_value}, [#{name => values, fnum => 1, rnum => 2, type => {msg, any_value}, occurrence => repeated, opts => []}]},
     {{msg, key_value_list}, [#{name => values, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []}]},
     {{msg, key_value}, [#{name => key, fnum => 1, rnum => 2, type => string, occurrence => defaulty, opts => []}, #{name => value, fnum => 2, rnum => 3, type => {msg, any_value}, occurrence => defaulty, opts => []}]},
     {{msg, instrumentation_scope},
      [#{name => name, fnum => 1, rnum => 2, type => string, occurrence => defaulty, opts => []},
       #{name => version, fnum => 2, rnum => 3, type => string, occurrence => defaulty, opts => []},
       #{name => attributes, fnum => 3, rnum => 4, type => {msg, key_value}, occurrence => repeated, opts => []},
       #{name => dropped_attributes_count, fnum => 4, rnum => 5, type => uint32, occurrence => defaulty, opts => []}]},
     {{msg, resource}, [#{name => attributes, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []}, #{name => dropped_attributes_count, fnum => 2, rnum => 3, type => uint32, occurrence => defaulty, opts => []}]}].


get_msg_names() ->
    [export_metrics_service_request,
     export_metrics_service_response,
     export_metrics_partial_success,
     metrics_data,
     resource_metrics,
     scope_metrics,
     metric,
     gauge,
     sum,
     histogram,
     exponential_histogram,
     summary,
     number_data_point,
     histogram_data_point,
     buckets,
     exponential_histogram_data_point,
     value_at_quantile,
     summary_data_point,
     exemplar,
     any_value,
     array_value,
     key_value_list,
     key_value,
     instrumentation_scope,
     resource].


get_group_names() -> [].


get_msg_or_group_names() ->
    [export_metrics_service_request,
     export_metrics_service_response,
     export_metrics_partial_success,
     metrics_data,
     resource_metrics,
     scope_metrics,
     metric,
     gauge,
     sum,
     histogram,
     exponential_histogram,
     summary,
     number_data_point,
     histogram_data_point,
     buckets,
     exponential_histogram_data_point,
     value_at_quantile,
     summary_data_point,
     exemplar,
     any_value,
     array_value,
     key_value_list,
     key_value,
     instrumentation_scope,
     resource].


get_enum_names() -> ['opentelemetry.proto.metrics.v1.AggregationTemporality', 'opentelemetry.proto.metrics.v1.DataPointFlags'].


fetch_msg_def(MsgName) ->
    case find_msg_def(MsgName) of
        Fs when is_list(Fs) -> Fs;
        error -> erlang:error({no_such_msg, MsgName})
    end.


fetch_enum_def(EnumName) ->
    case find_enum_def(EnumName) of
        Es when is_list(Es) -> Es;
        error -> erlang:error({no_such_enum, EnumName})
    end.


find_msg_def(export_metrics_service_request) -> [#{name => resource_metrics, fnum => 1, rnum => 2, type => {msg, resource_metrics}, occurrence => repeated, opts => []}];
find_msg_def(export_metrics_service_response) -> [#{name => partial_success, fnum => 1, rnum => 2, type => {msg, export_metrics_partial_success}, occurrence => defaulty, opts => []}];
find_msg_def(export_metrics_partial_success) ->
    [#{name => rejected_data_points, fnum => 1, rnum => 2, type => int64, occurrence => defaulty, opts => []}, #{name => error_message, fnum => 2, rnum => 3, type => string, occurrence => defaulty, opts => []}];
find_msg_def(metrics_data) -> [#{name => resource_metrics, fnum => 1, rnum => 2, type => {msg, resource_metrics}, occurrence => repeated, opts => []}];
find_msg_def(resource_metrics) ->
    [#{name => resource, fnum => 1, rnum => 2, type => {msg, resource}, occurrence => defaulty, opts => []},
     #{name => scope_metrics, fnum => 2, rnum => 3, type => {msg, scope_metrics}, occurrence => repeated, opts => []},
     #{name => schema_url, fnum => 3, rnum => 4, type => string, occurrence => defaulty, opts => []}];
find_msg_def(scope_metrics) ->
    [#{name => scope, fnum => 1, rnum => 2, type => {msg, instrumentation_scope}, occurrence => defaulty, opts => []},
     #{name => metrics, fnum => 2, rnum => 3, type => {msg, metric}, occurrence => repeated, opts => []},
     #{name => schema_url, fnum => 3, rnum => 4, type => string, occurrence => defaulty, opts => []}];
find_msg_def(metric) ->
    [#{name => name, fnum => 1, rnum => 2, type => string, occurrence => defaulty, opts => []},
     #{name => description, fnum => 2, rnum => 3, type => string, occurrence => defaulty, opts => []},
     #{name => unit, fnum => 3, rnum => 4, type => string, occurrence => defaulty, opts => []},
     #{name => data, rnum => 5,
       fields =>
           [#{name => gauge, fnum => 5, rnum => 5, type => {msg, gauge}, occurrence => optional, opts => []},
            #{name => sum, fnum => 7, rnum => 5, type => {msg, sum}, occurrence => optional, opts => []},
            #{name => histogram, fnum => 9, rnum => 5, type => {msg, histogram}, occurrence => optional, opts => []},
            #{name => exponential_histogram, fnum => 10, rnum => 5, type => {msg, exponential_histogram}, occurrence => optional, opts => []},
            #{name => summary, fnum => 11, rnum => 5, type => {msg, summary}, occurrence => optional, opts => []}],
       opts => []}];
find_msg_def(gauge) -> [#{name => data_points, fnum => 1, rnum => 2, type => {msg, number_data_point}, occurrence => repeated, opts => []}];
find_msg_def(sum) ->
    [#{name => data_points, fnum => 1, rnum => 2, type => {msg, number_data_point}, occurrence => repeated, opts => []},
     #{name => aggregation_temporality, fnum => 2, rnum => 3, type => {enum, 'opentelemetry.proto.metrics.v1.AggregationTemporality'}, occurrence => defaulty, opts => []},
     #{name => is_monotonic, fnum => 3, rnum => 4, type => bool, occurrence => defaulty, opts => []}];
find_msg_def(histogram) ->
    [#{name => data_points, fnum => 1, rnum => 2, type => {msg, histogram_data_point}, occurrence => repeated, opts => []},
     #{name => aggregation_temporality, fnum => 2, rnum => 3, type => {enum, 'opentelemetry.proto.metrics.v1.AggregationTemporality'}, occurrence => defaulty, opts => []}];
find_msg_def(exponential_histogram) ->
    [#{name => data_points, fnum => 1, rnum => 2, type => {msg, exponential_histogram_data_point}, occurrence => repeated, opts => []},
     #{name => aggregation_temporality, fnum => 2, rnum => 3, type => {enum, 'opentelemetry.proto.metrics.v1.AggregationTemporality'}, occurrence => defaulty, opts => []}];
find_msg_def(summary) -> [#{name => data_points, fnum => 1, rnum => 2, type => {msg, summary_data_point}, occurrence => repeated, opts => []}];
find_msg_def(number_data_point) ->
    [#{name => attributes, fnum => 7, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
     #{name => start_time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
     #{name => time_unix_nano, fnum => 3, rnum => 4, type => fixed64, occurrence => defaulty, opts => []},
     #{name => value, rnum => 5, fields => [#{name => as_double, fnum => 4, rnum => 5, type => double, occurrence => optional, opts => []}, #{name => as_int, fnum => 6, rnum => 5, type => sfixed64, occurrence => optional, opts => []}], opts => []},
     #{name => exemplars, fnum => 5, rnum => 6, type => {msg, exemplar}, occurrence => repeated, opts => []},
     #{name => flags, fnum => 8, rnum => 7, type => uint32, occurrence => defaulty, opts => []}];
find_msg_def(histogram_data_point) ->
    [#{name => attributes, fnum => 9, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
     #{name => start_time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
     #{name => time_unix_nano, fnum => 3, rnum => 4, type => fixed64, occurrence => defaulty, opts => []},
     #{name => count, fnum => 4, rnum => 5, type => fixed64, occurrence => defaulty, opts => []},
     #{name => sum, fnum => 5, rnum => 6, type => double, occurrence => optional, opts => []},
     #{name => bucket_counts, fnum => 6, rnum => 7, type => fixed64, occurrence => repeated, opts => [packed]},
     #{name => explicit_bounds, fnum => 7, rnum => 8, type => double, occurrence => repeated, opts => [packed]},
     #{name => exemplars, fnum => 8, rnum => 9, type => {msg, exemplar}, occurrence => repeated, opts => []},
     #{name => flags, fnum => 10, rnum => 10, type => uint32, occurrence => defaulty, opts => []},
     #{name => min, fnum => 11, rnum => 11, type => double, occurrence => optional, opts => []},
     #{name => max, fnum => 12, rnum => 12, type => double, occurrence => optional, opts => []}];
find_msg_def(buckets) -> [#{name => offset, fnum => 1, rnum => 2, type => sint32, occurrence => defaulty, opts => []}, #{name => bucket_counts, fnum => 2, rnum => 3, type => uint64, occurrence => repeated, opts => [packed]}];
find_msg_def(exponential_histogram_data_point) ->
    [#{name => attributes, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
     #{name => start_time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
     #{name => time_unix_nano, fnum => 3, rnum => 4, type => fixed64, occurrence => defaulty, opts => []},
     #{name => count, fnum => 4, rnum => 5, type => fixed64, occurrence => defaulty, opts => []},
     #{name => sum, fnum => 5, rnum => 6, type => double, occurrence => optional, opts => []},
     #{name => scale, fnum => 6, rnum => 7, type => sint32, occurrence => defaulty, opts => []},
     #{name => zero_count, fnum => 7, rnum => 8, type => fixed64, occurrence => defaulty, opts => []},
     #{name => positive, fnum => 8, rnum => 9, type => {msg, buckets}, occurrence => defaulty, opts => []},
     #{name => negative, fnum => 9, rnum => 10, type => {msg, buckets}, occurrence => defaulty, opts => []},
     #{name => flags, fnum => 10, rnum => 11, type => uint32, occurrence => defaulty, opts => []},
     #{name => exemplars, fnum => 11, rnum => 12, type => {msg, exemplar}, occurrence => repeated, opts => []},
     #{name => min, fnum => 12, rnum => 13, type => double, occurrence => optional, opts => []},
     #{name => max, fnum => 13, rnum => 14, type => double, occurrence => optional, opts => []}];
find_msg_def(value_at_quantile) -> [#{name => quantile, fnum => 1, rnum => 2, type => double, occurrence => defaulty, opts => []}, #{name => value, fnum => 2, rnum => 3, type => double, occurrence => defaulty, opts => []}];
find_msg_def(summary_data_point) ->
    [#{name => attributes, fnum => 7, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
     #{name => start_time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
     #{name => time_unix_nano, fnum => 3, rnum => 4, type => fixed64, occurrence => defaulty, opts => []},
     #{name => count, fnum => 4, rnum => 5, type => fixed64, occurrence => defaulty, opts => []},
     #{name => sum, fnum => 5, rnum => 6, type => double, occurrence => defaulty, opts => []},
     #{name => quantile_values, fnum => 6, rnum => 7, type => {msg, value_at_quantile}, occurrence => repeated, opts => []},
     #{name => flags, fnum => 8, rnum => 8, type => uint32, occurrence => defaulty, opts => []}];
find_msg_def(exemplar) ->
    [#{name => filtered_attributes, fnum => 7, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []},
     #{name => time_unix_nano, fnum => 2, rnum => 3, type => fixed64, occurrence => defaulty, opts => []},
     #{name => value, rnum => 4, fields => [#{name => as_double, fnum => 3, rnum => 4, type => double, occurrence => optional, opts => []}, #{name => as_int, fnum => 6, rnum => 4, type => sfixed64, occurrence => optional, opts => []}], opts => []},
     #{name => span_id, fnum => 4, rnum => 5, type => bytes, occurrence => defaulty, opts => []},
     #{name => trace_id, fnum => 5, rnum => 6, type => bytes, occurrence => defaulty, opts => []}];
find_msg_def(any_value) ->
    [#{name => value, rnum => 2,
       fields =>
           [#{name => string_value, fnum => 1, rnum => 2, type => string, occurrence => optional, opts => []},
            #{name => bool_value, fnum => 2, rnum => 2, type => bool, occurrence => optional, opts => []},
            #{name => int_value, fnum => 3, rnum => 2, type => int64, occurrence => optional, opts => []},
            #{name => double_value, fnum => 4, rnum => 2, type => double, occurrence => optional, opts => []},
            #{name => array_value, fnum => 5, rnum => 2, type => {msg, array_value}, occurrence => optional, opts => []},
            #{name => kvlist_value, fnum => 6, rnum => 2, type => {msg, key_value_list}, occurrence => optional, opts => []},
            #{name => bytes_value, fnum => 7, rnum => 2, type => bytes, occurrence => optional, opts => []}],
       opts => []}];
find_msg_def(array_value) -> [#{name => values, fnum => 1, rnum => 2, type => {msg, any_value}, occurrence => repeated, opts => []}];
find_msg_def(key_value_list) -> [#{name => values, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []}];
find_msg_def(key_value) -> [#{name => key, fnum => 1, rnum => 2, type => string, occurrence => defaulty, opts => []}, #{name => value, fnum => 2, rnum => 3, type => {msg, any_value}, occurrence => defaulty, opts => []}];
find_msg_def(instrumentation_scope) ->
    [#{name => name, fnum => 1, rnum => 2, type => string, occurrence => defaulty, opts => []},
     #{name => version, fnum => 2, rnum => 3, type => string, occurrence => defaulty, opts => []},
     #{name => attributes, fnum => 3, rnum => 4, type => {msg, key_value}, occurrence => repeated, opts => []},
     #{name => dropped_attributes_count, fnum => 4, rnum => 5, type => uint32, occurrence => defaulty, opts => []}];
find_msg_def(resource) -> [#{name => attributes, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []}, #{name => dropped_attributes_count, fnum => 2, rnum => 3, type => uint32, occurrence => defaulty, opts => []}];
find_msg_def(_) -> error.


find_enum_def('opentelemetry.proto.metrics.v1.AggregationTemporality') -> [{'AGGREGATION_TEMPORALITY_UNSPECIFIED', 0, []}, {'AGGREGATION_TEMPORALITY_DELTA', 1, []}, {'AGGREGATION_TEMPORALITY_CUMULATIVE', 2, []}];
find_enum_def('opentelemetry.proto.metrics.v1.DataPointFlags') -> [{'FLAG_NONE', 0, []}, {'FLAG_NO_RECORDED_VALUE', 1, []}];
find_enum_def(_) -> error.


enum_symbol_by_value('opentelemetry.proto.metrics.v1.AggregationTemporality', Value) -> 'enum_symbol_by_value_opentelemetry.proto.metrics.v1.AggregationTemporality'(Value);
enum_symbol_by_value('opentelemetry.proto.metrics.v1.DataPointFlags', Value) -> 'enum_symbol_by_value_opentelemetry.proto.metrics.v1.DataPointFlags'(Value).


enum_value_by_symbol('opentelemetry.proto.metrics.v1.AggregationTemporality', Sym) -> 'enum_value_by_symbol_opentelemetry.proto.metrics.v1.AggregationTemporality'(Sym);
enum_value_by_symbol('opentelemetry.proto.metrics.v1.DataPointFlags', Sym) -> 'enum_value_by_symbol_opentelemetry.proto.metrics.v1.DataPointFlags'(Sym).


'enum_symbol_by_value_opentelemetry.proto.metrics.v1.AggregationTemporality'(0) -> 'AGGREGATION_TEMPORALITY_UNSPECIFIED';
'enum_symbol_by_value_opentelemetry.proto.metrics.v1.AggregationTemporality'(1) -> 'AGGREGATION_TEMPORALITY_DELTA';
'enum_symbol_by_value_opentelemetry.proto.metrics.v1.AggregationTemporality'(2) -> 'AGGREGATION_TEMPORALITY_CUMULATIVE'.


'enum_value_by_symbol_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_UNSPECIFIED') -> 0;
'enum_value_by_symbol_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_DELTA') -> 1;
'enum_value_by_symbol_opentelemetry.proto.metrics.v1.AggregationTemporality'('AGGREGATION_TEMPORALITY_CUMULATIVE') -> 2.

'enum_symbol_by_value_opentelemetry.proto.metrics.v1.DataPointFlags'(0) -> 'FLAG_NONE';
'enum_symbol_by_value_opentelemetry.proto.metrics.v1.DataPointFlags'(1) -> 'FLAG_NO_RECORDED_VALUE'.


'enum_value_by_symbol_opentelemetry.proto.metrics.v1.DataPointFlags'('FLAG_NONE') -> 0;
'enum_value_by_symbol_opentelemetry.proto.metrics.v1.DataPointFlags'('FLAG_NO_RECORDED_VALUE') -> 1.


get_service_names() -> ['opentelemetry.proto.collector.metrics.v1.MetricsService'].


get_service_def('opentelemetry.proto.collector.metrics.v1.MetricsService') ->
    {{service, 'opentelemetry.proto.collector.metrics.v1.MetricsService'}, [#{name => 'Export', input => export_metrics_service_request, output => export_metrics_service_response, input_stream => false, output_stream => false, opts => []}]};
get_service_def(_) -> error.


get_rpc_names('opentelemetry.proto.collector.metrics.v1.MetricsService') -> ['Export'];
get_rpc_names(_) -> error.


find_rpc_def('opentelemetry.proto.collector.metrics.v1.MetricsService', RpcName) -> 'find_rpc_def_opentelemetry.proto.collector.metrics.v1.MetricsService'(RpcName);
find_rpc_def(_, _) -> error.


'find_rpc_def_opentelemetry.proto.collector.metrics.v1.MetricsService'('Export') -> #{name => 'Export', input => export_metrics_service_request, output => export_metrics_service_response, input_stream => false, output_stream => false, opts => []};
'find_rpc_def_opentelemetry.proto.collector.metrics.v1.MetricsService'(_) -> error.


fetch_rpc_def(ServiceName, RpcName) ->
    case find_rpc_def(ServiceName, RpcName) of
        Def when is_map(Def) -> Def;
        error -> erlang:error({no_such_rpc, ServiceName, RpcName})
    end.


%% Convert a a fully qualified (ie with package name) service name
%% as a binary to a service name as an atom.
fqbin_to_service_name(<<"opentelemetry.proto.collector.metrics.v1.MetricsService">>) -> 'opentelemetry.proto.collector.metrics.v1.MetricsService';
fqbin_to_service_name(X) -> error({gpb_error, {badservice, X}}).


%% Convert a service name as an atom to a fully qualified
%% (ie with package name) name as a binary.
service_name_to_fqbin('opentelemetry.proto.collector.metrics.v1.MetricsService') -> <<"opentelemetry.proto.collector.metrics.v1.MetricsService">>;
service_name_to_fqbin(X) -> error({gpb_error, {badservice, X}}).


%% Convert a a fully qualified (ie with package name) service name
%% and an rpc name, both as binaries to a service name and an rpc
%% name, as atoms.
fqbins_to_service_and_rpc_name(<<"opentelemetry.proto.collector.metrics.v1.MetricsService">>, <<"Export">>) -> {'opentelemetry.proto.collector.metrics.v1.MetricsService', 'Export'};
fqbins_to_service_and_rpc_name(S, R) -> error({gpb_error, {badservice_or_rpc, {S, R}}}).


%% Convert a service name and an rpc name, both as atoms,
%% to a fully qualified (ie with package name) service name and
%% an rpc name as binaries.
service_and_rpc_name_to_fqbins('opentelemetry.proto.collector.metrics.v1.MetricsService', 'Export') -> {<<"opentelemetry.proto.collector.metrics.v1.MetricsService">>, <<"Export">>};
service_and_rpc_name_to_fqbins(S, R) -> error({gpb_error, {badservice_or_rpc, {S, R}}}).


fqbin_to_msg_name(<<"opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest">>) -> export_metrics_service_request;
fqbin_to_msg_name(<<"opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse">>) -> export_metrics_service_response;
fqbin_to_msg_name(<<"opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess">>) -> export_metrics_partial_success;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.MetricsData">>) -> metrics_data;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.ResourceMetrics">>) -> resource_metrics;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.ScopeMetrics">>) -> scope_metrics;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.Metric">>) -> metric;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.Gauge">>) -> gauge;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.Sum">>) -> sum;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.Histogram">>) -> histogram;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.ExponentialHistogram">>) -> exponential_histogram;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.Summary">>) -> summary;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.NumberDataPoint">>) -> number_data_point;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.HistogramDataPoint">>) -> histogram_data_point;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets">>) -> buckets;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint">>) -> exponential_histogram_data_point;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile">>) -> value_at_quantile;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.SummaryDataPoint">>) -> summary_data_point;
fqbin_to_msg_name(<<"opentelemetry.proto.metrics.v1.Exemplar">>) -> exemplar;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.AnyValue">>) -> any_value;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.ArrayValue">>) -> array_value;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.KeyValueList">>) -> key_value_list;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.KeyValue">>) -> key_value;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.InstrumentationScope">>) -> instrumentation_scope;
fqbin_to_msg_name(<<"opentelemetry.proto.resource.v1.Resource">>) -> resource;
fqbin_to_msg_name(E) -> error({gpb_error, {badmsg, E}}).


msg_name_to_fqbin(export_metrics_service_request) -> <<"opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest">>;
msg_name_to_fqbin(export_metrics_service_response) -> <<"opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse">>;
msg_name_to_fqbin(export_metrics_partial_success) -> <<"opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess">>;
msg_name_to_fqbin(metrics_data) -> <<"opentelemetry.proto.metrics.v1.MetricsData">>;
msg_name_to_fqbin(resource_metrics) -> <<"opentelemetry.proto.metrics.v1.ResourceMetrics">>;
msg_name_to_fqbin(scope_metrics) -> <<"opentelemetry.proto.metrics.v1.ScopeMetrics">>;
msg_name_to_fqbin(metric) -> <<"opentelemetry.proto.metrics.v1.Metric">>;
msg_name_to_fqbin(gauge) -> <<"opentelemetry.proto.metrics.v1.Gauge">>;
msg_name_to_fqbin(sum) -> <<"opentelemetry.proto.metrics.v1.Sum">>;
msg_name_to_fqbin(histogram) -> <<"opentelemetry.proto.metrics.v1.Histogram">>;
msg_name_to_fqbin(exponential_histogram) -> <<"opentelemetry.proto.metrics.v1.ExponentialHistogram">>;
msg_name_to_fqbin(summary) -> <<"opentelemetry.proto.metrics.v1.Summary">>;
msg_name_to_fqbin(number_data_point) -> <<"opentelemetry.proto.metrics.v1.NumberDataPoint">>;
msg_name_to_fqbin(histogram_data_point) -> <<"opentelemetry.proto.metrics.v1.HistogramDataPoint">>;
msg_name_to_fqbin(buckets) -> <<"opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets">>;
msg_name_to_fqbin(exponential_histogram_data_point) -> <<"opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint">>;
msg_name_to_fqbin(value_at_quantile) -> <<"opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile">>;
msg_name_to_fqbin(summary_data_point) -> <<"opentelemetry.proto.metrics.v1.SummaryDataPoint">>;
msg_name_to_fqbin(exemplar) -> <<"opentelemetry.proto.metrics.v1.Exemplar">>;
msg_name_to_fqbin(any_value) -> <<"opentelemetry.proto.common.v1.AnyValue">>;
msg_name_to_fqbin(array_value) -> <<"opentelemetry.proto.common.v1.ArrayValue">>;
msg_name_to_fqbin(key_value_list) -> <<"opentelemetry.proto.common.v1.KeyValueList">>;
msg_name_to_fqbin(key_value) -> <<"opentelemetry.proto.common.v1.KeyValue">>;
msg_name_to_fqbin(instrumentation_scope) -> <<"opentelemetry.proto.common.v1.InstrumentationScope">>;
msg_name_to_fqbin(resource) -> <<"opentelemetry.proto.resource.v1.Resource">>;
msg_name_to_fqbin(E) -> error({gpb_error, {badmsg, E}}).


fqbin_to_enum_name(<<"opentelemetry.proto.metrics.v1.AggregationTemporality">>) -> 'opentelemetry.proto.metrics.v1.AggregationTemporality';
fqbin_to_enum_name(<<"opentelemetry.proto.metrics.v1.DataPointFlags">>) -> 'opentelemetry.proto.metrics.v1.DataPointFlags';
fqbin_to_enum_name(E) -> error({gpb_error, {badenum, E}}).


enum_name_to_fqbin('opentelemetry.proto.metrics.v1.AggregationTemporality') -> <<"opentelemetry.proto.metrics.v1.AggregationTemporality">>;
enum_name_to_fqbin('opentelemetry.proto.metrics.v1.DataPointFlags') -> <<"opentelemetry.proto.metrics.v1.DataPointFlags">>;
enum_name_to_fqbin(E) -> error({gpb_error, {badenum, E}}).


get_package_name() -> 'opentelemetry.proto.collector.metrics.v1'.


%% Whether or not the message names
%% are prepended with package name or not.
uses_packages() -> true.


source_basename() -> "metrics_service.proto".


%% Retrieve all proto file names, also imported ones.
%% The order is top-down. The first element is always the main
%% source file. The files are returned with extension,
%% see get_all_proto_names/0 for a version that returns
%% the basenames sans extension
get_all_source_basenames() -> ["metrics_service.proto", "metrics.proto", "common.proto", "resource.proto"].


%% Retrieve all proto file names, also imported ones.
%% The order is top-down. The first element is always the main
%% source file. The files are returned sans .proto extension,
%% to make it easier to use them with the various get_xyz_containment
%% functions.
get_all_proto_names() -> ["metrics_service", "metrics", "common", "resource"].


get_msg_containment("metrics_service") -> [export_metrics_partial_success, export_metrics_service_request, export_metrics_service_response];
get_msg_containment("metrics") ->
    [exemplar, exponential_histogram, exponential_histogram_data_point, buckets, gauge, histogram, histogram_data_point, metric, metrics_data, number_data_point, resource_metrics, scope_metrics, sum, summary, summary_data_point, value_at_quantile];
get_msg_containment("common") -> [any_value, array_value, instrumentation_scope, key_value, key_value_list];
get_msg_containment("resource") -> [resource];
get_msg_containment(P) -> error({gpb_error, {badproto, P}}).


get_pkg_containment("metrics_service") -> 'opentelemetry.proto.collector.metrics.v1';
get_pkg_containment("metrics") -> 'opentelemetry.proto.metrics.v1';
get_pkg_containment("common") -> 'opentelemetry.proto.common.v1';
get_pkg_containment("resource") -> 'opentelemetry.proto.resource.v1';
get_pkg_containment(P) -> error({gpb_error, {badproto, P}}).


get_service_containment("metrics_service") -> ['opentelemetry.proto.collector.metrics.v1.MetricsService'];
get_service_containment("metrics") -> [];
get_service_containment("common") -> [];
get_service_containment("resource") -> [];
get_service_containment(P) -> error({gpb_error, {badproto, P}}).


get_rpc_containment("metrics_service") -> [{'opentelemetry.proto.collector.metrics.v1.MetricsService', 'Export'}];
get_rpc_containment("metrics") -> [];
get_rpc_containment("common") -> [];
get_rpc_containment("resource") -> [];
get_rpc_containment(P) -> error({gpb_error, {badproto, P}}).


get_enum_containment("metrics_service") -> [];
get_enum_containment("metrics") -> ['opentelemetry.proto.metrics.v1.AggregationTemporality', 'opentelemetry.proto.metrics.v1.DataPointFlags'];
get_enum_containment("common") -> [];
get_enum_containment("resource") -> [];
get_enum_containment(P) -> error({gpb_error, {badproto, P}}).


get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.MetricsData">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.Exemplar">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.ScopeMetrics">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.ResourceMetrics">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.Metric">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess">>) -> "metrics_service";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.KeyValueList">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.SummaryDataPoint">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.NumberDataPoint">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.HistogramDataPoint">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest">>) -> "metrics_service";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.resource.v1.Resource">>) -> "resource";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.KeyValue">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.InstrumentationScope">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.ArrayValue">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.AnyValue">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.Gauge">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse">>) -> "metrics_service";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.Summary">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.Sum">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.Histogram">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.ExponentialHistogram">>) -> "metrics";
get_proto_by_msg_name_as_fqbin(E) -> error({gpb_error, {badmsg, E}}).


get_proto_by_service_name_as_fqbin(<<"opentelemetry.proto.collector.metrics.v1.MetricsService">>) -> "metrics_service";
get_proto_by_service_name_as_fqbin(E) -> error({gpb_error, {badservice, E}}).


get_proto_by_enum_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.DataPointFlags">>) -> "metrics";
get_proto_by_enum_name_as_fqbin(<<"opentelemetry.proto.metrics.v1.AggregationTemporality">>) -> "metrics";
get_proto_by_enum_name_as_fqbin(E) -> error({gpb_error, {badenum, E}}).


get_protos_by_pkg_name_as_fqbin(<<"opentelemetry.proto.resource.v1">>) -> ["resource"];
get_protos_by_pkg_name_as_fqbin(<<"opentelemetry.proto.common.v1">>) -> ["common"];
get_protos_by_pkg_name_as_fqbin(<<"opentelemetry.proto.metrics.v1">>) -> ["metrics"];
get_protos_by_pkg_name_as_fqbin(<<"opentelemetry.proto.collector.metrics.v1">>) -> ["metrics_service"];
get_protos_by_pkg_name_as_fqbin(E) -> error({gpb_error, {badpkg, E}}).



gpb_version_as_string() ->
    "4.19.5".

gpb_version_as_list() ->
    [4,19,5].

gpb_version_source() ->
    "file".
