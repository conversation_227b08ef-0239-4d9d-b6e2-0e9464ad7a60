%% -*- coding: utf-8 -*-
%% @private
%% Automatically generated, do not edit
%% Generated by gpb_compile version 4.19.5
%% Version source: file
-module(opentelemetry_exporter_logs_service_pb).

-export([encode_msg/2, encode_msg/3]).
-export([decode_msg/2, decode_msg/3]).
-export([merge_msgs/3, merge_msgs/4]).
-export([verify_msg/2, verify_msg/3]).
-export([get_msg_defs/0]).
-export([get_msg_names/0]).
-export([get_group_names/0]).
-export([get_msg_or_group_names/0]).
-export([get_enum_names/0]).
-export([find_msg_def/1, fetch_msg_def/1]).
-export([find_enum_def/1, fetch_enum_def/1]).
-export([enum_symbol_by_value/2, enum_value_by_symbol/2]).
-export(['enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'/1, 'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'/1]).
-export(['enum_symbol_by_value_opentelemetry.proto.logs.v1.LogRecordFlags'/1, 'enum_value_by_symbol_opentelemetry.proto.logs.v1.LogRecordFlags'/1]).
-export([get_service_names/0]).
-export([get_service_def/1]).
-export([get_rpc_names/1]).
-export([find_rpc_def/2, fetch_rpc_def/2]).
-export([fqbin_to_service_name/1]).
-export([service_name_to_fqbin/1]).
-export([fqbins_to_service_and_rpc_name/2]).
-export([service_and_rpc_name_to_fqbins/2]).
-export([fqbin_to_msg_name/1]).
-export([msg_name_to_fqbin/1]).
-export([fqbin_to_enum_name/1]).
-export([enum_name_to_fqbin/1]).
-export([get_package_name/0]).
-export([uses_packages/0]).
-export([source_basename/0]).
-export([get_all_source_basenames/0]).
-export([get_all_proto_names/0]).
-export([get_msg_containment/1]).
-export([get_pkg_containment/1]).
-export([get_service_containment/1]).
-export([get_rpc_containment/1]).
-export([get_enum_containment/1]).
-export([get_proto_by_msg_name_as_fqbin/1]).
-export([get_proto_by_service_name_as_fqbin/1]).
-export([get_proto_by_enum_name_as_fqbin/1]).
-export([get_protos_by_pkg_name_as_fqbin/1]).
-export([gpb_version_as_string/0, gpb_version_as_list/0]).
-export([gpb_version_source/0]).


%% enumerated types
-type 'opentelemetry.proto.logs.v1.SeverityNumber'() :: 'SEVERITY_NUMBER_UNSPECIFIED' | 'SEVERITY_NUMBER_TRACE' | 'SEVERITY_NUMBER_TRACE2' | 'SEVERITY_NUMBER_TRACE3' | 'SEVERITY_NUMBER_TRACE4' | 'SEVERITY_NUMBER_DEBUG' | 'SEVERITY_NUMBER_DEBUG2' | 'SEVERITY_NUMBER_DEBUG3' | 'SEVERITY_NUMBER_DEBUG4' | 'SEVERITY_NUMBER_INFO' | 'SEVERITY_NUMBER_INFO2' | 'SEVERITY_NUMBER_INFO3' | 'SEVERITY_NUMBER_INFO4' | 'SEVERITY_NUMBER_WARN' | 'SEVERITY_NUMBER_WARN2' | 'SEVERITY_NUMBER_WARN3' | 'SEVERITY_NUMBER_WARN4' | 'SEVERITY_NUMBER_ERROR' | 'SEVERITY_NUMBER_ERROR2' | 'SEVERITY_NUMBER_ERROR3' | 'SEVERITY_NUMBER_ERROR4' | 'SEVERITY_NUMBER_FATAL' | 'SEVERITY_NUMBER_FATAL2' | 'SEVERITY_NUMBER_FATAL3' | 'SEVERITY_NUMBER_FATAL4'.
-type 'opentelemetry.proto.logs.v1.LogRecordFlags'() :: 'LOG_RECORD_FLAG_UNSPECIFIED' | 'LOG_RECORD_FLAG_TRACE_FLAGS_MASK'.
-export_type(['opentelemetry.proto.logs.v1.SeverityNumber'/0, 'opentelemetry.proto.logs.v1.LogRecordFlags'/0]).

%% message types
-type export_logs_service_request() ::
      #{resource_logs           => [resource_logs()] % = 1, repeated
       }.

-type export_logs_service_response() ::
      #{partial_success         => export_logs_partial_success() % = 1, optional
       }.

-type export_logs_partial_success() ::
      #{rejected_log_records    => integer(),       % = 1, optional, 64 bits
        error_message           => unicode:chardata() % = 2, optional
       }.

-type logs_data() ::
      #{resource_logs           => [resource_logs()] % = 1, repeated
       }.

-type resource_logs() ::
      #{resource                => resource(),      % = 1, optional
        scope_logs              => [scope_logs()],  % = 2, repeated
        schema_url              => unicode:chardata() % = 3, optional
       }.

-type scope_logs() ::
      #{scope                   => instrumentation_scope(), % = 1, optional
        log_records             => [log_record()],  % = 2, repeated
        schema_url              => unicode:chardata() % = 3, optional
       }.

-type log_record() ::
      #{time_unix_nano          => non_neg_integer(), % = 1, optional, 64 bits
        observed_time_unix_nano => non_neg_integer(), % = 11, optional, 64 bits
        severity_number         => 'SEVERITY_NUMBER_UNSPECIFIED' | 'SEVERITY_NUMBER_TRACE' | 'SEVERITY_NUMBER_TRACE2' | 'SEVERITY_NUMBER_TRACE3' | 'SEVERITY_NUMBER_TRACE4' | 'SEVERITY_NUMBER_DEBUG' | 'SEVERITY_NUMBER_DEBUG2' | 'SEVERITY_NUMBER_DEBUG3' | 'SEVERITY_NUMBER_DEBUG4' | 'SEVERITY_NUMBER_INFO' | 'SEVERITY_NUMBER_INFO2' | 'SEVERITY_NUMBER_INFO3' | 'SEVERITY_NUMBER_INFO4' | 'SEVERITY_NUMBER_WARN' | 'SEVERITY_NUMBER_WARN2' | 'SEVERITY_NUMBER_WARN3' | 'SEVERITY_NUMBER_WARN4' | 'SEVERITY_NUMBER_ERROR' | 'SEVERITY_NUMBER_ERROR2' | 'SEVERITY_NUMBER_ERROR3' | 'SEVERITY_NUMBER_ERROR4' | 'SEVERITY_NUMBER_FATAL' | 'SEVERITY_NUMBER_FATAL2' | 'SEVERITY_NUMBER_FATAL3' | 'SEVERITY_NUMBER_FATAL4' | integer(), % = 2, optional, enum opentelemetry.proto.logs.v1.SeverityNumber
        severity_text           => unicode:chardata(), % = 3, optional
        body                    => any_value(),     % = 5, optional
        attributes              => [key_value()],   % = 6, repeated
        dropped_attributes_count => non_neg_integer(), % = 7, optional, 32 bits
        flags                   => non_neg_integer(), % = 8, optional, 32 bits
        trace_id                => iodata(),        % = 9, optional
        span_id                 => iodata()         % = 10, optional
       }.

-type any_value() ::
      #{value                   => {string_value, unicode:chardata()} | {bool_value, boolean() | 0 | 1} | {int_value, integer()} | {double_value, float() | integer() | infinity | '-infinity' | nan} | {array_value, array_value()} | {kvlist_value, key_value_list()} | {bytes_value, iodata()} % oneof
       }.

-type array_value() ::
      #{values                  => [any_value()]    % = 1, repeated
       }.

-type key_value_list() ::
      #{values                  => [key_value()]    % = 1, repeated
       }.

-type key_value() ::
      #{key                     => unicode:chardata(), % = 1, optional
        value                   => any_value()      % = 2, optional
       }.

-type instrumentation_scope() ::
      #{name                    => unicode:chardata(), % = 1, optional
        version                 => unicode:chardata(), % = 2, optional
        attributes              => [key_value()],   % = 3, repeated
        dropped_attributes_count => non_neg_integer() % = 4, optional, 32 bits
       }.

-type resource() ::
      #{attributes              => [key_value()],   % = 1, repeated
        dropped_attributes_count => non_neg_integer() % = 2, optional, 32 bits
       }.

-export_type(['export_logs_service_request'/0, 'export_logs_service_response'/0, 'export_logs_partial_success'/0, 'logs_data'/0, 'resource_logs'/0, 'scope_logs'/0, 'log_record'/0, 'any_value'/0, 'array_value'/0, 'key_value_list'/0, 'key_value'/0, 'instrumentation_scope'/0, 'resource'/0]).
-type '$msg_name'() :: export_logs_service_request | export_logs_service_response | export_logs_partial_success | logs_data | resource_logs | scope_logs | log_record | any_value | array_value | key_value_list | key_value | instrumentation_scope | resource.
-type '$msg'() :: export_logs_service_request() | export_logs_service_response() | export_logs_partial_success() | logs_data() | resource_logs() | scope_logs() | log_record() | any_value() | array_value() | key_value_list() | key_value() | instrumentation_scope() | resource().
-export_type(['$msg_name'/0, '$msg'/0]).

-if(?OTP_RELEASE >= 24).
-dialyzer({no_underspecs, encode_msg/2}).
-endif.
-spec encode_msg('$msg'(), '$msg_name'()) -> binary().
encode_msg(Msg, MsgName) when is_atom(MsgName) -> encode_msg(Msg, MsgName, []).

-if(?OTP_RELEASE >= 24).
-dialyzer({no_underspecs, encode_msg/3}).
-endif.
-spec encode_msg('$msg'(), '$msg_name'(), list()) -> binary().
encode_msg(Msg, MsgName, Opts) ->
    case proplists:get_bool(verify, Opts) of
        true -> verify_msg(Msg, MsgName, Opts);
        false -> ok
    end,
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        export_logs_service_request -> encode_msg_export_logs_service_request(id(Msg, TrUserData), TrUserData);
        export_logs_service_response -> encode_msg_export_logs_service_response(id(Msg, TrUserData), TrUserData);
        export_logs_partial_success -> encode_msg_export_logs_partial_success(id(Msg, TrUserData), TrUserData);
        logs_data -> encode_msg_logs_data(id(Msg, TrUserData), TrUserData);
        resource_logs -> encode_msg_resource_logs(id(Msg, TrUserData), TrUserData);
        scope_logs -> encode_msg_scope_logs(id(Msg, TrUserData), TrUserData);
        log_record -> encode_msg_log_record(id(Msg, TrUserData), TrUserData);
        any_value -> encode_msg_any_value(id(Msg, TrUserData), TrUserData);
        array_value -> encode_msg_array_value(id(Msg, TrUserData), TrUserData);
        key_value_list -> encode_msg_key_value_list(id(Msg, TrUserData), TrUserData);
        key_value -> encode_msg_key_value(id(Msg, TrUserData), TrUserData);
        instrumentation_scope -> encode_msg_instrumentation_scope(id(Msg, TrUserData), TrUserData);
        resource -> encode_msg_resource(id(Msg, TrUserData), TrUserData)
    end.


encode_msg_export_logs_service_request(Msg, TrUserData) -> encode_msg_export_logs_service_request(Msg, <<>>, TrUserData).


encode_msg_export_logs_service_request(#{} = M, Bin, TrUserData) ->
    case M of
        #{resource_logs := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_export_logs_service_request_resource_logs(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_export_logs_service_response(Msg, TrUserData) -> encode_msg_export_logs_service_response(Msg, <<>>, TrUserData).


encode_msg_export_logs_service_response(#{} = M, Bin, TrUserData) ->
    case M of
        #{partial_success := F1} ->
            begin
                TrF1 = id(F1, TrUserData),
                if TrF1 =:= undefined -> Bin;
                   true -> e_mfield_export_logs_service_response_partial_success(TrF1, <<Bin/binary, 10>>, TrUserData)
                end
            end;
        _ -> Bin
    end.

encode_msg_export_logs_partial_success(Msg, TrUserData) -> encode_msg_export_logs_partial_success(Msg, <<>>, TrUserData).


encode_msg_export_logs_partial_success(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{rejected_log_records := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= 0 -> Bin;
                        true -> e_type_int64(TrF1, <<Bin/binary, 8>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{error_message := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                case is_empty_string(TrF2) of
                    true -> B1;
                    false -> e_type_string(TrF2, <<B1/binary, 18>>, TrUserData)
                end
            end;
        _ -> B1
    end.

encode_msg_logs_data(Msg, TrUserData) -> encode_msg_logs_data(Msg, <<>>, TrUserData).


encode_msg_logs_data(#{} = M, Bin, TrUserData) ->
    case M of
        #{resource_logs := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_logs_data_resource_logs(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_resource_logs(Msg, TrUserData) -> encode_msg_resource_logs(Msg, <<>>, TrUserData).


encode_msg_resource_logs(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{resource := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= undefined -> Bin;
                        true -> e_mfield_resource_logs_resource(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{scope_logs := F2} ->
                 TrF2 = id(F2, TrUserData),
                 if TrF2 == [] -> B1;
                    true -> e_field_resource_logs_scope_logs(TrF2, B1, TrUserData)
                 end;
             _ -> B1
         end,
    case M of
        #{schema_url := F3} ->
            begin
                TrF3 = id(F3, TrUserData),
                case is_empty_string(TrF3) of
                    true -> B2;
                    false -> e_type_string(TrF3, <<B2/binary, 26>>, TrUserData)
                end
            end;
        _ -> B2
    end.

encode_msg_scope_logs(Msg, TrUserData) -> encode_msg_scope_logs(Msg, <<>>, TrUserData).


encode_msg_scope_logs(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{scope := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= undefined -> Bin;
                        true -> e_mfield_scope_logs_scope(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{log_records := F2} ->
                 TrF2 = id(F2, TrUserData),
                 if TrF2 == [] -> B1;
                    true -> e_field_scope_logs_log_records(TrF2, B1, TrUserData)
                 end;
             _ -> B1
         end,
    case M of
        #{schema_url := F3} ->
            begin
                TrF3 = id(F3, TrUserData),
                case is_empty_string(TrF3) of
                    true -> B2;
                    false -> e_type_string(TrF3, <<B2/binary, 26>>, TrUserData)
                end
            end;
        _ -> B2
    end.

encode_msg_log_record(Msg, TrUserData) -> encode_msg_log_record(Msg, <<>>, TrUserData).


encode_msg_log_record(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{time_unix_nano := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     if TrF1 =:= 0 -> Bin;
                        true -> e_type_fixed64(TrF1, <<Bin/binary, 9>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{observed_time_unix_nano := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     if TrF2 =:= 0 -> B1;
                        true -> e_type_fixed64(TrF2, <<B1/binary, 89>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{severity_number := F3} ->
                 begin
                     TrF3 = id(F3, TrUserData),
                     if TrF3 =:= 'SEVERITY_NUMBER_UNSPECIFIED'; TrF3 =:= 0 -> B2;
                        true -> 'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'(TrF3, <<B2/binary, 16>>, TrUserData)
                     end
                 end;
             _ -> B2
         end,
    B4 = case M of
             #{severity_text := F4} ->
                 begin
                     TrF4 = id(F4, TrUserData),
                     case is_empty_string(TrF4) of
                         true -> B3;
                         false -> e_type_string(TrF4, <<B3/binary, 26>>, TrUserData)
                     end
                 end;
             _ -> B3
         end,
    B5 = case M of
             #{body := F5} ->
                 begin
                     TrF5 = id(F5, TrUserData),
                     if TrF5 =:= undefined -> B4;
                        true -> e_mfield_log_record_body(TrF5, <<B4/binary, 42>>, TrUserData)
                     end
                 end;
             _ -> B4
         end,
    B6 = case M of
             #{attributes := F6} ->
                 TrF6 = id(F6, TrUserData),
                 if TrF6 == [] -> B5;
                    true -> e_field_log_record_attributes(TrF6, B5, TrUserData)
                 end;
             _ -> B5
         end,
    B7 = case M of
             #{dropped_attributes_count := F7} ->
                 begin
                     TrF7 = id(F7, TrUserData),
                     if TrF7 =:= 0 -> B6;
                        true -> e_varint(TrF7, <<B6/binary, 56>>, TrUserData)
                     end
                 end;
             _ -> B6
         end,
    B8 = case M of
             #{flags := F8} ->
                 begin
                     TrF8 = id(F8, TrUserData),
                     if TrF8 =:= 0 -> B7;
                        true -> e_type_fixed32(TrF8, <<B7/binary, 69>>, TrUserData)
                     end
                 end;
             _ -> B7
         end,
    B9 = case M of
             #{trace_id := F9} ->
                 begin
                     TrF9 = id(F9, TrUserData),
                     case iolist_size(TrF9) of
                         0 -> B8;
                         _ -> e_type_bytes(TrF9, <<B8/binary, 74>>, TrUserData)
                     end
                 end;
             _ -> B8
         end,
    case M of
        #{span_id := F10} ->
            begin
                TrF10 = id(F10, TrUserData),
                case iolist_size(TrF10) of
                    0 -> B9;
                    _ -> e_type_bytes(TrF10, <<B9/binary, 82>>, TrUserData)
                end
            end;
        _ -> B9
    end.

encode_msg_any_value(Msg, TrUserData) -> encode_msg_any_value(Msg, <<>>, TrUserData).


encode_msg_any_value(#{} = M, Bin, TrUserData) ->
    case M of
        #{value := F1} ->
            case id(F1, TrUserData) of
                {string_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_string(TrTF1, <<Bin/binary, 10>>, TrUserData) end;
                {bool_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_bool(TrTF1, <<Bin/binary, 16>>, TrUserData) end;
                {int_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_int64(TrTF1, <<Bin/binary, 24>>, TrUserData) end;
                {double_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_double(TrTF1, <<Bin/binary, 33>>, TrUserData) end;
                {array_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_mfield_any_value_array_value(TrTF1, <<Bin/binary, 42>>, TrUserData) end;
                {kvlist_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_mfield_any_value_kvlist_value(TrTF1, <<Bin/binary, 50>>, TrUserData) end;
                {bytes_value, TF1} -> begin TrTF1 = id(TF1, TrUserData), e_type_bytes(TrTF1, <<Bin/binary, 58>>, TrUserData) end
            end;
        _ -> Bin
    end.

encode_msg_array_value(Msg, TrUserData) -> encode_msg_array_value(Msg, <<>>, TrUserData).


encode_msg_array_value(#{} = M, Bin, TrUserData) ->
    case M of
        #{values := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_array_value_values(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_key_value_list(Msg, TrUserData) -> encode_msg_key_value_list(Msg, <<>>, TrUserData).


encode_msg_key_value_list(#{} = M, Bin, TrUserData) ->
    case M of
        #{values := F1} ->
            TrF1 = id(F1, TrUserData),
            if TrF1 == [] -> Bin;
               true -> e_field_key_value_list_values(TrF1, Bin, TrUserData)
            end;
        _ -> Bin
    end.

encode_msg_key_value(Msg, TrUserData) -> encode_msg_key_value(Msg, <<>>, TrUserData).


encode_msg_key_value(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{key := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false -> e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    case M of
        #{value := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                if TrF2 =:= undefined -> B1;
                   true -> e_mfield_key_value_value(TrF2, <<B1/binary, 18>>, TrUserData)
                end
            end;
        _ -> B1
    end.

encode_msg_instrumentation_scope(Msg, TrUserData) -> encode_msg_instrumentation_scope(Msg, <<>>, TrUserData).


encode_msg_instrumentation_scope(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{name := F1} ->
                 begin
                     TrF1 = id(F1, TrUserData),
                     case is_empty_string(TrF1) of
                         true -> Bin;
                         false -> e_type_string(TrF1, <<Bin/binary, 10>>, TrUserData)
                     end
                 end;
             _ -> Bin
         end,
    B2 = case M of
             #{version := F2} ->
                 begin
                     TrF2 = id(F2, TrUserData),
                     case is_empty_string(TrF2) of
                         true -> B1;
                         false -> e_type_string(TrF2, <<B1/binary, 18>>, TrUserData)
                     end
                 end;
             _ -> B1
         end,
    B3 = case M of
             #{attributes := F3} ->
                 TrF3 = id(F3, TrUserData),
                 if TrF3 == [] -> B2;
                    true -> e_field_instrumentation_scope_attributes(TrF3, B2, TrUserData)
                 end;
             _ -> B2
         end,
    case M of
        #{dropped_attributes_count := F4} ->
            begin
                TrF4 = id(F4, TrUserData),
                if TrF4 =:= 0 -> B3;
                   true -> e_varint(TrF4, <<B3/binary, 32>>, TrUserData)
                end
            end;
        _ -> B3
    end.

encode_msg_resource(Msg, TrUserData) -> encode_msg_resource(Msg, <<>>, TrUserData).


encode_msg_resource(#{} = M, Bin, TrUserData) ->
    B1 = case M of
             #{attributes := F1} ->
                 TrF1 = id(F1, TrUserData),
                 if TrF1 == [] -> Bin;
                    true -> e_field_resource_attributes(TrF1, Bin, TrUserData)
                 end;
             _ -> Bin
         end,
    case M of
        #{dropped_attributes_count := F2} ->
            begin
                TrF2 = id(F2, TrUserData),
                if TrF2 =:= 0 -> B1;
                   true -> e_varint(TrF2, <<B1/binary, 16>>, TrUserData)
                end
            end;
        _ -> B1
    end.

e_mfield_export_logs_service_request_resource_logs(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_resource_logs(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_export_logs_service_request_resource_logs([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_export_logs_service_request_resource_logs(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_export_logs_service_request_resource_logs(Rest, Bin3, TrUserData);
e_field_export_logs_service_request_resource_logs([], Bin, _TrUserData) -> Bin.

e_mfield_export_logs_service_response_partial_success(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_export_logs_partial_success(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_logs_data_resource_logs(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_resource_logs(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_logs_data_resource_logs([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_logs_data_resource_logs(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_logs_data_resource_logs(Rest, Bin3, TrUserData);
e_field_logs_data_resource_logs([], Bin, _TrUserData) -> Bin.

e_mfield_resource_logs_resource(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_resource(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_resource_logs_scope_logs(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_scope_logs(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_resource_logs_scope_logs([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 18>>,
    Bin3 = e_mfield_resource_logs_scope_logs(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_resource_logs_scope_logs(Rest, Bin3, TrUserData);
e_field_resource_logs_scope_logs([], Bin, _TrUserData) -> Bin.

e_mfield_scope_logs_scope(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_instrumentation_scope(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_scope_logs_log_records(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_log_record(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_scope_logs_log_records([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 18>>,
    Bin3 = e_mfield_scope_logs_log_records(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_scope_logs_log_records(Rest, Bin3, TrUserData);
e_field_scope_logs_log_records([], Bin, _TrUserData) -> Bin.

e_mfield_log_record_body(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_any_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_log_record_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_log_record_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 50>>,
    Bin3 = e_mfield_log_record_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_log_record_attributes(Rest, Bin3, TrUserData);
e_field_log_record_attributes([], Bin, _TrUserData) -> Bin.

e_mfield_any_value_array_value(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_array_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_any_value_kvlist_value(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value_list(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_array_value_values(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_any_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_array_value_values([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_array_value_values(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_array_value_values(Rest, Bin3, TrUserData);
e_field_array_value_values([], Bin, _TrUserData) -> Bin.

e_mfield_key_value_list_values(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_key_value_list_values([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_key_value_list_values(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_key_value_list_values(Rest, Bin3, TrUserData);
e_field_key_value_list_values([], Bin, _TrUserData) -> Bin.

e_mfield_key_value_value(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_any_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_mfield_instrumentation_scope_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_instrumentation_scope_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 26>>,
    Bin3 = e_mfield_instrumentation_scope_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_instrumentation_scope_attributes(Rest, Bin3, TrUserData);
e_field_instrumentation_scope_attributes([], Bin, _TrUserData) -> Bin.

e_mfield_resource_attributes(Msg, Bin, TrUserData) ->
    SubBin = encode_msg_key_value(Msg, <<>>, TrUserData),
    Bin2 = e_varint(byte_size(SubBin), Bin),
    <<Bin2/binary, SubBin/binary>>.

e_field_resource_attributes([Elem | Rest], Bin, TrUserData) ->
    Bin2 = <<Bin/binary, 10>>,
    Bin3 = e_mfield_resource_attributes(id(Elem, TrUserData), Bin2, TrUserData),
    e_field_resource_attributes(Rest, Bin3, TrUserData);
e_field_resource_attributes([], Bin, _TrUserData) -> Bin.

'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_UNSPECIFIED', Bin, _TrUserData) -> <<Bin/binary, 0>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE', Bin, _TrUserData) -> <<Bin/binary, 1>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE2', Bin, _TrUserData) -> <<Bin/binary, 2>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE3', Bin, _TrUserData) -> <<Bin/binary, 3>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE4', Bin, _TrUserData) -> <<Bin/binary, 4>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG', Bin, _TrUserData) -> <<Bin/binary, 5>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG2', Bin, _TrUserData) -> <<Bin/binary, 6>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG3', Bin, _TrUserData) -> <<Bin/binary, 7>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG4', Bin, _TrUserData) -> <<Bin/binary, 8>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO', Bin, _TrUserData) -> <<Bin/binary, 9>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO2', Bin, _TrUserData) -> <<Bin/binary, 10>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO3', Bin, _TrUserData) -> <<Bin/binary, 11>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO4', Bin, _TrUserData) -> <<Bin/binary, 12>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN', Bin, _TrUserData) -> <<Bin/binary, 13>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN2', Bin, _TrUserData) -> <<Bin/binary, 14>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN3', Bin, _TrUserData) -> <<Bin/binary, 15>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN4', Bin, _TrUserData) -> <<Bin/binary, 16>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR', Bin, _TrUserData) -> <<Bin/binary, 17>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR2', Bin, _TrUserData) -> <<Bin/binary, 18>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR3', Bin, _TrUserData) -> <<Bin/binary, 19>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR4', Bin, _TrUserData) -> <<Bin/binary, 20>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL', Bin, _TrUserData) -> <<Bin/binary, 21>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL2', Bin, _TrUserData) -> <<Bin/binary, 22>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL3', Bin, _TrUserData) -> <<Bin/binary, 23>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL4', Bin, _TrUserData) -> <<Bin/binary, 24>>;
'e_enum_opentelemetry.proto.logs.v1.SeverityNumber'(V, Bin, _TrUserData) -> e_varint(V, Bin).

-compile({nowarn_unused_function,e_type_sint/3}).
e_type_sint(Value, Bin, _TrUserData) when Value >= 0 -> e_varint(Value * 2, Bin);
e_type_sint(Value, Bin, _TrUserData) -> e_varint(Value * -2 - 1, Bin).

-compile({nowarn_unused_function,e_type_int32/3}).
e_type_int32(Value, Bin, _TrUserData) when 0 =< Value, Value =< 127 -> <<Bin/binary, Value>>;
e_type_int32(Value, Bin, _TrUserData) ->
    <<N:64/unsigned-native>> = <<Value:64/signed-native>>,
    e_varint(N, Bin).

-compile({nowarn_unused_function,e_type_int64/3}).
e_type_int64(Value, Bin, _TrUserData) when 0 =< Value, Value =< 127 -> <<Bin/binary, Value>>;
e_type_int64(Value, Bin, _TrUserData) ->
    <<N:64/unsigned-native>> = <<Value:64/signed-native>>,
    e_varint(N, Bin).

-compile({nowarn_unused_function,e_type_bool/3}).
e_type_bool(true, Bin, _TrUserData) -> <<Bin/binary, 1>>;
e_type_bool(false, Bin, _TrUserData) -> <<Bin/binary, 0>>;
e_type_bool(1, Bin, _TrUserData) -> <<Bin/binary, 1>>;
e_type_bool(0, Bin, _TrUserData) -> <<Bin/binary, 0>>.

-compile({nowarn_unused_function,e_type_string/3}).
e_type_string(S, Bin, _TrUserData) ->
    Utf8 = unicode:characters_to_binary(S),
    Bin2 = e_varint(byte_size(Utf8), Bin),
    <<Bin2/binary, Utf8/binary>>.

-compile({nowarn_unused_function,e_type_bytes/3}).
e_type_bytes(Bytes, Bin, _TrUserData) when is_binary(Bytes) ->
    Bin2 = e_varint(byte_size(Bytes), Bin),
    <<Bin2/binary, Bytes/binary>>;
e_type_bytes(Bytes, Bin, _TrUserData) when is_list(Bytes) ->
    BytesBin = iolist_to_binary(Bytes),
    Bin2 = e_varint(byte_size(BytesBin), Bin),
    <<Bin2/binary, BytesBin/binary>>.

-compile({nowarn_unused_function,e_type_fixed32/3}).
e_type_fixed32(Value, Bin, _TrUserData) -> <<Bin/binary, Value:32/little>>.

-compile({nowarn_unused_function,e_type_sfixed32/3}).
e_type_sfixed32(Value, Bin, _TrUserData) -> <<Bin/binary, Value:32/little-signed>>.

-compile({nowarn_unused_function,e_type_fixed64/3}).
e_type_fixed64(Value, Bin, _TrUserData) -> <<Bin/binary, Value:64/little>>.

-compile({nowarn_unused_function,e_type_sfixed64/3}).
e_type_sfixed64(Value, Bin, _TrUserData) -> <<Bin/binary, Value:64/little-signed>>.

-compile({nowarn_unused_function,e_type_float/3}).
e_type_float(V, Bin, _) when is_number(V) -> <<Bin/binary, V:32/little-float>>;
e_type_float(infinity, Bin, _) -> <<Bin/binary, 0:16, 128, 127>>;
e_type_float('-infinity', Bin, _) -> <<Bin/binary, 0:16, 128, 255>>;
e_type_float(nan, Bin, _) -> <<Bin/binary, 0:16, 192, 127>>.

-compile({nowarn_unused_function,e_type_double/3}).
e_type_double(V, Bin, _) when is_number(V) -> <<Bin/binary, V:64/little-float>>;
e_type_double(infinity, Bin, _) -> <<Bin/binary, 0:48, 240, 127>>;
e_type_double('-infinity', Bin, _) -> <<Bin/binary, 0:48, 240, 255>>;
e_type_double(nan, Bin, _) -> <<Bin/binary, 0:48, 248, 127>>.

-compile({nowarn_unused_function,e_unknown_elems/2}).
e_unknown_elems([Elem | Rest], Bin) ->
    BinR = case Elem of
               {varint, FNum, N} ->
                   BinF = e_varint(FNum bsl 3, Bin),
                   e_varint(N, BinF);
               {length_delimited, FNum, Data} ->
                   BinF = e_varint(FNum bsl 3 bor 2, Bin),
                   BinL = e_varint(byte_size(Data), BinF),
                   <<BinL/binary, Data/binary>>;
               {group, FNum, GroupFields} ->
                   Bin1 = e_varint(FNum bsl 3 bor 3, Bin),
                   Bin2 = e_unknown_elems(GroupFields, Bin1),
                   e_varint(FNum bsl 3 bor 4, Bin2);
               {fixed32, FNum, V} ->
                   BinF = e_varint(FNum bsl 3 bor 5, Bin),
                   <<BinF/binary, V:32/little>>;
               {fixed64, FNum, V} ->
                   BinF = e_varint(FNum bsl 3 bor 1, Bin),
                   <<BinF/binary, V:64/little>>
           end,
    e_unknown_elems(Rest, BinR);
e_unknown_elems([], Bin) -> Bin.

-compile({nowarn_unused_function,e_varint/3}).
e_varint(N, Bin, _TrUserData) -> e_varint(N, Bin).

-compile({nowarn_unused_function,e_varint/2}).
e_varint(N, Bin) when N =< 127 -> <<Bin/binary, N>>;
e_varint(N, Bin) ->
    Bin2 = <<Bin/binary, (N band 127 bor 128)>>,
    e_varint(N bsr 7, Bin2).

is_empty_string("") -> true;
is_empty_string(<<>>) -> true;
is_empty_string(L) when is_list(L) -> not string_has_chars(L);
is_empty_string(B) when is_binary(B) -> false.

string_has_chars([C | _]) when is_integer(C) -> true;
string_has_chars([H | T]) ->
    case string_has_chars(H) of
        true -> true;
        false -> string_has_chars(T)
    end;
string_has_chars(B) when is_binary(B), byte_size(B) =/= 0 -> true;
string_has_chars(C) when is_integer(C) -> true;
string_has_chars(<<>>) -> false;
string_has_chars([]) -> false.


decode_msg(Bin, MsgName) when is_binary(Bin) -> decode_msg(Bin, MsgName, []).

decode_msg(Bin, MsgName, Opts) when is_binary(Bin) ->
    TrUserData = proplists:get_value(user_data, Opts),
    decode_msg_1_catch(Bin, MsgName, TrUserData).

-ifdef('OTP_RELEASE').
decode_msg_1_catch(Bin, MsgName, TrUserData) ->
    try decode_msg_2_doit(MsgName, Bin, TrUserData)
    catch
        error:{gpb_error,_}=Reason:StackTrace ->
            erlang:raise(error, Reason, StackTrace);
        Class:Reason:StackTrace -> error({gpb_error,{decoding_failure, {Bin, MsgName, {Class, Reason, StackTrace}}}})
    end.
-else.
decode_msg_1_catch(Bin, MsgName, TrUserData) ->
    try decode_msg_2_doit(MsgName, Bin, TrUserData)
    catch
        error:{gpb_error,_}=Reason ->
            erlang:raise(error, Reason,
                         erlang:get_stacktrace());
        Class:Reason ->
            StackTrace = erlang:get_stacktrace(),
            error({gpb_error,{decoding_failure, {Bin, MsgName, {Class, Reason, StackTrace}}}})
    end.
-endif.

decode_msg_2_doit(export_logs_service_request, Bin, TrUserData) -> id(decode_msg_export_logs_service_request(Bin, TrUserData), TrUserData);
decode_msg_2_doit(export_logs_service_response, Bin, TrUserData) -> id(decode_msg_export_logs_service_response(Bin, TrUserData), TrUserData);
decode_msg_2_doit(export_logs_partial_success, Bin, TrUserData) -> id(decode_msg_export_logs_partial_success(Bin, TrUserData), TrUserData);
decode_msg_2_doit(logs_data, Bin, TrUserData) -> id(decode_msg_logs_data(Bin, TrUserData), TrUserData);
decode_msg_2_doit(resource_logs, Bin, TrUserData) -> id(decode_msg_resource_logs(Bin, TrUserData), TrUserData);
decode_msg_2_doit(scope_logs, Bin, TrUserData) -> id(decode_msg_scope_logs(Bin, TrUserData), TrUserData);
decode_msg_2_doit(log_record, Bin, TrUserData) -> id(decode_msg_log_record(Bin, TrUserData), TrUserData);
decode_msg_2_doit(any_value, Bin, TrUserData) -> id(decode_msg_any_value(Bin, TrUserData), TrUserData);
decode_msg_2_doit(array_value, Bin, TrUserData) -> id(decode_msg_array_value(Bin, TrUserData), TrUserData);
decode_msg_2_doit(key_value_list, Bin, TrUserData) -> id(decode_msg_key_value_list(Bin, TrUserData), TrUserData);
decode_msg_2_doit(key_value, Bin, TrUserData) -> id(decode_msg_key_value(Bin, TrUserData), TrUserData);
decode_msg_2_doit(instrumentation_scope, Bin, TrUserData) -> id(decode_msg_instrumentation_scope(Bin, TrUserData), TrUserData);
decode_msg_2_doit(resource, Bin, TrUserData) -> id(decode_msg_resource(Bin, TrUserData), TrUserData).



decode_msg_export_logs_service_request(Bin, TrUserData) -> dfp_read_field_def_export_logs_service_request(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_export_logs_service_request(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_export_logs_service_request_resource_logs(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_export_logs_service_request(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{resource_logs => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_export_logs_service_request(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_export_logs_service_request(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_export_logs_service_request(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_export_logs_service_request(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_export_logs_service_request(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_export_logs_service_request_resource_logs(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_export_logs_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_export_logs_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_export_logs_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_export_logs_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_export_logs_service_request(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_export_logs_service_request(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{resource_logs => lists_reverse(R1, TrUserData)}
    end.

d_field_export_logs_service_request_resource_logs(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_export_logs_service_request_resource_logs(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_export_logs_service_request_resource_logs(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_resource_logs(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_export_logs_service_request(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_export_logs_service_request(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_export_logs_service_request(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_export_logs_service_request(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_logs_service_request(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_export_logs_service_request(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_export_logs_service_request(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_export_logs_service_request(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_export_logs_service_request(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_export_logs_service_request(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_export_logs_service_request(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_export_logs_service_request(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_logs_service_request(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_export_logs_service_request(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_logs_service_request(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_export_logs_service_response(Bin, TrUserData) -> dfp_read_field_def_export_logs_service_response(Bin, 0, 0, 0, id('$undef', TrUserData), TrUserData).

dfp_read_field_def_export_logs_service_response(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_export_logs_service_response_partial_success(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_export_logs_service_response(<<>>, 0, 0, _, F@_1, _) ->
    S1 = #{},
    if F@_1 == '$undef' -> S1;
       true -> S1#{partial_success => F@_1}
    end;
dfp_read_field_def_export_logs_service_response(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_export_logs_service_response(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_export_logs_service_response(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_export_logs_service_response(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_export_logs_service_response(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_export_logs_service_response_partial_success(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_export_logs_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_export_logs_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_export_logs_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_export_logs_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_export_logs_service_response(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_export_logs_service_response(<<>>, 0, 0, _, F@_1, _) ->
    S1 = #{},
    if F@_1 == '$undef' -> S1;
       true -> S1#{partial_success => F@_1}
    end.

d_field_export_logs_service_response_partial_success(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_export_logs_service_response_partial_success(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_export_logs_service_response_partial_success(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_export_logs_partial_success(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_export_logs_service_response(RestF,
                                                    0,
                                                    0,
                                                    F,
                                                    if Prev == '$undef' -> NewFValue;
                                                       true -> merge_msg_export_logs_partial_success(Prev, NewFValue, TrUserData)
                                                    end,
                                                    TrUserData).

skip_varint_export_logs_service_response(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_export_logs_service_response(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_export_logs_service_response(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_logs_service_response(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_export_logs_service_response(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_export_logs_service_response(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_export_logs_service_response(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_export_logs_service_response(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_export_logs_service_response(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_export_logs_service_response(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_export_logs_service_response(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_logs_service_response(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_export_logs_service_response(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_export_logs_service_response(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_export_logs_partial_success(Bin, TrUserData) -> dfp_read_field_def_export_logs_partial_success(Bin, 0, 0, 0, id(0, TrUserData), id(<<>>, TrUserData), TrUserData).

dfp_read_field_def_export_logs_partial_success(<<8, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_export_logs_partial_success_rejected_log_records(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_export_logs_partial_success(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_export_logs_partial_success_error_message(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_export_logs_partial_success(<<>>, 0, 0, _, F@_1, F@_2, _) -> #{rejected_log_records => F@_1, error_message => F@_2};
dfp_read_field_def_export_logs_partial_success(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_export_logs_partial_success(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_export_logs_partial_success(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_export_logs_partial_success(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_export_logs_partial_success(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        8 -> d_field_export_logs_partial_success_rejected_log_records(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        18 -> d_field_export_logs_partial_success_error_message(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_export_logs_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_export_logs_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_export_logs_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_export_logs_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_export_logs_partial_success(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_export_logs_partial_success(<<>>, 0, 0, _, F@_1, F@_2, _) -> #{rejected_log_records => F@_1, error_message => F@_2}.

d_field_export_logs_partial_success_rejected_log_records(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_export_logs_partial_success_rejected_log_records(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_export_logs_partial_success_rejected_log_records(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = {begin <<Res:64/signed-native>> = <<(X bsl N + Acc):64/unsigned-native>>, id(Res, TrUserData) end, Rest},
    dfp_read_field_def_export_logs_partial_success(RestF, 0, 0, F, NewFValue, F@_2, TrUserData).

d_field_export_logs_partial_success_error_message(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_export_logs_partial_success_error_message(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_export_logs_partial_success_error_message(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_export_logs_partial_success(RestF, 0, 0, F, F@_1, NewFValue, TrUserData).

skip_varint_export_logs_partial_success(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_export_logs_partial_success(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_export_logs_partial_success(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_export_logs_partial_success(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_export_logs_partial_success(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_export_logs_partial_success(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_export_logs_partial_success(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_export_logs_partial_success(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_export_logs_partial_success(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_export_logs_partial_success(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_export_logs_partial_success(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_export_logs_partial_success(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_export_logs_partial_success(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_export_logs_partial_success(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

decode_msg_logs_data(Bin, TrUserData) -> dfp_read_field_def_logs_data(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_logs_data(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_logs_data_resource_logs(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_logs_data(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{resource_logs => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_logs_data(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_logs_data(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_logs_data(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_logs_data(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_logs_data(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_logs_data_resource_logs(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_logs_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_logs_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_logs_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_logs_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_logs_data(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_logs_data(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{resource_logs => lists_reverse(R1, TrUserData)}
    end.

d_field_logs_data_resource_logs(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_logs_data_resource_logs(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_logs_data_resource_logs(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_resource_logs(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_logs_data(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_logs_data(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_logs_data(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_logs_data(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_logs_data(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_logs_data(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_logs_data(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_logs_data(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_logs_data(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_logs_data(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_logs_data(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_logs_data(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_logs_data(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_logs_data(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_logs_data(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_resource_logs(Bin, TrUserData) -> dfp_read_field_def_resource_logs(Bin, 0, 0, 0, id('$undef', TrUserData), id([], TrUserData), id(<<>>, TrUserData), TrUserData).

dfp_read_field_def_resource_logs(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_resource_logs_resource(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_resource_logs(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_resource_logs_scope_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_resource_logs(<<26, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_resource_logs_schema_url(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_resource_logs(<<>>, 0, 0, _, F@_1, R1, F@_3, TrUserData) ->
    S1 = #{schema_url => F@_3},
    S2 = if F@_1 == '$undef' -> S1;
            true -> S1#{resource => F@_1}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{scope_logs => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_resource_logs(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dg_read_field_def_resource_logs(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

dg_read_field_def_resource_logs(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 32 - 7 -> dg_read_field_def_resource_logs(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
dg_read_field_def_resource_logs(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_resource_logs_resource(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        18 -> d_field_resource_logs_scope_logs(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        26 -> d_field_resource_logs_schema_url(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_resource_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                1 -> skip_64_resource_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                2 -> skip_length_delimited_resource_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                3 -> skip_group_resource_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                5 -> skip_32_resource_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData)
            end
    end;
dg_read_field_def_resource_logs(<<>>, 0, 0, _, F@_1, R1, F@_3, TrUserData) ->
    S1 = #{schema_url => F@_3},
    S2 = if F@_1 == '$undef' -> S1;
            true -> S1#{resource => F@_1}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{scope_logs => lists_reverse(R1, TrUserData)}
    end.

d_field_resource_logs_resource(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_resource_logs_resource(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_resource_logs_resource(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_resource(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_resource_logs(RestF,
                                     0,
                                     0,
                                     F,
                                     if Prev == '$undef' -> NewFValue;
                                        true -> merge_msg_resource(Prev, NewFValue, TrUserData)
                                     end,
                                     F@_2,
                                     F@_3,
                                     TrUserData).

d_field_resource_logs_scope_logs(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_resource_logs_scope_logs(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_resource_logs_scope_logs(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, Prev, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_scope_logs(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_resource_logs(RestF, 0, 0, F, F@_1, cons(NewFValue, Prev, TrUserData), F@_3, TrUserData).

d_field_resource_logs_schema_url(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_resource_logs_schema_url(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_resource_logs_schema_url(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_resource_logs(RestF, 0, 0, F, F@_1, F@_2, NewFValue, TrUserData).

skip_varint_resource_logs(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> skip_varint_resource_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
skip_varint_resource_logs(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_resource_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_length_delimited_resource_logs(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> skip_length_delimited_resource_logs(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
skip_length_delimited_resource_logs(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_resource_logs(Rest2, 0, 0, F, F@_1, F@_2, F@_3, TrUserData).

skip_group_resource_logs(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_resource_logs(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, TrUserData).

skip_32_resource_logs(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_resource_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_64_resource_logs(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_resource_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

decode_msg_scope_logs(Bin, TrUserData) -> dfp_read_field_def_scope_logs(Bin, 0, 0, 0, id('$undef', TrUserData), id([], TrUserData), id(<<>>, TrUserData), TrUserData).

dfp_read_field_def_scope_logs(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_scope_logs_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_scope_logs(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_scope_logs_log_records(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_scope_logs(<<26, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> d_field_scope_logs_schema_url(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
dfp_read_field_def_scope_logs(<<>>, 0, 0, _, F@_1, R1, F@_3, TrUserData) ->
    S1 = #{schema_url => F@_3},
    S2 = if F@_1 == '$undef' -> S1;
            true -> S1#{scope => F@_1}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{log_records => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_scope_logs(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dg_read_field_def_scope_logs(Other, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

dg_read_field_def_scope_logs(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 32 - 7 -> dg_read_field_def_scope_logs(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
dg_read_field_def_scope_logs(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_scope_logs_scope(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        18 -> d_field_scope_logs_log_records(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        26 -> d_field_scope_logs_schema_url(Rest, 0, 0, 0, F@_1, F@_2, F@_3, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_scope_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                1 -> skip_64_scope_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                2 -> skip_length_delimited_scope_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                3 -> skip_group_scope_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData);
                5 -> skip_32_scope_logs(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, TrUserData)
            end
    end;
dg_read_field_def_scope_logs(<<>>, 0, 0, _, F@_1, R1, F@_3, TrUserData) ->
    S1 = #{schema_url => F@_3},
    S2 = if F@_1 == '$undef' -> S1;
            true -> S1#{scope => F@_1}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{log_records => lists_reverse(R1, TrUserData)}
    end.

d_field_scope_logs_scope(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_scope_logs_scope(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_scope_logs_scope(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_instrumentation_scope(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_scope_logs(RestF,
                                  0,
                                  0,
                                  F,
                                  if Prev == '$undef' -> NewFValue;
                                     true -> merge_msg_instrumentation_scope(Prev, NewFValue, TrUserData)
                                  end,
                                  F@_2,
                                  F@_3,
                                  TrUserData).

d_field_scope_logs_log_records(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_scope_logs_log_records(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_scope_logs_log_records(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, Prev, F@_3, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_log_record(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_scope_logs(RestF, 0, 0, F, F@_1, cons(NewFValue, Prev, TrUserData), F@_3, TrUserData).

d_field_scope_logs_schema_url(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> d_field_scope_logs_schema_url(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
d_field_scope_logs_schema_url(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_scope_logs(RestF, 0, 0, F, F@_1, F@_2, NewFValue, TrUserData).

skip_varint_scope_logs(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> skip_varint_scope_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData);
skip_varint_scope_logs(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_scope_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_length_delimited_scope_logs(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) when N < 57 -> skip_length_delimited_scope_logs(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, TrUserData);
skip_length_delimited_scope_logs(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_scope_logs(Rest2, 0, 0, F, F@_1, F@_2, F@_3, TrUserData).

skip_group_scope_logs(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_scope_logs(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, TrUserData).

skip_32_scope_logs(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_scope_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

skip_64_scope_logs(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData) -> dfp_read_field_def_scope_logs(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, TrUserData).

decode_msg_log_record(Bin, TrUserData) ->
    dfp_read_field_def_log_record(Bin,
                                  0,
                                  0,
                                  0,
                                  id(0, TrUserData),
                                  id(0, TrUserData),
                                  id('SEVERITY_NUMBER_UNSPECIFIED', TrUserData),
                                  id(<<>>, TrUserData),
                                  id('$undef', TrUserData),
                                  id([], TrUserData),
                                  id(0, TrUserData),
                                  id(0, TrUserData),
                                  id(<<>>, TrUserData),
                                  id(<<>>, TrUserData),
                                  TrUserData).

dfp_read_field_def_log_record(<<9, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<89, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_observed_time_unix_nano(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<16, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_severity_number(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<26, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_severity_text(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<42, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_body(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<50, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_attributes(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<56, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_dropped_attributes_count(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<69, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_flags(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<74, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_trace_id(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<82, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    d_field_log_record_span_id(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dfp_read_field_def_log_record(<<>>, 0, 0, _, F@_1, F@_2, F@_3, F@_4, F@_5, R1, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    S1 = #{time_unix_nano => F@_1, observed_time_unix_nano => F@_2, severity_number => F@_3, severity_text => F@_4, dropped_attributes_count => F@_7, flags => F@_8, trace_id => F@_9, span_id => F@_10},
    S2 = if F@_5 == '$undef' -> S1;
            true -> S1#{body => F@_5}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{attributes => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_log_record(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) -> dg_read_field_def_log_record(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

dg_read_field_def_log_record(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 32 - 7 ->
    dg_read_field_def_log_record(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
dg_read_field_def_log_record(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        9 -> d_field_log_record_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        89 -> d_field_log_record_observed_time_unix_nano(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        16 -> d_field_log_record_severity_number(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        26 -> d_field_log_record_severity_text(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        42 -> d_field_log_record_body(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        50 -> d_field_log_record_attributes(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        56 -> d_field_log_record_dropped_attributes_count(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        69 -> d_field_log_record_flags(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        74 -> d_field_log_record_trace_id(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        82 -> d_field_log_record_span_id(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_log_record(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
                1 -> skip_64_log_record(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
                2 -> skip_length_delimited_log_record(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
                3 -> skip_group_log_record(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
                5 -> skip_32_log_record(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData)
            end
    end;
dg_read_field_def_log_record(<<>>, 0, 0, _, F@_1, F@_2, F@_3, F@_4, F@_5, R1, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    S1 = #{time_unix_nano => F@_1, observed_time_unix_nano => F@_2, severity_number => F@_3, severity_text => F@_4, dropped_attributes_count => F@_7, flags => F@_8, trace_id => F@_9, span_id => F@_10},
    S2 = if F@_5 == '$undef' -> S1;
            true -> S1#{body => F@_5}
         end,
    if R1 == '$undef' -> S2;
       true -> S2#{attributes => lists_reverse(R1, TrUserData)}
    end.

d_field_log_record_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, _, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    dfp_read_field_def_log_record(Rest, Z1, Z2, F, id(Value, TrUserData), F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

d_field_log_record_observed_time_unix_nano(<<Value:64/little, Rest/binary>>, Z1, Z2, F, F@_1, _, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    dfp_read_field_def_log_record(Rest, Z1, Z2, F, F@_1, id(Value, TrUserData), F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

d_field_log_record_severity_number(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 57 ->
    d_field_log_record_severity_number(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
d_field_log_record_severity_number(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, _, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    {NewFValue, RestF} = {id('d_enum_opentelemetry.proto.logs.v1.SeverityNumber'(begin <<Res:32/signed-native>> = <<(X bsl N + Acc):32/unsigned-native>>, id(Res, TrUserData) end), TrUserData), Rest},
    dfp_read_field_def_log_record(RestF, 0, 0, F, F@_1, F@_2, NewFValue, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

d_field_log_record_severity_text(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 57 ->
    d_field_log_record_severity_text(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
d_field_log_record_severity_text(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, _, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_log_record(RestF, 0, 0, F, F@_1, F@_2, F@_3, NewFValue, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

d_field_log_record_body(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 57 ->
    d_field_log_record_body(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
d_field_log_record_body(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, Prev, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_any_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_log_record(RestF,
                                  0,
                                  0,
                                  F,
                                  F@_1,
                                  F@_2,
                                  F@_3,
                                  F@_4,
                                  if Prev == '$undef' -> NewFValue;
                                     true -> merge_msg_any_value(Prev, NewFValue, TrUserData)
                                  end,
                                  F@_6,
                                  F@_7,
                                  F@_8,
                                  F@_9,
                                  F@_10,
                                  TrUserData).

d_field_log_record_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 57 ->
    d_field_log_record_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
d_field_log_record_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, Prev, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_log_record(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, cons(NewFValue, Prev, TrUserData), F@_7, F@_8, F@_9, F@_10, TrUserData).

d_field_log_record_dropped_attributes_count(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 57 ->
    d_field_log_record_dropped_attributes_count(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
d_field_log_record_dropped_attributes_count(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, _, F@_8, F@_9, F@_10, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_log_record(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, NewFValue, F@_8, F@_9, F@_10, TrUserData).

d_field_log_record_flags(<<Value:32/little, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, _, F@_9, F@_10, TrUserData) ->
    dfp_read_field_def_log_record(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, id(Value, TrUserData), F@_9, F@_10, TrUserData).

d_field_log_record_trace_id(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 57 ->
    d_field_log_record_trace_id(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
d_field_log_record_trace_id(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, _, F@_10, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_log_record(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, NewFValue, F@_10, TrUserData).

d_field_log_record_span_id(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 57 ->
    d_field_log_record_span_id(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
d_field_log_record_span_id(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_log_record(RestF, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, NewFValue, TrUserData).

skip_varint_log_record(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) -> skip_varint_log_record(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
skip_varint_log_record(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    dfp_read_field_def_log_record(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

skip_length_delimited_log_record(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) when N < 57 ->
    skip_length_delimited_log_record(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData);
skip_length_delimited_log_record(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_log_record(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

skip_group_log_record(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_log_record(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

skip_32_log_record(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) -> dfp_read_field_def_log_record(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

skip_64_log_record(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData) -> dfp_read_field_def_log_record(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, F@_5, F@_6, F@_7, F@_8, F@_9, F@_10, TrUserData).

decode_msg_any_value(Bin, TrUserData) -> dfp_read_field_def_any_value(Bin, 0, 0, 0, id('$undef', TrUserData), TrUserData).

dfp_read_field_def_any_value(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_string_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<16, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_bool_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<24, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_int_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<33, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_double_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<42, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_array_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<50, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_kvlist_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<58, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_any_value_bytes_value(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_any_value(<<>>, 0, 0, _, F@_1, _) ->
    S1 = #{},
    if F@_1 == '$undef' -> S1;
       true -> S1#{value => F@_1}
    end;
dfp_read_field_def_any_value(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_any_value(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_any_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_any_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_any_value(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_any_value_string_value(Rest, 0, 0, 0, F@_1, TrUserData);
        16 -> d_field_any_value_bool_value(Rest, 0, 0, 0, F@_1, TrUserData);
        24 -> d_field_any_value_int_value(Rest, 0, 0, 0, F@_1, TrUserData);
        33 -> d_field_any_value_double_value(Rest, 0, 0, 0, F@_1, TrUserData);
        42 -> d_field_any_value_array_value(Rest, 0, 0, 0, F@_1, TrUserData);
        50 -> d_field_any_value_kvlist_value(Rest, 0, 0, 0, F@_1, TrUserData);
        58 -> d_field_any_value_bytes_value(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_any_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_any_value(<<>>, 0, 0, _, F@_1, _) ->
    S1 = #{},
    if F@_1 == '$undef' -> S1;
       true -> S1#{value => F@_1}
    end.

d_field_any_value_string_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_string_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_string_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_any_value(RestF, 0, 0, F, id({string_value, NewFValue}, TrUserData), TrUserData).

d_field_any_value_bool_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_bool_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_bool_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, TrUserData) ->
    {NewFValue, RestF} = {id(X bsl N + Acc =/= 0, TrUserData), Rest},
    dfp_read_field_def_any_value(RestF, 0, 0, F, id({bool_value, NewFValue}, TrUserData), TrUserData).

d_field_any_value_int_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_int_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_int_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, TrUserData) ->
    {NewFValue, RestF} = {begin <<Res:64/signed-native>> = <<(X bsl N + Acc):64/unsigned-native>>, id(Res, TrUserData) end, Rest},
    dfp_read_field_def_any_value(RestF, 0, 0, F, id({int_value, NewFValue}, TrUserData), TrUserData).

d_field_any_value_double_value(<<0:48, 240, 127, Rest/binary>>, Z1, Z2, F, _, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, id({double_value, id(infinity, TrUserData)}, TrUserData), TrUserData);
d_field_any_value_double_value(<<0:48, 240, 255, Rest/binary>>, Z1, Z2, F, _, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, id({double_value, id('-infinity', TrUserData)}, TrUserData), TrUserData);
d_field_any_value_double_value(<<_:48, 15:4, _:4, _:1, 127:7, Rest/binary>>, Z1, Z2, F, _, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, id({double_value, id(nan, TrUserData)}, TrUserData), TrUserData);
d_field_any_value_double_value(<<Value:64/little-float, Rest/binary>>, Z1, Z2, F, _, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, id({double_value, id(Value, TrUserData)}, TrUserData), TrUserData).

d_field_any_value_array_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_array_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_array_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_array_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_any_value(RestF,
                                 0,
                                 0,
                                 F,
                                 case Prev of
                                     '$undef' -> id({array_value, NewFValue}, TrUserData);
                                     {array_value, MVPrev} -> id({array_value, merge_msg_array_value(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                     _ -> id({array_value, NewFValue}, TrUserData)
                                 end,
                                 TrUserData).

d_field_any_value_kvlist_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_kvlist_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_kvlist_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value_list(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_any_value(RestF,
                                 0,
                                 0,
                                 F,
                                 case Prev of
                                     '$undef' -> id({kvlist_value, NewFValue}, TrUserData);
                                     {kvlist_value, MVPrev} -> id({kvlist_value, merge_msg_key_value_list(MVPrev, NewFValue, TrUserData)}, TrUserData);
                                     _ -> id({kvlist_value, NewFValue}, TrUserData)
                                 end,
                                 TrUserData).

d_field_any_value_bytes_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_any_value_bytes_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_any_value_bytes_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_any_value(RestF, 0, 0, F, id({bytes_value, NewFValue}, TrUserData), TrUserData).

skip_varint_any_value(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_any_value(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_any_value(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_any_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_any_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_any_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_any_value(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_any_value(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_any_value(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_any_value(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_any_value(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_any_value(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_array_value(Bin, TrUserData) -> dfp_read_field_def_array_value(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_array_value(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_array_value_values(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_array_value(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{values => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_array_value(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_array_value(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_array_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_array_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_array_value(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_array_value_values(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_array_value(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_array_value(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{values => lists_reverse(R1, TrUserData)}
    end.

d_field_array_value_values(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_array_value_values(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_array_value_values(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_any_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_array_value(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_array_value(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_array_value(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_array_value(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_array_value(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_array_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_array_value(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_array_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_array_value(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_array_value(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_array_value(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_array_value(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_array_value(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_array_value(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_array_value(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_key_value_list(Bin, TrUserData) -> dfp_read_field_def_key_value_list(Bin, 0, 0, 0, id([], TrUserData), TrUserData).

dfp_read_field_def_key_value_list(<<10, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> d_field_key_value_list_values(Rest, Z1, Z2, F, F@_1, TrUserData);
dfp_read_field_def_key_value_list(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{values => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_key_value_list(Other, Z1, Z2, F, F@_1, TrUserData) -> dg_read_field_def_key_value_list(Other, Z1, Z2, F, F@_1, TrUserData).

dg_read_field_def_key_value_list(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 32 - 7 -> dg_read_field_def_key_value_list(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
dg_read_field_def_key_value_list(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_key_value_list_values(Rest, 0, 0, 0, F@_1, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                1 -> skip_64_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                2 -> skip_length_delimited_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                3 -> skip_group_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData);
                5 -> skip_32_key_value_list(Rest, 0, 0, Key bsr 3, F@_1, TrUserData)
            end
    end;
dg_read_field_def_key_value_list(<<>>, 0, 0, _, R1, TrUserData) ->
    S1 = #{},
    if R1 == '$undef' -> S1;
       true -> S1#{values => lists_reverse(R1, TrUserData)}
    end.

d_field_key_value_list_values(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> d_field_key_value_list_values(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
d_field_key_value_list_values(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_key_value_list(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), TrUserData).

skip_varint_key_value_list(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> skip_varint_key_value_list(Rest, Z1, Z2, F, F@_1, TrUserData);
skip_varint_key_value_list(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_key_value_list(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_length_delimited_key_value_list(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) when N < 57 -> skip_length_delimited_key_value_list(Rest, N + 7, X bsl N + Acc, F, F@_1, TrUserData);
skip_length_delimited_key_value_list(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_key_value_list(Rest2, 0, 0, F, F@_1, TrUserData).

skip_group_key_value_list(Bin, _, Z2, FNum, F@_1, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_key_value_list(Rest, 0, Z2, FNum, F@_1, TrUserData).

skip_32_key_value_list(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_key_value_list(Rest, Z1, Z2, F, F@_1, TrUserData).

skip_64_key_value_list(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, TrUserData) -> dfp_read_field_def_key_value_list(Rest, Z1, Z2, F, F@_1, TrUserData).

decode_msg_key_value(Bin, TrUserData) -> dfp_read_field_def_key_value(Bin, 0, 0, 0, id(<<>>, TrUserData), id('$undef', TrUserData), TrUserData).

dfp_read_field_def_key_value(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_key_value_key(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_key_value(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_key_value_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_key_value(<<>>, 0, 0, _, F@_1, F@_2, _) ->
    S1 = #{key => F@_1},
    if F@_2 == '$undef' -> S1;
       true -> S1#{value => F@_2}
    end;
dfp_read_field_def_key_value(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_key_value(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_key_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_key_value(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_key_value(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_key_value_key(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        18 -> d_field_key_value_value(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_key_value(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_key_value(<<>>, 0, 0, _, F@_1, F@_2, _) ->
    S1 = #{key => F@_1},
    if F@_2 == '$undef' -> S1;
       true -> S1#{value => F@_2}
    end.

d_field_key_value_key(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_key_value_key(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_key_value_key(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_key_value(RestF, 0, 0, F, NewFValue, F@_2, TrUserData).

d_field_key_value_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_key_value_value(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_key_value_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, Prev, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_any_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_key_value(RestF,
                                 0,
                                 0,
                                 F,
                                 F@_1,
                                 if Prev == '$undef' -> NewFValue;
                                    true -> merge_msg_any_value(Prev, NewFValue, TrUserData)
                                 end,
                                 TrUserData).

skip_varint_key_value(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_key_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_key_value(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_key_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_key_value(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_key_value(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_key_value(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_key_value(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_key_value(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_key_value(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_key_value(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_key_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_key_value(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_key_value(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

decode_msg_instrumentation_scope(Bin, TrUserData) -> dfp_read_field_def_instrumentation_scope(Bin, 0, 0, 0, id(<<>>, TrUserData), id(<<>>, TrUserData), id([], TrUserData), id(0, TrUserData), TrUserData).

dfp_read_field_def_instrumentation_scope(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_instrumentation_scope_name(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_instrumentation_scope(<<18, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_instrumentation_scope_version(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_instrumentation_scope(<<26, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_instrumentation_scope_attributes(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_instrumentation_scope(<<32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> d_field_instrumentation_scope_dropped_attributes_count(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dfp_read_field_def_instrumentation_scope(<<>>, 0, 0, _, F@_1, F@_2, R1, F@_4, TrUserData) ->
    S1 = #{name => F@_1, version => F@_2, dropped_attributes_count => F@_4},
    if R1 == '$undef' -> S1;
       true -> S1#{attributes => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_instrumentation_scope(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dg_read_field_def_instrumentation_scope(Other, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

dg_read_field_def_instrumentation_scope(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 32 - 7 -> dg_read_field_def_instrumentation_scope(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
dg_read_field_def_instrumentation_scope(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_instrumentation_scope_name(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        18 -> d_field_instrumentation_scope_version(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        26 -> d_field_instrumentation_scope_attributes(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        32 -> d_field_instrumentation_scope_dropped_attributes_count(Rest, 0, 0, 0, F@_1, F@_2, F@_3, F@_4, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                1 -> skip_64_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                2 -> skip_length_delimited_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                3 -> skip_group_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData);
                5 -> skip_32_instrumentation_scope(Rest, 0, 0, Key bsr 3, F@_1, F@_2, F@_3, F@_4, TrUserData)
            end
    end;
dg_read_field_def_instrumentation_scope(<<>>, 0, 0, _, F@_1, F@_2, R1, F@_4, TrUserData) ->
    S1 = #{name => F@_1, version => F@_2, dropped_attributes_count => F@_4},
    if R1 == '$undef' -> S1;
       true -> S1#{attributes => lists_reverse(R1, TrUserData)}
    end.

d_field_instrumentation_scope_name(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_instrumentation_scope_name(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_instrumentation_scope_name(<<0:1, X:7, Rest/binary>>, N, Acc, F, _, F@_2, F@_3, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_instrumentation_scope(RestF, 0, 0, F, NewFValue, F@_2, F@_3, F@_4, TrUserData).

d_field_instrumentation_scope_version(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_instrumentation_scope_version(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_instrumentation_scope_version(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, F@_3, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bytes:Len/binary, Rest2/binary>> = Rest, Bytes2 = binary:copy(Bytes), {id(Bytes2, TrUserData), Rest2} end,
    dfp_read_field_def_instrumentation_scope(RestF, 0, 0, F, F@_1, NewFValue, F@_3, F@_4, TrUserData).

d_field_instrumentation_scope_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> d_field_instrumentation_scope_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_instrumentation_scope_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, Prev, F@_4, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_instrumentation_scope(RestF, 0, 0, F, F@_1, F@_2, cons(NewFValue, Prev, TrUserData), F@_4, TrUserData).

d_field_instrumentation_scope_dropped_attributes_count(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 ->
    d_field_instrumentation_scope_dropped_attributes_count(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
d_field_instrumentation_scope_dropped_attributes_count(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, _, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_instrumentation_scope(RestF, 0, 0, F, F@_1, F@_2, F@_3, NewFValue, TrUserData).

skip_varint_instrumentation_scope(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> skip_varint_instrumentation_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
skip_varint_instrumentation_scope(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_instrumentation_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_length_delimited_instrumentation_scope(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) when N < 57 -> skip_length_delimited_instrumentation_scope(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData);
skip_length_delimited_instrumentation_scope(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_instrumentation_scope(Rest2, 0, 0, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_group_instrumentation_scope(Bin, _, Z2, FNum, F@_1, F@_2, F@_3, F@_4, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_instrumentation_scope(Rest, 0, Z2, FNum, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_32_instrumentation_scope(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_instrumentation_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

skip_64_instrumentation_scope(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData) -> dfp_read_field_def_instrumentation_scope(Rest, Z1, Z2, F, F@_1, F@_2, F@_3, F@_4, TrUserData).

decode_msg_resource(Bin, TrUserData) -> dfp_read_field_def_resource(Bin, 0, 0, 0, id([], TrUserData), id(0, TrUserData), TrUserData).

dfp_read_field_def_resource(<<10, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_resource_attributes(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_resource(<<16, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> d_field_resource_dropped_attributes_count(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
dfp_read_field_def_resource(<<>>, 0, 0, _, R1, F@_2, TrUserData) ->
    S1 = #{dropped_attributes_count => F@_2},
    if R1 == '$undef' -> S1;
       true -> S1#{attributes => lists_reverse(R1, TrUserData)}
    end;
dfp_read_field_def_resource(Other, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dg_read_field_def_resource(Other, Z1, Z2, F, F@_1, F@_2, TrUserData).

dg_read_field_def_resource(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 32 - 7 -> dg_read_field_def_resource(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
dg_read_field_def_resource(<<0:1, X:7, Rest/binary>>, N, Acc, _, F@_1, F@_2, TrUserData) ->
    Key = X bsl N + Acc,
    case Key of
        10 -> d_field_resource_attributes(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        16 -> d_field_resource_dropped_attributes_count(Rest, 0, 0, 0, F@_1, F@_2, TrUserData);
        _ ->
            case Key band 7 of
                0 -> skip_varint_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                1 -> skip_64_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                2 -> skip_length_delimited_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                3 -> skip_group_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData);
                5 -> skip_32_resource(Rest, 0, 0, Key bsr 3, F@_1, F@_2, TrUserData)
            end
    end;
dg_read_field_def_resource(<<>>, 0, 0, _, R1, F@_2, TrUserData) ->
    S1 = #{dropped_attributes_count => F@_2},
    if R1 == '$undef' -> S1;
       true -> S1#{attributes => lists_reverse(R1, TrUserData)}
    end.

d_field_resource_attributes(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_resource_attributes(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_resource_attributes(<<0:1, X:7, Rest/binary>>, N, Acc, F, Prev, F@_2, TrUserData) ->
    {NewFValue, RestF} = begin Len = X bsl N + Acc, <<Bs:Len/binary, Rest2/binary>> = Rest, {id(decode_msg_key_value(Bs, TrUserData), TrUserData), Rest2} end,
    dfp_read_field_def_resource(RestF, 0, 0, F, cons(NewFValue, Prev, TrUserData), F@_2, TrUserData).

d_field_resource_dropped_attributes_count(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> d_field_resource_dropped_attributes_count(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
d_field_resource_dropped_attributes_count(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, _, TrUserData) ->
    {NewFValue, RestF} = {id((X bsl N + Acc) band 4294967295, TrUserData), Rest},
    dfp_read_field_def_resource(RestF, 0, 0, F, F@_1, NewFValue, TrUserData).

skip_varint_resource(<<1:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> skip_varint_resource(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData);
skip_varint_resource(<<0:1, _:7, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_resource(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_length_delimited_resource(<<1:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) when N < 57 -> skip_length_delimited_resource(Rest, N + 7, X bsl N + Acc, F, F@_1, F@_2, TrUserData);
skip_length_delimited_resource(<<0:1, X:7, Rest/binary>>, N, Acc, F, F@_1, F@_2, TrUserData) ->
    Length = X bsl N + Acc,
    <<_:Length/binary, Rest2/binary>> = Rest,
    dfp_read_field_def_resource(Rest2, 0, 0, F, F@_1, F@_2, TrUserData).

skip_group_resource(Bin, _, Z2, FNum, F@_1, F@_2, TrUserData) ->
    {_, Rest} = read_group(Bin, FNum),
    dfp_read_field_def_resource(Rest, 0, Z2, FNum, F@_1, F@_2, TrUserData).

skip_32_resource(<<_:32, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_resource(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

skip_64_resource(<<_:64, Rest/binary>>, Z1, Z2, F, F@_1, F@_2, TrUserData) -> dfp_read_field_def_resource(Rest, Z1, Z2, F, F@_1, F@_2, TrUserData).

'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(0) -> 'SEVERITY_NUMBER_UNSPECIFIED';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(1) -> 'SEVERITY_NUMBER_TRACE';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(2) -> 'SEVERITY_NUMBER_TRACE2';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(3) -> 'SEVERITY_NUMBER_TRACE3';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(4) -> 'SEVERITY_NUMBER_TRACE4';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(5) -> 'SEVERITY_NUMBER_DEBUG';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(6) -> 'SEVERITY_NUMBER_DEBUG2';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(7) -> 'SEVERITY_NUMBER_DEBUG3';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(8) -> 'SEVERITY_NUMBER_DEBUG4';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(9) -> 'SEVERITY_NUMBER_INFO';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(10) -> 'SEVERITY_NUMBER_INFO2';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(11) -> 'SEVERITY_NUMBER_INFO3';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(12) -> 'SEVERITY_NUMBER_INFO4';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(13) -> 'SEVERITY_NUMBER_WARN';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(14) -> 'SEVERITY_NUMBER_WARN2';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(15) -> 'SEVERITY_NUMBER_WARN3';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(16) -> 'SEVERITY_NUMBER_WARN4';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(17) -> 'SEVERITY_NUMBER_ERROR';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(18) -> 'SEVERITY_NUMBER_ERROR2';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(19) -> 'SEVERITY_NUMBER_ERROR3';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(20) -> 'SEVERITY_NUMBER_ERROR4';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(21) -> 'SEVERITY_NUMBER_FATAL';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(22) -> 'SEVERITY_NUMBER_FATAL2';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(23) -> 'SEVERITY_NUMBER_FATAL3';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(24) -> 'SEVERITY_NUMBER_FATAL4';
'd_enum_opentelemetry.proto.logs.v1.SeverityNumber'(V) -> V.

read_group(Bin, FieldNum) ->
    {NumBytes, EndTagLen} = read_gr_b(Bin, 0, 0, 0, 0, FieldNum),
    <<Group:NumBytes/binary, _:EndTagLen/binary, Rest/binary>> = Bin,
    {Group, Rest}.

%% Like skipping over fields, but record the total length,
%% Each field is <(FieldNum bsl 3) bor FieldType> ++ <FieldValue>
%% Record the length because varints may be non-optimally encoded.
%%
%% Groups can be nested, but assume the same FieldNum cannot be nested
%% because group field numbers are shared with the rest of the fields
%% numbers. Thus we can search just for an group-end with the same
%% field number.
%%
%% (The only time the same group field number could occur would
%% be in a nested sub message, but then it would be inside a
%% length-delimited entry, which we skip-read by length.)
read_gr_b(<<1:1, X:7, Tl/binary>>, N, Acc, NumBytes, TagLen, FieldNum)
  when N < (32-7) ->
    read_gr_b(Tl, N+7, X bsl N + Acc, NumBytes, TagLen+1, FieldNum);
read_gr_b(<<0:1, X:7, Tl/binary>>, N, Acc, NumBytes, TagLen,
          FieldNum) ->
    Key = X bsl N + Acc,
    TagLen1 = TagLen + 1,
    case {Key bsr 3, Key band 7} of
        {FieldNum, 4} -> % 4 = group_end
            {NumBytes, TagLen1};
        {_, 0} -> % 0 = varint
            read_gr_vi(Tl, 0, NumBytes + TagLen1, FieldNum);
        {_, 1} -> % 1 = bits64
            <<_:64, Tl2/binary>> = Tl,
            read_gr_b(Tl2, 0, 0, NumBytes + TagLen1 + 8, 0, FieldNum);
        {_, 2} -> % 2 = length_delimited
            read_gr_ld(Tl, 0, 0, NumBytes + TagLen1, FieldNum);
        {_, 3} -> % 3 = group_start
            read_gr_b(Tl, 0, 0, NumBytes + TagLen1, 0, FieldNum);
        {_, 4} -> % 4 = group_end
            read_gr_b(Tl, 0, 0, NumBytes + TagLen1, 0, FieldNum);
        {_, 5} -> % 5 = bits32
            <<_:32, Tl2/binary>> = Tl,
            read_gr_b(Tl2, 0, 0, NumBytes + TagLen1 + 4, 0, FieldNum)
    end.

read_gr_vi(<<1:1, _:7, Tl/binary>>, N, NumBytes, FieldNum)
  when N < (64-7) ->
    read_gr_vi(Tl, N+7, NumBytes+1, FieldNum);
read_gr_vi(<<0:1, _:7, Tl/binary>>, _, NumBytes, FieldNum) ->
    read_gr_b(Tl, 0, 0, NumBytes+1, 0, FieldNum).

read_gr_ld(<<1:1, X:7, Tl/binary>>, N, Acc, NumBytes, FieldNum)
  when N < (64-7) ->
    read_gr_ld(Tl, N+7, X bsl N + Acc, NumBytes+1, FieldNum);
read_gr_ld(<<0:1, X:7, Tl/binary>>, N, Acc, NumBytes, FieldNum) ->
    Len = X bsl N + Acc,
    NumBytes1 = NumBytes + 1,
    <<_:Len/binary, Tl2/binary>> = Tl,
    read_gr_b(Tl2, 0, 0, NumBytes1 + Len, 0, FieldNum).

merge_msgs(Prev, New, MsgName) when is_atom(MsgName) -> merge_msgs(Prev, New, MsgName, []).

merge_msgs(Prev, New, MsgName, Opts) ->
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        export_logs_service_request -> merge_msg_export_logs_service_request(Prev, New, TrUserData);
        export_logs_service_response -> merge_msg_export_logs_service_response(Prev, New, TrUserData);
        export_logs_partial_success -> merge_msg_export_logs_partial_success(Prev, New, TrUserData);
        logs_data -> merge_msg_logs_data(Prev, New, TrUserData);
        resource_logs -> merge_msg_resource_logs(Prev, New, TrUserData);
        scope_logs -> merge_msg_scope_logs(Prev, New, TrUserData);
        log_record -> merge_msg_log_record(Prev, New, TrUserData);
        any_value -> merge_msg_any_value(Prev, New, TrUserData);
        array_value -> merge_msg_array_value(Prev, New, TrUserData);
        key_value_list -> merge_msg_key_value_list(Prev, New, TrUserData);
        key_value -> merge_msg_key_value(Prev, New, TrUserData);
        instrumentation_scope -> merge_msg_instrumentation_scope(Prev, New, TrUserData);
        resource -> merge_msg_resource(Prev, New, TrUserData)
    end.

-compile({nowarn_unused_function,merge_msg_export_logs_service_request/3}).
merge_msg_export_logs_service_request(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{resource_logs := PFresource_logs}, #{resource_logs := NFresource_logs}} -> S1#{resource_logs => 'erlang_++'(PFresource_logs, NFresource_logs, TrUserData)};
        {_, #{resource_logs := NFresource_logs}} -> S1#{resource_logs => NFresource_logs};
        {#{resource_logs := PFresource_logs}, _} -> S1#{resource_logs => PFresource_logs};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_export_logs_service_response/3}).
merge_msg_export_logs_service_response(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{partial_success := PFpartial_success}, #{partial_success := NFpartial_success}} -> S1#{partial_success => merge_msg_export_logs_partial_success(PFpartial_success, NFpartial_success, TrUserData)};
        {_, #{partial_success := NFpartial_success}} -> S1#{partial_success => NFpartial_success};
        {#{partial_success := PFpartial_success}, _} -> S1#{partial_success => PFpartial_success};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_export_logs_partial_success/3}).
merge_msg_export_logs_partial_success(PMsg, NMsg, _) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{rejected_log_records := NFrejected_log_records}} -> S1#{rejected_log_records => NFrejected_log_records};
             {#{rejected_log_records := PFrejected_log_records}, _} -> S1#{rejected_log_records => PFrejected_log_records};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{error_message := NFerror_message}} -> S2#{error_message => NFerror_message};
        {#{error_message := PFerror_message}, _} -> S2#{error_message => PFerror_message};
        _ -> S2
    end.

-compile({nowarn_unused_function,merge_msg_logs_data/3}).
merge_msg_logs_data(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{resource_logs := PFresource_logs}, #{resource_logs := NFresource_logs}} -> S1#{resource_logs => 'erlang_++'(PFresource_logs, NFresource_logs, TrUserData)};
        {_, #{resource_logs := NFresource_logs}} -> S1#{resource_logs => NFresource_logs};
        {#{resource_logs := PFresource_logs}, _} -> S1#{resource_logs => PFresource_logs};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_resource_logs/3}).
merge_msg_resource_logs(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{resource := PFresource}, #{resource := NFresource}} -> S1#{resource => merge_msg_resource(PFresource, NFresource, TrUserData)};
             {_, #{resource := NFresource}} -> S1#{resource => NFresource};
             {#{resource := PFresource}, _} -> S1#{resource => PFresource};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {#{scope_logs := PFscope_logs}, #{scope_logs := NFscope_logs}} -> S2#{scope_logs => 'erlang_++'(PFscope_logs, NFscope_logs, TrUserData)};
             {_, #{scope_logs := NFscope_logs}} -> S2#{scope_logs => NFscope_logs};
             {#{scope_logs := PFscope_logs}, _} -> S2#{scope_logs => PFscope_logs};
             {_, _} -> S2
         end,
    case {PMsg, NMsg} of
        {_, #{schema_url := NFschema_url}} -> S3#{schema_url => NFschema_url};
        {#{schema_url := PFschema_url}, _} -> S3#{schema_url => PFschema_url};
        _ -> S3
    end.

-compile({nowarn_unused_function,merge_msg_scope_logs/3}).
merge_msg_scope_logs(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{scope := PFscope}, #{scope := NFscope}} -> S1#{scope => merge_msg_instrumentation_scope(PFscope, NFscope, TrUserData)};
             {_, #{scope := NFscope}} -> S1#{scope => NFscope};
             {#{scope := PFscope}, _} -> S1#{scope => PFscope};
             {_, _} -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {#{log_records := PFlog_records}, #{log_records := NFlog_records}} -> S2#{log_records => 'erlang_++'(PFlog_records, NFlog_records, TrUserData)};
             {_, #{log_records := NFlog_records}} -> S2#{log_records => NFlog_records};
             {#{log_records := PFlog_records}, _} -> S2#{log_records => PFlog_records};
             {_, _} -> S2
         end,
    case {PMsg, NMsg} of
        {_, #{schema_url := NFschema_url}} -> S3#{schema_url => NFschema_url};
        {#{schema_url := PFschema_url}, _} -> S3#{schema_url => PFschema_url};
        _ -> S3
    end.

-compile({nowarn_unused_function,merge_msg_log_record/3}).
merge_msg_log_record(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{time_unix_nano := NFtime_unix_nano}} -> S1#{time_unix_nano => NFtime_unix_nano};
             {#{time_unix_nano := PFtime_unix_nano}, _} -> S1#{time_unix_nano => PFtime_unix_nano};
             _ -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{observed_time_unix_nano := NFobserved_time_unix_nano}} -> S2#{observed_time_unix_nano => NFobserved_time_unix_nano};
             {#{observed_time_unix_nano := PFobserved_time_unix_nano}, _} -> S2#{observed_time_unix_nano => PFobserved_time_unix_nano};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {_, #{severity_number := NFseverity_number}} -> S3#{severity_number => NFseverity_number};
             {#{severity_number := PFseverity_number}, _} -> S3#{severity_number => PFseverity_number};
             _ -> S3
         end,
    S5 = case {PMsg, NMsg} of
             {_, #{severity_text := NFseverity_text}} -> S4#{severity_text => NFseverity_text};
             {#{severity_text := PFseverity_text}, _} -> S4#{severity_text => PFseverity_text};
             _ -> S4
         end,
    S6 = case {PMsg, NMsg} of
             {#{body := PFbody}, #{body := NFbody}} -> S5#{body => merge_msg_any_value(PFbody, NFbody, TrUserData)};
             {_, #{body := NFbody}} -> S5#{body => NFbody};
             {#{body := PFbody}, _} -> S5#{body => PFbody};
             {_, _} -> S5
         end,
    S7 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S6#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S6#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S6#{attributes => PFattributes};
             {_, _} -> S6
         end,
    S8 = case {PMsg, NMsg} of
             {_, #{dropped_attributes_count := NFdropped_attributes_count}} -> S7#{dropped_attributes_count => NFdropped_attributes_count};
             {#{dropped_attributes_count := PFdropped_attributes_count}, _} -> S7#{dropped_attributes_count => PFdropped_attributes_count};
             _ -> S7
         end,
    S9 = case {PMsg, NMsg} of
             {_, #{flags := NFflags}} -> S8#{flags => NFflags};
             {#{flags := PFflags}, _} -> S8#{flags => PFflags};
             _ -> S8
         end,
    S10 = case {PMsg, NMsg} of
              {_, #{trace_id := NFtrace_id}} -> S9#{trace_id => NFtrace_id};
              {#{trace_id := PFtrace_id}, _} -> S9#{trace_id => PFtrace_id};
              _ -> S9
          end,
    case {PMsg, NMsg} of
        {_, #{span_id := NFspan_id}} -> S10#{span_id => NFspan_id};
        {#{span_id := PFspan_id}, _} -> S10#{span_id => PFspan_id};
        _ -> S10
    end.

-compile({nowarn_unused_function,merge_msg_any_value/3}).
merge_msg_any_value(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{value := {array_value, OPFvalue}}, #{value := {array_value, ONFvalue}}} -> S1#{value => {array_value, merge_msg_array_value(OPFvalue, ONFvalue, TrUserData)}};
        {#{value := {kvlist_value, OPFvalue}}, #{value := {kvlist_value, ONFvalue}}} -> S1#{value => {kvlist_value, merge_msg_key_value_list(OPFvalue, ONFvalue, TrUserData)}};
        {_, #{value := NFvalue}} -> S1#{value => NFvalue};
        {#{value := PFvalue}, _} -> S1#{value => PFvalue};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_array_value/3}).
merge_msg_array_value(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{values := PFvalues}, #{values := NFvalues}} -> S1#{values => 'erlang_++'(PFvalues, NFvalues, TrUserData)};
        {_, #{values := NFvalues}} -> S1#{values => NFvalues};
        {#{values := PFvalues}, _} -> S1#{values => PFvalues};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_key_value_list/3}).
merge_msg_key_value_list(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    case {PMsg, NMsg} of
        {#{values := PFvalues}, #{values := NFvalues}} -> S1#{values => 'erlang_++'(PFvalues, NFvalues, TrUserData)};
        {_, #{values := NFvalues}} -> S1#{values => NFvalues};
        {#{values := PFvalues}, _} -> S1#{values => PFvalues};
        {_, _} -> S1
    end.

-compile({nowarn_unused_function,merge_msg_key_value/3}).
merge_msg_key_value(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{key := NFkey}} -> S1#{key => NFkey};
             {#{key := PFkey}, _} -> S1#{key => PFkey};
             _ -> S1
         end,
    case {PMsg, NMsg} of
        {#{value := PFvalue}, #{value := NFvalue}} -> S2#{value => merge_msg_any_value(PFvalue, NFvalue, TrUserData)};
        {_, #{value := NFvalue}} -> S2#{value => NFvalue};
        {#{value := PFvalue}, _} -> S2#{value => PFvalue};
        {_, _} -> S2
    end.

-compile({nowarn_unused_function,merge_msg_instrumentation_scope/3}).
merge_msg_instrumentation_scope(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {_, #{name := NFname}} -> S1#{name => NFname};
             {#{name := PFname}, _} -> S1#{name => PFname};
             _ -> S1
         end,
    S3 = case {PMsg, NMsg} of
             {_, #{version := NFversion}} -> S2#{version => NFversion};
             {#{version := PFversion}, _} -> S2#{version => PFversion};
             _ -> S2
         end,
    S4 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S3#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S3#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S3#{attributes => PFattributes};
             {_, _} -> S3
         end,
    case {PMsg, NMsg} of
        {_, #{dropped_attributes_count := NFdropped_attributes_count}} -> S4#{dropped_attributes_count => NFdropped_attributes_count};
        {#{dropped_attributes_count := PFdropped_attributes_count}, _} -> S4#{dropped_attributes_count => PFdropped_attributes_count};
        _ -> S4
    end.

-compile({nowarn_unused_function,merge_msg_resource/3}).
merge_msg_resource(PMsg, NMsg, TrUserData) ->
    S1 = #{},
    S2 = case {PMsg, NMsg} of
             {#{attributes := PFattributes}, #{attributes := NFattributes}} -> S1#{attributes => 'erlang_++'(PFattributes, NFattributes, TrUserData)};
             {_, #{attributes := NFattributes}} -> S1#{attributes => NFattributes};
             {#{attributes := PFattributes}, _} -> S1#{attributes => PFattributes};
             {_, _} -> S1
         end,
    case {PMsg, NMsg} of
        {_, #{dropped_attributes_count := NFdropped_attributes_count}} -> S2#{dropped_attributes_count => NFdropped_attributes_count};
        {#{dropped_attributes_count := PFdropped_attributes_count}, _} -> S2#{dropped_attributes_count => PFdropped_attributes_count};
        _ -> S2
    end.


verify_msg(Msg, MsgName) when is_atom(MsgName) -> verify_msg(Msg, MsgName, []).

verify_msg(Msg, MsgName, Opts) ->
    TrUserData = proplists:get_value(user_data, Opts),
    case MsgName of
        export_logs_service_request -> v_msg_export_logs_service_request(Msg, [MsgName], TrUserData);
        export_logs_service_response -> v_msg_export_logs_service_response(Msg, [MsgName], TrUserData);
        export_logs_partial_success -> v_msg_export_logs_partial_success(Msg, [MsgName], TrUserData);
        logs_data -> v_msg_logs_data(Msg, [MsgName], TrUserData);
        resource_logs -> v_msg_resource_logs(Msg, [MsgName], TrUserData);
        scope_logs -> v_msg_scope_logs(Msg, [MsgName], TrUserData);
        log_record -> v_msg_log_record(Msg, [MsgName], TrUserData);
        any_value -> v_msg_any_value(Msg, [MsgName], TrUserData);
        array_value -> v_msg_array_value(Msg, [MsgName], TrUserData);
        key_value_list -> v_msg_key_value_list(Msg, [MsgName], TrUserData);
        key_value -> v_msg_key_value(Msg, [MsgName], TrUserData);
        instrumentation_scope -> v_msg_instrumentation_scope(Msg, [MsgName], TrUserData);
        resource -> v_msg_resource(Msg, [MsgName], TrUserData);
        _ -> mk_type_error(not_a_known_message, Msg, [])
    end.


-compile({nowarn_unused_function,v_msg_export_logs_service_request/3}).
-dialyzer({nowarn_function,v_msg_export_logs_service_request/3}).
v_msg_export_logs_service_request(#{} = M, Path, TrUserData) ->
    case M of
        #{resource_logs := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_resource_logs(Elem, [resource_logs | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, resource_logs}}, F1, [resource_logs | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (resource_logs) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_export_logs_service_request(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), export_logs_service_request}, M, Path);
v_msg_export_logs_service_request(X, Path, _TrUserData) -> mk_type_error({expected_msg, export_logs_service_request}, X, Path).

-compile({nowarn_unused_function,v_msg_export_logs_service_response/3}).
-dialyzer({nowarn_function,v_msg_export_logs_service_response/3}).
v_msg_export_logs_service_response(#{} = M, Path, TrUserData) ->
    case M of
        #{partial_success := F1} -> v_msg_export_logs_partial_success(F1, [partial_success | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (partial_success) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_export_logs_service_response(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), export_logs_service_response}, M, Path);
v_msg_export_logs_service_response(X, Path, _TrUserData) -> mk_type_error({expected_msg, export_logs_service_response}, X, Path).

-compile({nowarn_unused_function,v_msg_export_logs_partial_success/3}).
-dialyzer({nowarn_function,v_msg_export_logs_partial_success/3}).
v_msg_export_logs_partial_success(#{} = M, Path, TrUserData) ->
    case M of
        #{rejected_log_records := F1} -> v_type_int64(F1, [rejected_log_records | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{error_message := F2} -> v_type_string(F2, [error_message | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (rejected_log_records) -> ok;
                      (error_message) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_export_logs_partial_success(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), export_logs_partial_success}, M, Path);
v_msg_export_logs_partial_success(X, Path, _TrUserData) -> mk_type_error({expected_msg, export_logs_partial_success}, X, Path).

-compile({nowarn_unused_function,v_msg_logs_data/3}).
-dialyzer({nowarn_function,v_msg_logs_data/3}).
v_msg_logs_data(#{} = M, Path, TrUserData) ->
    case M of
        #{resource_logs := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_resource_logs(Elem, [resource_logs | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, resource_logs}}, F1, [resource_logs | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (resource_logs) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_logs_data(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), logs_data}, M, Path);
v_msg_logs_data(X, Path, _TrUserData) -> mk_type_error({expected_msg, logs_data}, X, Path).

-compile({nowarn_unused_function,v_msg_resource_logs/3}).
-dialyzer({nowarn_function,v_msg_resource_logs/3}).
v_msg_resource_logs(#{} = M, Path, TrUserData) ->
    case M of
        #{resource := F1} -> v_msg_resource(F1, [resource | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{scope_logs := F2} ->
            if is_list(F2) ->
                   _ = [v_msg_scope_logs(Elem, [scope_logs | Path], TrUserData) || Elem <- F2],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, scope_logs}}, F2, [scope_logs | Path])
            end;
        _ -> ok
    end,
    case M of
        #{schema_url := F3} -> v_type_string(F3, [schema_url | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (resource) -> ok;
                      (scope_logs) -> ok;
                      (schema_url) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_resource_logs(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), resource_logs}, M, Path);
v_msg_resource_logs(X, Path, _TrUserData) -> mk_type_error({expected_msg, resource_logs}, X, Path).

-compile({nowarn_unused_function,v_msg_scope_logs/3}).
-dialyzer({nowarn_function,v_msg_scope_logs/3}).
v_msg_scope_logs(#{} = M, Path, TrUserData) ->
    case M of
        #{scope := F1} -> v_msg_instrumentation_scope(F1, [scope | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{log_records := F2} ->
            if is_list(F2) ->
                   _ = [v_msg_log_record(Elem, [log_records | Path], TrUserData) || Elem <- F2],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, log_record}}, F2, [log_records | Path])
            end;
        _ -> ok
    end,
    case M of
        #{schema_url := F3} -> v_type_string(F3, [schema_url | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (scope) -> ok;
                      (log_records) -> ok;
                      (schema_url) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_scope_logs(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), scope_logs}, M, Path);
v_msg_scope_logs(X, Path, _TrUserData) -> mk_type_error({expected_msg, scope_logs}, X, Path).

-compile({nowarn_unused_function,v_msg_log_record/3}).
-dialyzer({nowarn_function,v_msg_log_record/3}).
v_msg_log_record(#{} = M, Path, TrUserData) ->
    case M of
        #{time_unix_nano := F1} -> v_type_fixed64(F1, [time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{observed_time_unix_nano := F2} -> v_type_fixed64(F2, [observed_time_unix_nano | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{severity_number := F3} -> 'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'(F3, [severity_number | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{severity_text := F4} -> v_type_string(F4, [severity_text | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{body := F5} -> v_msg_any_value(F5, [body | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{attributes := F6} ->
            if is_list(F6) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F6],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F6, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{dropped_attributes_count := F7} -> v_type_uint32(F7, [dropped_attributes_count | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{flags := F8} -> v_type_fixed32(F8, [flags | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{trace_id := F9} -> v_type_bytes(F9, [trace_id | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{span_id := F10} -> v_type_bytes(F10, [span_id | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (time_unix_nano) -> ok;
                      (observed_time_unix_nano) -> ok;
                      (severity_number) -> ok;
                      (severity_text) -> ok;
                      (body) -> ok;
                      (attributes) -> ok;
                      (dropped_attributes_count) -> ok;
                      (flags) -> ok;
                      (trace_id) -> ok;
                      (span_id) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_log_record(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), log_record}, M, Path);
v_msg_log_record(X, Path, _TrUserData) -> mk_type_error({expected_msg, log_record}, X, Path).

-compile({nowarn_unused_function,v_msg_any_value/3}).
-dialyzer({nowarn_function,v_msg_any_value/3}).
v_msg_any_value(#{} = M, Path, TrUserData) ->
    case M of
        #{value := {string_value, OF1}} -> v_type_string(OF1, [string_value, value | Path], TrUserData);
        #{value := {bool_value, OF1}} -> v_type_bool(OF1, [bool_value, value | Path], TrUserData);
        #{value := {int_value, OF1}} -> v_type_int64(OF1, [int_value, value | Path], TrUserData);
        #{value := {double_value, OF1}} -> v_type_double(OF1, [double_value, value | Path], TrUserData);
        #{value := {array_value, OF1}} -> v_msg_array_value(OF1, [array_value, value | Path], TrUserData);
        #{value := {kvlist_value, OF1}} -> v_msg_key_value_list(OF1, [kvlist_value, value | Path], TrUserData);
        #{value := {bytes_value, OF1}} -> v_type_bytes(OF1, [bytes_value, value | Path], TrUserData);
        #{value := F1} -> mk_type_error(invalid_oneof, F1, [value | Path]);
        _ -> ok
    end,
    lists:foreach(fun (value) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_any_value(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), any_value}, M, Path);
v_msg_any_value(X, Path, _TrUserData) -> mk_type_error({expected_msg, any_value}, X, Path).

-compile({nowarn_unused_function,v_msg_array_value/3}).
-dialyzer({nowarn_function,v_msg_array_value/3}).
v_msg_array_value(#{} = M, Path, TrUserData) ->
    case M of
        #{values := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_any_value(Elem, [values | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, any_value}}, F1, [values | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (values) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_array_value(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), array_value}, M, Path);
v_msg_array_value(X, Path, _TrUserData) -> mk_type_error({expected_msg, array_value}, X, Path).

-compile({nowarn_unused_function,v_msg_key_value_list/3}).
-dialyzer({nowarn_function,v_msg_key_value_list/3}).
v_msg_key_value_list(#{} = M, Path, TrUserData) ->
    case M of
        #{values := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [values | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [values | Path])
            end;
        _ -> ok
    end,
    lists:foreach(fun (values) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_key_value_list(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), key_value_list}, M, Path);
v_msg_key_value_list(X, Path, _TrUserData) -> mk_type_error({expected_msg, key_value_list}, X, Path).

-compile({nowarn_unused_function,v_msg_key_value/3}).
-dialyzer({nowarn_function,v_msg_key_value/3}).
v_msg_key_value(#{} = M, Path, TrUserData) ->
    case M of
        #{key := F1} -> v_type_string(F1, [key | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{value := F2} -> v_msg_any_value(F2, [value | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (key) -> ok;
                      (value) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_key_value(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), key_value}, M, Path);
v_msg_key_value(X, Path, _TrUserData) -> mk_type_error({expected_msg, key_value}, X, Path).

-compile({nowarn_unused_function,v_msg_instrumentation_scope/3}).
-dialyzer({nowarn_function,v_msg_instrumentation_scope/3}).
v_msg_instrumentation_scope(#{} = M, Path, TrUserData) ->
    case M of
        #{name := F1} -> v_type_string(F1, [name | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{version := F2} -> v_type_string(F2, [version | Path], TrUserData);
        _ -> ok
    end,
    case M of
        #{attributes := F3} ->
            if is_list(F3) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F3],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F3, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{dropped_attributes_count := F4} -> v_type_uint32(F4, [dropped_attributes_count | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (name) -> ok;
                      (version) -> ok;
                      (attributes) -> ok;
                      (dropped_attributes_count) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_instrumentation_scope(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), instrumentation_scope}, M, Path);
v_msg_instrumentation_scope(X, Path, _TrUserData) -> mk_type_error({expected_msg, instrumentation_scope}, X, Path).

-compile({nowarn_unused_function,v_msg_resource/3}).
-dialyzer({nowarn_function,v_msg_resource/3}).
v_msg_resource(#{} = M, Path, TrUserData) ->
    case M of
        #{attributes := F1} ->
            if is_list(F1) ->
                   _ = [v_msg_key_value(Elem, [attributes | Path], TrUserData) || Elem <- F1],
                   ok;
               true -> mk_type_error({invalid_list_of, {msg, key_value}}, F1, [attributes | Path])
            end;
        _ -> ok
    end,
    case M of
        #{dropped_attributes_count := F2} -> v_type_uint32(F2, [dropped_attributes_count | Path], TrUserData);
        _ -> ok
    end,
    lists:foreach(fun (attributes) -> ok;
                      (dropped_attributes_count) -> ok;
                      (OtherKey) -> mk_type_error({extraneous_key, OtherKey}, M, Path)
                  end,
                  maps:keys(M)),
    ok;
v_msg_resource(M, Path, _TrUserData) when is_map(M) -> mk_type_error({missing_fields, [] -- maps:keys(M), resource}, M, Path);
v_msg_resource(X, Path, _TrUserData) -> mk_type_error({expected_msg, resource}, X, Path).

-compile({nowarn_unused_function,'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'/3}).
-dialyzer({nowarn_function,'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'/3}).
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_UNSPECIFIED', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE2', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE3', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE4', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG2', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG3', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG4', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO2', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO3', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO4', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN2', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN3', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN4', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR2', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR3', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR4', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL2', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL3', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL4', _Path, _TrUserData) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'(V, _Path, _TrUserData) when -2147483648 =< V, V =< 2147483647, is_integer(V) -> ok;
'v_enum_opentelemetry.proto.logs.v1.SeverityNumber'(X, Path, _TrUserData) -> mk_type_error({invalid_enum, 'opentelemetry.proto.logs.v1.SeverityNumber'}, X, Path).

-compile({nowarn_unused_function,v_type_int64/3}).
-dialyzer({nowarn_function,v_type_int64/3}).
v_type_int64(N, _Path, _TrUserData) when -9223372036854775808 =< N, N =< 9223372036854775807 -> ok;
v_type_int64(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, int64, signed, 64}, N, Path);
v_type_int64(X, Path, _TrUserData) -> mk_type_error({bad_integer, int64, signed, 64}, X, Path).

-compile({nowarn_unused_function,v_type_uint32/3}).
-dialyzer({nowarn_function,v_type_uint32/3}).
v_type_uint32(N, _Path, _TrUserData) when 0 =< N, N =< 4294967295 -> ok;
v_type_uint32(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, uint32, unsigned, 32}, N, Path);
v_type_uint32(X, Path, _TrUserData) -> mk_type_error({bad_integer, uint32, unsigned, 32}, X, Path).

-compile({nowarn_unused_function,v_type_fixed32/3}).
-dialyzer({nowarn_function,v_type_fixed32/3}).
v_type_fixed32(N, _Path, _TrUserData) when 0 =< N, N =< 4294967295 -> ok;
v_type_fixed32(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, fixed32, unsigned, 32}, N, Path);
v_type_fixed32(X, Path, _TrUserData) -> mk_type_error({bad_integer, fixed32, unsigned, 32}, X, Path).

-compile({nowarn_unused_function,v_type_fixed64/3}).
-dialyzer({nowarn_function,v_type_fixed64/3}).
v_type_fixed64(N, _Path, _TrUserData) when 0 =< N, N =< 18446744073709551615 -> ok;
v_type_fixed64(N, Path, _TrUserData) when is_integer(N) -> mk_type_error({value_out_of_range, fixed64, unsigned, 64}, N, Path);
v_type_fixed64(X, Path, _TrUserData) -> mk_type_error({bad_integer, fixed64, unsigned, 64}, X, Path).

-compile({nowarn_unused_function,v_type_bool/3}).
-dialyzer({nowarn_function,v_type_bool/3}).
v_type_bool(false, _Path, _TrUserData) -> ok;
v_type_bool(true, _Path, _TrUserData) -> ok;
v_type_bool(0, _Path, _TrUserData) -> ok;
v_type_bool(1, _Path, _TrUserData) -> ok;
v_type_bool(X, Path, _TrUserData) -> mk_type_error(bad_boolean_value, X, Path).

-compile({nowarn_unused_function,v_type_double/3}).
-dialyzer({nowarn_function,v_type_double/3}).
v_type_double(N, _Path, _TrUserData) when is_float(N) -> ok;
v_type_double(N, _Path, _TrUserData) when is_integer(N) -> ok;
v_type_double(infinity, _Path, _TrUserData) -> ok;
v_type_double('-infinity', _Path, _TrUserData) -> ok;
v_type_double(nan, _Path, _TrUserData) -> ok;
v_type_double(X, Path, _TrUserData) -> mk_type_error(bad_double_value, X, Path).

-compile({nowarn_unused_function,v_type_string/3}).
-dialyzer({nowarn_function,v_type_string/3}).
v_type_string(S, Path, _TrUserData) when is_list(S); is_binary(S) ->
    try unicode:characters_to_binary(S) of
        B when is_binary(B) -> ok;
        {error, _, _} -> mk_type_error(bad_unicode_string, S, Path)
    catch
        error:badarg -> mk_type_error(bad_unicode_string, S, Path)
    end;
v_type_string(X, Path, _TrUserData) -> mk_type_error(bad_unicode_string, X, Path).

-compile({nowarn_unused_function,v_type_bytes/3}).
-dialyzer({nowarn_function,v_type_bytes/3}).
v_type_bytes(B, _Path, _TrUserData) when is_binary(B) -> ok;
v_type_bytes(B, _Path, _TrUserData) when is_list(B) -> ok;
v_type_bytes(X, Path, _TrUserData) -> mk_type_error(bad_binary_value, X, Path).

-compile({nowarn_unused_function,mk_type_error/3}).
-spec mk_type_error(_, _, list()) -> no_return().
mk_type_error(Error, ValueSeen, Path) ->
    Path2 = prettify_path(Path),
    erlang:error({gpb_type_error, {Error, [{value, ValueSeen}, {path, Path2}]}}).


-compile({nowarn_unused_function,prettify_path/1}).
-dialyzer({nowarn_function,prettify_path/1}).
prettify_path([]) -> top_level;
prettify_path(PathR) -> lists:append(lists:join(".", lists:map(fun atom_to_list/1, lists:reverse(PathR)))).


-compile({nowarn_unused_function,id/2}).
-compile({inline,id/2}).
id(X, _TrUserData) -> X.

-compile({nowarn_unused_function,v_ok/3}).
-compile({inline,v_ok/3}).
v_ok(_Value, _Path, _TrUserData) -> ok.

-compile({nowarn_unused_function,m_overwrite/3}).
-compile({inline,m_overwrite/3}).
m_overwrite(_Prev, New, _TrUserData) -> New.

-compile({nowarn_unused_function,cons/3}).
-compile({inline,cons/3}).
cons(Elem, Acc, _TrUserData) -> [Elem | Acc].

-compile({nowarn_unused_function,lists_reverse/2}).
-compile({inline,lists_reverse/2}).
'lists_reverse'(L, _TrUserData) -> lists:reverse(L).
-compile({nowarn_unused_function,'erlang_++'/3}).
-compile({inline,'erlang_++'/3}).
'erlang_++'(A, B, _TrUserData) -> A ++ B.


get_msg_defs() ->
    [{{enum, 'opentelemetry.proto.logs.v1.SeverityNumber'},
      [{'SEVERITY_NUMBER_UNSPECIFIED', 0},
       {'SEVERITY_NUMBER_TRACE', 1},
       {'SEVERITY_NUMBER_TRACE2', 2},
       {'SEVERITY_NUMBER_TRACE3', 3},
       {'SEVERITY_NUMBER_TRACE4', 4},
       {'SEVERITY_NUMBER_DEBUG', 5},
       {'SEVERITY_NUMBER_DEBUG2', 6},
       {'SEVERITY_NUMBER_DEBUG3', 7},
       {'SEVERITY_NUMBER_DEBUG4', 8},
       {'SEVERITY_NUMBER_INFO', 9},
       {'SEVERITY_NUMBER_INFO2', 10},
       {'SEVERITY_NUMBER_INFO3', 11},
       {'SEVERITY_NUMBER_INFO4', 12},
       {'SEVERITY_NUMBER_WARN', 13},
       {'SEVERITY_NUMBER_WARN2', 14},
       {'SEVERITY_NUMBER_WARN3', 15},
       {'SEVERITY_NUMBER_WARN4', 16},
       {'SEVERITY_NUMBER_ERROR', 17},
       {'SEVERITY_NUMBER_ERROR2', 18},
       {'SEVERITY_NUMBER_ERROR3', 19},
       {'SEVERITY_NUMBER_ERROR4', 20},
       {'SEVERITY_NUMBER_FATAL', 21},
       {'SEVERITY_NUMBER_FATAL2', 22},
       {'SEVERITY_NUMBER_FATAL3', 23},
       {'SEVERITY_NUMBER_FATAL4', 24}]},
     {{enum, 'opentelemetry.proto.logs.v1.LogRecordFlags'}, [{'LOG_RECORD_FLAG_UNSPECIFIED', 0}, {'LOG_RECORD_FLAG_TRACE_FLAGS_MASK', 255}]},
     {{msg, export_logs_service_request}, [#{name => resource_logs, fnum => 1, rnum => 2, type => {msg, resource_logs}, occurrence => repeated, opts => []}]},
     {{msg, export_logs_service_response}, [#{name => partial_success, fnum => 1, rnum => 2, type => {msg, export_logs_partial_success}, occurrence => optional, opts => []}]},
     {{msg, export_logs_partial_success}, [#{name => rejected_log_records, fnum => 1, rnum => 2, type => int64, occurrence => optional, opts => []}, #{name => error_message, fnum => 2, rnum => 3, type => string, occurrence => optional, opts => []}]},
     {{msg, logs_data}, [#{name => resource_logs, fnum => 1, rnum => 2, type => {msg, resource_logs}, occurrence => repeated, opts => []}]},
     {{msg, resource_logs},
      [#{name => resource, fnum => 1, rnum => 2, type => {msg, resource}, occurrence => optional, opts => []},
       #{name => scope_logs, fnum => 2, rnum => 3, type => {msg, scope_logs}, occurrence => repeated, opts => []},
       #{name => schema_url, fnum => 3, rnum => 4, type => string, occurrence => optional, opts => []}]},
     {{msg, scope_logs},
      [#{name => scope, fnum => 1, rnum => 2, type => {msg, instrumentation_scope}, occurrence => optional, opts => []},
       #{name => log_records, fnum => 2, rnum => 3, type => {msg, log_record}, occurrence => repeated, opts => []},
       #{name => schema_url, fnum => 3, rnum => 4, type => string, occurrence => optional, opts => []}]},
     {{msg, log_record},
      [#{name => time_unix_nano, fnum => 1, rnum => 2, type => fixed64, occurrence => optional, opts => []},
       #{name => observed_time_unix_nano, fnum => 11, rnum => 3, type => fixed64, occurrence => optional, opts => []},
       #{name => severity_number, fnum => 2, rnum => 4, type => {enum, 'opentelemetry.proto.logs.v1.SeverityNumber'}, occurrence => optional, opts => []},
       #{name => severity_text, fnum => 3, rnum => 5, type => string, occurrence => optional, opts => []},
       #{name => body, fnum => 5, rnum => 6, type => {msg, any_value}, occurrence => optional, opts => []},
       #{name => attributes, fnum => 6, rnum => 7, type => {msg, key_value}, occurrence => repeated, opts => []},
       #{name => dropped_attributes_count, fnum => 7, rnum => 8, type => uint32, occurrence => optional, opts => []},
       #{name => flags, fnum => 8, rnum => 9, type => fixed32, occurrence => optional, opts => []},
       #{name => trace_id, fnum => 9, rnum => 10, type => bytes, occurrence => optional, opts => []},
       #{name => span_id, fnum => 10, rnum => 11, type => bytes, occurrence => optional, opts => []}]},
     {{msg, any_value},
      [#{name => value, rnum => 2,
         fields =>
             [#{name => string_value, fnum => 1, rnum => 2, type => string, occurrence => optional, opts => []},
              #{name => bool_value, fnum => 2, rnum => 2, type => bool, occurrence => optional, opts => []},
              #{name => int_value, fnum => 3, rnum => 2, type => int64, occurrence => optional, opts => []},
              #{name => double_value, fnum => 4, rnum => 2, type => double, occurrence => optional, opts => []},
              #{name => array_value, fnum => 5, rnum => 2, type => {msg, array_value}, occurrence => optional, opts => []},
              #{name => kvlist_value, fnum => 6, rnum => 2, type => {msg, key_value_list}, occurrence => optional, opts => []},
              #{name => bytes_value, fnum => 7, rnum => 2, type => bytes, occurrence => optional, opts => []}],
         opts => []}]},
     {{msg, array_value}, [#{name => values, fnum => 1, rnum => 2, type => {msg, any_value}, occurrence => repeated, opts => []}]},
     {{msg, key_value_list}, [#{name => values, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []}]},
     {{msg, key_value}, [#{name => key, fnum => 1, rnum => 2, type => string, occurrence => optional, opts => []}, #{name => value, fnum => 2, rnum => 3, type => {msg, any_value}, occurrence => optional, opts => []}]},
     {{msg, instrumentation_scope},
      [#{name => name, fnum => 1, rnum => 2, type => string, occurrence => optional, opts => []},
       #{name => version, fnum => 2, rnum => 3, type => string, occurrence => optional, opts => []},
       #{name => attributes, fnum => 3, rnum => 4, type => {msg, key_value}, occurrence => repeated, opts => []},
       #{name => dropped_attributes_count, fnum => 4, rnum => 5, type => uint32, occurrence => optional, opts => []}]},
     {{msg, resource}, [#{name => attributes, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []}, #{name => dropped_attributes_count, fnum => 2, rnum => 3, type => uint32, occurrence => optional, opts => []}]}].


get_msg_names() -> [export_logs_service_request, export_logs_service_response, export_logs_partial_success, logs_data, resource_logs, scope_logs, log_record, any_value, array_value, key_value_list, key_value, instrumentation_scope, resource].


get_group_names() -> [].


get_msg_or_group_names() ->
    [export_logs_service_request, export_logs_service_response, export_logs_partial_success, logs_data, resource_logs, scope_logs, log_record, any_value, array_value, key_value_list, key_value, instrumentation_scope, resource].


get_enum_names() -> ['opentelemetry.proto.logs.v1.SeverityNumber', 'opentelemetry.proto.logs.v1.LogRecordFlags'].


fetch_msg_def(MsgName) ->
    case find_msg_def(MsgName) of
        Fs when is_list(Fs) -> Fs;
        error -> erlang:error({no_such_msg, MsgName})
    end.


fetch_enum_def(EnumName) ->
    case find_enum_def(EnumName) of
        Es when is_list(Es) -> Es;
        error -> erlang:error({no_such_enum, EnumName})
    end.


find_msg_def(export_logs_service_request) -> [#{name => resource_logs, fnum => 1, rnum => 2, type => {msg, resource_logs}, occurrence => repeated, opts => []}];
find_msg_def(export_logs_service_response) -> [#{name => partial_success, fnum => 1, rnum => 2, type => {msg, export_logs_partial_success}, occurrence => optional, opts => []}];
find_msg_def(export_logs_partial_success) ->
    [#{name => rejected_log_records, fnum => 1, rnum => 2, type => int64, occurrence => optional, opts => []}, #{name => error_message, fnum => 2, rnum => 3, type => string, occurrence => optional, opts => []}];
find_msg_def(logs_data) -> [#{name => resource_logs, fnum => 1, rnum => 2, type => {msg, resource_logs}, occurrence => repeated, opts => []}];
find_msg_def(resource_logs) ->
    [#{name => resource, fnum => 1, rnum => 2, type => {msg, resource}, occurrence => optional, opts => []},
     #{name => scope_logs, fnum => 2, rnum => 3, type => {msg, scope_logs}, occurrence => repeated, opts => []},
     #{name => schema_url, fnum => 3, rnum => 4, type => string, occurrence => optional, opts => []}];
find_msg_def(scope_logs) ->
    [#{name => scope, fnum => 1, rnum => 2, type => {msg, instrumentation_scope}, occurrence => optional, opts => []},
     #{name => log_records, fnum => 2, rnum => 3, type => {msg, log_record}, occurrence => repeated, opts => []},
     #{name => schema_url, fnum => 3, rnum => 4, type => string, occurrence => optional, opts => []}];
find_msg_def(log_record) ->
    [#{name => time_unix_nano, fnum => 1, rnum => 2, type => fixed64, occurrence => optional, opts => []},
     #{name => observed_time_unix_nano, fnum => 11, rnum => 3, type => fixed64, occurrence => optional, opts => []},
     #{name => severity_number, fnum => 2, rnum => 4, type => {enum, 'opentelemetry.proto.logs.v1.SeverityNumber'}, occurrence => optional, opts => []},
     #{name => severity_text, fnum => 3, rnum => 5, type => string, occurrence => optional, opts => []},
     #{name => body, fnum => 5, rnum => 6, type => {msg, any_value}, occurrence => optional, opts => []},
     #{name => attributes, fnum => 6, rnum => 7, type => {msg, key_value}, occurrence => repeated, opts => []},
     #{name => dropped_attributes_count, fnum => 7, rnum => 8, type => uint32, occurrence => optional, opts => []},
     #{name => flags, fnum => 8, rnum => 9, type => fixed32, occurrence => optional, opts => []},
     #{name => trace_id, fnum => 9, rnum => 10, type => bytes, occurrence => optional, opts => []},
     #{name => span_id, fnum => 10, rnum => 11, type => bytes, occurrence => optional, opts => []}];
find_msg_def(any_value) ->
    [#{name => value, rnum => 2,
       fields =>
           [#{name => string_value, fnum => 1, rnum => 2, type => string, occurrence => optional, opts => []},
            #{name => bool_value, fnum => 2, rnum => 2, type => bool, occurrence => optional, opts => []},
            #{name => int_value, fnum => 3, rnum => 2, type => int64, occurrence => optional, opts => []},
            #{name => double_value, fnum => 4, rnum => 2, type => double, occurrence => optional, opts => []},
            #{name => array_value, fnum => 5, rnum => 2, type => {msg, array_value}, occurrence => optional, opts => []},
            #{name => kvlist_value, fnum => 6, rnum => 2, type => {msg, key_value_list}, occurrence => optional, opts => []},
            #{name => bytes_value, fnum => 7, rnum => 2, type => bytes, occurrence => optional, opts => []}],
       opts => []}];
find_msg_def(array_value) -> [#{name => values, fnum => 1, rnum => 2, type => {msg, any_value}, occurrence => repeated, opts => []}];
find_msg_def(key_value_list) -> [#{name => values, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []}];
find_msg_def(key_value) -> [#{name => key, fnum => 1, rnum => 2, type => string, occurrence => optional, opts => []}, #{name => value, fnum => 2, rnum => 3, type => {msg, any_value}, occurrence => optional, opts => []}];
find_msg_def(instrumentation_scope) ->
    [#{name => name, fnum => 1, rnum => 2, type => string, occurrence => optional, opts => []},
     #{name => version, fnum => 2, rnum => 3, type => string, occurrence => optional, opts => []},
     #{name => attributes, fnum => 3, rnum => 4, type => {msg, key_value}, occurrence => repeated, opts => []},
     #{name => dropped_attributes_count, fnum => 4, rnum => 5, type => uint32, occurrence => optional, opts => []}];
find_msg_def(resource) -> [#{name => attributes, fnum => 1, rnum => 2, type => {msg, key_value}, occurrence => repeated, opts => []}, #{name => dropped_attributes_count, fnum => 2, rnum => 3, type => uint32, occurrence => optional, opts => []}];
find_msg_def(_) -> error.


find_enum_def('opentelemetry.proto.logs.v1.SeverityNumber') ->
    [{'SEVERITY_NUMBER_UNSPECIFIED', 0},
     {'SEVERITY_NUMBER_TRACE', 1},
     {'SEVERITY_NUMBER_TRACE2', 2},
     {'SEVERITY_NUMBER_TRACE3', 3},
     {'SEVERITY_NUMBER_TRACE4', 4},
     {'SEVERITY_NUMBER_DEBUG', 5},
     {'SEVERITY_NUMBER_DEBUG2', 6},
     {'SEVERITY_NUMBER_DEBUG3', 7},
     {'SEVERITY_NUMBER_DEBUG4', 8},
     {'SEVERITY_NUMBER_INFO', 9},
     {'SEVERITY_NUMBER_INFO2', 10},
     {'SEVERITY_NUMBER_INFO3', 11},
     {'SEVERITY_NUMBER_INFO4', 12},
     {'SEVERITY_NUMBER_WARN', 13},
     {'SEVERITY_NUMBER_WARN2', 14},
     {'SEVERITY_NUMBER_WARN3', 15},
     {'SEVERITY_NUMBER_WARN4', 16},
     {'SEVERITY_NUMBER_ERROR', 17},
     {'SEVERITY_NUMBER_ERROR2', 18},
     {'SEVERITY_NUMBER_ERROR3', 19},
     {'SEVERITY_NUMBER_ERROR4', 20},
     {'SEVERITY_NUMBER_FATAL', 21},
     {'SEVERITY_NUMBER_FATAL2', 22},
     {'SEVERITY_NUMBER_FATAL3', 23},
     {'SEVERITY_NUMBER_FATAL4', 24}];
find_enum_def('opentelemetry.proto.logs.v1.LogRecordFlags') -> [{'LOG_RECORD_FLAG_UNSPECIFIED', 0}, {'LOG_RECORD_FLAG_TRACE_FLAGS_MASK', 255}];
find_enum_def(_) -> error.


enum_symbol_by_value('opentelemetry.proto.logs.v1.SeverityNumber', Value) -> 'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(Value);
enum_symbol_by_value('opentelemetry.proto.logs.v1.LogRecordFlags', Value) -> 'enum_symbol_by_value_opentelemetry.proto.logs.v1.LogRecordFlags'(Value).


enum_value_by_symbol('opentelemetry.proto.logs.v1.SeverityNumber', Sym) -> 'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'(Sym);
enum_value_by_symbol('opentelemetry.proto.logs.v1.LogRecordFlags', Sym) -> 'enum_value_by_symbol_opentelemetry.proto.logs.v1.LogRecordFlags'(Sym).


'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(0) -> 'SEVERITY_NUMBER_UNSPECIFIED';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(1) -> 'SEVERITY_NUMBER_TRACE';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(2) -> 'SEVERITY_NUMBER_TRACE2';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(3) -> 'SEVERITY_NUMBER_TRACE3';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(4) -> 'SEVERITY_NUMBER_TRACE4';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(5) -> 'SEVERITY_NUMBER_DEBUG';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(6) -> 'SEVERITY_NUMBER_DEBUG2';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(7) -> 'SEVERITY_NUMBER_DEBUG3';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(8) -> 'SEVERITY_NUMBER_DEBUG4';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(9) -> 'SEVERITY_NUMBER_INFO';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(10) -> 'SEVERITY_NUMBER_INFO2';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(11) -> 'SEVERITY_NUMBER_INFO3';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(12) -> 'SEVERITY_NUMBER_INFO4';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(13) -> 'SEVERITY_NUMBER_WARN';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(14) -> 'SEVERITY_NUMBER_WARN2';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(15) -> 'SEVERITY_NUMBER_WARN3';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(16) -> 'SEVERITY_NUMBER_WARN4';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(17) -> 'SEVERITY_NUMBER_ERROR';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(18) -> 'SEVERITY_NUMBER_ERROR2';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(19) -> 'SEVERITY_NUMBER_ERROR3';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(20) -> 'SEVERITY_NUMBER_ERROR4';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(21) -> 'SEVERITY_NUMBER_FATAL';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(22) -> 'SEVERITY_NUMBER_FATAL2';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(23) -> 'SEVERITY_NUMBER_FATAL3';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.SeverityNumber'(24) -> 'SEVERITY_NUMBER_FATAL4'.


'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_UNSPECIFIED') -> 0;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE') -> 1;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE2') -> 2;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE3') -> 3;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_TRACE4') -> 4;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG') -> 5;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG2') -> 6;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG3') -> 7;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_DEBUG4') -> 8;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO') -> 9;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO2') -> 10;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO3') -> 11;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_INFO4') -> 12;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN') -> 13;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN2') -> 14;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN3') -> 15;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_WARN4') -> 16;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR') -> 17;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR2') -> 18;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR3') -> 19;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_ERROR4') -> 20;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL') -> 21;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL2') -> 22;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL3') -> 23;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.SeverityNumber'('SEVERITY_NUMBER_FATAL4') -> 24.

'enum_symbol_by_value_opentelemetry.proto.logs.v1.LogRecordFlags'(0) -> 'LOG_RECORD_FLAG_UNSPECIFIED';
'enum_symbol_by_value_opentelemetry.proto.logs.v1.LogRecordFlags'(255) -> 'LOG_RECORD_FLAG_TRACE_FLAGS_MASK'.


'enum_value_by_symbol_opentelemetry.proto.logs.v1.LogRecordFlags'('LOG_RECORD_FLAG_UNSPECIFIED') -> 0;
'enum_value_by_symbol_opentelemetry.proto.logs.v1.LogRecordFlags'('LOG_RECORD_FLAG_TRACE_FLAGS_MASK') -> 255.


get_service_names() -> ['opentelemetry.proto.collector.logs.v1.LogsService'].


get_service_def('opentelemetry.proto.collector.logs.v1.LogsService') ->
    {{service, 'opentelemetry.proto.collector.logs.v1.LogsService'}, [#{name => 'Export', input => export_logs_service_request, output => export_logs_service_response, input_stream => false, output_stream => false, opts => []}]};
get_service_def(_) -> error.


get_rpc_names('opentelemetry.proto.collector.logs.v1.LogsService') -> ['Export'];
get_rpc_names(_) -> error.


find_rpc_def('opentelemetry.proto.collector.logs.v1.LogsService', RpcName) -> 'find_rpc_def_opentelemetry.proto.collector.logs.v1.LogsService'(RpcName);
find_rpc_def(_, _) -> error.


'find_rpc_def_opentelemetry.proto.collector.logs.v1.LogsService'('Export') -> #{name => 'Export', input => export_logs_service_request, output => export_logs_service_response, input_stream => false, output_stream => false, opts => []};
'find_rpc_def_opentelemetry.proto.collector.logs.v1.LogsService'(_) -> error.


fetch_rpc_def(ServiceName, RpcName) ->
    case find_rpc_def(ServiceName, RpcName) of
        Def when is_map(Def) -> Def;
        error -> erlang:error({no_such_rpc, ServiceName, RpcName})
    end.


%% Convert a a fully qualified (ie with package name) service name
%% as a binary to a service name as an atom.
fqbin_to_service_name(<<"opentelemetry.proto.collector.logs.v1.LogsService">>) -> 'opentelemetry.proto.collector.logs.v1.LogsService';
fqbin_to_service_name(X) -> error({gpb_error, {badservice, X}}).


%% Convert a service name as an atom to a fully qualified
%% (ie with package name) name as a binary.
service_name_to_fqbin('opentelemetry.proto.collector.logs.v1.LogsService') -> <<"opentelemetry.proto.collector.logs.v1.LogsService">>;
service_name_to_fqbin(X) -> error({gpb_error, {badservice, X}}).


%% Convert a a fully qualified (ie with package name) service name
%% and an rpc name, both as binaries to a service name and an rpc
%% name, as atoms.
fqbins_to_service_and_rpc_name(<<"opentelemetry.proto.collector.logs.v1.LogsService">>, <<"Export">>) -> {'opentelemetry.proto.collector.logs.v1.LogsService', 'Export'};
fqbins_to_service_and_rpc_name(S, R) -> error({gpb_error, {badservice_or_rpc, {S, R}}}).


%% Convert a service name and an rpc name, both as atoms,
%% to a fully qualified (ie with package name) service name and
%% an rpc name as binaries.
service_and_rpc_name_to_fqbins('opentelemetry.proto.collector.logs.v1.LogsService', 'Export') -> {<<"opentelemetry.proto.collector.logs.v1.LogsService">>, <<"Export">>};
service_and_rpc_name_to_fqbins(S, R) -> error({gpb_error, {badservice_or_rpc, {S, R}}}).


fqbin_to_msg_name(<<"opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest">>) -> export_logs_service_request;
fqbin_to_msg_name(<<"opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse">>) -> export_logs_service_response;
fqbin_to_msg_name(<<"opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess">>) -> export_logs_partial_success;
fqbin_to_msg_name(<<"opentelemetry.proto.logs.v1.LogsData">>) -> logs_data;
fqbin_to_msg_name(<<"opentelemetry.proto.logs.v1.ResourceLogs">>) -> resource_logs;
fqbin_to_msg_name(<<"opentelemetry.proto.logs.v1.ScopeLogs">>) -> scope_logs;
fqbin_to_msg_name(<<"opentelemetry.proto.logs.v1.LogRecord">>) -> log_record;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.AnyValue">>) -> any_value;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.ArrayValue">>) -> array_value;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.KeyValueList">>) -> key_value_list;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.KeyValue">>) -> key_value;
fqbin_to_msg_name(<<"opentelemetry.proto.common.v1.InstrumentationScope">>) -> instrumentation_scope;
fqbin_to_msg_name(<<"opentelemetry.proto.resource.v1.Resource">>) -> resource;
fqbin_to_msg_name(E) -> error({gpb_error, {badmsg, E}}).


msg_name_to_fqbin(export_logs_service_request) -> <<"opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest">>;
msg_name_to_fqbin(export_logs_service_response) -> <<"opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse">>;
msg_name_to_fqbin(export_logs_partial_success) -> <<"opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess">>;
msg_name_to_fqbin(logs_data) -> <<"opentelemetry.proto.logs.v1.LogsData">>;
msg_name_to_fqbin(resource_logs) -> <<"opentelemetry.proto.logs.v1.ResourceLogs">>;
msg_name_to_fqbin(scope_logs) -> <<"opentelemetry.proto.logs.v1.ScopeLogs">>;
msg_name_to_fqbin(log_record) -> <<"opentelemetry.proto.logs.v1.LogRecord">>;
msg_name_to_fqbin(any_value) -> <<"opentelemetry.proto.common.v1.AnyValue">>;
msg_name_to_fqbin(array_value) -> <<"opentelemetry.proto.common.v1.ArrayValue">>;
msg_name_to_fqbin(key_value_list) -> <<"opentelemetry.proto.common.v1.KeyValueList">>;
msg_name_to_fqbin(key_value) -> <<"opentelemetry.proto.common.v1.KeyValue">>;
msg_name_to_fqbin(instrumentation_scope) -> <<"opentelemetry.proto.common.v1.InstrumentationScope">>;
msg_name_to_fqbin(resource) -> <<"opentelemetry.proto.resource.v1.Resource">>;
msg_name_to_fqbin(E) -> error({gpb_error, {badmsg, E}}).


fqbin_to_enum_name(<<"opentelemetry.proto.logs.v1.SeverityNumber">>) -> 'opentelemetry.proto.logs.v1.SeverityNumber';
fqbin_to_enum_name(<<"opentelemetry.proto.logs.v1.LogRecordFlags">>) -> 'opentelemetry.proto.logs.v1.LogRecordFlags';
fqbin_to_enum_name(E) -> error({gpb_error, {badenum, E}}).


enum_name_to_fqbin('opentelemetry.proto.logs.v1.SeverityNumber') -> <<"opentelemetry.proto.logs.v1.SeverityNumber">>;
enum_name_to_fqbin('opentelemetry.proto.logs.v1.LogRecordFlags') -> <<"opentelemetry.proto.logs.v1.LogRecordFlags">>;
enum_name_to_fqbin(E) -> error({gpb_error, {badenum, E}}).


get_package_name() -> 'opentelemetry.proto.collector.logs.v1'.


%% Whether or not the message names
%% are prepended with package name or not.
uses_packages() -> true.


source_basename() -> "logs_service.proto".


%% Retrieve all proto file names, also imported ones.
%% The order is top-down. The first element is always the main
%% source file. The files are returned with extension,
%% see get_all_proto_names/0 for a version that returns
%% the basenames sans extension
get_all_source_basenames() -> ["logs_service.proto", "logs.proto", "common.proto", "resource.proto"].


%% Retrieve all proto file names, also imported ones.
%% The order is top-down. The first element is always the main
%% source file. The files are returned sans .proto extension,
%% to make it easier to use them with the various get_xyz_containment
%% functions.
get_all_proto_names() -> ["logs_service", "logs", "common", "resource"].


get_msg_containment("logs_service") -> [export_logs_partial_success, export_logs_service_request, export_logs_service_response];
get_msg_containment("logs") -> [log_record, logs_data, resource_logs, scope_logs];
get_msg_containment("common") -> [any_value, array_value, instrumentation_scope, key_value, key_value_list];
get_msg_containment("resource") -> [resource];
get_msg_containment(P) -> error({gpb_error, {badproto, P}}).


get_pkg_containment("logs_service") -> 'opentelemetry.proto.collector.logs.v1';
get_pkg_containment("logs") -> 'opentelemetry.proto.logs.v1';
get_pkg_containment("common") -> 'opentelemetry.proto.common.v1';
get_pkg_containment("resource") -> 'opentelemetry.proto.resource.v1';
get_pkg_containment(P) -> error({gpb_error, {badproto, P}}).


get_service_containment("logs_service") -> ['opentelemetry.proto.collector.logs.v1.LogsService'];
get_service_containment("logs") -> [];
get_service_containment("common") -> [];
get_service_containment("resource") -> [];
get_service_containment(P) -> error({gpb_error, {badproto, P}}).


get_rpc_containment("logs_service") -> [{'opentelemetry.proto.collector.logs.v1.LogsService', 'Export'}];
get_rpc_containment("logs") -> [];
get_rpc_containment("common") -> [];
get_rpc_containment("resource") -> [];
get_rpc_containment(P) -> error({gpb_error, {badproto, P}}).


get_enum_containment("logs_service") -> [];
get_enum_containment("logs") -> ['opentelemetry.proto.logs.v1.LogRecordFlags', 'opentelemetry.proto.logs.v1.SeverityNumber'];
get_enum_containment("common") -> [];
get_enum_containment("resource") -> [];
get_enum_containment(P) -> error({gpb_error, {badproto, P}}).


get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.logs.v1.LogsData">>) -> "logs";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.logs.v1.ScopeLogs">>) -> "logs";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.logs.v1.ResourceLogs">>) -> "logs";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess">>) -> "logs_service";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.KeyValueList">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.logs.v1.LogRecord">>) -> "logs";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest">>) -> "logs_service";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.resource.v1.Resource">>) -> "resource";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.KeyValue">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.InstrumentationScope">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.ArrayValue">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.common.v1.AnyValue">>) -> "common";
get_proto_by_msg_name_as_fqbin(<<"opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse">>) -> "logs_service";
get_proto_by_msg_name_as_fqbin(E) -> error({gpb_error, {badmsg, E}}).


get_proto_by_service_name_as_fqbin(<<"opentelemetry.proto.collector.logs.v1.LogsService">>) -> "logs_service";
get_proto_by_service_name_as_fqbin(E) -> error({gpb_error, {badservice, E}}).


get_proto_by_enum_name_as_fqbin(<<"opentelemetry.proto.logs.v1.SeverityNumber">>) -> "logs";
get_proto_by_enum_name_as_fqbin(<<"opentelemetry.proto.logs.v1.LogRecordFlags">>) -> "logs";
get_proto_by_enum_name_as_fqbin(E) -> error({gpb_error, {badenum, E}}).


get_protos_by_pkg_name_as_fqbin(<<"opentelemetry.proto.resource.v1">>) -> ["resource"];
get_protos_by_pkg_name_as_fqbin(<<"opentelemetry.proto.common.v1">>) -> ["common"];
get_protos_by_pkg_name_as_fqbin(<<"opentelemetry.proto.logs.v1">>) -> ["logs"];
get_protos_by_pkg_name_as_fqbin(<<"opentelemetry.proto.collector.logs.v1">>) -> ["logs_service"];
get_protos_by_pkg_name_as_fqbin(E) -> error({gpb_error, {badpkg, E}}).



gpb_version_as_string() ->
    "4.19.5".

gpb_version_as_list() ->
    [4,19,5].

gpb_version_source() ->
    "file".
