{application,sentry,
             [{modules,['Elixir.Mix.Tasks.Sentry.Install',
                        'Elixir.Mix.Tasks.Sentry.Install.Docs',
                        'Elixir.Mix.Tasks.Sentry.PackageSourceCode',
                        'Elixir.Mix.Tasks.Sentry.SendTestEvent',
                        'Elixir.SamplingContext','Elixir.Sentry',
                        'Elixir.Sentry.Application',
                        'Elixir.Sentry.Attachment','Elixir.Sentry.CheckIn',
                        'Elixir.Sentry.Client','Elixir.Sentry.ClientError',
                        'Elixir.Sentry.ClientReport',
                        'Elixir.Sentry.ClientReport.Sender',
                        'Elixir.Sentry.Config','Elixir.Sentry.Context',
                        'Elixir.Sentry.DSN','Elixir.Sentry.Dedupe',
                        'Elixir.Sentry.DefaultEventFilter',
                        'Elixir.Sentry.Envelope','Elixir.Sentry.Event',
                        'Elixir.Sentry.EventFilter',
                        'Elixir.Sentry.HTTPClient',
                        'Elixir.Sentry.HackneyClient',
                        'Elixir.Sentry.Integrations.CheckInIDMappings',
                        'Elixir.Sentry.Integrations.Oban.Cron',
                        'Elixir.Sentry.Integrations.Oban.ErrorReporter',
                        'Elixir.Sentry.Integrations.Quantum.Cron',
                        'Elixir.Sentry.Integrations.Telemetry',
                        'Elixir.Sentry.Interfaces',
                        'Elixir.Sentry.Interfaces.Breadcrumb',
                        'Elixir.Sentry.Interfaces.Exception',
                        'Elixir.Sentry.Interfaces.Exception.Mechanism',
                        'Elixir.Sentry.Interfaces.Message',
                        'Elixir.Sentry.Interfaces.Request',
                        'Elixir.Sentry.Interfaces.SDK',
                        'Elixir.Sentry.Interfaces.Span',
                        'Elixir.Sentry.Interfaces.Stacktrace',
                        'Elixir.Sentry.Interfaces.Stacktrace.Frame',
                        'Elixir.Sentry.Interfaces.Thread',
                        'Elixir.Sentry.JSON','Elixir.Sentry.LoggerBackend',
                        'Elixir.Sentry.LoggerHandler',
                        'Elixir.Sentry.LoggerHandler.RateLimiter',
                        'Elixir.Sentry.LoggerUtils',
                        'Elixir.Sentry.OpenTelemetry.VersionChecker',
                        'Elixir.Sentry.Options','Elixir.Sentry.PlugCapture',
                        'Elixir.Sentry.PlugContext','Elixir.Sentry.Sources',
                        'Elixir.Sentry.Test','Elixir.Sentry.Transaction',
                        'Elixir.Sentry.Transport',
                        'Elixir.Sentry.Transport.Sender',
                        'Elixir.Sentry.Transport.SenderPool',
                        'Elixir.Sentry.UUID']},
              {optional_applications,[hackney,jason,phoenix,phoenix_live_view,
                                      plug,telemetry,igniter,opentelemetry,
                                      opentelemetry_api,
                                      opentelemetry_exporter,
                                      opentelemetry_semantic_conventions]},
              {applications,[kernel,stdlib,elixir,logger,nimble_options,
                             nimble_ownership,hackney,jason,phoenix,
                             phoenix_live_view,plug,telemetry,igniter,
                             opentelemetry,opentelemetry_api,
                             opentelemetry_exporter,
                             opentelemetry_semantic_conventions]},
              {description,"The Official Elixir client for Sentry"},
              {vsn,"11.0.2"},
              {mod,{'Elixir.Sentry.Application',[]}},
              {registered,['Elixir.Sentry.Dedupe',
                           'Elixir.Sentry.Transport.SenderRegistry',
                           'Elixir.Sentry.Supervisor']}]}.
