{erl_opts,[debug_info]}.
{deps,[{grpcbox,">= 0.0.0"},
       {tls_certificate_check,"~> 1.18"},
       {opentelemetry,"~> 1.3"},
       {opentelemetry_api,"~> 1.2"}]}.
{grpc,[{protos,["opentelemetry-proto/opentelemetry/proto/collector/trace/v1",
                "opentelemetry-proto/opentelemetry/proto/collector/metrics/v1",
                "opentelemetry-proto/opentelemetry/proto/collector/logs/v1"]},
       {gpb_opts,[{module_name_prefix,"opentelemetry_exporter_"},
                  {module_name_suffix,"_pb"},
                  {i,"apps/opentelemetry_exporter/opentelemetry-proto/"}]}]}.
{profiles,[{docs,[{edoc_opts,[{doclet,edoc_doclet_chunks},
                              {layout,edoc_layout_chunks},
                              {preprocess,true},
                              {dir,"_build/default/lib/opentelemetry_exporter/doc"},
                              {subpackages,true}]}]}]}.
{overrides,[]}.
