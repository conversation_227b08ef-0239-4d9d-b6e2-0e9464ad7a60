{application,opentelemetry_exporter,
             [{description,"OpenTelemetry Protocol Exporter"},
              {vsn,"1.4.1"},
              {registered,[]},
              {applications,[kernel,stdlib,inets,ctx,grpcbox,
                             tls_certificate_check,opentelemetry_api]},
              {env,[]},
              {modules,[opentelemetry_exporter,
                        opentelemetry_exporter_logs_service_pb,
                        opentelemetry_exporter_metrics_service_pb,
                        opentelemetry_exporter_trace_service_pb,
                        opentelemetry_logs_service,
                        opentelemetry_metrics_service,
                        opentelemetry_trace_service,otel_otlp_common,
                        otel_otlp_traces]},
              {doc,"doc"},
              {licenses,["Apache-2.0"]},
              {links,[{"GitHub",
                       "https://github.com/open-telemetry/opentelemetry-erlang"}]}]}.
