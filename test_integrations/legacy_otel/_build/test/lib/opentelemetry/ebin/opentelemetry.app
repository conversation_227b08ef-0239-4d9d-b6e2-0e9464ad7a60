{application,opentelemetry,
             [{description,"Implementation of stable OpenTelemetry signals"},
              {vsn,"1.3.1"},
              {registered,[otel_tracer_provider_sup]},
              {mod,{opentelemetry_app,[]}},
              {applications,[kernel,stdlib,opentelemetry_api]},
              {env,[]},
              {modules,[opentelemetry_app,opentelemetry_sup,otel_attributes,
                        otel_batch_processor,otel_configuration,otel_events,
                        otel_exporter,otel_exporter_pid,otel_exporter_stdout,
                        otel_exporter_tab,otel_id_generator,otel_links,
                        otel_resource,otel_resource_app_env,
                        otel_resource_detector,otel_resource_env_var,
                        otel_sampler,otel_sampler_always_off,
                        otel_sampler_always_on,otel_sampler_parent_based,
                        otel_sampler_trace_id_ratio_based,
                        otel_simple_processor,otel_span_ets,otel_span_limits,
                        otel_span_processor,otel_span_processor_sup,
                        otel_span_sup,otel_span_sweeper,otel_span_utils,
                        otel_tracer_default,otel_tracer_provider_sup,
                        otel_tracer_server,otel_tracer_server_sup]},
              {doc,"doc"},
              {licenses,["Apache-2.0"]},
              {links,[{"GitHub",
                       "https://github.com/open-telemetry/opentelemetry-erlang"}]}]}.
