{application,acceptor_pool,
             [{description,"A tcp acceptor pool library"},
              {vsn,"1.0.0"},
              {registered,[]},
              {applications,[kernel,stdlib]},
              {env,[]},
              {modules,[acceptor,acceptor_loop,acceptor_pool]},
              {maintainers,["James Fish"]},
              {licenses,["Apache v2.0"]},
              {links,[{"Github",
                       "https://github.com/fishcakez/acceptor_pool"}]}]}.
