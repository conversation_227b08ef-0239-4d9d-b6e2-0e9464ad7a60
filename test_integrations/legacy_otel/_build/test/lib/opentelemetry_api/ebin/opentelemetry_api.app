{application,opentelemetry_api,
             [{modules,['Elixir.OpenTelemetry','Elixir.OpenTelemetry.Baggage',
                        'Elixir.OpenTelemetry.Ctx',
                        'Elixir.OpenTelemetry.Span',
                        'Elixir.OpenTelemetry.Tracer',opentelemetry,
                        otel_baggage,otel_ctx,otel_propagator,
                        otel_propagator_b3,otel_propagator_b3multi,
                        otel_propagator_b3single,otel_propagator_baggage,
                        otel_propagator_text_map,
                        otel_propagator_text_map_composite,
                        otel_propagator_text_map_noop,
                        otel_propagator_trace_context,otel_span,otel_tracer,
                        otel_tracer_noop,otel_tracer_provider,otel_utils]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,
                             opentelemetry_semantic_conventions]},
              {description,"OpenTelemetry API"},
              {registered,[]},
              {vsn,"1.2.2"}]}.
