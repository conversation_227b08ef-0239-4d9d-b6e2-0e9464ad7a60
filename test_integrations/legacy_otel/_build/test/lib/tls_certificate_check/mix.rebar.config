{erl_opts,[debug_info,warn_export_all,warn_export_vars,warn_missing_spec,
           warn_obsolete_guards,warn_shadow_vars,warn_unused_import,
           {platform_define,"^23.[1-3]",
                            'HARDCODED_AUTHORITIES_UPDATER_SUPPORTED'},
           {platform_define,"^2[4-9]",
                            'HARDCODED_AUTHORITIES_UPDATER_SUPPORTED'},
           {platform_define,"^[3-9]",
                            'HARDCODED_AUTHORITIES_UPDATER_SUPPORTED'},
           {platform_define,"^22\\.",'EXPIRED_CAs_ARE_CONSIDERED_VALID'},
           {platform_define,"^23\\.[0-2][^0-9]",
                            'EXPIRED_CAs_ARE_CONSIDERED_VALID'},
           {platform_define,"^22\\.",'FLAKY_CROSS_SIGNING_VALIDATION'},
           {platform_define,"^23\\.[0-1][^0-9]",
                            'FLAKY_CROSS_SIGNING_VALIDATION'},
           {platform_define,"^2[2-4]\\.",'NO_PUBLIC_KEY_CACERTS_GET'},
           {platform_define,"^22\\.",'MISMATCHED_SNI_DOESNT_CLOSE_CONN'}]}.
{minimum_otp_vsn,"22.0"}.
{deps,[{ssl_verify_fun,"~> 1.1"}]}.
{shell,[{apps,[tls_certificate_check]},{config,"shell.config"}]}.
{dialyzer,[{plt_include_all_deps,true},
           {warnings,[unmatched_returns,error_handling,underspecs,unknown]},
           {plt_extra_apps,[changelog_updater]}]}.
{xref_checks,[undefined_function_calls,undefined_functions,locals_not_used,
              exports_not_used,deprecated_function_calls,
              deprecated_functions]}.
{profiles,
    [{development,
         [{deps,[{recon,"~> 2.5"}]},
          {erl_opts,[nowarn_missing_spec,nowarnings_as_errors]}]},
     {test,
         [{erl_opts,
              [debug_info,nowarn_export_all,nowarn_missing_spec,
               nowarnings_as_errors]},
          {deps,[{certifi,"~> 2.10"},{meck,"0.9.2"}]},
          {cover_enabled,true},
          {cover_opts,[verbose]}]},
     {elixir_test,
         [{deps,[{castore,"0.1.20"}]},
          {plugins,[{rebar_mix,"0.5.1"}]},
          {provider_hooks,[{post,[{compile,{mix,consolidate_protocols}}]}]}]},
     {hardcoded_authorities_update,
         [{erl_opts,[{src_dirs,["src","util"]}]},
          {deps,
              [{changelog_updater,
                   {git,"https://github.com/g-andrade/changelog_updater.git",
                       {ref,"07b13ce"}}}]},
          {escript_incl_apps,[tls_certificate_check,changelog_updater]},
          {escript_name,"tls_certificate_check_hardcoded_authorities_updater"},
          {escript_emu_args,"%%! -noinput\n"}]}]}.
{ex_doc,[{source_url,<<"https://github.com/g-andrade/tls_certificate_check">>},
         {extras,[<<"README.md">>,<<"CHANGELOG.md">>,<<"LICENSE">>]},
         {main,<<"readme">>},
         {proglang,erlang}]}.
{hex,[{doc,#{provider => ex_doc}}]}.
{overrides,[]}.
