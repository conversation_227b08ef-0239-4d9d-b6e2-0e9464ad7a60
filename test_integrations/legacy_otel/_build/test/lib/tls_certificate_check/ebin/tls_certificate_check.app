{application,tls_certificate_check,
             [{description,"CA store + Partial chain handler"},
              {vsn,"1.28.0"},
              {registered,[tls_certificate_check_shared_state,
                           tls_certificate_check_sup]},
              {mod,{tls_certificate_check_app,[]}},
              {applications,[crypto,kernel,public_key,ssl,stdlib,
                             ssl_verify_fun]},
              {env,[]},
              {modules,[tls_certificate_check,tls_certificate_check_app,
                        tls_certificate_check_hardcoded_authorities,
                        tls_certificate_check_shared_state,
                        tls_certificate_check_sup,tls_certificate_check_util]},
              {licenses,["MIT"]},
              {links,[{"GitHub",
                       "https://github.com/g-andrade/tls_certificate_check"},
                      {"GitLab",
                       "https://gitlab.com/g-andrade/tls_certificate_check"}]}]}.
