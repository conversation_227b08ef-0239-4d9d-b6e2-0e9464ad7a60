{application,opentelemetry_semantic_conventions,
             [{modules,['Elixir.OpenTelemetry.SemanticConventions.Logs',
                        'Elixir.OpenTelemetry.SemanticConventions.Resource',
                        'Elixir.OpenTelemetry.SemanticConventions.Trace',
                        opentelemetry_semantic_conventions]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"Macros defining the keys of OpenTelemetry semantic conventions"},
              {registered,[]},
              {vsn,"0.2.0"}]}.
