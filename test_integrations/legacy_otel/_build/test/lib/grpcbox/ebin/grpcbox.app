{application,grpcbox,
             [{description,"Erlang grpc library based on chatterbox"},
              {vsn,"0.17.1"},
              {registered,[]},
              {mod,{grpcbox_app,[]}},
              {applications,[kernel,stdlib,chatterbox,acceptor_pool,gproc,
                             ctx]},
              {env,[{client,#{channels => []}},
                    {grpc_opts,#{service_protos => [],client_cert_dir => []}},
                    {transport_opts,#{ssl => false,cacertfile => [],
                                      certfile => [],keyfile => []}},
                    {listen_opts,#{port => 8080,ip => {0,0,0,0}}},
                    {pool_opts,#{size => 10}},
                    {server_opts,#{header_table_size => 4096,enable_push => 1,
                                   max_concurrent_streams => unlimited,
                                   initial_window_size => 65535,
                                   max_frame_size => 16384,
                                   max_header_list_size => unlimited}}]},
              {modules,[grpcbox,grpcbox_acceptor,grpcbox_app,
                        grpcbox_chain_interceptor,grpcbox_channel,
                        grpcbox_channel_sup,grpcbox_client,
                        grpcbox_client_stream,grpcbox_frame,
                        grpcbox_health_bhvr,grpcbox_health_client,
                        grpcbox_health_pb,grpcbox_health_service,
                        grpcbox_metadata,grpcbox_name_resolver,
                        grpcbox_oc_stats,grpcbox_oc_stats_handler,
                        grpcbox_pool,grpcbox_reflection_bhvr,
                        grpcbox_reflection_client,grpcbox_reflection_pb,
                        grpcbox_reflection_service,
                        grpcbox_services_simple_sup,grpcbox_services_sup,
                        grpcbox_socket,grpcbox_stream,grpcbox_subchannel,
                        grpcbox_sup,grpcbox_trace,grpcbox_utils]},
              {licenses,["Apache 2.0"]},
              {links,[{"Github","https://github.com/tsloughter/grpcbox"}]}]}.
