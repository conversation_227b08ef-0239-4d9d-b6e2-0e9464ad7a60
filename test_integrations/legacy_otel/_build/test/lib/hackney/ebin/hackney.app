{application,hackney,
             [{description,"simple HTTP client"},
              {vsn,"1.25.0"},
              {registered,[hackney_pool]},
              {applications,[kernel,stdlib,crypto,asn1,public_key,ssl,idna,
                             mimerl,certifi,parse_trans,ssl_verify_fun,
                             metrics,unicode_util_compat]},
              {included_applications,[]},
              {mod,{hackney_app,[]}},
              {env,[{timeout,150000},
                    {max_connections,50},
                    {restart,permanent},
                    {shutdown,10000},
                    {maxr,10},
                    {maxt,1}]},
              {licenses,["Apache-2.0"]},
              {links,[{"Github","https://github.com/benoitc/hackney"}]},
              {files,["src","include","rebar.config","rebar.lock","README.md",
                      "NEWS.md","LICENSE","NOTICE","MAINTAINERS"]},
              {modules,[hackney,hackney_app,hackney_bstr,hackney_cidr,
                        hackney_connect,hackney_connection,
                        hackney_connections,hackney_cookie,hackney_date,
                        hackney_happy,hackney_headers,hackney_headers_new,
                        hackney_http,hackney_http_connect,hackney_local_tcp,
                        hackney_manager,hackney_metrics,hackney_multipart,
                        hackney_pool,hackney_pool_handler,hackney_request,
                        hackney_response,hackney_socks5,hackney_ssl,
                        hackney_ssl_certificate,hackney_stream,hackney_sup,
                        hackney_tcp,hackney_trace,hackney_url,hackney_util]}]}.
