{application,legacy_otel,
             [{modules,['Elixir.LegacyOtel']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,sentry,hackney,
                             opentelemetry,opentelemetry_api,
                             opentelemetry_exporter,
                             opentelemetry_semantic_conventions]},
              {description,"legacy_otel"},
              {registered,[]},
              {vsn,"0.1.0"}]}.
