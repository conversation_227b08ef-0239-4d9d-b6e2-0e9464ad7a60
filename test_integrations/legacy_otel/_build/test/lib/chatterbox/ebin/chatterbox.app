{application,chatterbox,
             [{description,"chatterbox library for http2"},
              {vsn,"0.15.1"},
              {registered,[]},
              {applications,[kernel,stdlib,crypto,public_key,ssl,hpack]},
              {env,[{port,80},
                    {concurrent_acceptors,20},
                    {client_header_table_size,4096},
                    {client_enable_push,1},
                    {client_max_concurrent_streams,unlimited},
                    {client_initial_window_size,65535},
                    {client_max_frame_size,16384},
                    {client_max_header_list_size,unlimited},
                    {client_flow_control,auto},
                    {server_header_table_size,4096},
                    {server_enable_push,1},
                    {server_max_concurrent_streams,unlimited},
                    {server_initial_window_size,65535},
                    {server_max_frame_size,16384},
                    {server_max_header_list_size,unlimited},
                    {server_flow_control,auto}]},
              {pkg_name,ts_chatterbox},
              {licenses,["MIT"]},
              {links,[{"Github","https://github.com/joedevivo/chatterbox"}]},
              {modules,[chatterbox,chatterbox_ranch_protocol,
                        chatterbox_static_stream,chatterbox_sup,h2_client,
                        h2_connection,h2_frame,h2_frame_continuation,
                        h2_frame_data,h2_frame_goaway,h2_frame_headers,
                        h2_frame_ping,h2_frame_priority,h2_frame_push_promise,
                        h2_frame_rst_stream,h2_frame_settings,
                        h2_frame_window_update,h2_padding,h2_settings,
                        h2_stream,h2_stream_set,sock]}]}.
