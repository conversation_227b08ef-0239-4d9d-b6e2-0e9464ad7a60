{deps,[{hpack,".*",
              {git,"https://github.com/joedevivo/hpack.git",{tag,"0.2.3"}}}]}.
{erl_opts,[debug_info]}.
{cover_enabled,true}.
{ct_opts,[{verbose,true}]}.
{profiles,[{test,[{erl_opts,[{i,["include"]},nowarn_export_all]},
                  {deps,[{ranch,"2.0.0"}]}]},
           {check,[{deps,[{ranch,"2.0.0"}]},
                   {dialyzer,[{warnings,[error_handling,unknown]},
                              {plt_extra_apps,[inets,ranch]}]},
                   {xref_checks,[deprecated_function_calls,locals_not_used,
                                 undefined_function_calls]}]}]}.
{relx,[{release,{chatterbox,"0.0.1"},[chatterbox]},
       {sys_config,"./config/sys.config"},
       {dev_mode,true},
       {include_erts,false},
       {extended_start_script,true},
       {overlay,[{template,"config/sys.config","sys.config"},
                 {copy,"config/localhost.crt","."},
                 {copy,"config/localhost.key","."}]}]}.
{overrides,[]}.
