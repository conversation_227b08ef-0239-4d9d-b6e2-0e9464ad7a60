{erl_opts,[debug_info]}.
{deps,[]}.
{dialyzer_opts,[{warnings,[no_unused,no_improper_lists,no_fun_app,no_match,
                           no_opaque,no_fail_call,error_handling,no_match,
                           unmatched_returns,behaviours,underspecs]}]}.
{profiles,
    [{doc,
         [{deps,
              [{edown,".*",
                   {git,"https://github.com/uwiger/edown.git","HEAD"}}]},
          {edoc_opts,
              [{doclet,edown_doclet},
               {app_default,"http://www.erlang.org/doc/man"},
               {top_level_readme,
                   {"./README.md","http://github.com/uwiger/gproc"}}]}]}]}.
{shell,[{apps,[gproc]}]}.
{overrides,[]}.
